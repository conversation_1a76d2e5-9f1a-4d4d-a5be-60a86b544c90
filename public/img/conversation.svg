<svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
<foreignObject x="1.63661" y="2.64052" width="19.9072" height="18.9655"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.24px);clip-path:url(#bgblur_0_1_5057_clip_path);height:100%;width:100%"></div></foreignObject><path data-figma-bg-blur-radius="2.48839" d="M18.5553 6.9952C19.0553 7.86135 19.0553 9.02419 19.0553 11.3499C19.0553 13.6756 19.0553 14.8384 18.5553 15.7046C18.2277 16.272 17.7565 16.7432 17.189 17.0708C16.3229 17.5708 15.1601 17.5708 12.8344 17.5708H10.346C9.64506 17.5708 9.04977 17.5708 8.53639 17.5572L5.9967 19.0647C5.73491 19.2201 5.4072 19.0136 5.43442 18.7103L5.60458 16.8142C5.20852 16.5144 4.87501 16.1375 4.62507 15.7046C4.125 14.8384 4.125 13.6756 4.125 11.3499C4.125 9.02419 4.125 7.86135 4.62507 6.9952C4.95267 6.42777 5.42387 5.95658 5.99129 5.62898C6.85744 5.12891 8.02028 5.12891 10.346 5.12891L12.8344 5.12891C15.16 5.12891 16.3229 5.12891 17.189 5.62898C17.7565 5.95658 18.2277 6.42777 18.5553 6.9952Z" fill="url(#paint0_linear_1_5057)"/>
<foreignObject x="6.61317" y="6.37294" width="19.9072" height="18.9565"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.24px);clip-path:url(#bgblur_1_1_5057_clip_path);height:100%;width:100%"></div></foreignObject><path data-figma-bg-blur-radius="2.48839" d="M9.60163 10.7276C9.10156 11.5938 9.10156 12.7566 9.10156 15.0823C9.10156 17.408 9.10156 18.5708 9.60163 19.437C9.92924 20.0044 10.4004 20.4756 10.9679 20.8032C11.834 21.3033 12.9968 21.3033 15.3225 21.3033H17.8109C18.5427 21.3033 19.1593 21.3033 19.6878 21.2877L22.1563 22.7862C22.418 22.9451 22.7491 22.7387 22.7217 22.4338L22.5524 20.5466C22.9484 20.2467 23.2819 19.8699 23.5318 19.437C24.0319 18.5708 24.0319 17.408 24.0319 15.0823C24.0319 12.7566 24.0319 11.5938 23.5318 10.7276C23.2042 10.1602 22.733 9.689 22.1656 9.3614C21.2995 8.86133 20.1366 8.86133 17.8109 8.86133L15.3225 8.86133C12.9968 8.86133 11.834 8.86133 10.9679 9.3614C10.4004 9.689 9.92924 10.1602 9.60163 10.7276Z" fill="#6AE092" fill-opacity="0.6"/>
<foreignObject x="10.2959" y="10.0628" width="12.6843" height="6.34199"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.27px);clip-path:url(#bgblur_2_1_5057_clip_path);height:100%;width:100%"></div></foreignObject><rect data-figma-bg-blur-radius="2.53684" x="20.3967" y="12.6463" width="1.17509" height="7.51719" rx="0.587543" transform="rotate(90 20.3967 12.6463)" fill="url(#paint1_linear_1_5057)" fill-opacity="0.9" stroke="url(#paint2_linear_1_5057)" stroke-width="0.0933333"/>
<foreignObject x="10.2959" y="13.7962" width="10.9069" height="6.47358"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.27px);clip-path:url(#bgblur_3_1_5057_clip_path);height:100%;width:100%"></div></foreignObject><rect data-figma-bg-blur-radius="2.53684" x="18.6193" y="16.3797" width="1.30667" height="5.74" rx="0.653333" transform="rotate(90 18.6193 16.3797)" fill="url(#paint3_linear_1_5057)" fill-opacity="0.9" stroke="url(#paint4_linear_1_5057)" stroke-width="0.0933333"/>
<defs>
<clipPath id="bgblur_0_1_5057_clip_path" transform="translate(-1.63661 -2.64052)"><path d="M18.5553 6.9952C19.0553 7.86135 19.0553 9.02419 19.0553 11.3499C19.0553 13.6756 19.0553 14.8384 18.5553 15.7046C18.2277 16.272 17.7565 16.7432 17.189 17.0708C16.3229 17.5708 15.1601 17.5708 12.8344 17.5708H10.346C9.64506 17.5708 9.04977 17.5708 8.53639 17.5572L5.9967 19.0647C5.73491 19.2201 5.4072 19.0136 5.43442 18.7103L5.60458 16.8142C5.20852 16.5144 4.87501 16.1375 4.62507 15.7046C4.125 14.8384 4.125 13.6756 4.125 11.3499C4.125 9.02419 4.125 7.86135 4.62507 6.9952C4.95267 6.42777 5.42387 5.95658 5.99129 5.62898C6.85744 5.12891 8.02028 5.12891 10.346 5.12891L12.8344 5.12891C15.16 5.12891 16.3229 5.12891 17.189 5.62898C17.7565 5.95658 18.2277 6.42777 18.5553 6.9952Z"/>
</clipPath><clipPath id="bgblur_1_1_5057_clip_path" transform="translate(-6.61317 -6.37294)"><path d="M9.60163 10.7276C9.10156 11.5938 9.10156 12.7566 9.10156 15.0823C9.10156 17.408 9.10156 18.5708 9.60163 19.437C9.92924 20.0044 10.4004 20.4756 10.9679 20.8032C11.834 21.3033 12.9968 21.3033 15.3225 21.3033H17.8109C18.5427 21.3033 19.1593 21.3033 19.6878 21.2877L22.1563 22.7862C22.418 22.9451 22.7491 22.7387 22.7217 22.4338L22.5524 20.5466C22.9484 20.2467 23.2819 19.8699 23.5318 19.437C24.0319 18.5708 24.0319 17.408 24.0319 15.0823C24.0319 12.7566 24.0319 11.5938 23.5318 10.7276C23.2042 10.1602 22.733 9.689 22.1656 9.3614C21.2995 8.86133 20.1366 8.86133 17.8109 8.86133L15.3225 8.86133C12.9968 8.86133 11.834 8.86133 10.9679 9.3614C10.4004 9.689 9.92924 10.1602 9.60163 10.7276Z"/>
</clipPath><clipPath id="bgblur_2_1_5057_clip_path" transform="translate(-10.2959 -10.0628)"><rect x="20.3967" y="12.6463" width="1.17509" height="7.51719" rx="0.587543" transform="rotate(90 20.3967 12.6463)"/>
</clipPath><clipPath id="bgblur_3_1_5057_clip_path" transform="translate(-10.2959 -13.7962)"><rect x="18.6193" y="16.3797" width="1.30667" height="5.74" rx="0.653333" transform="rotate(90 18.6193 16.3797)"/>
</clipPath><linearGradient id="paint0_linear_1_5057" x1="0.815495" y1="5.13368" x2="6.77568" y2="26.6356" gradientUnits="userSpaceOnUse">
<stop stop-color="#75EDA1"/>
<stop offset="1" stop-color="#349256"/>
</linearGradient>
<linearGradient id="paint1_linear_1_5057" x1="22.6627" y1="11.7013" x2="21.0293" y2="18.9347" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="0.979167" stop-color="white"/>
</linearGradient>
<linearGradient id="paint2_linear_1_5057" x1="21.0776" y1="12.5996" x2="21.0776" y2="20.2101" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint3_linear_1_5057" x1="20.5211" y1="16.2705" x2="19.8884" y2="21.0079" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="0.979167" stop-color="white"/>
</linearGradient>
<linearGradient id="paint4_linear_1_5057" x1="19.366" y1="16.333" x2="19.366" y2="22.1663" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
</defs>
</svg>
