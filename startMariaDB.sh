#!/bin/sh

if [ "$USE_MARIADB" = "true" ]; then
    # 启动 MariaDB
    mariadbd --user=mysql --datadir=/var/lib/mysql --port=$MARIADB_PORT &

    # 等待 MariaDB 启动（兼容 Alpine ash）
    echo "Waiting for MariaDB to start..."
    i=1
    while [ "$i" -le 60 ]; do
        if mariadb -u root -e "SELECT 1" > /dev/null 2>&1; then
            echo "MariaDB is up!"
            break
        fi
        echo "Retrying... ($i/60)"
        sleep 1
        i=$((i+1))
    done
    if [ "$i" -gt 60 ]; then
        echo "MariaDB failed to start. Exiting."
        exit 1
    fi

    # 创建数据库
    mariadb -u root -e "CREATE DATABASE IF NOT EXISTS dipeak;"

    # 创建 root 用户记录（远程连接）
    mariadb -u root -e "CREATE USER IF NOT EXISTS 'root'@'%' IDENTIFIED BY 'lijiacheng';"

    # 设置 root 用户密码并赋予权限
    mariadb -u root -e "\
        SET PASSWORD FOR 'root'@'localhost' = PASSWORD('lijiacheng'); \
        GRANT ALL PRIVILEGES ON *.* TO 'root'@'localhost' WITH GRANT OPTION; \
        GRANT ALL PRIVILEGES ON *.* TO 'root'@'%' WITH GRANT OPTION; \
        FLUSH PRIVILEGES;"

    # 验证密码是否生效
    if mariadb -u root -p'lijiacheng' -e "SELECT 1" > /dev/null 2>&1; then
        echo "root password set successfully!"
    else
        echo "ERROR: Failed to set root password. Check MariaDB logs."
        exit 1
    fi
fi

exec "$@"
