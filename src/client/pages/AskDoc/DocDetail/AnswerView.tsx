import { useAtomValue, useSetAtom } from 'jotai'
import React, { useRef } from 'react'
import rehypeKatex from 'rehype-katex'
import remarkGfm from 'remark-gfm'
import remarkMath from 'remark-math'
import clsx from 'clsx'
import { useBoolean } from 'ahooks'
import ReactMarkdown from 'react-markdown'
import { CaretDownOutlined, CaretUpOutlined, SyncOutlined } from '@ant-design/icons'
import { Tooltip } from 'antd'
import { nanoid } from 'nanoid'
import { useExpand } from 'src/client/components/agent/hooks/use-expand'
import { AssistantDocItem, ChatStatus } from 'src/client/utils'
import { TextNode } from 'src/shared/askdoc-types'
import { IS_H5 } from 'src/shared/constants'
import { askdocAnswerLinkAtom, askdocAnswerLinkKeyTagAtom, currentSelectFileBySceneAtom } from '../askDocAtoms'
import { currentLoginUserAtom } from '../../AskBI/askBIAtoms'
import useDocStreamDataFetch from './useDocStreamDataFetch'

export interface AnswerViewProps {
  content: AssistantDocItem
  status: keyof typeof ChatStatus
  isViewMode?: boolean
  roleIds?: string[]
}

/**
 * 检查文本中是否包含 HTML 标签，如果包含，则会将 HTML 标签去除，只保留纯文本内容。
 * @param text
 * @param maxLength
 * @returns
 */
const truncateText = (text: string) => {
  // 如果文本内容为空，直接返回文本内容
  if (!text) {
    return text
  }

  // 检查是否包含 HTML 标签
  const isHTML = /<[a-z][\s\S]*>/i.test(text)
  if (isHTML) {
    // 创建一个 DOMParser 实例来解析 HTML 文本
    const parser = new DOMParser()
    const parsedDoc = parser.parseFromString(text, 'text/html')

    // 获取解析后的纯文本内容
    const plainText = parsedDoc.body.textContent || parsedDoc.body.innerText || ''

    return plainText
  } else {
    return text
  }
}

function Markdown(props: { content: string; className: string }) {
  return (
    <ReactMarkdown
      className={clsx(`askbi-markdown mb-[20px] text-[16px] font-[400] leading-[28px]`, props.className)}
      remarkPlugins={[remarkGfm, remarkMath]}
      rehypePlugins={[rehypeKatex]}
    >
      {props.content}
    </ReactMarkdown>
  )
}

export function DocReferenceTextNode({ node, index }: { node: TextNode; index: number }) {
  const setAskdocAnswerLink = useSetAtom(askdocAnswerLinkAtom)
  const setAskdocAnswerLinkKeyTag = useSetAtom(askdocAnswerLinkKeyTagAtom)
  const currentSelectFileByScene = useAtomValue(currentSelectFileBySceneAtom)
  const indexType = currentSelectFileByScene ? 'Document' : 'Folder'
  const isExcelFile = node.fileName.endsWith('.xlsx') || node.fileName.endsWith('.xls')
  const columnIndex = node?.columnIndex || false
  const unit = isExcelFile ? (columnIndex ? '列' : '行') : '页'

  /**
   * href的点击事件
   * @param event
   * @param data 获取点击的item的其他参数，例如哪一页，文档id等
   */
  const handleLinkClick = () => {
    if (IS_H5) {
      return
    }
    setAskdocAnswerLink(node)
    setAskdocAnswerLinkKeyTag(nanoid())
  }

  return (
    <div>
      <Tooltip
        title={<div dangerouslySetInnerHTML={{ __html: node.content }} />}
        placement="bottomLeft"
        overlayInnerStyle={{ width: IS_H5 ? '320px' : '420px', maxHeight: '300px', overflow: 'auto' }}
      >
        <p onClick={handleLinkClick} className="line-clamp-2 cursor-pointer text-[#515154]">
          {indexType === 'Folder' ? (
            <span className="text-sm">《{node.fileName}》</span>
          ) : (
            <span className="text-sm">{index + 1}.</span>
          )}
          <span className="font-bold">{` 第${node.page}${unit}：`}</span>
          <span>{truncateText(node.content)}</span>
        </p>
      </Tooltip>
    </div>
  )
}
export function DocReferenceTextNodeList({ nodes = [] }: { nodes?: TextNode[] }) {
  const { list, node } = useExpand({ list: nodes, expandContent: '展开' })
  if (!nodes?.length) return null
  return (
    <div className="mt-2">
      <div className="mb-[4px] flex justify-between">
        <p className="!m-0 !p-0 font-bold">引用原文</p>
        {node}
      </div>
      {list?.map((item: TextNode, index: number) => <DocReferenceTextNode key={index} index={index} node={item} />)}
    </div>
  )
}

export default function AnswerView(props: AnswerViewProps) {
  const {
    status,
    content: { text },
  } = props
  const currentLoginUser = useAtomValue(currentLoginUserAtom)
  const containerRef = useRef<HTMLDivElement | null>(null)
  const [thinking, thinkingOps] = useBoolean(true)
  const roleIds = currentLoginUser?.groups.map((item) => item.id) || []

  const { thinkContent, resultContent, sourceNodesRef, loadingData } = useDocStreamDataFetch({
    ...props,
    roleIds,
  })

  return (
    <div ref={containerRef} className="w-full">
      {status === ChatStatus.failure ? (
        <div className="text-red-400 dark:text-red-500">{text}</div>
      ) : (
        <div>
          {thinkContent ? (
            <div className="flex h-[24px] items-center justify-end">
              <div
                className="flex h-full shrink-0 cursor-pointer items-center justify-end"
                onClick={thinkingOps.toggle}
              >
                <span className="text-[14px] font-[400] text-[#575757]">收起</span>
                {thinking ? (
                  <CaretUpOutlined className="ml-[4px] h-[20px] w-[20px] text-[#858585]" />
                ) : (
                  <CaretDownOutlined className="ml-[4px] h-[20px] w-[20px] text-[#858585]" />
                )}
              </div>
            </div>
          ) : null}
          {thinking && thinkContent ? <Markdown content={thinkContent} className="text-[#8B8B8B]" /> : null}
          {resultContent ? <Markdown content={resultContent} className="mb-[16px] text-[#404040]" /> : null}
          {status === ChatStatus.success && sourceNodesRef.current && sourceNodesRef.current.textNodes?.length > 0 && (
            <DocReferenceTextNodeList nodes={sourceNodesRef.current.textNodes} />
          )}
          {loadingData && (
            <div className="rounded-[4px] px-[16px]">
              <div className="flex">
                {text}
                <SyncOutlined spin className="ml-4 flex flex-none text-xl text-gray-400 dark:text-gray-500" />
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
