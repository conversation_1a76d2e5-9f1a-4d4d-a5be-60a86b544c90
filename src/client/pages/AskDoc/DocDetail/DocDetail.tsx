/**
 * @description AskDoc 问答页面
 */
import React, { forwardRef, useCallback, useEffect, useRef, useState } from 'react'
import axios from 'axios'
import InfoCircleOutlined from '@ant-design/icons/lib/icons/InfoCircleOutlined'
import { Col, Empty, Row, Tooltip, App, InputNumber, Select, Tabs } from 'antd'
import { useAtom, useAtomValue } from 'jotai'
import { MenuFoldOutlined, MinusCircleOutlined, PlusCircleOutlined } from '@ant-design/icons'
import { useRequest } from 'ahooks'
import { PDF_MAX_SCALE, PDF_MIN_SCALE, PDF_SCALE_LIST } from '@shared/constants'
import { askDocApiUrls } from '@shared/url-map'
import { AskDocQuestions, AskDocQuestionsType, ChatNewAnswerLinkType, Doc } from '@shared/askdoc-types'
import { copyToClipboard } from 'src/client/utils'
import {
  selectedD<PERSON><PERSON><PERSON>,
  selected<PERSON><PERSON><PERSON><PERSON><PERSON>,
  selectTreeFile<PERSON>tom,
  docPreviewOpenTagAtom,
  docPreviewTabFileListAtom,
  askdocAnswerLinkAtom,
  askdocAnswerLinkKeyTagAtom,
} from '../askDocAtoms'
import ScrollContentWithHeader from './ScrollContentWithHeader'
import PDFView from './PDFView/PDFView'
import { IHighlight } from './PDFView/components/types'
import './DocDetail.css'
import DocxView from './DocxView'
import ExcelView from './ExcelView'
type TabTargetKey = React.MouseEvent | React.KeyboardEvent | string

function DocDetail() {
  const [selectedDoc, setSelectedDoc] = useAtom(selectedDocAtom)
  const selectedFolder = useAtomValue(selectedFolderAtom)
  const [_highlights, setPdfHighlights] = useState<Array<IHighlight>>([])
  const [currentFileList, setCurrentFileList] = useState<Doc[]>([])
  const [totalPageList, setTotalPageList] = useState<{ page: number; id: number }[]>([])
  const [currentPage, setCurrentPage] = useState<number>(1)
  const pdfRefs = useRef<any>({}) // 存储每个 PDFView 的 ref
  const [pdfViewer, setPdfViewer] = useState<any>()
  const [scaleSelectValue, setSaleSelectValue] = useState<string | number>()
  const [_currentSelectTreeFile, setCurrentSelectTreeFile] = useAtom(selectTreeFileAtom)
  const [activeKey, setActiveKey] = useState<string>('')
  const [tabFileList, setTabFileList] = useAtom(docPreviewTabFileListAtom)
  const { message } = App.useApp()
  const [docPreviewOpenTag, setDocPreviewOpenTag] = useAtom<boolean>(docPreviewOpenTagAtom)
  const [askdocAnswerLink, setAskdocAnswerLink] = useAtom(askdocAnswerLinkAtom)
  const [askdocAnswerLinkKeyTag, setAskdocAnswerLinkKeyTag] = useAtom(askdocAnswerLinkKeyTagAtom)

  /**
   * 选中pdf之后三个按钮的点击事件 分别为 "question" | "explain" | "rewrite"
   * @param type
   * @param data
   */
  const handleOperationClick = (type: AskDocQuestionsType, data: { text?: string; image?: string }) => {
    if (data.text) {
      if (type === AskDocQuestions.question) {
        copyToClipboard(data.text)
        message.success('复制成功')
      }
    }
  }

  const getHighlightsChange = (data: Array<IHighlight>) => {
    setPdfHighlights(data)
  }

  /**
   * 获取到PDF的Viewer ，表示文档加载完成
   * @param data PDFView对象
   */
  const handlePdfRenderReady = (_doc: Doc, viewer: any) => {
    !pdfViewer && setPdfViewer(viewer)
    // 新的 pdf 加载完成后，先重置一下分页页码
    // 如果有 pdfLinkToJump 信息就跳转到对应的页面
    if (askdocAnswerLink) {
      // FIXME: PdfHighlighter 中的 throttle scroll 方法有问题，所以这里延迟一下
      setTimeout(() => {
        handleAnswerClickInfo(askdocAnswerLink as ChatNewAnswerLinkType, currentFileList)
      }, 2000)
    }
  }

  /**
   * 回答的问题中的参考地址的点击事件
   * @param data
   */
  const handleAnswerClickInfo = (data: ChatNewAnswerLinkType, fileList?: Doc[]) => {
    const { fileId, page, content } = data
    const itemData = fileList && fileList.find((item) => Number(item.id) === Number(fileId))
    if (selectedDoc && Number(fileId) === Number(selectedDoc.id) && pdfRefs.current && pdfRefs.current[activeKey]) {
      if (itemData && itemData.mimeType === 'application/pdf') {
        pdfRefs.current[activeKey].scrollPageIntoViewPage(Number(page))
        pdfRefs.current[activeKey].onSearchKeyWords(content, page)
      }
      currentPage !== Number(page) && setCurrentPage(Number(page))
    }
  }

  const { run: getFileList } = useRequest(
    async () => {
      const queryData = {
        ids: askdocAnswerLink?.fileId,
      }
      const response = await axios.get(askDocApiUrls.getDocInfoById, { params: queryData })
      const fileList = response.data.data.files
      if (fileList && askdocAnswerLink) {
        const selectFile = fileList.find((file: Doc) => Number(file.id) === Number(askdocAnswerLink?.fileId))
        if (selectFile && selectFile.mimeType !== 'application/pdf') {
          setTabFileList([])
          setActiveKey('')
        }
        setSelectedDoc(selectFile)
        setCurrentFileList(fileList)
        handleAnswerClickInfo(askdocAnswerLink, fileList)
      }
    },
    {
      manual: true,
      onError: (error: any) => {
        console.error('获取文件列表失败', error)
        message.error('获取文件列表失败')
      },
    },
  )

  useEffect(() => {
    if (!docPreviewOpenTag) return
    getFileList()
  }, [askdocAnswerLinkKeyTag, docPreviewOpenTag, getFileList])

  /**
   * pdf 分页
   * @param page
   */
  const handlePaginationChange = (page: number | null) => {
    if (pdfRefs.current) {
      pdfRefs.current[activeKey].scrollPageIntoViewPage(Number(page))
    }
  }

  /**
   * 最近阅读列表的点击事件
   * @param doc 选中的文件信息
   */
  const onReadingListClick = (doc: Doc) => {
    setSelectedDoc(doc)
  }

  /**
   * 加减按钮缩放
   */
  const setPdfCustomScale = (value: string) => {
    if (pdfViewer) {
      const currentScale = pdfViewer.currentScale
      if (value === 'zoomOut') {
        handlePdfScaleSelectChange((currentScale - 0.1) * 100)
      } else if (value === 'zoomIn') {
        handlePdfScaleSelectChange((currentScale + 0.1) * 100)
      }
    }
  }

  /**
   * PDF scale的select事件触发缩放
   * @param value
   */
  const handlePdfScaleSelectChange = (value: string | number) => {
    const parsedValue = Number(value)
    if (isNaN(parsedValue)) {
      setSaleSelectValue(value)
      pdfRefs.current[activeKey].handleSetPdfScale(value)
    } else {
      setSaleSelectValue(Math.round(value as number) + '%')
      const scaleValue = parsedValue / 100
      const clampedScaleValue = Math.min(Math.max(scaleValue, PDF_MIN_SCALE), PDF_MAX_SCALE)
      pdfRefs.current[activeKey].handleSetPdfScale(clampedScaleValue.toString())
    }
  }

  const renderDocument = (selectedDoc: Doc) => {
    if (!pdfRefs.current[selectedDoc.id]) {
      pdfRefs.current[selectedDoc.id] = React.createRef()
    }
    switch (selectedDoc.mimeType) {
      case 'text/html':
        return (
          <iframe
            width="100%"
            height="100%"
            loading="lazy"
            src={askDocApiUrls.fileUrlProxy(
              encodeURIComponent(selectedDoc.sourceUrl),
              encodeURIComponent(selectedDoc.mimeType),
            )}
          />
        )
      case 'application/pdf':
        return (
          <PDFView
            fileInfo={selectedDoc}
            ref={(ref) => (pdfRefs.current[selectedDoc.id] = ref)} // 为每个 PDFView 存储独立的 ref
            onTotalPageChange={(value) => {
              setTotalPageList([...totalPageList, { id: selectedDoc.id, page: value }])
            }}
            onPageChange={setCurrentPage}
            onOperationClick={handleOperationClick}
            onHighlightsChange={getHighlightsChange}
            onPdfRenderReady={handlePdfRenderReady}
          />
        )
      case 'application/msword':
      case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        return <DocxView fileInfo={selectedDoc.sourceUrl} key={selectedDoc.sourceUrl} />
      case 'text/csv':
      case 'application/vnd.ms-excel':
      case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
        return (
          <ExcelView
            fileInfo={askDocApiUrls.fileUrlProxy(
              encodeURIComponent(selectedDoc.sourceUrl),
              encodeURIComponent(selectedDoc.mimeType),
            )}
            key={selectedDoc.sourceUrl}
          />
        )
      case 'application/vnd.openxmlformats-officedocument.presentationml.presentation':
        return <p>暂时不支持pptx文件预览</p>
      default:
        return null
    }
  }

  const renderPdfContent = () => {
    // 如果文件夹下有多个文件，但没有选择文件，渲染最近阅读列表
    if (selectedFolder && selectedFolder.files.length > 1 && !selectedDoc) {
      return (
        <div className="relative h-full w-full">
          <Row gutter={16} align="top">
            {selectedFolder &&
              selectedFolder.files.slice(0, 6).map((doc: Doc) => {
                const finalThumbnailUrl = askDocApiUrls.fileUrlProxy(
                  encodeURIComponent(doc.thumbnailUrl),
                  encodeURIComponent('image/jpeg'),
                )
                return (
                  <Col
                    span={8}
                    key={doc.id}
                    onClick={() => {
                      onReadingListClick(doc)
                    }}
                  >
                    <div className="mb-6 cursor-pointer">
                      <div className="h-70 max-h-70 border hover:border-primary dark:border-slate-700">
                        {doc.thumbnailUrl ? (
                          <img src={finalThumbnailUrl} alt="" className="h-64 max-h-64 w-full" />
                        ) : (
                          <Empty description={false} className="h-64 w-full pt-20" />
                        )}
                      </div>
                      <Tooltip title={doc.name} placement="bottom">
                        <div className="mx-2.5 line-clamp-2 pt-2 text-sm">{doc.name}</div>
                      </Tooltip>
                    </div>
                  </Col>
                )
              })}
          </Row>
        </div>
      )
    }

    if (!selectedDoc || !selectedDoc.sourceUrl) {
      return (
        <div className="flex h-full flex-col items-center justify-center text-gray-400">
          <InfoCircleOutlined className="mb-4 text-5xl text-gray-300" />
          请先选中一个文件，然后在此查看预览
        </div>
      )
    }

    // render PDF
    return <>{selectedDoc && renderDocument(selectedDoc)}</>
  }

  /**渲染pdf的header 显示名字和分页 */
  const renderPdfHeader = (doc: Doc | null) => {
    if (!selectedDoc || !doc) {
      return (
        <div className="pdf-header flex max-w-full justify-between">
          <p>PDF预览</p>
          <div
            className="flex w-32 cursor-pointer items-center justify-center"
            onClick={() => {
              setDocPreviewOpenTag(false)
              setTabFileList([])
              setActiveKey('')
              setSelectedDoc(undefined)
            }}
          >
            <MenuFoldOutlined className="text-link" />
            <p className="ml-2.5 font-semibold text-link">收起文档</p>
          </div>
        </div>
      )
    }

    return (
      <div className="pdf-header flex min-w-[350px] items-center justify-between px-4 pb-4">
        <div className="mr-2 flex w-2/4 flex-1">
          <Tooltip placement="bottom" title={doc.name}>
            <div className="truncate">{doc.name}</div>
          </Tooltip>
        </div>
        {doc?.mimeType === 'application/pdf' && (
          <div className="mr-0.5 flex shrink-0 items-center">
            <MinusCircleOutlined
              onClick={() => {
                setPdfCustomScale('zoomOut')
              }}
            />
            <Select
              defaultValue="auto"
              value={scaleSelectValue}
              className="w-27 mx-2 flex h-6 items-center"
              options={PDF_SCALE_LIST}
              onChange={handlePdfScaleSelectChange}
              popupClassName="pdfScaleSelectDrop"
            />
            <PlusCircleOutlined
              className="mr-2 cursor-pointer text-base"
              onClick={() => {
                setPdfCustomScale('zoomIn')
              }}
            />
            <InputNumber
              min={1}
              max={totalPageList.find((item) => item.id === doc.id)?.page}
              controls={false}
              className="w-12 text-black"
              value={currentPage}
              onChange={handlePaginationChange}
              size="small"
            />
            <p className="mx-1">/{totalPageList.find((item) => item.id === doc.id)?.page}</p>
          </div>
        )}
      </div>
    )
  }

  const handlePushTabFiles = useCallback(() => {
    if (!selectedDoc) return

    setTabFileList((prevTabs) => {
      const isDocExist = prevTabs.some((item: { doc: { id: number } }) => item.doc.id === selectedDoc.id)
      if (!isDocExist) {
        const newTab = {
          label: selectedDoc.name,
          key: selectedDoc.id.toString(),
          doc: selectedDoc,
        }
        return [...prevTabs, newTab]
      }
      return prevTabs
    })
    setActiveKey(selectedDoc.id.toString())
  }, [selectedDoc, setTabFileList])

  //当selectedDoc有值的时候就加入到tablist中去
  useEffect(() => {
    if (selectedDoc) {
      handlePushTabFiles()
    }
  }, [handlePushTabFiles, selectedDoc])

  // 删除Tab标签
  const onEdit = (targetKey: TabTargetKey, action: 'add' | 'remove') => {
    if (action === 'remove') {
      if (tabFileList.length === 1) {
        setSelectedDoc(undefined)
        setTabFileList([])
        setActiveKey('')
        setTotalPageList([])
        setAskdocAnswerLink(null)
        setAskdocAnswerLinkKeyTag('')
      } else {
        const newPanes = tabFileList.filter((pane) => pane.key !== targetKey)
        const newTotalPageList = totalPageList.filter((item) => item.id.toString() !== targetKey)
        if (newPanes.length) {
          const targetIndex = tabFileList.findIndex((pane) => pane.key === targetKey)
          const nextActiveKey = newPanes[targetIndex === newPanes.length ? targetIndex - 1 : targetIndex].key
          setActiveKey(nextActiveKey)
        }
        // 如果删除的是当前选中的文档，清空selectedDoc
        if (selectedDoc && selectedDoc.id.toString() === targetKey) {
          setSelectedDoc(undefined)
        }
        setTabFileList(newPanes)
        setTotalPageList(newTotalPageList)
      }
    }
  }

  const onDocPreviewChange = (key: string) => {
    const doc = tabFileList.find((item) => item.key === key)?.doc
    if (doc && key) {
      setSelectedDoc(doc)
      setActiveKey(key)
      setCurrentSelectTreeFile(doc)
    }
  }

  return (
    <main className="ask-doc-detail-page flex flex-grow flex-row items-stretch divide-x divide-solid overflow-hidden border-t border-slate-200 bg-white pb-0 dark:border-slate-700">
      {tabFileList && tabFileList.length > 0 ? (
        <div className="relative flex h-full w-full min-w-[500px] flex-1 dark:border-slate-700">
          <Tabs
            className="w-full"
            hideAdd
            onChange={onDocPreviewChange}
            activeKey={activeKey}
            type="editable-card"
            onEdit={onEdit}
            items={tabFileList.map((tab) => ({
              key: tab.key,
              label: (
                <Tooltip title={tab.label} placement="bottom">
                  <div className="w-[100px] max-w-[180px] overflow-hidden overflow-ellipsis whitespace-nowrap">
                    {tab.label}
                  </div>
                </Tooltip>
              ),
              children: (
                <div className="h-screen w-full" key={tab.doc.name}>
                  {tab.doc.name && renderPdfHeader(tab.doc)}
                  {tab.doc.sourceUrl && renderDocument(tab.doc)}
                </div>
              ),
            }))}
          />
          <div
            className="absolute right-0 flex h-10 w-32 cursor-pointer items-center justify-center border-b"
            onClick={() => {
              setDocPreviewOpenTag(false)
              setTabFileList([])
              setAskdocAnswerLink(null)
              setAskdocAnswerLinkKeyTag('')
            }}
          >
            <MenuFoldOutlined className="text-link" />
            <p className="ml-2.5 font-semibold text-link">收起文档</p>
          </div>
        </div>
      ) : (
        <ScrollContentWithHeader
          className="flex min-w-[500px] flex-1 dark:border-slate-700"
          contentClassName="bg-white"
          header={renderPdfHeader(null)}
        >
          {renderPdfContent()}
        </ScrollContentWithHeader>
      )}
    </main>
  )
}

export default forwardRef(DocDetail)
