/**
 * @description 元数据-指标详情
 */
import React, { useEffect } from 'react'
import { App, Result, Skeleton } from 'antd'
import { useAtomValue } from 'jotai'
import { useRequest } from 'ahooks'
import axios from 'axios'
import { DATE_ALIAS, MetricTypeNames, QueryParams, QueryParamsVerified } from 'src/shared/metric-types'
import {
  APIResponse,
  ChartType,
  ChatResponseError,
  ChatResponseErrorTypes,
  ChatResponseQueryMetric,
  OlapRow,
} from 'src/shared/common-types'
import { askBIApiUrls } from 'src/shared/url-map'
import QueryParamsSelector from 'src/client/components/QueryParamsSelector'
import IconButtonGroup from 'src/client/components/IconButtonGroup'
import ChartWrapper from 'src/client/charts/ChartWrapper'
import { TryQueryToSqlData } from 'src/client/components/TryQueryToSql/try-query-to-sql'
import { getVisibleChartIconMap } from 'src/client/charts/Card'
import { AssistantChartChatItem } from 'src/client/utils'
import ChatError from 'src/client/components/ChatError/ChatError'
import { conversationIdAtom, currentDatasetAtom, llmTypeAtom, metricConfigAtom, themeAtom } from '../../askBIAtoms'
import { readyResponseToChatAns } from '../utils'

export default function MetricDetail({ metricName }: { metricName: string }) {
  const currentDataset = useAtomValue(currentDatasetAtom)
  const metricConfig = useAtomValue(metricConfigAtom)
  const theme = useAtomValue(themeAtom)
  const conversationId = useAtomValue(conversationIdAtom)
  const llmType = useAtomValue(llmTypeAtom)
  const { message: antdMessage } = App.useApp()
  const [chartType, setChartType] = React.useState<ChartType>('LineChart')

  const queryParams = {
    groupBys: [],
    metricNames: [metricName || ''],
    timeQueryParams: metricConfig?.timeDimensionDatum && {
      timeDimensionName: metricConfig.timeDimensionDatum.timeDimensionName,
      timeGranularity: 'month',
      // REMOVE!!! 为P环境中国人寿项目定制 只有2019年的数据
      timeStartFunction:
        currentDataset?.sceneId === 'CX7sQeGbXCPLOb2U'
          ? { type: 'specificMonth', year: 2019, month: 1 }
          : { type: 'recentMonths', months: 12 },
      timeEndFunction:
        currentDataset?.sceneId === 'CX7sQeGbXCPLOb2U'
          ? { type: 'specificMonth', year: 2019, month: 12 }
          : { type: 'recentMonths', months: 0 },
    },
  } as QueryParams
  const [queryParamsVerified, setQueryParamsVerified] = React.useState<QueryParamsVerified>({
    queryParams: queryParams,
    originalQueryParams: queryParams,
    extraParams: {
      extraMetricNames: [],
      extraGroupBys: [],
      extraOrderBys: [],
    },
  })

  const {
    run: loadChartData,
    data: chartData,
    loading: isChartDataLoading,
    error: chartDataError,
  } = useRequest(
    async (queryParamsVerified: QueryParamsVerified) => {
      // 如果没有时间的维度，不需要请求趋势数据
      if (metricConfig?.timeDimensionDatum == null) {
        return
      }
      const response = await axios.post<ChatResponseQueryMetric | ChatResponseError>(askBIApiUrls.queryMetric, {
        queryParamsVerified,
        sceneId: currentDataset?.sceneId,
        conversationId,
        llmType,
      })
      if (response.data.ready === true) {
        // 更新推荐的图表
        setChartType(response.data.chartType)
        return response.data
      } else {
        throw new Error(response.data?.unreadyReason || '获取指标相关数据失败')
      }
    },
    {
      onError: (error) => {
        console.error('Load chart data with error', error)
      },
      manual: true,
    },
  )

  useEffect(() => {
    if (queryParamsVerified.queryParams != null && currentDataset && currentDataset.sceneId != null) {
      loadChartData(queryParamsVerified)
    }
  }, [currentDataset, loadChartData, queryParamsVerified, queryParamsVerified.queryParams])

  const {
    data: metricTime,
    loading: _isMetricTimeLoading,
    error: _metricTimeError,
  } = useRequest(
    async () => {
      if (currentDataset?.sceneId != null && metricName != null) {
        const response = await axios.get<APIResponse<{ list: OlapRow[] }>>(
          askBIApiUrls.metrics.detailWithTime(currentDataset.sceneId, metricName),
        )
        return response.data.data
      } else {
        antdMessage.error('URL 错误，指标 ID 不能为空')
      }
    },
    {
      onError: (error) => {
        console.error('Load metric detail with error', error)
      },
    },
  )

  const handleQueryParamsChange = (newValue: QueryParamsVerified) => {
    setQueryParamsVerified(newValue)
  }

  const metricDetail = metricConfig?.allMetrics.find((metric) => metric.name === metricName)

  if (metricDetail == null) {
    return (
      <ChatError
        currentDataset={currentDataset}
        content={{
          errType: ChatResponseErrorTypes.METRICS_NOT_EXIST,
          unreadyReason: '您询问的指标不存在',
          type: 'chat-error',
          tryQueryToSqlData: new TryQueryToSqlData(),
        }}
      />
    )
  }

  if (currentDataset == null) {
    return <Result status="error" title="没有可用的场景" />
  }

  const handleChartTypeChange = (newValue: ChartType) => {
    setChartType(newValue)
  }

  const renderTrend = () => {
    if (chartDataError) {
      return <Result status="error" title="获取图表数据失败" subTitle={chartDataError.message} />
    }

    if (isChartDataLoading) {
      return <Skeleton active />
    }

    if (chartData == null) {
      return <Result status="error" title="获取图表数据失败" subTitle="图表数据为空" />
    }

    const assistantChartChatItem = readyResponseToChatAns(chartData, currentDataset.sceneId)
      .content[0] as AssistantChartChatItem

    return (
      <div className="flex flex-col">
        <ChartWrapper
          data={{ ...assistantChartChatItem, chartType }}
          theme={theme}
          onTableDataChange={() => {
            /** table 有排序，暂不支持 */
          }}
        />
        <div className="m-2">
          <span className="mt-2">切换图表：</span>
          <IconButtonGroup<ChartType>
            options={getVisibleChartIconMap(assistantChartChatItem.recommendChartTypes)}
            value={chartType}
            onChange={handleChartTypeChange}
          />
        </div>
      </div>
    )
  }

  const renderDetailItem = (label: string, value: string | number | null | undefined) => {
    return (
      <div className="mb-2 w-full md:w-1/2">
        {label}: <span className="font-bold">{value || '-'}</span>
      </div>
    )
  }

  return (
    <div className="card rounded-tremor-default group flex max-w-full flex-col rounded-xl dark:bg-slate-700 md:bg-white">
      <h3 className="p-2 pb-0 text-lg font-semibold">{metricDetail.label}的指标详情</h3>
      <div className="flex flex-col p-2">
        <h4 className="text-lg font-bold">指标基本信息</h4>
        <div className="flex flex-row flex-wrap">
          {renderDetailItem('标识', metricDetail?.name)}
          {renderDetailItem('名称', metricDetail?.label)}
          {renderDetailItem('类型', MetricTypeNames[metricDetail.type])}
          {renderDetailItem('计算方式', metricDetail.displayExpr)}
          {metricConfig?.timeDimensionDatum && (
            <>
              {renderDetailItem(
                '数据起始于',
                metricTime?.list && metricTime?.list?.length > 0 ? metricTime.list[0][DATE_ALIAS] : '-',
              )}
              {renderDetailItem(
                '数据结束于',
                metricTime?.list && metricTime?.list?.length > 0
                  ? metricTime.list[metricTime.list.length - 1][DATE_ALIAS]
                  : '-',
              )}
            </>
          )}
        </div>
        {metricDetail?.description && (
          <div className="flex flex-row flex-wrap">{renderDetailItem('描述', metricDetail?.description)}</div>
        )}
      </div>
      {metricConfig?.timeDimensionDatum && (
        <>
          <h4 className="px-2 text-lg font-bold">指标数据趋势</h4>
          <div className="flex flex-col">
            <QueryParamsSelector
              queryParamsVerified={queryParamsVerified}
              onChange={handleQueryParamsChange}
              metricConfig={metricConfig}
            />
            {renderTrend()}
          </div>
        </>
      )}
    </div>
  )
}
