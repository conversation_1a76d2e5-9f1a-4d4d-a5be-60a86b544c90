/**
 * 电信经分 - 文档解析 and 生成报告
 */
import {
  CopyOutlined,
  DownloadOutlined,
  FormOutlined,
  LoadingOutlined,
  MinusCircleOutlined,
  PlusCircleOutlined,
  ReloadOutlined,
} from '@ant-design/icons'
import { AdjustmentsHorizontalIcon, CheckCircleIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline'
import { App, Button, DatePicker, Dropdown, Form, Input, MenuProps, Modal, Select, Space, Table, Tooltip } from 'antd'
import React, { useEffect, useState } from 'react'
import axios from 'axios'
import { useAtom, useAtomValue } from 'jotai'
import { nanoid } from 'nanoid'
import dayjs, { Dayjs } from 'dayjs'
import { useRequest } from 'ahooks'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import { FieldCorrectionColumns } from 'src/shared/common-types'
import { AnalyzeReportTypes, AnsChatItem, AssistantDocReportAnalyze, copyToClipboard } from 'src/client/utils'
import { askBIPageUrls, askDocApiUrls } from 'src/shared/url-map'
import { useMakeAgentRequest } from 'src/client/components/chats'
import { SvgIcon, docxIcon, expandIcon, pdfIcon } from 'src/client/components/SvgIcon'
import { DOC_REPORT_TIMEOUT } from 'src/shared/constants'
import { currentDatasetAtom, docReportParamsAtom, reportOpListAtom } from '../askBIAtoms'
import './DocReportAnalyze.css'
import FieldCorrection from './FieldCorrection'

const { RangePicker } = DatePicker

const { TextArea } = Input

interface ReportAnalyzeProps {
  currentChatId: string
  chatAnsItem: AnsChatItem
  content: AssistantDocReportAnalyze
}

interface FieldCorrection {
  metrics: string[]
  dimensions: string[]
  timeDimensions: string[]
}

interface Option {
  code: string
  name: string
}

interface OptionsList {
  options: Option[]
}

export default function DocReportAnalyze(props: ReportAnalyzeProps) {
  const { currentChatId, content, chatAnsItem } = props
  const [form] = Form.useForm()
  const chatData = chatAnsItem.content[0] as AssistantDocReportAnalyze
  const { message: antdMessage } = App.useApp()
  const fileId = content?.rows?.fileId?.toString()
  const [showReportSettingsModal, setShowReportSettingsModal] = useState<boolean>(false)
  const [showFieldCorrectionModal, setShowFieldCorrectionModal] = useState<boolean>(false)
  const [chatRowsData, setChatRowsData] = useState(content.rows)
  const [fieldCorrectionColumns, setFieldCorrectionColumns] = useState<FieldCorrection>()
  const [_docReportParams, setDocReportParams] = useAtom(docReportParamsAtom)
  const [localReportTableData, setLocalReportTableData] = useState<{ key: string; label: string; value: string }[]>() // 本地的参数，用于显示，和上面的docReportParamsAtom作用不同，上面这个是会清空的
  const [expanded, setExpanded] = useState(false)
  const [fieldCorrectionLoading, setFieldCorrectionLoading] = useState(false)
  const [isRegenerateReportAgainTag, setIsRegenerateReportAgainTag] = useState(false)
  const [isDisabledTimeRange, setIsDisabledTimeRange] = useState(false)
  const [selectMetricsFields, setSelectMetricsFields] = useState<Option[]>([])
  const [selectDimensionsFields, setSelectDimensionsFields] = useState<Option[]>([])
  const [selectFieldsOptions, setSelectFieldsOptions] = useState<OptionsList[]>([])
  const [reportOpList, setReportOpList] = useAtom(reportOpListAtom)
  const [columnValues, setColumnValues] = useState<{ [key: string]: string[] }>({})
  const [filterTypes, setFilterTypes] = useState<number[]>([])
  const [selectTimeRange, setSelectTimeRange] = useState<string[]>([])
  const currentDataset = useAtomValue(currentDatasetAtom)
  const makeAgentRequest = useMakeAgentRequest()

  /**
   * content.status的状态来处理显示table的值
   * chatRowsData?.result?.requestInfo 历史会话的值回显
   * chatData.rows 生成报告时保存的值
   */
  useEffect(() => {
    const isReportGenerating = content?.status === AnalyzeReportTypes.reportGenerating
    const isReportGeneratedSuccess = content?.status === AnalyzeReportTypes.reportGeneratedSuccess

    if (isReportGenerating || isReportGeneratedSuccess) {
      const localTableData = chatRowsData?.result?.requestInfo || chatData.rows
      const dataSource = [
        {
          key: '1',
          label: '业务口径筛选',
          value: localTableData.columnFilter.map((item: { columnName: string }) => item.columnName).join('，'),
        },
        { key: '2', label: '分析时间', value: localTableData.timeDimension },
        {
          key: '3',
          label: '时间范围',
          value: `${localTableData.timeRangeStart || ''} - ${localTableData.timeRangeEnd || ''}`,
        },
        { key: '4', label: '业务关注度量', value: (localTableData.focusMetrics || []).join('，') },
        { key: '5', label: '业务关注维度', value: (localTableData.focusDimensions || []).join('，') },
        {
          key: '6',
          label: '生成指令',
          value: localTableData.userIntent,
        },
      ]
      setLocalReportTableData(dataSource)
    }
  }, [chatData.rows, chatRowsData, content?.status])

  // 获取维度码值
  const { run: getOpList } = useRequest(
    async () => {
      const response = await axios.get(askDocApiUrls.getReportOpList, { timeout: DOC_REPORT_TIMEOUT })
      setReportOpList(response.data.data.opList || [])
    },
    {
      manual: true,
      onError: (error: any) => {
        antdMessage.error(error?.message)
        console.error('获取运算符列表', error)
      },
    },
  )

  // 报告需求按钮点击事件
  const handleReportClick = () => {
    setFilterTypes([])
    reportOpList.length === 0 && getOpList()
    getColumnClassify()
    setShowReportSettingsModal(true)
  }

  const getReportColumnValue = async (columnName: string, index: number) => {
    try {
      const response = await axios.get(askDocApiUrls.getReportColumnValue, {
        params: {
          fileId: fileId,
          searchValue: '',
          columnName,
          page: 1,
          pageSize: 1000,
        },
        timeout: DOC_REPORT_TIMEOUT,
      })
      setColumnValues((prev) => ({
        ...prev,
        [index]: response.data.data.valueList || [],
      }))
    } catch (error: any) {
      antdMessage.error(error?.message)
      console.error('获取码值列表失败', error)
    }
  }

  // 筛选类型
  const handleFilterTypeChange = (value: number, index: number) => {
    const newFilterTypes = [...filterTypes]
    newFilterTypes[index] = value
    setFilterTypes(newFilterTypes)

    const currentValues = form.getFieldValue('columnFilter') || []
    if (currentValues.length > 0) {
      currentValues[index].values = []
      currentValues[index].columnName = undefined
      currentValues[index].operator = currentValues[index].filterType === 2 ? null : currentValues[index].operator
      form.setFieldsValue({
        columnFilter: currentValues,
      })
    }

    const newOptionsList = [...selectFieldsOptions]
    newOptionsList[index] = { options: value === 1 ? selectDimensionsFields : selectMetricsFields }
    setSelectFieldsOptions(newOptionsList)
  }

  // 下载PDF和word文档报告
  const handleDownloadFile = (url: string) => {
    if (!url) {
      antdMessage.error('下载链接获取失败')
      return
    }
    if (content.status === AnalyzeReportTypes.reportGenerated) {
      window.location.href = askDocApiUrls.downloadFileProxy(encodeURIComponent(url))
    }
  }

  const items: MenuProps['items'] = [
    {
      key: '1',
      label: <a className="font-semibold">下载为PDF</a>,
      icon: <SvgIcon icon={pdfIcon} className="mr-1 h-4 w-4" />,
      onClick: () => {
        handleDownloadFile(content?.rows?.result?.pdfUrl)
      },
    },
    {
      key: '2',
      label: <a className="font-semibold">下载为Word</a>,
      icon: <SvgIcon icon={docxIcon} className="mr-1 h-4 w-4" />,
      onClick: () => {
        handleDownloadFile(content?.rows?.result?.wordUrl)
      },
    },
  ]

  // markdown展示的文本内容 - 展开和收起操作
  const toggleExpand = () => {
    setExpanded(!expanded)
  }

  // 防止字段分类校正页面修改了字段分类，在生成报告弹窗页面没有更新，所以需要在重新获取一遍数据
  const getColumnClassify = async (fileId?: string) => {
    try {
      const response = await axios.get(askDocApiUrls.docColumnClassify, {
        params: { fileId: fileId || content.rows.fileId, useScene: true },
        timeout: DOC_REPORT_TIMEOUT,
      })
      const result = response.data.data

      if (result) {
        setChatRowsData(result)
        setSelectMetricsFields(result.metrics)
        setSelectDimensionsFields(result.dimensions)
      }
    } catch (error: any) {
      console.error('getColumnClassify', error)
      antdMessage.error(`获取字段分类失败${error.message}`, 2)
    }
  }

  // 生成报告按钮
  const handleCreateDocReport = async () => {
    await form.validateFields()
    const fields = form.getFieldsValue()
    if (fields && fields.columnFilter.length > 0) {
      fields.columnFilter.forEach((column: { filterType: number; operator: string; values: string[] }) => {
        if (column.filterType === 1) {
          // 如果 filterType 为 1，设置 operator 为 "in"
          column.operator = 'in'
        } else if (column.filterType === 2) {
          // 如果 filterType 为 2，将 values 转为数组
          if (typeof column.values === 'string') {
            column.values = [column.values]
          }
        }
      })
    }
    if (selectTimeRange.length > 0) {
      fields.timeRangeStart = selectTimeRange[0]
      fields.timeRangeEnd = selectTimeRange[1]
    }

    try {
      // 因为在时间列上追加了一条不进行时间趋势分析，所以值为null的时候，要给后端返回为空
      const tempTimeDimension = fields.timeDimension === 'null' ? '' : fields.timeDimension
      const reqParams = {
        ...fields,
        timeDimension: tempTimeDimension,
        fileId,
        chatId: isRegenerateReportAgainTag ? nanoid() : currentChatId, // 如果是重新生成报告的逻辑，需要新的chatId，否则就用之前的chatId
        isRegenerateReportAgainTag,
      }
      setDocReportParams(reqParams)
      setShowReportSettingsModal(false)
      form.resetFields()
      makeAgentRequest({
        isMiniMode: location.pathname === askBIPageUrls.home,
        message: `请基于${currentDataset?.sceneLabel}场景生成一份数据分析报告`,
        // isReportModelNameTag: '',
      })
    } catch (error: any) {
      console.error('生成报告', error.message)
      antdMessage.error(`生成报告失败${error.message}`)
    }
  }

  // 业务关注时间选择事件，当选择的值是code为null的话，时间范围应该要不可选
  const onTimeDimensionsChange = (value: string) => {
    if (value === 'null') {
      form.setFieldsValue({ timeRange: null })
      setIsDisabledTimeRange(true)
    } else {
      setIsDisabledTimeRange(false)
    }
  }

  // 选择字段
  const onSelectFieldsChange = (value: string, index: number) => {
    // 清空筛选条件
    const columnFilter = form.getFieldValue('columnFilter')
    if (columnFilter && columnFilter[index].filterType === 1) {
      getReportColumnValue(value, index)
    }
    columnFilter[index].values = []
    form.setFieldsValue({
      columnFilter,
    })
  }

  const onRangeChange = (_dates: null | (Dayjs | null)[], dateStrings: string[]) => {
    setSelectTimeRange(dateStrings || [])
  }

  // 渲染生成报告弹窗
  const renderReportModal = (
    <Modal
      title="生成报告"
      width={'700px'}
      className="report-modal"
      open={showReportSettingsModal}
      closeIcon={
        <div
          className="flex cursor-pointer items-center"
          onClick={() => {
            setShowFieldCorrectionModal(true)
          }}
        >
          <p className="mr-2 text-xs text-rose-500">编辑字段分类</p>
          <Tooltip placement="top" title={'点击修改字段分类，修改后重置选项'}>
            <FormOutlined className="text-black" />
          </Tooltip>
        </div>
      }
      footer={[
        <Button
          key="cancel"
          onClick={() => {
            form.resetFields()
            setShowReportSettingsModal(false)
          }}
        >
          取消
        </Button>,
        <Button
          key="ok"
          type="primary"
          onClick={() => {
            handleCreateDocReport()
          }}
        >
          生成报告
        </Button>,
      ]}
    >
      {chatRowsData && (
        <Form
          form={form}
          initialValues={{
            columnFilter: [{}], // 初始化时添加一条默认记录
          }}
        >
          <p className="mb-3 font-bold">业务口径筛选</p>
          <Form.List name="columnFilter">
            {(fields, { add, remove }) => (
              <div className="filterFormList">
                {fields.map(({ key, name, ...restField }, index) => (
                  <div key={key} className="filterFormListItem">
                    <Space className="flex w-full" align="baseline">
                      <Form.Item name={[name, 'filterType']} className="flex" label="筛选类型">
                        <Select
                          options={[
                            { label: '普通筛选', value: 1 },
                            { label: '特殊筛选', value: 2 },
                          ]}
                          placeholder="请选择筛选类型"
                          onChange={(value) => handleFilterTypeChange(value, index)}
                        />
                      </Form.Item>
                      <Form.Item {...restField} name={[name, 'columnName']} label="选择字段">
                        <Select
                          showSearch
                          style={{ width: 200 }}
                          placeholder="请选择字段"
                          options={selectFieldsOptions[index]?.options || []}
                          fieldNames={{ label: 'name', value: 'code' }}
                          onChange={(value) => onSelectFieldsChange(value, index)}
                        />
                      </Form.Item>
                    </Space>
                    <Space className="flex items-center">
                      {filterTypes[index] === 1 && (
                        <Form.Item {...restField} name={[name, 'values']} className="flex" label="筛选条件">
                          <Select
                            style={{ width: 530 }}
                            mode="multiple"
                            options={(columnValues[name] || []).map((item) => ({ label: item, value: item }))}
                            placeholder="请选择筛选条件"
                          />
                        </Form.Item>
                      )}
                      {filterTypes[index] === 2 && (
                        <Space>
                          <Form.Item {...restField} name={[name, 'operator']} className="flex" label="筛选条件">
                            <Select
                              style={{ width: 170 }}
                              options={reportOpList.filter((option) => option.key !== 'in')}
                              fieldNames={{ label: 'value', value: 'key' }}
                              placeholder="请选择特殊筛选条件"
                            />
                          </Form.Item>
                          <Form.Item {...restField} name={[name, 'values']}>
                            <Input placeholder="请输入" style={{ width: 350 }} />
                          </Form.Item>
                        </Space>
                      )}

                      {filterTypes[index] && (
                        <Space>
                          {index === 0 ? (
                            <PlusCircleOutlined onClick={() => add()} className="mb-2" />
                          ) : (
                            <MinusCircleOutlined onClick={() => remove(name)} className="mb-2" />
                          )}
                        </Space>
                      )}
                    </Space>
                  </div>
                ))}
              </div>
            )}
          </Form.List>
          <p className="mb-2 mt-3 font-bold">分析时间范围</p>
          <Form.Item
            name="timeDimension"
            label="时间维度（单选）"
            rules={[
              {
                required: !!chatRowsData?.timeDimensions?.length && !isDisabledTimeRange,
                message: '请选择时间维度',
              },
            ]}
          >
            <Select
              disabled={!chatRowsData?.timeDimensions?.length}
              placeholder="请选择业务关注时间"
              options={chatRowsData?.timeDimensions}
              fieldNames={{ label: 'name', value: 'code' }}
              onChange={onTimeDimensionsChange}
            />
          </Form.Item>
          <Form.Item
            name="timeRange"
            label="时间范围"
            rules={[
              {
                required: !!chatRowsData?.timeDimensions?.length && !isDisabledTimeRange,
                message: '请选择时间范围',
              },
            ]}
          >
            <RangePicker
              showTime
              format="YYYY-MM-DD HH:mm:ss"
              onChange={onRangeChange}
              disabled={!chatRowsData?.timeDimensions?.length || isDisabledTimeRange}
            />
          </Form.Item>
          <p className="mb-2 font-bold">可放空由大模型推荐，或最多选择两项</p>
          <Form.Item label="" name="focusMetrics">
            <Select
              mode="multiple"
              placeholder="请选择业务关注度量"
              options={chatRowsData?.metrics}
              fieldNames={{ label: 'name', value: 'code' }}
              maxCount={2}
            />
          </Form.Item>
          <p className="mb-2 font-bold">可放空由大模型推荐，或最多选择两项</p>
          <Form.Item label="" name="focusDimensions">
            <Select
              mode="multiple"
              placeholder="请选择业务关注维度"
              options={chatRowsData?.dimensions}
              fieldNames={{ label: 'name', value: 'code' }}
              maxCount={2}
            />
          </Form.Item>
          <p className="mb-2 font-bold">生成报告指令</p>
          <Form.Item label="" name="userIntent">
            <TextArea placeholder="请输入生成报告的背景、目的或简述需求，当前支持企业经营分析类报告生成，例如“请生成一份针对合同签约的数据分析报告/为月度汇报生成一份经营分析洞察文档/给我一份数据探索报告”等。" />
          </Form.Item>
        </Form>
      )}
    </Modal>
  )

  // 重新生成点击事件 - 要回显之前选择的值
  const handleRegenerateClick = () => {
    const sourceData = content.rows?.result?.requestInfo
    getColumnClassify(sourceData.fileId)
    setIsRegenerateReportAgainTag(true)
    setShowReportSettingsModal(true)
    setSelectTimeRange([sourceData.timeRangeStart, sourceData.timeRangeEnd])
    if (sourceData) {
      let columnFilter = null
      if (sourceData.columnFilter) {
        columnFilter = sourceData.columnFilter.map(
          (
            item: { columnName: string; operator: string; filterType: number; values: string[] | string },
            index: number,
          ) => {
            // 判断 operator 是否为 "in"
            if (item.operator === 'in') {
              item = { ...item, filterType: item?.filterType ?? 1 }
              getReportColumnValue(item.columnName, index)
            } else {
              item = { ...item, filterType: item.filterType ?? 2 }
              // 将 values 数组转为字符串
              if (Array.isArray(item.values)) {
                item.values = item.values.join(',')
              }
            }
            return item
          },
        )
      }
      setFilterTypes(columnFilter.map((items: { filterType: number }) => items.filterType))
      form.setFieldsValue({
        columnFilter: columnFilter,
        focusDimensions: sourceData.focusDimensions,
        focusMetrics: sourceData.focusMetrics,
        userIntent: sourceData.userIntent,
        timeDimension: sourceData.timeDimension,
        timeRangeStart: sourceData.timeRangeStart,
        timeRangeEnd: sourceData.timeRangeEnd,
        timeRange: [
          dayjs(sourceData.timeRangeStart, 'YYYY-MM-DD HH:mm:ss'),
          dayjs(sourceData.timeRangeEnd, 'YYYY-MM-DD HH:mm:ss'),
        ],
      })
    }
  }

  // 字段分类校正 - 拖拽后的结果
  const handleDragEndResult = (data: FieldCorrectionColumns) => {
    const result = {
      metrics: data.metricsColumn.items.map((item) => item.name),
      dimensions: data.dimensionsColumn.items.map((item) => item.name),
      timeDimensions: data.timeDimensionsColumn.items.map((item) => item.name),
    }
    setFieldCorrectionColumns(result)
  }

  // 校正字段分类
  const handleFieldCorrection = async () => {
    try {
      if (fieldCorrectionColumns) {
        setFieldCorrectionLoading(true)
        const data = { ...fieldCorrectionColumns, fileId }
        await axios.post(askDocApiUrls.docColumnClassify, data, { timeout: DOC_REPORT_TIMEOUT })
        form.resetFields()
        getColumnClassify()
        antdMessage.success('校正成功')
        setFieldCorrectionLoading(false)
        setShowFieldCorrectionModal(false)
      }
    } catch (error: any) {
      antdMessage.error(`字段分类校正失败${error.message}`)
      setFieldCorrectionLoading(false)
      console.error('handleFieldCorrection', error)
    }
  }

  // 渲染字段分类校正弹窗
  const renderFieldCorrectionModal = (
    <Modal
      title="字段分类校正"
      open={showFieldCorrectionModal}
      onOk={() => {
        handleFieldCorrection()
      }}
      onCancel={() => {
        setFieldCorrectionColumns(undefined)
        setShowFieldCorrectionModal(false)
      }}
      okText="确认"
      cancelText="取消"
      maskClosable={false}
      width={800}
      confirmLoading={fieldCorrectionLoading}
      okButtonProps={{
        disabled: !fieldCorrectionColumns,
      }}
    >
      <FieldCorrection onDragEndResult={handleDragEndResult} fieldList={chatRowsData} />
    </Modal>
  )

  const renderReportGenerated = (
    <div>
      <div className={`transition-max-height relative overflow-y-hidden px-1 pb-2 ${expanded ? 'h-full' : 'h-40'}`}>
        <div className="askbi-markdown rounded-md bg-slate-50 p-2 dark:bg-slate-700">
          <ReactMarkdown remarkPlugins={[remarkGfm]}>{content?.rows?.result?.originalReport}</ReactMarkdown>
        </div>
        {!expanded && <div className="absolute inset-x-0 bottom-0 h-2 bg-white bg-opacity-80 dark:bg-transparent" />}
      </div>
      <div className="mt-2 flex items-center justify-center text-center">
        <div className="flex cursor-pointer" onClick={toggleExpand}>
          <SvgIcon icon={expandIcon} className={`text-customThemeBlue mr-1 h-4 w-4`} />
          <span className={`text-customThemeBlue text-xs`}>{expanded ? '收起' : '展开'}</span>
        </div>
      </div>

      <div className="mt-2 flex w-96 gap-2">
        <Dropdown menu={{ items }} placement="top">
          <Button
            block
            icon={<DownloadOutlined />}
            className="rounded border-borderColor bg-white font-medium dark:border-gray-400 dark:bg-slate-800"
          >
            下载报告
          </Button>
        </Dropdown>
        <Button
          block
          icon={<CopyOutlined />}
          className="rounded border-borderColor bg-white font-medium dark:border-gray-400 dark:bg-slate-800"
          onClick={() => {
            copyToClipboard(content?.rows?.result?.originalReport)
            antdMessage.success('复制成功')
          }}
        >
          复制
        </Button>
        <Button
          block
          icon={<ReloadOutlined />}
          className="rounded border-borderColor bg-white font-medium dark:border-gray-400 dark:bg-slate-800"
          onClick={() => {
            handleRegenerateClick()
          }}
        >
          重新生成
        </Button>
      </div>
    </div>
  )

  return (
    <div className="report-analyze w-full">
      {/* 解析完成 */}
      {(content.status === AnalyzeReportTypes.analyzeSuccess ||
        content.status === AnalyzeReportTypes.reportGeneratedFailed) && (
        <>
          {content.status === AnalyzeReportTypes.analyzeSuccess && (
            <div className="flex items-center">
              <CheckCircleIcon className={`text-customThemeBlue mr-1 h-5 w-5`} />
              <p>解析完成，请确认报告生成需求</p>
            </div>
          )}
          {content.status === AnalyzeReportTypes.reportGeneratedFailed && (
            <div className="flex flex-col">
              <div className="flex items-center">
                <ExclamationTriangleIcon className="mr-1 h-5 w-5 text-rose-600" />
                <p>生成报告失败，请确认报告重新生成需求</p>
              </div>
              {content.rows?.errorReason && (
                <p className="ml-6 text-rose-600">
                  失败原因：{content.rows?.errorReason?.text || content.rows?.errorReason?.unreadyReason}
                </p>
              )}
            </div>
          )}
          <div
            className="mt-1 flex w-24 cursor-pointer rounded border bg-white p-1 hover:border-slate-400"
            onClick={() => {
              handleReportClick()
            }}
          >
            <AdjustmentsHorizontalIcon className="mr-1 h-5 w-5 text-black" />
            <span className="dark:text-black">报告需求</span>
          </div>
        </>
      )}

      {/* 分析报告生成中 */}
      {(content.status === AnalyzeReportTypes.reportGenerating ||
        content.status === AnalyzeReportTypes.reportGeneratedSuccess) && (
        <div className="analyze-report">
          {content.status === AnalyzeReportTypes.reportGenerating && (
            <div className="flex items-center">
              <LoadingOutlined spin className="flex flex-none text-xl text-link dark:text-gray-600" />
              <p className="p-2 font-semibold text-black dark:text-white">已确认数据洞察报告需求，分析报告生成中...</p>
            </div>
          )}
          {content.status === AnalyzeReportTypes.reportGeneratedSuccess && (
            <div className="flex">
              <p className="mb-2 p-2 font-semibold text-black dark:text-white">
                已确认数据洞察报告需求，分析报告已生成
              </p>
            </div>
          )}
          {localReportTableData && (
            <Table
              rowKey={() => nanoid()}
              dataSource={localReportTableData}
              columns={[{ dataIndex: 'label' }, { dataIndex: 'value' }]}
              showHeader={false}
              pagination={false}
            />
          )}
        </div>
      )}

      {/* 生成报告成功 - markdown显示 */}
      {content.status === AnalyzeReportTypes.reportGenerated && renderReportGenerated}
      {/* 生成报告参数配置弹窗 */}
      {showReportSettingsModal && renderReportModal}
      {/* 字段分类校正弹窗 */}
      {showFieldCorrectionModal && renderFieldCorrectionModal}
    </div>
  )
}
