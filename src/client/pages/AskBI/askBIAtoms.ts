/**
 * Ask BI 的全局状态管理
 */
import { atom } from 'jotai'
import { nanoid } from 'nanoid'
import axios from 'axios'
import { message as antdMessage } from 'antd'
import { produce } from 'immer'
import { atomWithStorage } from 'jotai/utils'
import {
  APIResponse,
  AccessPathsType,
  BotFeature,
  BrandInfoType,
  ChatRequestProps,
  ChatResponse,
  JsonContentItem,
  DatasetDatum,
  DocReportParamsType,
  Llm,
  LlmType,
  MessageInputEditorRef,
  ReadyChartResponse,
  STRUCTURED_MESSAGE_DATA_TYPE,
  ThemeType,
  QueryState,
  ReportTemplateType,
  UserInfo,
} from '@shared/common-types'
import { sceneToDataset } from 'src/shared/common-utils'
import { ChartThemeType, CHART_THEMES, DISPLAY_INSIGHT, DISABLE_INSIGHT_PROJECT_NAME_LIST } from 'src/shared/constants'
import { askBIApiUrls } from 'src/shared/url-map'
import {
  Dimension,
  Id,
  Metric,
  MetricConfigResponse,
  MetricConfigResponseForProject,
  ProjectType,
  SceneType,
} from 'src/shared/metric-types'
import {
  Chat,
  ChatStatus,
  getConfigFromProjectMetricConfig,
  getTempDimensionList,
  searchMetricConfigBySearchValue,
} from 'src/client/utils'
import { isBaoWu } from 'src/shared/baowu-share-utils'
import { AnsChat } from 'src/client/components/chats'
import AskBot from '/img/askBot.webp'
import dipeakChatUserIcon from '/img/dipeakChatUserIcon.webp'
import { PlanData } from 'src/client/components/agent/utils/agent'

export const isSubmittingAtom = atom<boolean>(false)
export const currentLoginUserAtom = atomWithStorage<UserInfo | null>('loginUserInfoData', null)
export const loginAppIdAtom = atomWithStorage<string>('loginAppIdAtom', '')
export const brandInfoAtom = atomWithStorage<BrandInfoType>('brandInfo', {
  appId: '',
  logo: AskBot,
  chatUserIcon: dipeakChatUserIcon,
  companyName: '数巅科技',
  brandName: 'DIPEAK',
  jointLogo: '',
})
// 智能报告引擎 报告生成的当前选中的报告模板
export const currentReportTemplateAtom = atomWithStorage<ReportTemplateType | null>('currentReportTemplate', null)

export const currentParamsExtractApiAtom = atomWithStorage<string>('currentParamsExtractApi', '')

// 智能报告引擎中报告模板的分时时间范围
export const reportDataTimeParamsAtom = atom<{
  timeColumn: string
  timeRangeEnd: string
  timeRangeStart: string
} | null>(null)
export const paramsExtractUrlListAtom = atom<string[]>([])
export const requestParamsExtractUrlListAtom = atom(null, async (_get, set) => {
  try {
    const response = await axios.get<APIResponse<string[]>>(askBIApiUrls.env.paramsExtractUrlList)
    if (response && response.data.data) {
      set(paramsExtractUrlListAtom, response.data.data || [])
      set(currentParamsExtractApiAtom, response.data.data[0])
    }
  } catch (error: any) {
    set(paramsExtractUrlListAtom, [])
  }
})

const getHelloTexts = (brandName: string) => {
  let helloTexts: string[] = []
  const helloTemplate = [
    '你好，我是 DIPeak BI 智能分析助手，从一个问题开始你的数据分析之旅吧',
    '欢迎使用 DIPeak BI 智能分析助手！有什么问题我可以帮助你解答？',
    '👋 我是 DIPeak BI 智能分析助手。有什么需要帮助的吗？请随时提问',
  ]

  switch (brandName) {
    case 'DIPEAK':
      helloTexts = helloTemplate
      break
    case 'China Telecom':
      helloTexts = helloTemplate.map((text) => text.replace(/DIPeak/g, '中国电信'))
      break
    case 'bbwsy':
      helloTexts = helloTemplate.map((text) => text.replace(/DIPeak/g, 'GK'))
      break
    default:
      helloTexts = helloTemplate
      break
  }

  return helloTexts
}

export const pageHeightAtom = atom<string | number>(window.innerHeight)

export const initChatsAtom = atom(null, async (get, set) => {
  const brandInfo = get(brandInfoAtom)
  const brandName = brandInfo.brandName
  const helloTexts = getHelloTexts(brandName)
  const helloText = helloTexts[Math.floor(Math.random() * helloTexts.length)]
  const item: Chat = {
    id: nanoid(),
    isSystemPrompt: true,
    askTime: new Date(),
    ask: null as any,
    ans: [
      {
        role: 'assistant',
        sceneId: 'MULTI_SCENE_CHAT_MOCK_SCENE_ID',
        status: 'success',
        content: [
          {
            type: 'hello-text',
            text: helloText,
          },
        ],
      },
    ],
    selectedSceneId: 'MULTI_SCENE_CHAT_MOCK_SCENE_ID',
    docAns: {
      role: 'assistant',
      status: 'pending',
      content: [
        {
          type: 'hello-text',
          text: helloText,
        },
      ],
    },
  }
  set(chatsAtom, [item])
})
export const chatsAtom = atom<Chat[]>([])

export const conversationIdAtom = atom<string | null>(null)
export const cancelTokenSourceAtom = atom<AbortController>(new AbortController())

// 多租户列表
export const tenantListAtom = atom<
  {
    tenantName: string
    state: 'NORMAL' | 'DISCARDED'
    projects?: { projectName: string; comment: string }[]
    catalogName: string
  }[]
>([])

// 首页tab bi是数据洞察 doc是知识洞察 report是报告生成
export const botFeatureTypeAtom = atomWithStorage<BotFeature>('BotFeatureValue', 'BI')
export const docReportParamsAtom = atom<DocReportParamsType | null>(null)
export const lastSuccessModelNameAtom = atom<string>('')
export const reportOpListAtom = atom<{ key: string; value: string }[]>([])

/** 当前推荐问题的suggestion数据 */
export const currentSuggestionAtom = atom<{
  data: string[]
  error: Error | null
  loading: boolean
} | null>(null)

/** 当前场景的提问历史 */
export const askHistoryAtom = atom<string[] | null>(null)

/** 项目与场景相关的信息 从xengine获取 */
export const semanticProjectInfoAtom = atomWithStorage<ProjectType[]>('semanticProjectInfo', [])

/** Dataset 中包含了当前的 项目/场景相关信息 */
const datasetAtom = atom<DatasetDatum | null>(null)

/** 在localStorage中持久化存储的 Dataset 需要校验是否有效 */
export const storedDatasetAtom = atomWithStorage<DatasetDatum | null>('currentDataset', null)

/** 是否正在加载 dataset 的标识，加载成功后会设置为false */
export const isLoadingDatasetAtom = atom<boolean>(true)

/** 是否正在加载 metricConfig 相关信息 */
export const isLoadingMetricConfigDataAtom = atom<boolean>(false)

// 选择了项目 基于项目问答
export const isProjectChosenAtom = atomWithStorage<boolean>('isProjectChosen', false, undefined, {
  getOnInit: true,
})

/** 校验持久化的storedDataset是否有效 返回有效的Dataset */
function verifyStoredDataset(projectInfo: ProjectType[], storedDataset: DatasetDatum | null): DatasetDatum | null {
  if (!storedDataset) return null

  const matchedProject = projectInfo.find((project) => project.id === storedDataset.projectId)
  const matchedScene = matchedProject?.scenes.find((scene) => scene.id === storedDataset.sceneId)
  const isValidDataset =
    matchedScene &&
    matchedScene.label === storedDataset.sceneLabel &&
    matchedScene.tableName === storedDataset.tableName &&
    matchedScene.enableFollowUpQuestion === storedDataset.enableFollowUpQuestion &&
    matchedScene.enableMetricExactMatch === storedDataset.enableMetricExactMatch &&
    matchedScene.enableTryQueryUp === storedDataset.enableTryQueryUp &&
    matchedScene.enableSelectToastWhenEmptyData === storedDataset.enableSelectToastWhenEmptyData &&
    matchedScene.enableAccMetricToastWhenEmptyData === storedDataset.enableAccMetricToastWhenEmptyData
  if (isValidDataset) {
    console.info('持久化Dataset数据校验成功')
    return storedDataset
  } else {
    console.info('持久化Dataset数据校验失败，准备获取默认的Dataset信息...')
    return null
  }
}

export const requestDefaultDatasetLoadingAtom = atom(false)

/** 初始化 获取默认的 dataset 信息 */
export const requestDefaultDatasetAtom = atom(null, async (get, set) => {
  const env = await get(envAtom)
  if (env?.VITE_PRODUCTS?.trim() === 'X-Engine') {
    return null
  }
  try {
    set(requestDefaultDatasetLoadingAtom, true)
    const { data: projectInfo } = (
      await axios.get<
        APIResponse<{
          projects: ProjectType[]
          DEFAULT_SELECT_PROJECT: boolean
          DEFAULT_PROJECT: string
          DEFAULT_SCENE: string
        }>
      >(askBIApiUrls.auth.projects)
    ).data
    const { projects: semanticProjectInfo, DEFAULT_SELECT_PROJECT, DEFAULT_PROJECT, DEFAULT_SCENE } = projectInfo ?? {}

    if (semanticProjectInfo) {
      set(semanticProjectInfoAtom, semanticProjectInfo || [])
    } else {
      throw new Error('获取当前用户的项目信息失败')
    }
    const storedDataset = verifyStoredDataset(semanticProjectInfo, get(storedDatasetAtom))

    // 在env中可以设置默认的项目明name以及场景名label，如果没有设置则默认选择第一个项目和场景
    const projectItem = DEFAULT_PROJECT
      ? (semanticProjectInfo.find((item) => item.name === DEFAULT_PROJECT) ?? semanticProjectInfo[0])
      : semanticProjectInfo[0]

    const defaultScene = semanticProjectInfo[0]?.scenes[0]
      ? sceneToDataset({
          scene: projectItem.scenes.find((scene) => scene.label === DEFAULT_SCENE) ?? projectItem.scenes[0],
          project: projectItem,
        })
      : null
    const dataset = storedDataset ?? defaultScene

    if (DEFAULT_SELECT_PROJECT) {
      console.info('Set isProjectChosen true because SELECT_PROJECT is', DEFAULT_SELECT_PROJECT)
      set(isProjectChosenAtom, true)
    }

    set(currentDatasetAtom, dataset)
    set(isLoadingDatasetAtom, false)
  } catch (error: any) {
    set(currentDatasetAtom, null)
    set(isLoadingDatasetAtom, false)
    antdMessage.error(error.message || '场景信息获取失败，请联系管理员')
  } finally {
    set(requestDefaultDatasetLoadingAtom, false)
  }
})

/** 获取/更新 当前的dataset 获取当前项目的指标信息 */
export const currentDatasetAtom = atom(
  (get) => get(storedDatasetAtom),
  async (get, set, newDataset: DatasetDatum | null) => {
    if (!newDataset) {
      console.info('更新Dataset失败，传入的值为空')
      return
    }
    set(storedDatasetAtom, newDataset)
    set(datasetAtom, newDataset)

    // 获取某一个 场景 下面的指标信息
    try {
      const isProjectChosen = get(isProjectChosenAtom)
      console.info('Is Project Chosen', isProjectChosen)

      set(isLoadingMetricConfigDataAtom, true)

      if (isProjectChosen) {
        const metricConfigDataOfProject = await axios.get<APIResponse<MetricConfigResponseForProject>>(
          askBIApiUrls.metrics.listInProject(newDataset.projectId),
        )
        set(metricConfigOfProjectAtom, metricConfigDataOfProject.data.data || null)
        set(
          metricConfigAtom,
          metricConfigDataOfProject.data.data?.find((e) => e.sceneId === newDataset.sceneId)?.data || null,
        )
      } else {
        const projectList = get(semanticProjectInfoAtom)
        const sceneDetail = (projectList || [])
          .find((project) => project.id === newDataset.projectId)
          ?.scenes.find((scene) => scene.id === newDataset.sceneId)
        if (sceneDetail?.agent?.includes('BI')) {
          const metricConfigOfScene = await axios.get<APIResponse<MetricConfigResponse>>(askBIApiUrls.auth.metrics, {
            params: { sceneId: newDataset.sceneId },
          })
          const response = await axios.post(askBIApiUrls.metrics.getAllDimensionsValues, {
            sceneId: newDataset.sceneId,
          })
          if (metricConfigOfScene.data.data && response.data.data) {
            const updateMetricConfig = {
              ...metricConfigOfScene.data.data,
              allDimensions: response.data.data,
            }
            set(metricConfigAtom, updateMetricConfig || null)
          } else {
            set(metricConfigAtom, metricConfigOfScene.data.data || null)
          }
        }
      }
      set(isLoadingMetricConfigDataAtom, false)
      // !!!特殊处理只在药监局下显示数字人，POC后续删除
      const isShowDigitalHuman = newDataset.sceneId === 'dtVjr9M6Dz0Hye0u' || newDataset.sceneId === 'U9ilCbuBmwKYkrP2'
      if (!isShowDigitalHuman) {
        set(showDigitalHumanAtom, isShowDigitalHuman)
      }
    } catch (error: any) {
      antdMessage.error(error.message || '获取指标中心信息失败，请联系管理员')
      set(isLoadingMetricConfigDataAtom, false)
      set(metricConfigOfProjectAtom, null)
      set(metricConfigAtom, null)
    }
    // 更新针对场景的推荐问题
    axios
      .get(askBIApiUrls.suggestions, {
        params: { sceneId: newDataset.sceneId, tableName: newDataset.tableName },
      })
      .then((res) => {
        set(currentSuggestionAtom, {
          data: res.data.data,
          error: null,
          loading: false,
        })
      })
      .catch((error) => {
        console.error('Get suggest questions with error:', error)
        set(currentSuggestionAtom, {
          data: [],
          error,
          loading: false,
        })
      })
    const isProjectChosen = get(isProjectChosenAtom)
    axios
      .get(askBIApiUrls.convers.askHistory, {
        params: {
          sceneId: newDataset.sceneId,
          isProjectChosen,
          projectId: newDataset.projectId,
        },
      })
      .then((res) => {
        set(askHistoryAtom, res.data.data)
      })
      .catch((error) => {
        set(askHistoryAtom, [])
        console.error('Get Ask History with error:', error)
      })
  },
)

export const allChartsAtom = atom<ReadyChartResponse[]>([])
export const envAtom = atom(async () => {
  const response = await axios.get<APIResponse<Record<string, string>>>(askBIApiUrls.env.list)
  return response.data?.data || {}
})
export const pageMenuAccessAtom = atom(async () => {
  try {
    const response = await axios.post<APIResponse<AccessPathsType[]>>(askBIApiUrls.rolesPermission.pageList, {
      serviceName: 'xengine_path',
    })
    const allowedResources: string[] = []
    const pageAccessResult = response.data.data
    if (pageAccessResult == null || pageAccessResult.length === 0) {
      return allowedResources
    }
    pageAccessResult.forEach((item: { allowed: boolean; path: string }) => {
      if (item.allowed && /^\/menuPermission\//.test(item.path)) {
        allowedResources.push(item.path.replace('/menuPermission/', ''))
      }
    })
    return allowedResources
  } catch (error: any) {
    console.error('Get access resource error:', error.message, error.response?.data)
  }
})

export const showChatHistoryListAtom = atom<boolean>(false)
export const showAskHistoryListAtom = atom<boolean>(false)

/** 模型列表 */
export const llmListAtom = atom<Llm[]>([])
/** 当前使用的 Llm 类型 */
export const llmTypeAtom = atom<LlmType | null>(null)
export const currentChartThemeTypeAtom = atom<ChartThemeType>('default')
export const currentChartThemeNameAtom = atom(
  (get) => CHART_THEMES.find((theme) => theme.type === get(currentChartThemeTypeAtom))?.name || '未知',
)

/** 颜色的默认值，就是先检查 localStorage，然后检查 prefers-color-scheme */
// const defaultTheme = IS_DARK ? 'dark' : 'light'
const defaultTheme = 'light'
export const themeAtom = atom<ThemeType>(defaultTheme)

/** 指标中心的元信息。TODO: 改个更好的名字 */
export const metricConfigAtom = atom<MetricConfigResponse | null>(null)
export const metricConfigOfProjectAtom = atom<MetricConfigResponseForProject | null>(null)
// 变量名字先用这个 因为指标名字上面有重复的。
export const metricListAtomForOption = atom<(Metric & Id)[]>([])

// json的已输入的提问内容数据
export const agentOriginStructuredMessageAtom = atom<JsonContentItem[]>([])
// export const agentStructuredMessageAtom = atom<string | JsonContentItem[]>('')
export const agentStructuredMessageAtom = atom<JsonContentItem[], [JsonContentItem[] | string], void>(
  (get) => get(agentOriginStructuredMessageAtom),
  (get, set, data) => {
    let result: JsonContentItem[]
    if (!data || data.length === 0) {
      set(agentOriginStructuredMessageAtom, [])
      const { setHtml } = get(messageInputEditorRefAtom)
      setHtml('')
      return
    }
    if (typeof data === 'string') {
      result = [
        {
          type: STRUCTURED_MESSAGE_DATA_TYPE.TEXT,
          'data-type': STRUCTURED_MESSAGE_DATA_TYPE.TEXT,
          'data-content': data,
        },
      ]
    } else {
      result = data
    }
    set(agentOriginStructuredMessageAtom, result)
  },
)
// 根据json派生的纯文本提问内容
export const agentMessageAtom = atom<string>((get) => {
  const structuredMessage = get(agentStructuredMessageAtom)

  let result = ''
  structuredMessage.map((item) => {
    if (item['data-content']) {
      result += item['data-content']
    }
  })
  return result
})

/** 输入框处是否显示语音识别样式 */
export const isShowSpeechRecognitionAtom = atom<boolean>(false)
/** 存对话输入框相关方法 */
export const messageInputEditorRefAtom = atom<MessageInputEditorRef>({ setHtml: () => {} })

export const updateMessageAndHtmlAtom = atom<null, [JsonContentItem[] | JsonContentItem | string, boolean?], void>(
  null,
  (get, set, data, updateCursor = true) => {
    const { updateCursorPosition, setHtml } = get(messageInputEditorRefAtom)
    let result: JsonContentItem[] = []

    const structuredMessage = get(agentStructuredMessageAtom)
    if (!data || data === '') {
      set(agentStructuredMessageAtom, data)
    } else if (typeof data === 'string') {
      const result = [{ 'data-type': STRUCTURED_MESSAGE_DATA_TYPE.TEXT, 'data-content': data }]
      set(agentStructuredMessageAtom, result)
      setHtml?.(result)
    } else if (Array.isArray(data)) {
      // 如果是数组,则认为是完整替换原数据
      set(agentStructuredMessageAtom, data)
      setHtml?.(data)
    } else {
      result = [...structuredMessage, data]
      set(agentStructuredMessageAtom, result)
      setHtml?.(result)
    }
    if (updateCursor) {
      updateCursorPosition?.()
    }
  },
)

// 已选中的指标数据
export const agentSelectedMetricAtom = atom<JsonContentItem[]>((get) => {
  const structuredMessage = get(agentStructuredMessageAtom)
  if (typeof structuredMessage === 'string') {
    return []
  }

  const result: JsonContentItem[] = structuredMessage.filter((item) => {
    return item['data-type'] === STRUCTURED_MESSAGE_DATA_TYPE.METRIC
  })
  return result
})

// 已选中的维度数据
export const agentSelectedDimensionAtom = atom<JsonContentItem[]>((get) => {
  const structuredMessage = get(agentStructuredMessageAtom)
  if (typeof structuredMessage === 'string') {
    return []
  }

  const result: JsonContentItem[] = structuredMessage.filter((item) => {
    return item['data-type'] === STRUCTURED_MESSAGE_DATA_TYPE.DIMENSION
  })
  return result
})

// 热门指标中已选中的维度数据
const originSelectedHotMetricAtom = atom<JsonContentItem | null>(null)
// 热门指标中已选中的维度数据
export const selectedHotMetricAtom = atom<JsonContentItem | null, [JsonContentItem | null], void>(
  (get) => get(originSelectedHotMetricAtom),
  (get, set, data) => {
    console.info('get', get)
    set(originSelectedHotMetricAtom, data)
    set(isShowRecommendedMetricsAtom, true)
  },
)

export const isShowRecommendedMetricsAtom = atom<boolean>(false)
export const recommendedMetricsAtom = atom<
  Promise<Array<{ id: number; type: 'digit' | 'trend' | 'topN'; jsonContent: JsonContentItem[] }>>
>(async (get) => {
  const selectedHotMetric = get(selectedHotMetricAtom)
  const metricName = selectedHotMetric?.['data-name']
  if (!metricName) {
    return Promise.resolve([])
  }
  const currentDataset = get(currentDatasetAtom)
  const data = await axios
    .post(askBIApiUrls.metrics.recommendQuestion, {
      metricName,
      sceneId: currentDataset?.sceneId,
    })
    .then((res) => {
      return res.data.data
    })
    .catch(() => {
      return []
    })
  return data
})

type SearchedMetricConfig = {
  [Property in keyof Pick<MetricConfigResponse, 'allMetrics' | 'allDimensions'>]: {
    list: MetricConfigResponse[Property]
    searchValue?: string
  }
}

type AllMetricsAndDimensionsType = {
  allDimensions: Dimension[]
  allMetrics: Metric[]
}

export const AllMetricsAndDimensionsAtom = atom<AllMetricsAndDimensionsType>((get) => {
  const isProjectChosen = get(isProjectChosenAtom)
  const metricConfigOfProject = get(metricConfigOfProjectAtom)

  const metricConfig = get(metricConfigAtom)
  const result: AllMetricsAndDimensionsType = {
    allDimensions: metricConfig?.allDimensions || [],
    allMetrics: metricConfig?.allMetrics || [],
  }
  if (isProjectChosen) {
    result.allMetrics = getConfigFromProjectMetricConfig<Metric>(metricConfigOfProject, 'allMetrics')
    result.allDimensions = getConfigFromProjectMetricConfig<Dimension>(metricConfigOfProject, 'allDimensions')
  }
  return result
})

// 已选的维度源数据
export const searchedOriginMetricConfigValueAtom = atom<SearchedMetricConfig>({
  allMetrics: { list: [], searchValue: '' },
  allDimensions: { list: [], searchValue: '' },
})
// 已选的维度数据
export const searchedMetricConfigValueAtom = atom<SearchedMetricConfig>((get) => {
  const originSelectedHotMetric = get(searchedOriginMetricConfigValueAtom)

  const { allMetrics, allDimensions } = originSelectedHotMetric

  const metricConfig = get(metricConfigAtom)

  const allMetricsAndDimensions = get(AllMetricsAndDimensionsAtom)
  const metrics = allMetricsAndDimensions.allMetrics
  if (allMetrics.list.length === 0 && allDimensions.list.length === 0) {
    const tempList = getTempDimensionList(allMetricsAndDimensions.allDimensions, isBaoWu(metricConfig?.metricTableName))

    return {
      allMetrics: {
        list: metrics || [],
        searchValue: allMetrics.searchValue,
      },
      allDimensions: {
        list: tempList,
        searchValue: allDimensions.searchValue,
      },
    }
  }
  return originSelectedHotMetric
})

// 根据搜索词搜索已选的维度数据
export const searchMetricConfigAtom = atom<null, [string], void>(null, async (get, set, searchValue) => {
  const metricConfig = get(metricConfigAtom)
  const { allDimensions, allMetrics } = get(AllMetricsAndDimensionsAtom)

  let searchedAllMetrics: SearchedMetricConfig['allMetrics'] = { list: [], searchValue: '' }
  let searchedAllDimensions: SearchedMetricConfig['allDimensions'] = { list: [], searchValue: '' }
  // 搜索数据过滤掉虚拟维度
  const tempList = getTempDimensionList(allDimensions, isBaoWu(metricConfig?.metricTableName))

  if (allMetrics?.length > 0) {
    if (searchValue) {
      searchedAllMetrics = await searchMetricConfigBySearchValue<Metric>(allMetrics, searchValue)
    } else {
      // 如果查询的字符串为空,则返回全部的
      searchedAllMetrics.list = allMetrics
    }
  }

  if (tempList?.length > 0) {
    if (searchValue) {
      searchedAllDimensions = await searchMetricConfigBySearchValue<Dimension>(tempList, searchValue)
    } else {
      // 如果查询的字符串为空,则返回全部的
      searchedAllDimensions.list = tempList
    }
  }

  set(searchedOriginMetricConfigValueAtom, { allDimensions: searchedAllDimensions, allMetrics: searchedAllMetrics })
})

/**是否显示维度-指标弹窗 */
export const isShowMatchedParamDrawerAtom = atom<boolean>(false)
/** 最近选中的非码值的数据 */
export const latestSelectedMessageAtom = atom<JsonContentItem>({})

/** 最近输入的数据 */
export const latestMessageAtom = atom<JsonContentItem>((get) => {
  const structuredMessage = get(agentOriginStructuredMessageAtom)
  return structuredMessage[structuredMessage.length - 1]
})

export const isShowSubDimensionAtom = atom<boolean>(false)
/**码值列表 */
export const subDimensionsAtom = atom<string[]>((get) => {
  const latestSelectedMessage = get(latestSelectedMessageAtom)
  const latestMessage = get(latestMessageAtom)
  const metricConfig = get(metricConfigAtom)
  if (
    !metricConfig ||
    !latestMessage ||
    (latestMessage['data-type'] !== STRUCTURED_MESSAGE_DATA_TYPE.DIMENSION &&
      latestMessage['data-type'] !== STRUCTURED_MESSAGE_DATA_TYPE.SUB_DIMENSION)
  ) {
    return []
  }
  if (latestSelectedMessage['data-type'] === STRUCTURED_MESSAGE_DATA_TYPE.DIMENSION) {
    const { allDimensions } = metricConfig
    const findData = allDimensions.find((item) => {
      if (item.label === latestSelectedMessage['data-content']) {
        return true
      }
      return false
    })
    if (findData?.type === 'categorical' && findData?.values) {
      return findData.values.filter(Boolean)
    }
  }
  return []
})

export const currentFollowUpQuestionAtom = atom<Chat | null>(null)
export const currentFollowUpQuestionNextAtom = atom<AnsChat | null>(null)

export const configSceneParamsAtom = atom<any>(null)

// 标识是从场景里面点击了报告生成，并赋值modelName
export const isReportModelNameTagAtom = atom<string | null>(null)

// 场景弹窗中是否勾选了对文件提问的switch开关
export const scenePopoverIsCheckedDocAtom = atom<boolean>(false)

// 有权限的页面list
export const accessPathsAtom = atomWithStorage<AccessPathsType[]>('pageAccessResult', [])

// export const chatQueryParamsChangeAtom = atom(
//   null,
//   async (
//     get,
//     _set,
//     {
//       queryParamsVerified,
//       chatId,
//       sceneId,
//       planData,
//       infoTexts,
//       setAnsItem,
//     }: {
//       queryParamsVerified: QueryParamsVerified
//       chatId: string
//       sceneId: string
//       planData?: PlanData
//       infoTexts?: string[]
//       setAnsItem?: (v: AnsChatItem[]) => void
//     },
//   ) => {
//     console.info('Make query change request', queryParamsVerified)
//     try {
//       const llmType = get(llmTypeAtom)
//       const conversationId = get(conversationIdAtom)
//       const chats = get(chatsAtom)
//       const currentChat = chats.find((item) => item.id === chatId)
//       const multiAgentData = getMultiAgentData(currentChat)
//       if (!currentChat || !multiAgentData) return
//       const response = await axios.post<ChatResponse>(askBIApiUrls.metricQuery, {
//         queryParamsVerified,
//         infoTexts,
//         llmType: llmType,
//         conversationId: conversationId,
//         sceneId,
//         chatId,
//         customize: true,
//       })
//       const data = response.data

//       if (data.ready) {
//         const readyChatAns = readyResponseToChatAns(data, sceneId)
//         planData?.emit('onUpdateEarlyAnsItem', [readyChatAns])
//         setAnsItem?.([readyChatAns])
//         const currentConfidenceSelection = {
//           sceneId,
//           metricNames: queryParamsVerified.queryParams.metricNames,
//           where: queryParamsVerified.queryParams.where || '',
//         }
//         planData?.emit('onUpdateCurrentConfidenceSelection', currentConfidenceSelection)
//         console.info('currentConfidenceSelection is', currentConfidenceSelection)
//         // setTimeout(scrollToBottom, 100)
//       } else {
//         const unreadyChatAns = unreadyResponseToChatAns(data, sceneId)
//         planData?.emit('onUpdateEarlyAnsItem', [unreadyChatAns])
//         setAnsItem?.([unreadyChatAns])
//         const currentConfidenceSelection = {
//           sceneId,
//           metricNames: queryParamsVerified.queryParams.metricNames,
//           where: queryParamsVerified.queryParams.where || '',
//         }
//         planData?.emit('onUpdateCurrentConfidenceSelection', currentConfidenceSelection)
//         console.info('currentConfidenceSelection is', currentConfidenceSelection)
//         // setTimeout(scrollToBottom, 100)
//       }
//     } catch (e: any) {
//       console.error('Make query change request with error:' + e?.message)
//     }
//   },
// )

export let intervalId: NodeJS.Timeout | null = null

// const updateAssistantMessageAtom = atom(null, async (get, set, { chatId }: { chatId: string }) => {
//   const cancelTokenSource = get(cancelTokenSourceAtom)
//   const isSubmitting = get(isSubmittingAtom)

//   const startTime = Date.now()

//   // 清除旧的定时器（如果存在）
//   if (intervalId) {
//     clearInterval(intervalId)
//   }

//   const id = setInterval(() => {
//     const elapsedTime = Date.now() - startTime
//     let newMessageText = ''
//     let isError = false
//     if (elapsedTime <= 15000) {
//       newMessageText = '问题解析中，匹配指标、维度...'
//     } else if (elapsedTime <= 30000) {
//       newMessageText = 'SQL智能生成...'
//     } else if (elapsedTime <= 45000) {
//       newMessageText = '数据查询、洞察提取...'
//     } else if (elapsedTime <= 60000 * 10) {
//       newMessageText = '图形化输出、图表推荐...'
//     } else {
//       isError = true
//       newMessageText = '请求后端接口超时，请重试'
//       intervalId && clearInterval(intervalId)
//       intervalId = null
//       if (isSubmitting) {
//         // 取消请求，会导致配有相应token的axios返回失败态的promise，直接走catch的handleErrorStatusChat的状态更改
//         cancelTokenSource.abort(newMessageText)
//         set(isSubmittingAtom, false)
//         return
//       }
//     }

//     // 更新页面上显示的文本
//     set(
//       chatsAtom,
//       produce((draft) => {
//         const theChat = draft.find((item) => item.id === chatId)
//         if (theChat) {
//           const newAns = theChat.ans.map((ans) => {
//             if (ans.status !== ChatStatus.success) {
//               return {
//                 ...ans,
//                 content: [{ type: 'text' as const, text: newMessageText }],
//                 status: isError ? ChatStatus.failure : ChatStatus.pending,
//               }
//             }
//             return ans
//           })
//           theChat.ans = newAns
//         }
//       }),
//     )
//   }, 1000)
//   intervalId = id // 更新全局状态中的定时器ID
// })

export const stopAssistantMessageAtom = atom(null, async (get, set, { chatId }: { chatId: string }) => {
  // 1. 停止提交状态
  set(isSubmittingAtom, false)

  // 2. 终止请求
  const cancelTokenSource = get(cancelTokenSourceAtom)
  cancelTokenSource.abort('用户手动终止')

  // 3. 清除定时器
  if (intervalId) {
    clearInterval(intervalId)
    intervalId = null
  }

  const chat = get(chatsAtom).find((item) => item.id === chatId)

  // 4. 更新页面显示
  set(
    chatsAtom,
    produce((draft) => {
      const theChat = draft.find((item) => item.id === chatId)
      if (theChat) {
        const newAns = theChat.ans.map((ans) => {
          if (ans.status !== ChatStatus.success) {
            return {
              ...ans,
              content: [{ type: 'text' as const, text: '用户手动终止' }],
              status: ChatStatus.failure,
            }
          }
          return ans
        })
        theChat.ans = newAns
      }
    }),
  )

  // 记录日志
  axios.post(askBIApiUrls.converChats.stopChat, { traceId: chat?.traceId, chat })
})

/**
 * 发送数据解读的请求
 * @param {respData} - 后端给前端返回的原始信息
 * @param {llmType} - 模型类型
 * @param {chatId} - chat ID
 * @param {sceneId} - 场景 ID
 */
export const sendChartInsightAtom = atom(
  null,
  async (
    get,
    set,
    {
      respData,
      chatId,
      sceneId,
      traceId,
    }: {
      respData: ChatResponse
      chatId: string
      sceneId: string
      traceId: string
    },
  ) => {
    const currentDataset = get(storedDatasetAtom || currentDatasetAtom)

    // 检查当前项目名称是否在禁用列表中
    const isProjectNameDisabled = DISABLE_INSIGHT_PROJECT_NAME_LIST.some((name) =>
      currentDataset?.projectName.includes(name),
    )
    // 展示数据解读的开关打开 & ready: true & [taskType: 'query-metric' || taskType: 'attribution-metric-analysis']
    const SHOW_DATA_INSIGHT =
      DISPLAY_INSIGHT &&
      !isProjectNameDisabled &&
      respData.ready === true &&
      (respData.taskType === 'query-metric' || respData.taskType === 'attribution-metric-analysis')

    if (SHOW_DATA_INSIGHT) {
      // 先设置loading状态
      set(
        chatsAtom,
        produce((draft: Chat[]) => {
          const theChat = draft.find((item) => item.id === chatId)
          if (!theChat) {
            return
          }
          const currentAns = theChat.ans.find((m) => m.sceneId === sceneId)

          if (!currentAns) {
            return
          }
          currentAns.content = [
            ...currentAns.content,
            {
              type: 'chart-insight',
              status: 'pending',
              chartItem: {} as any,
              sceneId,
              chatId,
              traceId,
              text: null,
            },
          ]
        }),
      )
    }
  },
)

export const isMultiAgentModeAtom = atom(true)
export const chatRequestPropsAtom = atom<ChatRequestProps | null>(null)

/**
 * 发起 Chat 请求
 */
// export const makeAgentRequestAtom = atom(
//   null,
//   async (
//     get,
//     set,
//     {
//       isMiniMode,
//       navigate,
//       newMessage,
//       isReportModelNameTag,
//       chatResponse,
//     }: {
//       isMiniMode: boolean
//       navigate?: (path: string) => void
//       newMessage?: string
//       isReportModelNameTag?: string
//       chatResponse?: ChatResponse
//     },
//   ) => {
//     const selectDoc = get(currentSelectFileBySceneAtom)
//     const deepSeekEnableNetWork = get(deepSeekEnableNetWorkAtom)
//     const llmType = get(llmTypeAtom)
//     // const loginAppId = get(loginAppIdAtom)
//     const additionalInfo = get(additionalInfoAtom)
//     const traceId = createTraceId()
//     const isMultiAgentMode = get(isMultiAgentModeAtom)
//     const isLoadingDataset = get(isLoadingDatasetAtom)
//     const currentDataset = get(storedDatasetAtom || currentDatasetAtom)
//     const chats = get(chatsAtom)
//     const metricConfigRecord = get(metricConfigRecordAtom)
//     const isProjectChosen = get(isProjectChosenAtom)
//     const docReportParams = get(docReportParamsAtom)
//     const paramsExtractUrlList = await get(paramsExtractUrlListAtom)
//     const defaultParamsExtractUrl = paramsExtractUrlList ? paramsExtractUrlList[0] : ''
//     const currentParamsExtractApi = get(currentParamsExtractApiAtom) || defaultParamsExtractUrl

//     set(isReportModelNameTagAtom, isReportModelNameTag || null)

//     set(isShowMatchedParamDrawerAtom, false)

//     if (isLoadingDataset) {
//       antdMessage.warning('数据源正在加载中，请稍候...')
//       return
//     }

//     if (currentDataset == null) {
//       antdMessage.error('当前用户无数据源权限或数据源信息正在加载中')
//       return
//     }

//     // if (llmType == null) {
//     //   antdMessage.error('您的账号未配置模型，请联系管理员配置')
//     //   return
//     // }

//     // if (llmList.length === 0) {
//     //   antdMessage.error('您的账号未配置模型，请联系管理员配置')
//     //   return
//     // }

//     const taskId = nanoid()
//     const message = get(agentMessageAtom)
//     const jsonMessage = get(agentOriginStructuredMessageAtom)
//     const isSubmitting = get(isSubmittingAtom)
//     const defaultConversationId = get(conversationIdAtom)
//     const currentFollowUpQuestion = get(currentFollowUpQuestionAtom)
//     // const reportModelNameTag = get(isReportModelNameTagAtom)
//     const msg = newMessage || message
//     let condenseMsg: string | null = null
//     if (isDeepseekErrorTriggerChatItem(chats.at(-1))) {
//       const latestCondenseMessage = chats.findLast((chat) => chat.ask?.condenseContent)?.ask?.condenseContent
//       const messages = latestCondenseMessage
//         ? [{ role: 'user', content: latestCondenseMessage }]
//         : loadAllMessages({ chats, metricConfigRecord })
//       messages.push({ role: 'user', content: msg })
//       const res = await axios.post(askBIApiUrls.condense, { messages, currentParamsExtractApi })
//       condenseMsg = res.data.data
//     }
//     const jsonContent = jsonMessage.length > 0 ? JSON.stringify(jsonMessage) : ''
//     if (msg.length < 1) {
//       return
//     }

//     if (isSubmitting) {
//       return
//     }

//     set(isSubmittingAtom, true)
//     set(updateMessageAndHtmlAtom, '', false)
//     set(currentFollowUpQuestionAtom, null)

//     const currentConversationId = defaultConversationId || nanoid()
//     let chatId = nanoid()

//     const cancelTokenSource = new AbortController()
//     set(cancelTokenSourceAtom, cancelTokenSource)

//     // 判断 Agent 类型
//     const semanticProjectInfo = get(semanticProjectInfoAtom)

//     let scenesList = semanticProjectInfo.find((p) => p.id === currentDataset?.projectId)?.scenes ?? []
//     if (!isProjectChosen) {
//       scenesList = scenesList?.filter((s) => s.id === currentDataset?.sceneId) ?? []
//     }

//     const isDocAgent = !!scenesList?.some((v) => v.agent?.includes('Doc'))
//     const isBIAgent = !!scenesList?.some((v) => v.agent?.includes('BI'))

//     // 如果是 mini mode，点击 chat box 时，跳转到 chart page
//     if (isMiniMode && navigate) {
//       navigate(askBIPageUrls.chatNew)
//     }

//     if (!docReportParams) {
//       const assistantText = isReportModelNameTag
//         ? '解析中...解析完成后可对该文档下达报告需求指示'
//         : '正在处理，请稍后...'

//       // 设置Loading内容
//       set(chatsAtom, (prevChats) => [
//         ...prevChats,
//         {
//           id: chatId,
//           taskId,
//           traceId,
//           askTime: new Date(),
//           ask: {
//             role: 'user',
//             content: msg,
//             jsonContent,
//             parentId: currentFollowUpQuestion ? currentFollowUpQuestion.id : null,
//           },
//           ans: [
//             {
//               role: 'assistant' as const,
//               content: [{ type: 'text', text: assistantText } as const],
//               sceneId: isProjectChosen ? MULTI_SCENE_CHAT_MOCK_SCENE_ID : currentDataset.sceneId,
//               status: ChatStatus.pending,
//             },
//           ],
//           selectedSceneId: isProjectChosen ? MULTI_SCENE_CHAT_MOCK_SCENE_ID : currentDataset.sceneId,
//           docAns: {
//             role: 'assistant',
//             status: ChatStatus.pending,
//             content: [
//               {
//                 type: 'doc-result',
//                 text: '正在为您查询这个问题...',
//                 payload: {
//                   msg,
//                   conversationId: currentConversationId,
//                   chatId,
//                   llmType,
//                   cancelTokenSource,
//                   sceneId: currentDataset.sceneId,
//                 },
//               },
//             ],
//           },
//         },
//       ])
//     } else {
//       // 选择完参数后，要走生成报告的逻辑。 docReportParams是生成报告所需的参数
//       set(isSubmittingAtom, true)
//       if (docReportParams.isRegenerateReportAgainTag) {
//         console.info('重新生成docReportParams', docReportParams)
//         chatId = docReportParams.chatId
//         set(chatsAtom, (prevChats) => [
//           ...prevChats,
//           {
//             id: chatId,
//             askTime: new Date(),
//             ask: {
//               role: 'user',
//               content: msg,
//               jsonContent,
//               parentId: null,
//             },
//             ans: [
//               {
//                 role: 'assistant' as const,
//                 content: [
//                   {
//                     type: 'doc-report',
//                     status: AnalyzeReportTypes.reportGenerating,
//                     rows: docReportParams,
//                   },
//                 ],

//                 sceneId: docReportParams.fileId,
//                 status: ChatStatus.pending,
//               },
//             ],
//             selectedSceneId: docReportParams.fileId,
//             docAns: {} as any,
//           },
//         ])
//       } else {
//         set(
//           chatsAtom,
//           produce((draft: Chat[]) => {
//             const theChat = draft.find((item) => item.id === docReportParams.chatId)
//             if (!theChat) {
//               return
//             }
//             theChat.ans = [
//               {
//                 role: 'assistant' as const,
//                 content: [
//                   {
//                     type: 'doc-report',
//                     status: AnalyzeReportTypes.reportGenerating,
//                     rows: docReportParams,
//                   },
//                 ],
//                 status: ChatStatus.pending,

//                 sceneId: docReportParams.fileId,
//               },
//             ]
//           }),
//         )
//       }
//     }

//     // 更新conversation的数据 异步执行不阻塞
//     const upsertResp = await axios
//       .post(askBIApiUrls.convers.upsert, {
//         message: msg,
//         llmType,
//         isNewConversation: !defaultConversationId,
//         conversationId: currentConversationId,
//         sceneId: isProjectChosen ? null : currentDataset.sceneId,
//         projectId: currentDataset.projectId,
//         chatId,
//         parentId: currentFollowUpQuestion ? currentFollowUpQuestion.id : null,
//         traceId,
//       })
//       .catch((error) => {
//         console.error('Record Conversation History Failed', error)
//         antdMessage.warning('会话历史记录失败，不影响问答')
//         return null
//       })

//     // DOC不需要设置message
//     if (!isDocAgent && !isReportModelNameTag && !docReportParams) {
//       set(updateAssistantMessageAtom, { chatId })
//     }
//     setTimeout(scrollToBottom, 100)

//     function getPrevLlmResponse() {
//       for (let i = chats.length - 1; i >= 0; i--) {
//         const chat = chats[i]
//         const content: any = chat?.ans?.[0].content?.[0]
//         const END_TEXT = `本次问答已终止。`
//         if (!content) continue
//         if (content.type === 'text' && content.text === END_TEXT) continue
//         return content?.originLlmResponse?.data
//       }
//     }

//     // 单场景
//     async function singleSceneAskBIChat(
//       msg: string,
//       conversationId: string,
//       chatId: string,
//       sceneId: string,
//       projectId: string,
//       llmType: LlmType | null,
//       currentFollowUpQuestion: Chat | null,
//       cancelTokenSource: AbortController,
//       currentParamsExtractApi: string,
//     ) {
//       const scene = scenesList?.find((s) => s.id === sceneId)
//       let rspData
//       let requestData!: ChatRequestProps
//       if (ENABLE_MOCK) {
//         rspData = JSON.parse(MOCK_RESPONSE)
//       } else {
//         const currentLoginUser = get(currentLoginUserAtom)
//         const roleIds = currentLoginUser?.groups.map((item) => item.id) || []
//         let fileIds: string[] | undefined = undefined
//         let dirIds: number[] | undefined = undefined
//         if (isDocAgent) {
//           if (selectDoc) {
//             fileIds = [selectDoc.id.toString()]
//           }
//           if (!fileIds && !dirIds) {
//             console.info('获取全部文档')

//             const responseList = await Promise.all(
//               scenesList
//                 .filter((v) => v.agent?.includes('Doc'))
//                 .map((v) =>
//                   axios.post(askDocApiUrls.getDocDirList, {
//                     roleIds,
//                     dirId: -1,
//                     page: 1,
//                     pageSize: 99999,
//                     name: '',
//                     fileType: '',
//                     sceneId: v.id,
//                   }),
//                 ),
//             )

//             const files: Doc[] = responseList.flatMap((v) => v.data.data.files)
//             fileIds = files.filter((v) => !v.isDir).map((v) => v.id.toString())
//             dirIds = files.filter((v) => v.isDir).map((v) => v.id)
//           }
//         }
//         const newMessage = condenseMsg ?? msg
//         requestData = {
//           message: newMessage,
//           conversationId,
//           traceId,
//           chatId,
//           sceneId: isProjectChosen ? undefined : sceneId,
//           projectId,
//           llmType,
//           parentId: currentFollowUpQuestion ? currentFollowUpQuestion.id : null,
//           enableFollowUpQuestion: !!scene?.enableFollowUpQuestion,
//           enableMetricExactMatch: !!scene?.enableMetricExactMatch,
//           enableTryQueryUp: !!scene?.enableTryQueryUp,
//           enableSelectToastWhenEmptyData: !!scene?.enableSelectToastWhenEmptyData,
//           enableAccMetricToastWhenEmptyData: !!scene?.enableAccMetricToastWhenEmptyData,
//           currentParamsExtractApi,
//           prevLlmResponse: getPrevLlmResponse(),
//           taskId,
//           condenseMsg,
//           enableInternetSearch: deepSeekEnableNetWork,
//           enableDocSearch: isDocAgent,
//           enableBi: isBIAgent,
//           fileIds,
//           dirIds,
//           additional_info: {
//             hint_dynamic_limit: additionalInfo.hintDynamicLimit,
//             hint_dense_weight: additionalInfo.hintDenseWeight,
//             hint_sparse_weight: 1 - additionalInfo.hintDenseWeight,
//           },
//         }
//         set(conversationIdAtom, conversationId)
//         if (isMultiAgentMode) {
//           if (scenesList.some((v) => v.enableFollowUpQuestion)) {
//             const metricConfigRecord = get(metricConfigRecordAtom)
//             requestData.messages = toMessages({
//               chats,
//               newMessage,
//               metricConfigRecord,
//               parentId: currentFollowUpQuestion?.id,
//             })
//           } else {
//             requestData.messages = [
//               {
//                 role: 'user',
//                 content: newMessage,
//               },
//             ]
//           }
//         }
//         set(chatRequestPropsAtom, requestData)

//         const response = chatResponse
//           ? { data: chatResponse }
//           : await axios.post(isMultiAgentMode ? askBIApiUrls.agent.chat : askBIApiUrls.chat, requestData, {
//               signal: cancelTokenSource.signal,
//               headers: { traceId },
//             })
//         rspData = response.data
//       }

//       const data = rspData as ChatResponse
//       set(isSubmittingAtom, false)
//       intervalId && clearInterval(intervalId)
//       intervalId = null
//       if (isMultiAgentMode) {
//         console.info('After Multi Agent Make Agent Single Scene 暂时不做操作')
//         const chat = get(chatsAtom).find((item) => item.id === chatId)
//         const multiAgentData = getMultiAgentData(chat)
//         const code = Number(rspData?.data?.code)
//         // TODO: 只有敏感词，这里很丑，而且不终止chatProgress，但展示正常，没有时间改了。同多场景
//         if (!Number.isNaN(code) && code === -19) {
//           const id = upsertResp?.data?.data?.id
//           if (id) {
//             await axios.delete(askBIApiUrls.convers.delete(id))
//           }
//           set(
//             chatsAtom,
//             produce((draft: Chat[]) => {
//               const theChat = draft.find((item) => item.id === chatId)
//               if (!theChat) {
//                 return
//               }
//               theChat.ans = [
//                 {
//                   role: 'assistant',
//                   content: [
//                     {
//                       type: 'chat-error',
//                       unreadyReason: rspData?.data?.data?.unreadyReason,
//                       errType: 'SENSITIVE_QUESTION',
//                       tryQueryToSqlData: new TryQueryToSqlData(),
//                     },
//                   ],
//                   sceneId,
//                   status: 'failure',
//                 },
//               ]
//             }),
//           )
//         } else if (
//           // 假设触发了置
//           code === 0 &&
//           rspData?.data?.data?.data?.[0]?.name === 'manual_selects' &&
//           rspData?.data?.data?.data?.[0]?.manual_selects
//         ) {
//           multiAgentData?.emit('onNeedMatch', rspData?.data?.data?.data?.[0]?.manual_selects)
//         }
//       } else {
//         if (data.ready) {
//           // 此时图表渲染完成，开始获取数据解读，需要触发一次滚动
//           setTimeout(scrollToBottom, 100)
//           // const traceId = data.traceId || ''
//           // 数据解读
//           // set(sendChartInsightAtom, { respData: data, chatId, sceneId, llmType, traceId })
//         } else {
//           set(conversationIdAtom, data.conversationId)
//           const unreadyChatAns = unreadyResponseToChatAns(data as ChatResponseError, sceneId)
//           console.error('unreadyChatAns失败：', unreadyChatAns.content[0])
//           setTimeout(scrollToBottom, 100)
//         }
//       }
//     }

//     // 根据场景id生成报告
//     // async function sceneToReport(
//     //   msg: string,
//     //   jsonContent: string,
//     //   conversationId: string,
//     //   chatId: string,
//     //   sceneId: string,
//     //   llmType: LlmType | null | undefined,
//     //   cancelTokenSource: AbortController,
//     //   isReportModelNameTag: string,
//     // ) {
//     //   const requestData = {
//     //     conversationId,
//     //     chatId,
//     //     llmType,
//     //     fileId: sceneId,
//     //     useScene: true,
//     //     msg,
//     //     isReportModelNameTag,
//     //     docReportParams,
//     //   }

//     //   const response = await axios.post(askBIApiUrls.docReport, requestData, {
//     //     timeout: DOC_REPORT_TIMEOUT,
//     //     signal: cancelTokenSource.signal,
//     //   })
//     //   const rspData = response.data

//     //   const data = rspData as ChatResponse

//     //   set(isSubmittingAtom, false)
//     //   intervalId && clearInterval(intervalId)
//     //   intervalId = null
//     //   if (data.ready) {
//     //     set(isSubmittingAtom, false)
//     //     set(conversationIdAtom, data.conversationId)
//     //     const readyChatAns = readyResponseToChatAns(rspData.data, sceneId)
//     //     if (reportModelNameTag) {
//     //       set(lastSuccessModelNameAtom, reportModelNameTag)
//     //       set(
//     //         chatsAtom,
//     //         produce((draft: Chat[]) => {
//     //           const theChat = draft.find((item) => item.id === chatId)
//     //           if (!theChat) {
//     //             return
//     //           }
//     //           theChat.ans = [{ ...readyChatAns }]
//     //         }),
//     //       )
//     //       set(isReportModelNameTagAtom, null)
//     //       setTimeout(scrollToBottom, 100)
//     //       return
//     //     }
//     //     // 报告生成成功时的操作 - 更新当前chat状态 以及 新chat显示报告
//     //     if (docReportParams) {
//     //       set(
//     //         chatsAtom,
//     //         produce((draft: Chat[]) => {
//     //           const theChat = draft.find((item) => item.id === docReportParams.chatId)
//     //           if (!theChat) {
//     //             return
//     //           }
//     //           theChat.ans = [
//     //             {
//     //               role: 'assistant' as const,
//     //               content: [
//     //                 {
//     //                   type: 'doc-report',
//     //                   status: AnalyzeReportTypes.reportGeneratedSuccess,
//     //                   rows: docReportParams,
//     //                 },
//     //               ],

//     //               sceneId: sceneId,
//     //               status: ChatStatus.success,
//     //             },
//     //           ]
//     //         }),
//     //       )
//     //       set(chatsAtom, (prevChats) => [
//     //         ...prevChats,
//     //         {
//     //           id: chatId,
//     //           askTime: new Date(),
//     //           ask: {
//     //             role: 'user',
//     //             content: msg,
//     //             jsonContent,
//     //             parentId: currentFollowUpQuestion ? currentFollowUpQuestion.id : null,
//     //           },
//     //           ans: [
//     //             {
//     //               role: 'assistant' as const,
//     //               content: readyChatAns.content,
//     //               sceneId: sceneId,
//     //               status: ChatStatus.pending,
//     //             },
//     //           ],
//     //           selectedSceneId: sceneId,
//     //           docAns: {} as any,
//     //         },
//     //       ])
//     //       set(docReportParamsAtom, null)
//     //       setTimeout(scrollToBottom, 100)
//     //       return
//     //     }

//     //     setTimeout(scrollToBottom, 100)
//     //   } else {
//     //     set(conversationIdAtom, data.conversationId)
//     //     const unreadyChatAns = unreadyResponseToChatAns(data, sceneId)
//     //     if (docReportParams) {
//     //       set(docReportParamsAtom, null)
//     //       const lastSuccessModelName = get(lastSuccessModelNameAtom)
//     //       set(
//     //         chatsAtom,
//     //         produce((draft: Chat[]) => {
//     //           const theChat = draft.find((item) => item.id === docReportParams.chatId)
//     //           if (!theChat) {
//     //             return
//     //           }
//     //           theChat.ans = [
//     //             {
//     //               role: 'assistant' as const,
//     //               content: [
//     //                 {
//     //                   type: 'doc-report',
//     //                   status: AnalyzeReportTypes.reportGeneratedFailed,
//     //                   rows: { errorReason: unreadyChatAns.content[0], fileId: lastSuccessModelName },
//     //                 },
//     //               ],
//     //               sceneId,
//     //               status: ChatStatus.success,
//     //             },
//     //           ]
//     //         }),
//     //       )
//     //       return
//     //     }

//     //     set(
//     //       chatsAtom,
//     //       produce((draft: Chat[]) => {
//     //         const theChat = draft.find((item) => item.id === chatId)
//     //         if (!theChat) {
//     //           return
//     //         }
//     //         theChat.ans = [unreadyChatAns]
//     //       }),
//     //     )
//     //   }
//     // }

//     // agent 融合 doc

//     /** 处理错误情况下的 chat 更新状态 */
//     function handleErrorStatusChat(chatIdToUpdate: string, status: ChatStatusType, message: string) {
//       set(
//         chatsAtom,
//         produce((draft) => {
//           const theChat = draft.find((item) => item.id === chatIdToUpdate)
//           if (!theChat) {
//             return
//           }
//           theChat.ans = [
//             {
//               role: 'assistant' as const,
//               content: [{ type: 'text', text: message } as const],
//               sceneId: theChat.ans[0]?.sceneId,
//               status: status,
//               ansTime: new Date(),
//             },
//           ]
//           theChat.docAns = {
//             role: 'assistant',
//             content: [{ type: 'text', text: message } as const],
//             status: status,
//             ansTime: new Date(),
//           }
//         }),
//       )

//       handleAbortAll()
//     }

//     const promiseArray = []
//     // // 属于DOC场景 & 多场景问答
//     // if (isDocAgent && isProjectChosen) {
//     //   set(isSubmittingAtom, false)
//     //   set(
//     //     chatsAtom,
//     //     produce((draft: Chat[]) => {
//     //       const theChat = draft.find((item) => item.id === chatId)
//     //       if (!theChat) {
//     //         return
//     //       }
//     //       theChat.docAns = {
//     //         role: 'assistant',
//     //         status: ChatStatus.failure,
//     //         ansTime: new Date(),
//     //         content: [{ type: 'text', text: '回答失败，请选择特定场景或场景下单个文档进行知识问答！' }],
//     //       }
//     //     }),
//     //   )
//     //   return
//     // }

//     // // 属于DOC场景 & 单场景问答
//     // if (isDocAgent && !isProjectChosen) {
//     //   // promiseArray.push(
//     //   //   handleDocChat(msg, currentConversationId, chatId, llmType, currentDataset.sceneId, cancelTokenSource),
//     //   // )
//     //   //  set(
//     //   //   chatsAtom,
//     //   //   produce((draft: Chat[]) => {
//     //   //     const theChat = draft.find((item) => item.id === chatId)
//     //   //     if (!theChat) {
//     //   //       return
//     //   //     }
//     //   //     theChat.docAns = {
//     //   //       role: 'assistant',
//     //   //       status: ChatStatus.pending,
//     //   //       ansTime: new Date(),
//     //   //       content: [
//     //   //         {
//     //   //           type: 'doc-result',
//     //   //           text: '正在为您查询这个问题...',
//     //   //           payload: {
//     //   //             msg,
//     //   //             conversationId: currentConversationId,
//     //   //             chatId,
//     //   //             llmType,
//     //   //           },
//     //   //         },
//     //   //       ],
//     //   //     }
//     //   //   }),
//     //   // )
//     // }

//     // // 多场景问答 & 不属于追问
//     // if (isProjectChosen && currentFollowUpQuestion == null) {
//     //   promiseArray.push(
//     //     multiScenesAskBIChat(
//     //       msg,
//     //       currentConversationId,
//     //       chatId,
//     //       currentDataset.projectId,
//     //       llmType,
//     //       cancelTokenSource,
//     //       currentParamsExtractApi,
//     //     ),
//     //   )
//     // }

//     promiseArray.push(
//       singleSceneAskBIChat(
//         msg,
//         currentConversationId,
//         chatId,
//         currentDataset.sceneId,
//         currentDataset.projectId,
//         llmType,
//         currentFollowUpQuestion,
//         cancelTokenSource,
//         currentParamsExtractApi,
//       ),
//     )

//     // 属于BI场景
//     // if (isBIAgent) {
//     //   if (isReportModelNameTag || docReportParams) {
//     //     promiseArray.push(
//     //       sceneToReport(
//     //         msg,
//     //         jsonContent,
//     //         currentConversationId,
//     //         chatId,
//     //         currentDataset.sceneId,
//     //         llmType,
//     //         cancelTokenSource,
//     //         isReportModelNameTag || '',
//     //       ),
//     //     )
//     //   } else {
//     //     const singleSceneModelId = currentFollowUpQuestion ? (currentFollowUpSceneId as string) : currentDataset.sceneId
//     //     promiseArray.push(
//     //       singleSceneAskBIChat(
//     //         msg,
//     //         currentConversationId,
//     //         chatId,
//     //         singleSceneModelId,
//     //         currentDataset.projectId,
//     //         llmType,
//     //         currentFollowUpQuestion,
//     //         cancelTokenSource,
//     //         currentParamsExtractApi,
//     //       ),
//     //     )
//     //   }
//     // }
//     Promise.all(promiseArray)
//       .then(() => {
//         console.info('All requests have been done.')
//       })
//       .catch((error) => {
//         console.error('请求中出现错误', error)
//         intervalId && clearInterval(intervalId)
//         intervalId = null
//         set(isSubmittingAtom, false)
//         if (axios.isCancel(error)) {
//           console.info('The request was cancelled manually.', error.message)
//           set(cancelTokenSourceAtom, new AbortController())
//           if (error.message !== ABORT_IGNORED) {
//             handleAbortAll()
//             // handleErrorStatusChat(chatId, ChatStatus.failure, (error as AxiosError).message)
//           } else {
//             handleErrorStatusChat(chatId, ChatStatus.failure, '本次问答已终止。')
//           }
//         } else {
//           handleErrorStatusChat(chatId, ChatStatus.failure, '出错了，请联系管理员。' + (error as AxiosError).message)
//         }
//       })
//   },
// )

/**
 * 格式化解析query后的数据存储，结合getQueryState，后面对query的处理都通过它们转成js对象后再处理
 */
export const queryStateAtom = atomWithStorage<QueryState>('queryState', {
  enableOnlyChat: false,
  enableAutoLogin: false,
  enableReloadQueryState: false,
  enableLangfuse: false,
})

// url上是否携带了问卷调查的参数
export const questionnaireAtom = atom<boolean>(false)
/**
 * 是否展示Lingjing数字人
 */
export const showDigitalHumanAtom = atom<boolean>(false)

export const confidenceDrawerConfigAtom = atom<{ open: boolean; chatId: null | string; planData?: PlanData }>({
  open: false,
  chatId: null,
  planData: undefined,
})

export const deepSeekEnableNetWorkAtom = atomWithStorage<boolean>('deepSeekEnableNetWork', false)

// 业务术语动态值 from zhouxiaorong
export const additionalInfoAtom = atom({
  hintDynamicLimit: 8,
  hintDenseWeight: 0.5,
  // 这个值不用设置，只需要 1 - hintDenseWeight来拿到就行
  // hint_sparse_weight: 0.5
})

export const lastTenantSelectionAtom = atomWithStorage<{
  catalogName: string
  project: string
  tenant: string
} | null>('lastTenantSelection', null)

export const isOpenTenantAtom = atom(false)

export const metricConfigRecordAtom = atom((get) => {
  const isProjectChosen = get(isProjectChosenAtom)
  const currentDataset = get(currentDatasetAtom)
  const metricConfig = get(metricConfigAtom)
  const metricConfigOfProject = get(metricConfigOfProjectAtom)
  const res: Record<string, MetricConfigResponse> = {}
  if (isProjectChosen && metricConfigOfProject) {
    for (const item of metricConfigOfProject) {
      res[item.sceneId] = item.data
    }
  } else if (!isProjectChosen && currentDataset?.sceneId && metricConfig) {
    res[currentDataset.sceneId] = metricConfig
  }
  return res
})

export const scenesListAtom = atom((get) => {
  const currentDataset = get(currentDatasetAtom)
  const isProjectChosen = get(isProjectChosenAtom)
  const semanticProjectInfo = get(semanticProjectInfoAtom)
  const scenesList = semanticProjectInfo.find((p) => p.id === currentDataset?.projectId)?.scenes ?? []
  if (isProjectChosen) {
    return scenesList
  } else {
    return scenesList?.filter((s) => s.id === currentDataset?.sceneId) ?? []
  }
})

export const sceneRecordAtom = atom((get) =>
  get(scenesListAtom).reduce(
    (o, scene) => {
      o[scene.id] = scene
      return o
    },
    {} as Record<string, SceneType>,
  ),
)

export const sceneInfoAtom = atom((get) => {
  const list = get(scenesListAtom)
  let isDocAgent = false
  let isBiAgent = false
  for (const scene of list) {
    isDocAgent ||= !!scene.agent?.includes('Doc')
    isBiAgent ||= !!scene.agent?.includes('BI')
  }
  return {
    isDocAgent,
    isBiAgent,
  }
})
