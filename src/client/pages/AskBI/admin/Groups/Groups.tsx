import React from 'react'
import axios from 'axios'
import dayjs from 'dayjs'
import { useRequest } from 'ahooks'
import { App, Button, Form, Input, Popconfirm, Table } from 'antd'
import { ColumnsType } from 'antd/es/table'
import { UserAddOutlined } from '@ant-design/icons'
import { XGroup } from '@prisma/client'
import { askBIApiUrls } from 'src/shared/url-map'
import AdminCard from 'src/client/components/AdminCard'
import AdminPage from 'src/client/components/AdminPage'
import { useCreateEdit } from 'src/client/hooks/useCreateEdit'

export default function Groups() {
  const { message } = App.useApp()

  const { data, refresh, loading } = useRequest(async () => {
    const res = await axios.get(askBIApiUrls.auth.admin.group.list)
    return res.data.data
  })

  const { node: CreateEditNode, open } = useCreateEdit<XGroup>({
    refresh,
    label: '用户组',
    useFormItems: () => {
      return (
        <>
          <Form.Item name="groupName" label="用户组名" rules={[{ required: true }]}>
            <Input />
          </Form.Item>
        </>
      )
    },
    handleCreate: async (data) => {
      const res = await axios.post(askBIApiUrls.auth.admin.group.rest, {
        groupName: data.groupName,
      })
      return res.data
    },
    handleEdit: async (data, payload) => {
      const body: Record<string, any> = {
        id: payload.id,
        groupName: data.groupName,
      }
      const res = await axios.post(askBIApiUrls.auth.admin.group.update, body)
      return res.data
    },
  })
  const onDeleteGroup = (row: XGroup) =>
    axios
      .delete(askBIApiUrls.auth.admin.group.rest, { data: { id: row.id } })
      .then((res) => {
        if (res.data.code === 0) {
          message.success('删除用户组成功')
          return refresh()
        } else {
          throw res.data.msg
        }
      })
      .catch((err) => {
        message.error('删除用户组失败：' + (err?.toString() ?? ''))
      })

  const columns: ColumnsType<XGroup> = [
    {
      title: '用户组ID',
      dataIndex: 'id',
      width: 80,
    },
    {
      title: '用户组名',
      dataIndex: 'groupName',
      render: (text: string) => <span className="font-bold">{text}</span>,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      render: (value: string) => <span>{dayjs(value).format('YYYY-MM-DD HH:mm:ss')}</span>,
    },
    {
      title: '操作',
      dataIndex: 'operations',
      render: (_, row) => (
        <div className="flex flex-row">
          <Button type="link" onClick={() => open({ mode: 'edit', data: row })}>
            编辑
          </Button>
          <Popconfirm
            title="删除用户组"
            description={`确认删除当前${row.groupName}用户组吗？`}
            onConfirm={() => onDeleteGroup(row)}
            okText="确认"
            cancelText="取消"
          >
            <Button type="link" danger>
              删除
            </Button>
          </Popconfirm>
        </div>
      ),
    },
  ]

  return (
    <AdminPage title="权限管理">
      <AdminCard
        title="用户组列表"
        actions={
          <Button type="primary" icon={<UserAddOutlined />} size="middle" onClick={() => open({ mode: 'create' })}>
            添加用户组
          </Button>
        }
      >
        <Table rowKey="id" columns={columns} dataSource={data?.list} loading={loading} scroll={{ x: true }} />
      </AdminCard>
      {CreateEditNode}
    </AdminPage>
  )
}
