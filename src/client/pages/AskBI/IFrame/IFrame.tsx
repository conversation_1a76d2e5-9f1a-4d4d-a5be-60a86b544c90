import { useAtom } from 'jotai'
import React, { useRef, useEffect, Suspense } from 'react'
import { useLocation } from 'react-router-dom'
import { Spin } from 'antd'
import { envAtom } from '../askBIAtoms'

const IFrame = (props: { hostName?: string; url: string; height: string }) => {
  const iframeRef = useRef<HTMLIFrameElement | null>(null)
  const location = useLocation()
  const [env] = useAtom(envAtom)
  useEffect(() => {
    const handleReceiveMessage = (event: { origin: string; data: any }) => {
      console.info('Message from child:', event.data)
    }

    window.addEventListener('message', handleReceiveMessage)

    // Cleanup listener on component unmount
    return () => {
      window.removeEventListener('message', handleReceiveMessage)
    }
  }, [])
  const host = props.hostName ? env?.[props.hostName] : env?.['VITE_XENGINE_ORIGIN']
  const iframeUrl = `${host}${props.url}${location.search || ''}`

  return (
    <div className="relative h-full">
      <Suspense fallback={<Spin size="large" className="absolute inset-0 flex items-center justify-center" />}>
        <iframe
          allow="*"
          className="absolute inset-0 h-full w-full border-none"
          ref={iframeRef}
          src={iframeUrl}
          style={{ width: '100%', height: props.height || '100vh' }}
          title="Child Frame"
        />
      </Suspense>
    </div>
  )
}

export default IFrame
