import React, { useState, useEffect } from 'react'
import { Button, Table, App, Modal, Result, Dropdown, Form, Input, Select, Typography, message } from 'antd'
import { ColumnsType } from 'antd/es/table'
import axios from 'axios'
import { useRequest } from 'ahooks'
import Search from 'antd/es/input/Search'
import { MoreOutlined, DeleteOutlined } from '@ant-design/icons'
import { mapValues, omitBy, debounce } from 'lodash-es'
import { ResponseErrorType, TableStructureResponse } from 'src/shared/metric-types'
import { askBIApiUrls } from 'src/shared/url-map'
import { APIResponse } from 'src/shared/common-types'
import TextHighlight from 'src/client/components/TextHighlight'
import AdminPage from 'src/client/components/AdminPage'
import GreyFooterModal from 'src/client/components/GreyFooterModel'
import type { ModificationVO } from 'src/shared/xengine-types'
import LoadingText from 'src/client/components/LoadingText'
import ETLPaintDrawer from './ETLPaint/ETLPaintDrawer'

type IDataSceneListItem = {
  description?: string
  modification: ModificationVO
  projectName: string
  sceneId: string
  sceneName: string
  sceneType: string
}

type IGetSceneListParams = {
  current?: number
  name?: string
  pageSize?: number
  projectName?: string
  type?: string
  [property: string]: any
}

type DataSceneModelOperationInfoType = {
  open: boolean
  isEdit: boolean
  currentSceneId?: string
}

const manageKeyTextMap = {
  tag: '标签',
  project: '文件夹',
}

const DataScene = () => {
  const { message: antdMessage } = App.useApp()
  const [etlForm] = Form.useForm()
  const [createProjectForm] = Form.useForm()
  const [createTypeForm] = Form.useForm()
  const [openDeleteScenarioModal, setOpenDeleteScenarioModal] = useState(false)
  const [deletingScenario, _setDeletingScenario] = useState<IDataSceneListItem>()
  const [searchValue, setSearchValue] = useState({
    projectName: '',
    type: '',
    name: '',
  })

  const [scenarioListTotal, setScenarioListTotal] = useState(0)

  const [paginationInfo, setPaginationInfo] = useState({
    page: 1,
    pageSize: 10,
  })

  const [dataSceneModelOperationInfo, setDataSceneModelOperationInfo] = useState<DataSceneModelOperationInfoType>({
    open: false,
    isEdit: false,
  })

  const [etlPaintDrawerProps, setEtlPaintDrawerProps] = useState({
    title: 'ETL画布',
    open: false,
    sceneId: '',
  })

  const [height, setHeight] = useState(window.innerHeight)
  const [manageModalInfo, setManageModalInfo] = useState({
    open: false,
    mode: 'project' as keyof typeof manageKeyTextMap,
  })

  const [createDataSceneProjectModalOpen, setCreateDataSceneProjectModalOpen] = useState(false)
  const [createDataSceneTypeModalOpen, setCreateDataSceneTypeModalOpen] = useState(false)
  const {
    run: listProjects,
    data: dataSceneProjects,
    loading: listProjectsLoading,
  } = useRequest(() =>
    axios
      .get<APIResponse<{ id: string; name: string }[]>>(askBIApiUrls.xengine.dataScene.listProjects)
      .then((res) => res.data?.data),
  )
  const {
    run: listTypes,
    data: dataSceneTypes,
    loading: listTypesLoading,
  } = useRequest(() =>
    axios
      .get<APIResponse<{ id: string; name: string }[]>>(askBIApiUrls.xengine.dataScene.listTypes)
      .then((res) => res.data?.data),
  )
  const {
    run: getDataScenario,
    refresh: refreshDataScenario,
    data: scenarioConfig,
    loading: isScenarioListLoading,
    error: scenarioListError,
  } = useRequest(
    async ({ name, projectName, type, pageSize = 10, page = 1 }: IGetSceneListParams = {}) => {
      const requestParams: IGetSceneListParams = { current: page, pageSize, name, projectName, type }
      const response = await axios.get<APIResponse<TableStructureResponse<IDataSceneListItem>>>(
        askBIApiUrls.xengine.dataScene.dataSceneList,
        {
          params: omitBy(requestParams, (v) => v === undefined || v === '' || v === null),
        },
      )
      setScenarioListTotal(response.data?.data?.total || 0)
      setPaginationInfo({ pageSize, page })
      if (!response.data.data) {
        return []
      }
      return response.data.data.list
    },
    {
      onError: (error) => {
        antdMessage.error('获取画布信息失败')
        console.error('Load data scenario list with error', error)
      },
    },
  )

  const { run: handleDelete, loading: isDeleteLoading } = useRequest(
    // return axios.delete(askBIApiUrls.auth.scene.rest, { data: { id } })
    //  TODO 确认路径
    (id) => axios.delete(`${askBIApiUrls.xengine.dataScene.delete}?sceneId=${id}`),
    {
      manual: true,
      onSuccess: () => {
        antdMessage.success('删除成功')
        setOpenDeleteScenarioModal(false)
        refreshDataScenario()
      },
      onError: (error: ResponseErrorType) => {
        antdMessage.error('删除画布失败：' + error.msg || error.error || '未知原因')
        console.error('deleteDataScenario error =', error)
      },
    },
  )

  const { run: operateDataScene, loading: operateDataSceneLoading } = useRequest(
    (data) =>
      dataSceneModelOperationInfo.isEdit
        ? axios.put(askBIApiUrls.xengine.dataScene.update, {
            ...data,
            sceneId: dataSceneModelOperationInfo.currentSceneId,
          })
        : axios.post(askBIApiUrls.xengine.dataScene.create, data),
    {
      manual: true,
      onSuccess() {
        antdMessage.success(`${dataSceneModelOperationInfo.isEdit ? '编辑' : '创建'}成功`)
        setDataSceneModelOperationInfo({
          open: false,
          isEdit: false,
        })
        refreshDataScenario()
      },
      onError(err) {
        antdMessage.error(err.message)
      },
    },
  )

  const { run: createDataSceneProject, loading: createDataSceneProjectLoading } = useRequest(
    (data) =>
      axios.post(
        askBIApiUrls.xengine.dataScene.createProject,
        mapValues(data, (v) => v?.trim() || ''),
      ),
    {
      manual: true,
      onSuccess() {
        antdMessage.success('创建成功')
        createProjectForm.resetFields()
        setCreateDataSceneProjectModalOpen(false)
        listProjects()
      },
      onError(err) {
        antdMessage.error(err.message || '创建失败')
      },
    },
  )

  const { run: createDataSceneType, loading: createDataSceneTypeLoading } = useRequest(
    (data) =>
      axios.post(
        askBIApiUrls.xengine.dataScene.createType,
        mapValues(data, (v) => v?.trim() || ''),
      ),
    {
      manual: true,
      onSuccess() {
        antdMessage.success('创建成功')
        createTypeForm.resetFields()
        setCreateDataSceneTypeModalOpen(false)
        listTypes()
      },
      onError(err) {
        antdMessage.error(err.message || '创建失败')
      },
    },
  )

  const scenarioColumn: ColumnsType<IDataSceneListItem> = [
    {
      title: '画布名称',
      dataIndex: 'sceneName',
      width: 240,
      render(value, record) {
        return (
          <Typography.Link
            onClick={() =>
              setEtlPaintDrawerProps({
                open: true,
                title: record.sceneName,
                sceneId: record.sceneId,
              })
            }
          >
            <TextHighlight text={value} highlight={searchValue.name} />
          </Typography.Link>
        )
      },
    },
    {
      title: '文件夹',
      dataIndex: 'projectName',
      width: 220,
    },
    {
      title: '标签',
      dataIndex: 'sceneType',
      width: 180,
    },
    { title: '描述', dataIndex: 'description', ellipsis: true },
    {
      title: '操作',
      width: 80,
      render(record: IDataSceneListItem) {
        return (
          <Dropdown
            overlayClassName="text-center w-[100px]"
            menu={{
              items: [
                {
                  key: 'edit',
                  label: (
                    <Typography.Link
                      onClick={() => {
                        setDataSceneModelOperationInfo({
                          open: true,
                          isEdit: true,
                          currentSceneId: record.sceneId,
                        })
                        etlForm.setFieldsValue(record)
                      }}
                    >
                      编辑
                    </Typography.Link>
                  ),
                },
                {
                  key: 'delete',
                  label: (
                    <Typography.Text
                      type="danger"
                      onClick={() => {
                        setOpenDeleteScenarioModal(true)
                        _setDeletingScenario(record)
                      }}
                    >
                      删除
                    </Typography.Text>
                  ),
                },
              ],
            }}
            placement="bottomRight"
            arrow={{ pointAtCenter: true }}
          >
            <MoreOutlined className="cursor-pointer" />
          </Dropdown>
        )
      },
    },
  ]

  const handleSearch = (value: string, type: 'projectName' | 'type' | 'name') => {
    setSearchValue((pre) => {
      return { ...pre, [type]: value }
    })
    getDataScenario({ ...(searchValue || {}), [type]: value })
  }

  useEffect(() => {
    const handleResize = debounce(() => {
      setHeight(window.innerHeight)
    }, 800)

    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])

  const handleOpenManageModal = (mode: keyof typeof manageKeyTextMap) => {
    setManageModalInfo({
      open: true,
      mode,
    })
  }

  return (
    <AdminPage
      title="ETL画布"
      extra={
        <Button
          type="primary"
          onClick={() => {
            etlForm.resetFields()
            setDataSceneModelOperationInfo({
              open: true,
              isEdit: false,
            })
          }}
        >
          创建ETL画布
        </Button>
      }
    >
      <div>
        <div className="flex justify-between pb-3 pt-2">
          <div>{`共${scenarioListTotal || 0}个`}</div>
          <div className="flex gap-3">
            <Select
              placeholder="搜索文件夹名称"
              allowClear
              onChange={(val) => handleSearch(val, 'projectName')}
              loading={listProjectsLoading}
              fieldNames={{
                value: 'name',
                label: 'name',
              }}
              showSearch
              options={dataSceneProjects}
              className="w-[180px]"
            />

            <Select
              placeholder="搜索标签"
              allowClear
              fieldNames={{
                value: 'name',
                label: 'name',
              }}
              loading={listTypesLoading}
              options={dataSceneTypes}
              showSearch
              onChange={(val) => handleSearch(val, 'type')}
              className="w-[180px]"
            />
            <Search
              placeholder="搜索画布名称"
              allowClear
              onSearch={(val) => handleSearch(val, 'name')}
              className="w-[240px]"
            />
          </div>
        </div>

        {scenarioListError != null ? (
          <Result status="error" title="获取画布列表失败" subTitle="请稍后再试" />
        ) : (
          <Table
            columns={scenarioColumn}
            loading={isScenarioListLoading}
            rowKey="sceneId"
            dataSource={scenarioConfig}
            pagination={{
              ...paginationInfo,
              current: paginationInfo.page,
              pageSize: paginationInfo.pageSize,
              total: scenarioListTotal,
              showSizeChanger: true,
              onChange(page, pageSize) {
                getDataScenario({ pageSize, page, ...(searchValue || {}) })
              },
            }}
          />
        )}
      </div>
      <Modal
        destroyOnClose
        title={`确认删除${deletingScenario?.sceneName}`}
        open={openDeleteScenarioModal}
        onOk={() => handleDelete(deletingScenario?.sceneId)}
        okButtonProps={{ loading: isDeleteLoading }}
        onCancel={() => setOpenDeleteScenarioModal(false)}
      >
        该ETL画布将被删除，请确认
      </Modal>
      <ETLPaintDrawer
        {...etlPaintDrawerProps}
        closeController={() =>
          setEtlPaintDrawerProps({
            open: false,
            sceneId: '',
            title: '',
          })
        }
      />
      <GreyFooterModal
        title={`${dataSceneModelOperationInfo.isEdit ? '编辑' : '创建'}ETL画布`}
        open={dataSceneModelOperationInfo.open}
        styles={{
          body: { padding: '0 24px' },
        }}
        onCancel={() => {
          setDataSceneModelOperationInfo((pre) => ({
            open: false,
            isEdit: pre.isEdit,
          }))
        }}
        onOk={async () => operateDataScene(await etlForm.validateFields())}
        okButtonProps={{
          loading: operateDataSceneLoading,
        }}
      >
        <Form form={etlForm} layout="vertical">
          <Form.Item label="画布名称" rules={[{ required: true }]} required name="sceneName">
            <Input placeholder="请输入" />
          </Form.Item>
          <Form.Item
            className="special-label-width-full"
            name="projectName"
            label={
              <div className="flex w-full justify-between">
                <span>所属文件夹</span>
                <div>
                  <Typography.Link onClick={() => setCreateDataSceneProjectModalOpen(true)}>创建文件夹</Typography.Link>
                  <Typography.Link className="ml-2" onClick={() => handleOpenManageModal('project')}>
                    管理文件夹
                  </Typography.Link>
                </div>
              </div>
            }
            rules={[{ required: true, message: '请选择' }]}
          >
            <Select
              placeholder="请选择"
              loading={listProjectsLoading}
              fieldNames={{
                value: 'name',
                label: 'name',
              }}
              options={dataSceneProjects}
            />
          </Form.Item>
          <Form.Item
            className="special-label-width-full"
            label={
              <div className="flex w-full justify-between">
                <span>标签</span>
                <div>
                  <Typography.Link onClick={() => setCreateDataSceneTypeModalOpen(true)}>创建标签</Typography.Link>
                  <Typography.Link className="ml-2" onClick={() => handleOpenManageModal('tag')}>
                    管理标签
                  </Typography.Link>
                </div>
              </div>
            }
            rules={[{ required: true, message: '请选择' }]}
            name="sceneType"
          >
            <Select
              placeholder="请选择"
              loading={listTypesLoading}
              fieldNames={{
                value: 'name',
                label: 'name',
              }}
              options={dataSceneTypes}
            />
          </Form.Item>
          <Form.Item label="描述" name="description">
            <Input.TextArea placeholder="请输入" />
          </Form.Item>
        </Form>
      </GreyFooterModal>

      <Modal
        title="创建ETL画布文件夹"
        open={createDataSceneProjectModalOpen}
        okButtonProps={{
          loading: createDataSceneProjectLoading,
        }}
        onCancel={() => {
          createProjectForm.resetFields()
          setCreateDataSceneProjectModalOpen(false)
        }}
        onOk={async () => {
          createDataSceneProject(await createProjectForm.validateFields())
        }}
        zIndex={1001}
      >
        <Form form={createProjectForm}>
          <Form.Item label="文件夹名称" name="projectName" rules={[{ required: true }]}>
            <Input placeholder="请输入" />
          </Form.Item>
        </Form>
      </Modal>

      <Modal
        title="创建ETL画布标签"
        open={createDataSceneTypeModalOpen}
        okButtonProps={{
          loading: createDataSceneTypeLoading,
        }}
        onCancel={() => {
          createTypeForm.resetFields()
          setCreateDataSceneTypeModalOpen(false)
        }}
        onOk={async () => createDataSceneType(await createTypeForm.validateFields())}
        zIndex={1001}
      >
        <Form form={createTypeForm}>
          <Form.Item label="标签名称" rules={[{ required: true }]} name="typeName">
            <Input placeholder="请输入" />
          </Form.Item>
        </Form>
      </Modal>

      <Modal
        width={740}
        open={manageModalInfo.open}
        title={`${manageKeyTextMap[manageModalInfo.mode]}管理`}
        onCancel={() => setManageModalInfo((pre) => ({ open: false, mode: pre.mode }))}
      >
        <div className="mb-1 mr-1 text-right">
          <Typography.Link
            onClick={() =>
              manageModalInfo.mode === 'tag'
                ? setCreateDataSceneTypeModalOpen(true)
                : setCreateDataSceneProjectModalOpen(true)
            }
          >
            创建{manageKeyTextMap[manageModalInfo.mode]}
          </Typography.Link>
        </div>
        <Table
          loading={manageModalInfo.mode === 'tag' ? listTypesLoading : listProjectsLoading}
          scroll={{ y: height > 700 ? height - 400 : 'auto' }}
          columns={[
            {
              title: manageKeyTextMap[manageModalInfo.mode],
              dataIndex: 'name',
            },
            {
              title: '操作',
              width: 80,
              render(record) {
                return (
                  <LoadingText
                    api={() => {
                      const url =
                        manageModalInfo.mode === 'tag'
                          ? askBIApiUrls.xengine.dataScene.deleteType
                          : askBIApiUrls.xengine.dataScene.deleteProject
                      const params = manageModalInfo.mode === 'tag' ? { typeId: record.id } : { projectId: record.id }
                      return axios.delete(url, { params })
                    }}
                    type="danger"
                    popconfirmProps={{
                      title: '确认删除？',
                    }}
                    onSuccess={() => {
                      manageModalInfo.mode === 'tag' ? listTypes() : listProjects()
                    }}
                    onFail={(err) => message.error(err.message || '删除失败')}
                  >
                    <DeleteOutlined className="cursor-pointer text-red-600" />
                  </LoadingText>
                )
              },
            },
          ]}
          dataSource={manageModalInfo.mode === 'project' ? dataSceneProjects : dataSceneTypes}
        />
      </Modal>
    </AdminPage>
  )
}

export default DataScene
