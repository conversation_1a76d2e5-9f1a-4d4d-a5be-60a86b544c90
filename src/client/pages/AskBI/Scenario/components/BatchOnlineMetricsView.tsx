import React from 'react'
import { Drawer, Table, type TableProps, Typography, Tooltip } from 'antd'
import dayjs from 'dayjs'
import { useRequest } from 'ahooks'
import axios from 'axios'
import { askBIApiUrls } from 'src/shared/url-map'
import { MetricStatusText } from './conf'
export default function BatchOnlineMetricsView({
  open,
  onClose,
  baseInfo,
}: {
  open: boolean
  onClose: () => void
  baseInfo: {
    semanticProjectId: string
    semanticSceneId: string
    modelName: string
  }
}) {
  const { loading, data, run } = useRequest(
    () =>
      axios
        .get(askBIApiUrls.metrics.list, {
          params: {
            batchMode: true,
            ...(baseInfo || {}),
          },
        })
        .then((res) => res.data?.data?.list),
    {
      ready: open,
    },
  )
  return (
    <Drawer open={open} title="批量上线管理" onClose={() => onClose?.()} height="calc(100vh - 20px)" placement="bottom">
      <Table
        loading={loading}
        rowKey="id"
        dataSource={data}
        columns={
          [
            {
              title: '批量指标名称',
              dataIndex: 'metricLabels',
              ellipsis: true,
              render(metricLabels) {
                const text = (metricLabels || []).join(',')
                return (
                  <Tooltip title={text} placement="topLeft">
                    {text}
                  </Tooltip>
                )
              },
            },
            {
              title: '批量指标上线ID',
              dataIndex: 'publishId',
              render(_, record) {
                return record.publishInfo?.publishId
              },
            },
            {
              title: '实时指标状态',
              dataIndex: 'status',
              render(_, record) {
                return (
                  <MetricStatusText
                    modelName={baseInfo.modelName}
                    metricInfo={record}
                    onOffLineMetricOk={run}
                    streamTaskActSuccess={run}
                    isBatch
                  />
                )
              },
            },
            {
              title: '作业',
              dataIndex: 'jobUrl',
              render(_, record) {
                return (
                  <>
                    {record.publishInfo?.jobUrl && (
                      <a href={record.publishInfo?.jobUrl} target="_blank" rel="noreferrer">
                        <Typography.Link>作业详情</Typography.Link>
                      </a>
                    )}
                  </>
                )
              },
            },
            {
              title: '上线时间',
              dataIndex: 'publishTime',
              render(_, record) {
                const time = record.publishInfo?.publishTime
                return time ? dayjs(time).format('YYYY-MM-DD HH:mm:ss') : '-'
              },
            },
          ] as TableProps['columns']
        }
      />
    </Drawer>
  )
}
