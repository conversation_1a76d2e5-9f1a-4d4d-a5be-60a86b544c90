import React, { useCallback, useEffect, useMemo, useRef, useState, useContext } from 'react'
import { useLocation } from 'react-router-dom'
import { Popover, Button, App, Popconfirm, notification, Tag, Typography } from 'antd'
import axios from 'axios'
import { useBoolean } from 'ahooks'
import { MoreOutlined } from '@ant-design/icons'
import dayjs from 'dayjs'
import { SemanticMetric } from '@prisma/client'
import { useSetAtom } from 'jotai'
import { type ColumnProps } from 'antd/es/table'
import CountSearchTable, { type ApiArgsType } from 'src/client/components/CountSearchTable'
import CreateMetricModal from 'src/client/pages/MetricStore/Metrics/fusion/Create'
import { SynonymsForm } from 'src/client/pages/MetricStore/Metrics/fusion/SynonymsForm'
import { askBIApiUrls } from 'src/shared/url-map'
import SynonymsText from 'src/client/components/SynonymsText'
import { ColumnDescType, MetricModelType, MetricTypeNamesWithAll, PublishMetric } from 'src/shared/metric-types'
import { customerFilterValue } from 'src/shared/customer-resolver'
import { currentDatasetAtom } from '../../askBIAtoms'
import { MetricModelContext } from '../context'
import { MetricStatusText } from './conf'
import DeleteMetricModal from './DeleteMetricModal'
import { useBatchUpload } from './useBatchUpload'
import BatchOnlineManagerBtn from './BatchOnlineManagerBtn'
import OnlineMetricModal from './OnlineMetricsModal'
import BatchOnlineMetricsView from './BatchOnlineMetricsView'

type MetricWithPublishInfo = SemanticMetric & { publishInfo?: PublishMetric }
export default function MetricList({ modelData }: { modelData?: MetricModelType }) {
  const { message } = App.useApp()
  const tableColumns = modelData?.columns as ColumnDescType[]
  const [notificationApi, notificationContextHolder] = notification.useNotification()
  const [createMetricModalOpen, setCreateMetricModalOpen] = useState(false)
  const [synonymsFormOpen, setSynonymsFormOpen] = useState(false)
  const [deleteMetricModalOpen, setDeleteMetricModalOpen] = useState(false)
  const [semanticProjectId, setSemanticProjectId] = useState('')
  const [semanticModelId, setSemanticModelId] = useState('')
  const [semanticSceneId, setSemanticSceneId] = useState('')
  const location = useLocation()
  const actionRef = useRef<{ reload: (args: ApiArgsType) => void; refresh: () => void }>()
  const [currentMetric, setCurrentMetric] = useState<SemanticMetric>()
  const [createMetricValues, setCreateMetricValues] = useState<SemanticMetric>()
  const [synonymsIsReadOnly, setSynonymsIsReadOnly] = useState(false)
  const [createMetricModelIsEdit, setCreateMetricModelIsEdit] = useState(false)
  const updateCurrentDataset = useSetAtom(currentDatasetAtom)
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([])
  const [deleting, deletingOps] = useBoolean(false)
  const metricListData = useRef<MetricWithPublishInfo[]>([])
  const [onlineMetricModelProps, setOnlineMetricModelProps] = useState({
    open: false,
    isBatchMode: false,
    metrics: [] as SemanticMetric[],
  })
  const [batchOnlineMetricsViewVisible, batchOnlineMetricsViewVisibleOps] = useBoolean(false)
  const metricModel = useContext(MetricModelContext)

  useEffect(() => {
    const urlParams = new URLSearchParams(location.search)
    setSemanticModelId(urlParams.get('name') || '')
    setSemanticProjectId(urlParams.get('projectId') || '')
    setSemanticSceneId(urlParams.get('scenarioId') || '')
  }, [location.search])

  const { triggerNode, modalNode } = useBatchUpload({
    semanticProjectId,
    semanticModelId,
    semanticSceneId,
    refresh: () => actionRef.current?.refresh(),
    semanticProjectName: metricModel?.scenarioDetailData?.projectName || '',
    semanticSceneName: metricModel?.scenarioDetailData?.label || '',
  })
  return (
    <>
      <CountSearchTable
        actionRef={actionRef}
        isFullList
        extra={
          <div className="flex gap-1">
            <Button
              className="ml-1"
              type="primary"
              onClick={() => {
                setCreateMetricModelIsEdit(false)
                setCreateMetricModalOpen(true)
              }}
            >
              创建指标
            </Button>
            {triggerNode}
            <Popconfirm
              placement="top"
              title={'删除提示'}
              description={`确认删除这些指标吗？`}
              okText="确定"
              cancelText="取消"
              okButtonProps={{ loading: deleting }}
              onConfirm={() => {
                const someMetricPublished = metricListData.current
                  .filter((i) => selectedRowKeys.includes(i.id))
                  .some((metric) => {
                    const isProcessing = !!metric?.publishInfo?.publishId
                    const isOnline = !!(isProcessing && metric?.publishInfo?.jobUrl)
                    return isOnline
                  })

                if (someMetricPublished) return message.error('存在已上线的指标，请先下线后再删除')
                deletingOps.setTrue()
                axios
                  .delete(askBIApiUrls.metrics.batchDelete, { data: { ids: selectedRowKeys, semanticModelId } })
                  .then(() => {
                    message.success('指标删除成功')
                    setSelectedRowKeys([])
                    actionRef.current?.refresh?.()
                  })
                  .catch((err) => {
                    if (err instanceof Error) {
                      notificationApi.error({
                        message: '删除失败',
                        description: err.message.split('\n'),
                      })
                    } else {
                      console.info('批量删除时发生未知错误', err)
                    }
                  })
                  .finally(() => {
                    deletingOps.setFalse()
                  })
              }}
            >
              <Button loading={deleting} className="ml-1" type="primary" danger disabled={selectedRowKeys.length === 0}>
                批量删除
              </Button>
            </Popconfirm>
            {modelData?.computeType !== 'BATCH' && (
              <BatchOnlineManagerBtn
                handleClickOnlineMetricsView={batchOnlineMetricsViewVisibleOps.setTrue}
                handleClickOnlineMetrics={() => {
                  const onlineMetrics = metricListData.current.filter((metric) => selectedRowKeys.includes(metric.id))
                  if (onlineMetrics.length <= 1) {
                    message.info('批量上线指标需至少选择两个指标，请在指标列表前勾选批量上线的指标')
                  } else if (metricModel?.computeType === 'BATCH') {
                    message.info('该场景依赖的表为批表不支持上线')
                  } else {
                    setOnlineMetricModelProps({
                      isBatchMode: true,
                      open: true,
                      metrics: onlineMetrics,
                    })
                  }
                }}
              />
            )}
          </div>
        }
        searchKey={['name', 'label']}
        api={useCallback(async () => {
          const response = await axios.get(askBIApiUrls.metrics.list, {
            params: {
              semanticProjectId,
              semanticModelId,
              semanticSceneId,
              modelName: metricModel?.name,
              batchMode: false,
            },
          })
          const data = response.data.data
          metricListData.current = data?.list || []
          return data
        }, [semanticProjectId, semanticModelId, semanticSceneId, metricModel])}
        tableProps={{
          rowSelection: {
            type: 'checkbox',
            onChange: (selectedRowKeys: React.Key[]) => {
              setSelectedRowKeys(selectedRowKeys)
            },
          },
          rowKey: 'id',
          scroll: {
            x: 'max-content',
          },
          columns: customerFilterValue('metricListTableColumnsFilter', [
            {
              title: '指标ID',
              dataIndex: 'name',
              width: 160,
              fixed: 'left',
              render(name) {
                return <div className="text-wrap break-all">{name}</div>
              },
            },
            {
              title: '指标名称',
              dataIndex: 'label',
              fixed: 'left',
            },
            {
              title: '同义词',
              dataIndex: 'synonyms',
              width: 200,
              render(synonyms, record) {
                return (
                  <SynonymsText
                    disabled={record.autoCreateByMeasure}
                    synonyms={synonyms}
                    handleViewSynonyms={(synonyms) => {
                      setSynonymsFormOpen(true)
                      setCurrentMetric({
                        ...(record || {}),
                        synonyms,
                      })
                      setSynonymsIsReadOnly(true)
                    }}
                    handleEditSynonyms={(synonyms) => {
                      setSynonymsIsReadOnly(false)
                      setCurrentMetric({
                        ...(record || {}),
                        synonyms,
                      })
                      setSynonymsFormOpen(true)
                    }}
                  />
                )
              },
            },
            {
              title: '类型',
              dataIndex: 'type',
              width: 90,
              render(type, record) {
                const text = MetricTypeNamesWithAll[type as keyof typeof MetricTypeNamesWithAll]
                return <>{record.autoCreateByMeasure ? <Tag>自动生成</Tag> : <Tag>{text}</Tag>}</>
              },
            },
            {
              title: '实时指标状态',
              dataIndex: 'status',
              render(_, record) {
                return (
                  <MetricStatusText
                    onOffLineMetricOk={() => actionRef.current?.refresh()}
                    modelName={metricModel?.name}
                    metricInfo={record}
                    streamTaskActSuccess={() => actionRef.current?.refresh()}
                    onlineTextClick={() => {
                      if (metricModel?.computeType === 'BATCH') {
                        message.info('该场景依赖的表为批表不支持上线')
                      } else {
                        setOnlineMetricModelProps({ isBatchMode: false, open: true, metrics: [record] })
                      }
                    }}
                  />
                )
              },
            },
            {
              title: '作业',
              dataIndex: 'jobUrl',
              render(_, record) {
                return (
                  <>
                    {record.publishInfo?.jobUrl && (
                      <a href={record.publishInfo?.jobUrl} target="_blank" rel="noreferrer">
                        <Typography.Link>作业详情</Typography.Link>
                      </a>
                    )}
                  </>
                )
              },
            },
            {
              title: '描述',
              dataIndex: 'description',
            },
            {
              title: '配置和过滤条件',
              dataIndex: 'typeParams',
              render(value, record) {
                let ctx = (
                  <div>
                    <code>{JSON.stringify(value)}</code>
                  </div>
                )
                if (record.type === 'simple' && record.filter != null && record.filter.length > 0) {
                  ctx = (
                    <div>
                      <code>{JSON.stringify(value)}</code>
                      <br />
                      <b>过滤条件：</b>
                      <br />
                      <code>{record.filter}</code>
                    </div>
                  )
                }
                return (
                  <Popover content={ctx} title="指标配置：">
                    <Typography.Link>详细配置</Typography.Link>
                  </Popover>
                )
              },
            },
            {
              title: '创建时间',
              dataIndex: 'createdAt',
              render(t) {
                return t && dayjs(t).format('YYYY-MM-DD HH:mm:ss')
              },
            },
            {
              title: '操作',
              dataIndex: 'operation',
              width: 60,
              fixed: 'right',
              render: (_, record) => {
                return (
                  <Popover
                    placement="top"
                    content={
                      <div className="w-[74px]">
                        <Button
                          type="text"
                          block
                          danger
                          onClick={() => {
                            if (record.publishInfo?.publishId) {
                              message.info('删除指标前，请先下线指标')
                              return
                            }
                            setCurrentMetric(record as SemanticMetric)
                            setDeleteMetricModalOpen(true)
                          }}
                        >
                          删除指标
                        </Button>
                        <br />
                        <Button
                          type="text"
                          block
                          disabled={record.autoCreateByMeasure}
                          onClick={() => {
                            setCreateMetricModelIsEdit(true)
                            setCreateMetricValues(record as SemanticMetric)
                            setCreateMetricModalOpen(true)
                          }}
                        >
                          编辑指标
                        </Button>
                      </div>
                    }
                  >
                    <MoreOutlined className="cursor-pointer" />
                  </Popover>
                )
              },
            },
          ] as ColumnProps<SemanticMetric & { publishInfo?: PublishMetric }>[])?.filter(
            (i) => modelData?.computeType !== 'BATCH' || (i.dataIndex !== 'status' && i.dataIndex !== 'jobUrl'),
          ),
        }}
      />
      <OnlineMetricModal
        {...onlineMetricModelProps}
        baseInfo={{
          semanticProjectId,
          semanticSceneId,
        }}
        onSuccess={() => {
          setOnlineMetricModelProps((props) => ({ ...props, open: false }))
          actionRef.current?.refresh()
        }}
        onClose={() => setOnlineMetricModelProps((props) => ({ ...props, open: false }))}
      />
      <BatchOnlineMetricsView
        baseInfo={{
          modelName: metricModel?.name || '',
          semanticProjectId,
          semanticSceneId,
        }}
        open={batchOnlineMetricsViewVisible}
        onClose={batchOnlineMetricsViewVisibleOps.setFalse}
      />
      <DeleteMetricModal
        open={deleteMetricModalOpen}
        metric={currentMetric as SemanticMetric}
        onSuccess={() => {
          actionRef.current?.refresh?.()
          setDeleteMetricModalOpen(false)
        }}
        onClose={() => {
          setDeleteMetricModalOpen(false)
        }}
      />

      <SynonymsForm
        couldDeleteAll
        isReadOnly={synonymsIsReadOnly}
        onOk={async (synonyms: string[]) => {
          if (currentMetric) {
            axios
              .put(askBIApiUrls.metrics.update(currentMetric.id), {
                synonyms,
                name: currentMetric.name,
                description: currentMetric.description,
              })
              .then(
                () => {
                  actionRef.current?.refresh?.()
                  message.success('同义词更新成功')
                },
                () => {
                  message.error('同义词更新失败')
                },
              )
          }
        }}
        createSynonymsOpen={synonymsFormOpen}
        setSynonymsCalculateOpen={setSynonymsFormOpen}
        initValue={(currentMetric?.synonyms || []) as string[]}
      />
      <CreateMetricModal
        factTable={modelData?.dataModelDesc?.factTable}
        catalog={modelData?.catalogName}
        databaseName={modelData?.databaseName}
        table={modelData?.name}
        tableColumns={tableColumns}
        baseInfo={useMemo(
          () => ({
            semanticModelName: semanticModelId,
            semanticProjectId: semanticProjectId,
            semanticSceneId: semanticSceneId,
          }),
          [semanticModelId, semanticProjectId, semanticSceneId],
        )}
        isEdit={createMetricModelIsEdit}
        values={createMetricValues}
        onOk={() => {
          actionRef.current?.refresh?.()
          updateCurrentDataset(null)
        }}
        createMetricModalOpen={createMetricModalOpen}
        setCreateMetricModalOpen={setCreateMetricModalOpen}
      />
      {notificationContextHolder}
      {modalNode}
    </>
  )
}
