import { PlusOutlined } from '@ant-design/icons'
import { useBoolean } from 'ahooks'
import { App, Dropdown, Button } from 'antd'
import axios from 'axios'
import React, { useState } from 'react'
import crypto from 'crypto-js'
import ValidatedUploadFile from 'src/client/components/ValidatedUploadFile'
import { formatPathWithBaseUrl } from 'src/client/utils'
import { askBIApiUrls } from 'src/shared/url-map'
import { customerFilterValue } from 'src/shared/customer-resolver'
import { downloadFile } from 'src/shared/common-utils'

export function useBatchUpload({
  semanticProjectId,
  semanticModelId,
  semanticSceneId,
  refresh,
  semanticProjectName,
  semanticSceneName,
}: {
  semanticProjectId: string
  semanticModelId: string
  semanticSceneId: string
  refresh: () => void
  semanticProjectName: string
  semanticSceneName: string
}) {
  const { message } = App.useApp()
  const [validatedUploadFileModalVisible, validatedUploadFileModalVisibleOps] = useBoolean(false)
  const [mode, setMode] = useState<'create' | 'update'>('create')
  const label = mode === 'create' ? '创建' : '更新'
  const triggerNode = (
    <Dropdown
      menu={{
        items: [
          {
            key: 'create',
            label: (
              <span
                onClick={() => {
                  validatedUploadFileModalVisibleOps.setTrue()
                  setMode('create')
                }}
              >
                从XLSX文件创建指标
              </span>
            ),
          },
          {
            key: 'update',
            label: (
              <span
                onClick={() => {
                  validatedUploadFileModalVisibleOps.setTrue()
                  setMode('update')
                }}
              >
                从XLSX文件更新指标
              </span>
            ),
          },
          {
            key: 'download',
            label: (
              <span
                onClick={() => {
                  downloadFile(
                    formatPathWithBaseUrl(
                      customerFilterValue('xlsxMetricBatchUploadTemplate', '/files/metric-upload.xlsx'),
                    ),
                    '示例.xlsx',
                  )
                }}
              >
                下载XLSX模版
              </span>
            ),
          },
          {
            key: 'download-metrics',
            label: (
              <span
                onClick={() =>
                  downloadFile(
                    `${askBIApiUrls.metrics.downloadSceneMetric}?semanticSceneId=${semanticSceneId}&semanticProjectId=${semanticProjectId}`,
                    `${semanticProjectName}项目_${semanticSceneName}场景_全部指标.xlsx`,
                  )
                }
              >
                下载场景全部指标
              </span>
            ),
          },
        ],
      }}
      placement="bottomRight"
      arrow
    >
      <Button type="primary" icon={<PlusOutlined />}>
        批量上传
      </Button>
    </Dropdown>
  )

  const uploadFileWithChecksum = (uploadFile: Blob) => {
    if (!uploadFile) {
      console.error('文件不存在')
      return Promise.reject('文件不存在')
    }

    // 计算文件的 SHA-256 checksum 使用 crypto-js
    const calculateChecksum = (file: Blob) => {
      return new Promise<string>((resolve, reject) => {
        const reader = new FileReader()
        reader.onload = () => {
          try {
            const arrayBuffer = reader.result as ArrayBuffer // 这是一个 ArrayBuffer
            if (!arrayBuffer) {
              reject('读取文件失败。')
              return
            }

            // 将 ArrayBuffer 转换为 WordArray
            const wordArray = crypto.lib.WordArray.create(arrayBuffer)

            // 使用 SHA-256 计算 checksum
            // eslint-disable-next-line new-cap
            const checksum = crypto.SHA256(wordArray).toString(crypto.enc.Hex)

            resolve(checksum)
          } catch (error: any) {
            console.error('计算 checksum 时出错', error)
            reject(`计算 checksum 时出错: ${error?.message}`)
          }
        }
        reader.onerror = () => reject('读取文件时出错')
        reader.readAsArrayBuffer(file)
      })
    }

    // 获取文件的 checksum
    return calculateChecksum(uploadFile)
      .then((checksum) => {
        const formData = new FormData()
        formData.append('file', uploadFile)
        formData.append('checksum', checksum) // checksum 已经是字符串，不需要再转换
        formData.append('semanticProjectId', semanticProjectId)
        formData.append('semanticModelId', semanticModelId)
        formData.append('semanticSceneId', semanticSceneId)

        // 根据模式发送请求
        return mode === 'create'
          ? axios.post(askBIApiUrls.metrics.createFromFile, formData, {
              headers: {
                'Content-Type': 'multipart/form-data',
              },
            })
          : axios.post(askBIApiUrls.metrics.updateFromFile, formData, {
              headers: {
                'Content-Type': 'multipart/form-data',
              },
            })
      })
      .catch((error) => {
        message.error(`checksum error 上传文件时出错：${error}`)
        return Promise.reject(error)
      })
  }

  const modalNode = (
    <ValidatedUploadFile
      modalProps={{
        title: `从XLSX${label}指标`,
        open: validatedUploadFileModalVisible,
        onCancel: validatedUploadFileModalVisibleOps.setFalse,
      }}
      uploadApi={uploadFileWithChecksum}
      acceptTypes={['.xlsx']}
      samples={[
        {
          fileName: '示例.xlsx',
          fileUrl: formatPathWithBaseUrl(
            customerFilterValue('xlsxMetricBatchUploadTemplate', '/files/metric-upload.xlsx'),
          ),
        },
      ]}
      onSuccess={() => {
        message.success(`指标${label}成功`)
        refresh()
        validatedUploadFileModalVisibleOps.setFalse()
      }}
    />
  )
  return { triggerNode, validatedUploadFileModalVisible, validatedUploadFileModalVisibleOps, modalNode }
}
