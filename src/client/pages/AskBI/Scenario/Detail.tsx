import React, { useCallback, useRef, useState, useMemo } from 'react'
import { useSearchParams } from 'react-router-dom'
import { useRequest } from 'ahooks'
import { type TabsProps, Card, App, Spin, Space, Button, Form, type TableProps } from 'antd'
import { LoadingOutlined, EyeOutlined } from '@ant-design/icons'
import axios from 'axios'
import dayjs from 'dayjs'
import { omit } from 'lodash-es'
import { ResponseErrorType } from '@shared/metric-types'
import ScenarioDetailModel from 'src/client/pages/MetricStore/components/ScenarioDetailModal'
import MetricModelTable from 'src/client/pages/MetricStore/components/MetricModelTable/MetricModelTable'
import DetailTableHeader from 'src/client/pages/MetricStore/components/MetricModelTable/DetailTableHeader'
import { formatMetricModelMetaToTable } from 'src/client/pages/MetricStore/components/MetricModelTable/conf'
import request from 'src/shared/xengine-axios'
import { askBIApiUrls } from 'src/shared/url-map'
import { SvgIcon, folderIcon } from 'src/client/components/SvgIcon'
import { type MetricModelType, TimeGranularityMap, type MetricFuncType } from 'src/shared/metric-types'
import CountSearchTable from 'src/client/components/CountSearchTable'
import './styles.css'
import ColumnCodeUpdate from 'src/client/pages/MetricStore/components/ColumnCodeUpdate'
import { SynonymsForm } from 'src/client/pages/MetricStore/Metrics/fusion/SynonymsForm'
import SynonymsText from 'src/client/components/SynonymsText'
import DocumentDataTable from 'src/client/components/DocumentDataTable'
import AdminPage from 'src/client/components/AdminPage'
import ColumnCodeDrawer from 'src/client/pages/MetricStore/components/ColumnCodeDrawer'
import ValidatedUploadFile from 'src/client/components/ValidatedUploadFile'
import { formatPathWithBaseUrl } from 'src/client/utils'
import { customerFilterValue } from 'src/shared/customer-resolver'
import CustomerHiddenWrap from 'src/client/components/CustomerHiddenWrap'
import MetricList from './components/MetricList'
import { MetricModelContext } from './context'

const BITabs = [
  { key: 'metricModel', label: '指标模型' },
  { key: 'measures', label: '度量' },
  { key: 'dimensions', label: '维度' },
  { key: 'metrics', label: '指标' },
]

const DocTabs = [{ key: 'document', label: '文档' }]

const downloadCSV = (data: string, fileName: string) => {
  // const BOM = '\uFEFF'
  const blob = new Blob([data], { type: 'text/csv' })
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.setAttribute('download', fileName + '.csv')
  document.body.appendChild(link)
  link.click()
  link.remove()
}
export default function ScenarioDetail() {
  const [searchParams, setSearchParams] = useSearchParams()
  const scenarioId = searchParams.get('scenarioId') || ''
  const projectId = searchParams.get('projectId') || ''
  const [scenarioDetailModelOpen, setScenarioDetailModelOpen] = useState(false)
  const [activeTabKey, setActiveTabKey] = useState('metricModel')
  const [synonymsFormOpen, setSynonymsFormOpen] = useState(false)
  const [synonymsIsReadOnly, setSynonymsIsReadOnly] = useState(false)
  const [timeColumnForm] = Form.useForm()
  const [currentEditSynonymsInfo, setCurrentEditSynonymsInfo] = useState<{ name: string; synonyms: string[] }>({
    name: '',
    synonyms: [],
  })
  const currentDimensionInfo = useRef<MetricFuncType>()
  const { message } = App.useApp()
  const [items, setItems] = useState<TabsProps['items']>([])
  const [columnCodeDrawerOpen, setColumnCodeDrawerOpen] = useState(false)
  const [batchUpdateSynonymsOpen, setBatchUpdateSynonymsOpen] = useState(false)

  const {
    data: scenarioDetailData,
    loading: scenarioDetailLoading,
    run: getScenarioDetail,
  } = useRequest(
    () => axios.get(askBIApiUrls.auth.scene.rest, { params: { id: scenarioId } }).then((res) => res.data.data),
    {
      onSuccess(data) {
        if (data && data.agent) {
          const agentTypes = data.agent.split(',')
          if (agentTypes.length === 0) return
          if (agentTypes.length === 1) {
            const includesBI = agentTypes.includes('BI')
            setItems(includesBI ? BITabs : DocTabs)
            setActiveTabKey(includesBI ? 'metricModel' : 'document')
          } else {
            setItems([...BITabs, ...DocTabs])
          }
        }
      },
      onError() {
        message.error('获取场景信息失败')
      },
    },
  )
  const {
    loading,
    data,
    run: getMetricModel,
  } = useRequest(
    async (isDelay?: boolean) => {
      if (isDelay) {
        await new Promise((resolve) => setTimeout(resolve, 1000))
      }
      const name = searchParams.get('name') || scenarioDetailData?.modelNames?.[0]
      return request.get<{ name: string }, MetricModelType>(askBIApiUrls.model.meta, {
        params: {
          name,
        },
      })
    },
    {
      refreshDeps: [scenarioDetailData],
      ready: Boolean(searchParams.get('name') || scenarioDetailData?.modelNames?.[0]),
    },
  )
  const { run: downloadAllCodeValues, loading: downloadAllCodeValuesLoading } = useRequest(
    () => {
      return request
        .get<
          {
            modelName: string
          },
          { data: string }
        >(askBIApiUrls.model.CSVDownloadAllColumnCodeValues + `?modelName=${data?.name}`)
        .then((responseData: { data: string }) => {
          if (data?.name) {
            downloadCSV(responseData?.data || '', data?.name)
          }
        })
    },
    {
      manual: true,
      onError(err: ResponseErrorType) {
        message.error(err?.msg || '下载全部码值失败')
      },
    },
  )
  async function handleUpdateSynonyms(synonyms: string[]) {
    const name = searchParams.get('name') || scenarioDetailData?.modelNames?.[0]
    return request
      .post(askBIApiUrls.model.updateSynonyms, {
        metricModelName: name,
        columnName: currentEditSynonymsInfo.name,
        synonyms,
      })
      .then(
        () => {
          message.success('更新同义词成功')
          // 后端更新同义词异步操作，需要时间列表才会更新
          getMetricModel(true)
        },
        (err) => {
          message.error('更新同义词失败')
          return Promise.reject(err)
        },
      )
  }

  const modelUniteData = useMemo(() => {
    return {
      ...data,
      scenarioDetailData,
    } as MetricModelType & { scenarioDetailData: { projectName: string; label: string } }
  }, [data, scenarioDetailData])

  const children = {
    metricModel: (
      <div key="metricModel">
        <DetailTableHeader
          data={data as MetricModelType}
          className="mt-2"
          onReplaceMetricModelModalSuccess={(modelNames: string[]) => {
            const modelName = (modelNames && modelNames[0]) || ''
            setSearchParams({
              projectId,
              scenarioId,
              name: modelName,
            })
            searchParams.set('name', modelName)
            getMetricModel()
          }}
          onUpdateMetricModelSuccess={getMetricModel}
        />
        <MetricModelTable
          timeColumnForm={timeColumnForm}
          VTableInfo={{
            catalog: data?.catalogName || '',
            database: data?.databaseName || '',
            tableName: data?.name || '',
          }}
          initMetricModelTableData={formatMetricModelMetaToTable(data as MetricModelType)}
          className="mt-1"
        />
      </div>
    ),
    measures: (
      <CountSearchTable
        isFullList
        api={useCallback(async () => {
          const list = data?.dataModelDesc?.measures || []
          return {
            list: list.map((item) => omit(item, 'children')),
            total: list.length,
          }
        }, [data?.dataModelDesc?.measures])}
        className="mt-2"
        key="measures"
        searchKey="name"
        tableProps={{
          scroll: {
            x: 'max-content',
          },
          rowKey: 'name',
          columns: customerFilterValue('scenarioDetailMeaTableColumnsFilter', [
            {
              title: '度量ID',
              dataIndex: 'name',
              fixed: 'left',
            },
            {
              title: '名称',
              dataIndex: 'nameZh',
              fixed: 'left',
            },
            {
              title: '同义词',
              dataIndex: 'synonyms',
              render(synonyms, record) {
                return (
                  <SynonymsText
                    synonyms={synonyms}
                    handleEditSynonyms={(synonyms) => {
                      setSynonymsIsReadOnly(false)
                      setCurrentEditSynonymsInfo((origin) => ({ ...origin, synonyms, name: record.name }))
                      setSynonymsFormOpen(true)
                    }}
                    handleViewSynonyms={(synonyms) => {
                      setSynonymsIsReadOnly(true)
                      setCurrentEditSynonymsInfo((origin) => ({ ...origin, synonyms, name: record.name }))
                      setSynonymsFormOpen(true)
                    }}
                  />
                )
              },
            },
            {
              title: '描述',
              dataIndex: 'comment',
            },
            {
              title: '表达式',
              dataIndex: 'expression',
            },
            {
              title: '聚合方式',
              dataIndex: 'function',
              render(func) {
                return func.function || '-'
              },
            },
            {
              title: '创建时间',
              dataIndex: 'time',
              render() {
                return data?.createTime ? dayjs(data?.createTime).format('YYYY-MM-DD HH:mm:ss') : '-'
              },
            },
          ] as TableProps['columns']),
        }}
      />
    ),
    dimensions: (
      <CountSearchTable<MetricFuncType>
        searchKey="name"
        isFullList
        extra={
          <CustomerHiddenWrap type="scenarioDetailCodeValuesBtnsHide">
            <Space>
              <Button onClick={downloadAllCodeValues} loading={downloadAllCodeValuesLoading}>
                下载全部码值
              </Button>
              <Button type="primary" onClick={() => setBatchUpdateSynonymsOpen(true)}>
                批量添加码值同义词
              </Button>
            </Space>
          </CustomerHiddenWrap>
        }
        api={useCallback(async () => {
          const list = data?.dataModelDesc?.dimensions || []
          return {
            list: list.map((item) => omit(item, 'children')),
            total: list.length,
          }
        }, [data?.dataModelDesc?.dimensions])}
        className="mt-2"
        key="dimensions"
        tableProps={{
          scroll: {
            x: 'max-content',
          },
          rowKey: 'name',
          columns: customerFilterValue('scenarioDetailDimTableColumnsFilter', [
            {
              title: '维度ID',
              dataIndex: 'name',
              fixed: 'left',
            },
            {
              title: '名称',
              dataIndex: 'nameZh',
              fixed: 'left',
            },
            {
              title: '同义词',
              dataIndex: 'synonyms',
              render(synonyms, record) {
                return (
                  <SynonymsText
                    synonyms={synonyms}
                    handleEditSynonyms={(synonyms) => {
                      setSynonymsIsReadOnly(false)
                      setCurrentEditSynonymsInfo((origin) => ({ ...origin, synonyms, name: record.name }))
                      setSynonymsFormOpen(true)
                    }}
                    handleViewSynonyms={(synonyms) => {
                      setSynonymsIsReadOnly(true)
                      setCurrentEditSynonymsInfo((origin) => ({ ...origin, synonyms, name: record.name }))
                      setSynonymsFormOpen(true)
                    }}
                  />
                )
              },
            },
            {
              title: '召回',
              dataIndex: 'filterSwitch',
              render(filterSwitch) {
                return <>{filterSwitch === false ? '否' : '是'}</>
              },
            },
            {
              title: '码值更新',
              dataIndex: 'values',
              render(_, record) {
                return (
                  <div className="inline-flex items-center">
                    <ColumnCodeUpdate
                      isUpdated={false}
                      onSuccess={getMetricModel}
                      data={{
                        metricModelName: data?.name || '',
                        columnName: record.name,
                      }}
                    />
                    <EyeOutlined
                      className="ml-1 cursor-pointer text-[#503CE4]"
                      onClick={() => {
                        setColumnCodeDrawerOpen(true)
                        currentDimensionInfo.current = record
                      }}
                    />
                  </div>
                )
              },
            },
            {
              title: '描述',
              dataIndex: 'comment',
            },
            {
              title: '表达式',
              dataIndex: 'expression',
            },
            {
              title: '时间粒度',
              dataIndex: 'granularity',
              render() {
                const granularity = data?.dataModelDesc?.timeColumnDesc?.granularity as keyof typeof TimeGranularityMap
                return TimeGranularityMap[granularity] || granularity
              },
            },
            {
              title: '创建时间',
              dataIndex: 'time',
              render() {
                return data?.createTime ? dayjs(data?.createTime).format('YYYY-MM-DD HH:mm:ss') : '-'
              },
            },
          ] as TableProps<MetricFuncType>['columns']),
        }}
      />
    ),
    metrics: (
      <MetricModelContext.Provider value={modelUniteData || null}>
        <MetricList modelData={data} />
      </MetricModelContext.Provider>
    ),
    document: <DocumentDataTable />,
  }

  return (
    <AdminPage
      title={
        scenarioDetailLoading ? (
          <Spin />
        ) : (
          <div className="flex items-center">
            <SvgIcon icon={folderIcon} className="h-6 w-6" />
            <span className="ml-1">{scenarioDetailData?.semanticProject?.name || '-'}</span>
            <span className="mx-2 font-normal text-[#C8C8C8]">/</span>
            <span>{scenarioDetailData?.label || '-'}</span>
            <Button type="link" onClick={() => setScenarioDetailModelOpen(true)}>
              场景配置
            </Button>
          </div>
        )
      }
      onBack={() => history.back()}
    >
      <Card
        className="scenario-detail-card mx-6 mb-6 mt-3"
        activeTabKey={activeTabKey}
        onTabChange={useCallback((key: string) => {
          setActiveTabKey(key)
        }, [])}
        loading={loading}
        tabList={items}
        bordered={false}
      >
        {children[activeTabKey as keyof typeof children]}
      </Card>
      <ScenarioDetailModel
        open={scenarioDetailModelOpen}
        onClose={() => {
          getScenarioDetail()
          setScenarioDetailModelOpen(false)
        }}
        scenarioId={scenarioId || ''}
      />
      <SynonymsForm
        couldDeleteAll={true}
        isReadOnly={synonymsIsReadOnly}
        onOk={handleUpdateSynonyms}
        createSynonymsOpen={synonymsFormOpen}
        setSynonymsCalculateOpen={setSynonymsFormOpen}
        initValue={currentEditSynonymsInfo.synonyms}
      />
      <ColumnCodeDrawer
        open={columnCodeDrawerOpen}
        onClose={() => setColumnCodeDrawerOpen(false)}
        modelName={data?.name || ''}
        columnName={currentDimensionInfo.current?.name || ''}
      />

      <ValidatedUploadFile
        modalProps={{
          title: '批量添加码值同义词',
          open: batchUpdateSynonymsOpen,
          onCancel() {
            setBatchUpdateSynonymsOpen(false)
          },
        }}
        uploadApi={(file) => {
          const formData = new FormData()
          formData.append('file', file)
          return request.post(askBIApiUrls.model.batchUpdateColumnCodeValueSynonyms, formData, {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          })
        }}
        acceptTypes={['.csv']}
        samples={[
          {
            fileName: '示例文件.csv',
            fileUrl: formatPathWithBaseUrl('/files/codeValuesSynonymsSample.csv'),
          },
        ]}
        onSuccess={() => {
          message.success('批量添加码值同义词成功')
          setBatchUpdateSynonymsOpen(false)
        }}
      >
        <div>
          <span>请下载码值进行上传: </span>
          <a
            className="inline-flex items-center"
            onClick={(e) => {
              e.stopPropagation()
              downloadAllCodeValues()
            }}
          >
            {downloadAllCodeValuesLoading && <LoadingOutlined className="mr-1" />}
            全部维度码值.csv
          </a>
        </div>
      </ValidatedUploadFile>
    </AdminPage>
  )
}
