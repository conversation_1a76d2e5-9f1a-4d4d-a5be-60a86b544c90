import { Form, Input, Select, Radio, Checkbox, Modal } from 'antd'
import React, { useEffect, useState } from 'react'
import { useForm } from 'antd/es/form/Form'
import clsx from 'clsx'
import axios from 'axios'
import { useRequest } from 'ahooks'
import { FormInstance } from 'antd/lib'
import { useSearchParams } from 'react-router-dom'
import { ProjectType } from '@shared/metric-types'
import { askBIApiUrls } from 'src/shared/url-map'
import { APIResponse } from 'src/shared/common-types'
import CustomerHiddenWrap from 'src/client/components/CustomerHiddenWrap'
import { CommonListType } from 'src/shared/xengine-types'

const Option = Select.Option

type IBasicInfoType = {
  label: string
  projectId: string
  agent: string[]
  description: string
  iconType: number
}
type INewProjectForm = {
  name: string
  description: string
}
export default function ScenarioBasicInfoForm({
  className,
  onOk,
  form,
}: {
  form: FormInstance<IBasicInfoType>
  onOk?: (arg: IBasicInfoType) => void
  className?: string
  updateList?: () => void
}) {
  const [newProjectForm] = useForm<INewProjectForm>()

  const [createProjectModalOpen, setCreateProjectModalOpen] = useState(false)

  const [searchParams] = useSearchParams()
  useEffect(() => {
    const projectId = searchParams.get('projectId') || ''
    form.setFieldValue('projectId', projectId)
  }, [searchParams, form])

  const {
    data: projectList,
    run: getProjectList,
    loading: isProjectListLoading,
  } = useRequest(
    async () => {
      const response = await axios.get<APIResponse<CommonListType<ProjectType>>>(askBIApiUrls.auth.project.list)
      return response.data.data
    },
    {
      onError: (error: any) => {
        console.error('get projectList =', error)
      },
    },
  )

  const { run: createProject, loading: isCreateProjectLoading } = useRequest(
    async (data) => {
      return axios.post(askBIApiUrls.auth.project.rest, { ...data })
    },
    {
      manual: true,
      onSuccess() {
        setCreateProjectModalOpen(false)
        getProjectList()
      },
    },
  )
  return (
    <div
      className={clsx(
        'metric-list-page text-grey-900 mx-auto flex w-full flex-grow flex-col justify-start gap-2 overflow-y-auto bg-white text-base dark:bg-slate-900 dark:text-slate-100 md:max-w-screen-lg',
        className,
      )}
    >
      <Form
        className="mt-2 w-full"
        form={form}
        onFinish={(val) => {
          // agent 在页面上 可能是不会呈现，但是会影响业务逻辑，所以设置默认值 BI
          onOk?.({ ...{ agent: ['BI'] }, ...val })
        }}
        layout={'vertical'}
        labelCol={{ span: 24 }}
        wrapperCol={{ span: 24 }}
      >
        <Form.Item label="场景名" name="label" rules={[{ required: true, message: '请输入场景名' }]}>
          <Input placeholder="请输入场景名" />
        </Form.Item>
        <Form.Item
          className="special-label-width-full"
          style={{ width: '100%' }}
          labelCol={{
            style: {
              width: '100%',
            },
          }}
          label={
            <div className="flex w-full justify-between">
              <div className="mr-20">所属项目</div>
              <div
                className="cursor-pointer text-center text-[14px] tracking-[1px] text-[#503CE4]"
                onClick={() => {
                  setCreateProjectModalOpen(true)
                }}
              >
                新增项目
              </div>
            </div>
          }
          name="projectId"
          rules={[{ required: true, message: '请选择所属项目' }]}
        >
          <Select placeholder="选择所属项目" loading={isProjectListLoading}>
            {projectList?.list?.map(({ id, name }) => (
              <Option key={id} value={id}>
                {name}
              </Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item label="描述" name="description">
          <Input.TextArea placeholder="描述" />
        </Form.Item>
        <Form.Item label="场景封面" name="iconType" className="hidden">
          <Radio.Group defaultValue={'1'}>
            <Radio value={'1'}>场景1</Radio>
            <Radio value={'2'}>场景2</Radio>
            <Radio value={'3'}>场景3</Radio>
            <Radio value={'4'}>场景4</Radio>
            <Radio value={'5'}>场景5</Radio>
          </Radio.Group>
        </Form.Item>
        <CustomerHiddenWrap type="agentHide">
          <Form.Item label="选择agent" name="agent" rules={[{ required: true, message: '请选择agent' }]}>
            <Checkbox.Group>
              <Checkbox value={'BI'}>
                <div className="flex items-center gap-1">BI</div>
              </Checkbox>
              <Checkbox value={'Doc'}>
                <div className="flex items-center gap-1">Doc</div>
              </Checkbox>
            </Checkbox.Group>
          </Form.Item>
        </CustomerHiddenWrap>
      </Form>
      <Modal
        destroyOnClose
        title={`新增项目`}
        open={createProjectModalOpen}
        onOk={() => {
          newProjectForm.submit()
        }}
        okButtonProps={{ loading: isCreateProjectLoading }}
        onCancel={() => setCreateProjectModalOpen(false)}
      >
        <Form
          className="mt-2 w-full"
          form={newProjectForm}
          onFinish={(val: INewProjectForm) => {
            createProject(val)
          }}
          layout={'vertical'}
          labelCol={{ span: 24 }}
          wrapperCol={{ span: 24 }}
        >
          <Form.Item label="项目名称" name="name" rules={[{ required: true, message: '请输入项目名称' }]}>
            <Input placeholder="请输入项目名称" />
          </Form.Item>
          <Form.Item label="描述" name="description">
            <Input.TextArea placeholder="描述" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}
