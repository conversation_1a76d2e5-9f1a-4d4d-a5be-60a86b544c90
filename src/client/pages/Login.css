.login-form .ant-btn-primary {
  border-radius: 4px !important;
  background-color: var(--color-primary);
}

.login-form .ant-btn-primary:hover {
  background-color: var(--color-primary-hover) !important;
}

.login-form .ant-input-outlined:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px var(--color-primary-hover);
}

.login-form .ant-input-outlined:focus-within {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px var(--color-primary-hover);
}

.login-form .ant-input-affix-wrapper:hover {
  border-color: var(--color-primary) !important;
}

.login-form .ant-checkbox:hover .ant-checkbox-inner {
  border-color: var(--color-primary) !important;
  border-radius: 4px !important;
}

.login-form .ant-checkbox-checked:not(.ant-checkbox-disabled) .ant-checkbox-inner {
  border: none !important;
  background-color: var(--color-primary) !important;
}

.login-form .ant-checkbox-wrapper:not(.ant-checkbox-wrapper-disabled):hover .ant-checkbox-inner {
  border-color: var(--color-primary) !important;
}

.login-form .ant-input {
  height: 40px !important;
  border-radius: 4px !important;
}

.login-form .ant-input-affix-wrapper-lg {
  padding: 0 12px !important;
  border-radius: 4px !important;
}

.login-form .proxy-form-item {
  margin-bottom: 0 !important;
}

.login-form .ant-select-selector {
  border-radius: 4px !important;
}
