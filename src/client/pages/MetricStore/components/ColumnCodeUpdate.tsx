import { useRequest } from 'ahooks'
import React from 'react'
import { App } from 'antd'
import { LoadingOutlined } from '@ant-design/icons'
import { askBIApiUrls } from '@shared/url-map'
import { SvgIcon, columnNotUpdate, columnUpdated } from 'src/client/components/SvgIcon'
import Request from 'src/shared/xengine-axios'
type PropsType = {
  isUpdated: boolean
  onSuccess?: () => void
  className?: string
  data: { modelCatalog?: string; modelDatabase?: string; metricModelName: string; columnName: string }
}
export default function ColumnCodeUpdate(props: PropsType) {
  const { message } = App.useApp()
  const { isUpdated, onSuccess, className, data } = props
  const { loading, run: updateColumn } = useRequest(
    () => {
      return Request.post(askBIApiUrls.model.updateColumnCodeValues, data)
    },
    {
      manual: true,
      onSuccess() {
        message.success('码值更新成功')
        onSuccess && onSuccess()
      },
      onError(e) {
        message.error(e?.message || '码值更新失败')
      },
    },
  )
  return (
    <div
      className={`${className ? className : ''} flex h-full cursor-pointer items-center`}
      onClick={() => {
        if (!data || !data.metricModelName || !data.columnName) {
          message.error('缺少参数')
          return
        }
        !loading && updateColumn()
      }}
    >
      {isUpdated ? (
        <div className="inline-flex h-full items-center text-[#503CE4]">
          {loading ? <LoadingOutlined /> : <SvgIcon icon={columnUpdated} className="h-[13px] w-[13px]" />}
          <span className="ml-1">更新值</span>
        </div>
      ) : (
        <div className="inline-flex h-full items-center text-[#503CE4]">
          {loading ? <LoadingOutlined /> : <SvgIcon icon={columnNotUpdate} className="h-5 w-5" />}
          <span className="ml-1">更新值</span>
        </div>
      )}
    </div>
  )
}
