import React, { useEffect, useState } from 'react'
import { App, Modal, Input, Table, Button, Radio, Space } from 'antd'
import { useRequest } from 'ahooks'
import dayjs from 'dayjs'
import { EyeOutlined } from '@ant-design/icons'
import axios from 'axios'
import { askBIApiUrls } from 'src/shared/url-map'
import request from 'src/shared/xengine-axios'
import { MetricModelType } from 'src/shared/metric-types'
import ReadingMetricModelDrawer from 'src/client/pages/MetricStore/components/ReadingMetricModelDrawer'
import { SvgIcon, warningIcon } from 'src/client/components/SvgIcon'

type PropsType = {
  open: boolean
  onClose?: () => void
  onSuccess?: (modelNames: string[]) => void
  scenarioId: string
}
export default function ReplaceMetricModelModal(props: PropsType) {
  const { open, onClose, onSuccess, scenarioId } = props
  const { message } = App.useApp()
  const [showList, setShowList] = useState<MetricModelType[]>([])
  const [selectModelNames, setSelectMetricModelNames] = useState<string[]>([])
  const [drawerOpen, setDrawerOpen] = useState(false)
  const [previewName, setPreviewName] = useState('')
  const [replaceMetricModelConfirmOpen, setReplaceMetricModelConfirmOpen] = useState(false)

  const {
    loading,
    data,
    run: getModelList,
  } = useRequest(
    () => {
      return request.get<
        { current: number; pageSize: number },
        {
          list: MetricModelType[]
          total: number
        }
      >(askBIApiUrls.model.list, {
        params: {
          current: 1,
          pageSize: -1,
        },
      })
    },
    {
      onSuccess(data) {
        const list = data?.list
        setShowList(list)
      },
    },
  )
  // 先获取详情然后再发请求
  const { loading: updateScenarioLoading, run: updateScenario } = useRequest(
    async () => {
      const modelDetail = await axios
        .get(`${askBIApiUrls.model.meta}?name=${encodeURIComponent(selectModelNames[0])}`)
        .then((res) => {
          // 这里把 timeColumnDesc 都改成小写
          if (res.data.data.dataModelDesc.timeColumnDesc && res.data.data.dataModelDesc.timeColumnDesc.granularity) {
            res.data.data.dataModelDesc.timeColumnDesc.granularity =
              res.data.data.dataModelDesc.timeColumnDesc.granularity.toLowerCase()
          }
          return res.data.data
        })

      return request.put(askBIApiUrls.auth.scene.rest, {
        data: {
          id: scenarioId,
          modelNames: selectModelNames,
          tableName: modelDetail.dataModelDesc.factTable,
          timeDimensionFormat: modelDetail.dataModelDesc.timeColumnDesc?.format || '',
          timeDimensionType: modelDetail.dataModelDesc.timeColumnDesc?.type || '',
          timeGranularityMin: modelDetail.dataModelDesc.timeColumnDesc?.granularity || '',
        },
      })
    },
    {
      onSuccess() {
        message.success('模型替换成功')
        onSuccess && onSuccess(selectModelNames)
      },
      onError() {
        message.error('模型替换失败')
      },
      manual: true,
    },
  )

  useEffect(() => {
    if (open) {
      getModelList()
    }
  }, [open, getModelList])
  function onSearch(value: string) {
    const reg = new RegExp(`${value}`, 'i')
    const originList = data?.list || []
    const filterProjects = originList?.filter((item) => {
      const text = item.name
      return reg.test((text as string) || '')
    })
    setShowList(filterProjects)
  }

  function renderFooterButtons({
    onCancel,
    onOK,
    OKBtnLoading,
  }: {
    onCancel: () => void
    onOK: () => void
    OKBtnLoading?: boolean
  }) {
    return (
      <Space>
        <Button
          className="font-[PingFang SC] mr-[6px] w-[74px] rounded text-[13px] font-medium leading-5"
          onClick={() => {
            onCancel && onCancel()
          }}
        >
          取消
        </Button>
        <Button
          type="primary"
          className="font-[PingFang SC] w-[74px] rounded text-[13px] font-medium leading-5"
          loading={OKBtnLoading}
          onClick={() => {
            onOK && onOK()
          }}
        >
          确认
        </Button>
      </Space>
    )
  }
  if (!scenarioId) {
    return <></>
  }
  return (
    <>
      {/* 替换模型确认 */}
      <Modal
        open={replaceMetricModelConfirmOpen}
        onCancel={() => {
          setReplaceMetricModelConfirmOpen(false)
        }}
        className="grey-modal-footer"
        title={<div className="pl-5">确认</div>}
        footer={
          <div className="rounded-b bg-[#F4F4F4] px-5 py-3 dark:bg-gray-800">
            {renderFooterButtons({
              onCancel() {
                setReplaceMetricModelConfirmOpen(false)
              },
              onOK() {
                updateScenario()
              },
              OKBtnLoading: updateScenarioLoading,
            })}
          </div>
        }
      >
        <div className="mb-[40px] mt-[20px] flex items-center justify-center">
          <SvgIcon icon={warningIcon} className="mr-4 h-6 w-6" />
          <div>
            <p>替换模型可能造成已有指标无法查询</p>
            <p>请确认后替换</p>
          </div>
        </div>
      </Modal>
      <Modal
        width={780}
        onCancel={() => {
          onClose && onClose()
        }}
        className="grey-modal-footer"
        title={<span className="ml-3">替换模型</span>}
        open={open}
        footer={
          <div className="rounded-b bg-[#F4F4F4] px-5 py-3 dark:bg-gray-800">
            {renderFooterButtons({
              onCancel() {
                onClose && onClose()
              },
              onOK() {
                setReplaceMetricModelConfirmOpen(true)
              },
            })}
          </div>
        }
      >
        <div className="flex justify-between px-4 pb-3 pt-2 align-middle">
          <div>{`共${data?.list?.length || 0}个`}</div>
          <div className="w-[200px]">
            <Input.Search placeholder="指标模型名称" allowClear onSearch={onSearch} />
          </div>
        </div>
        <Table
          loading={loading}
          className="px-2"
          dataSource={showList}
          rowKey="name"
          pagination={{
            defaultPageSize: 8,
          }}
          columns={[
            {
              title: '指标模型',
              dataIndex: 'name',
              render(name) {
                return (
                  <div className="flex items-center">
                    <Radio
                      checked={selectModelNames.includes(name)}
                      onClick={() => {
                        setSelectMetricModelNames([name])
                      }}
                    />
                    <span>{name}</span>
                    <div
                      className="flex cursor-pointer items-center text-[#503CE4]"
                      onClick={() => {
                        setPreviewName(name)
                        setDrawerOpen(true)
                      }}
                    >
                      <EyeOutlined className="ml-[16px] mr-[6px]" />
                      <span>预览</span>
                    </div>
                  </div>
                )
              },
            },
            {
              title: '创建时间',
              dataIndex: 'createTime',
              width: 200,
              render(createTime: number) {
                return <>{createTime ? dayjs(createTime).format('YYYY-MM-DD HH:mm:ss') : '-'}</>
              },
            },
          ]}
        />
      </Modal>
      <ReadingMetricModelDrawer
        open={drawerOpen}
        name={previewName}
        onClose={() => {
          setDrawerOpen(false)
        }}
      />
    </>
  )
}
