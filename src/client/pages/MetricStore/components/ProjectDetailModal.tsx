import React, { useEffect, useState } from 'react'
import { Button, Input, Modal, message, Empty, Skeleton } from 'antd'
import axios from 'axios'
import { useRequest } from 'ahooks'
import { askBIApiUrls } from 'src/shared/url-map'
import { type ProjectType } from 'src/shared/metric-types'

type PropsType = {
  projectId: string
  open: boolean
  onClose: () => void
}
export default function ProjectDetailModal(props: PropsType) {
  const { projectId, open, onClose } = props
  const [nameValue, setNameValue] = useState('')
  const [descriptionValue, setDescriptionValue] = useState('')
  const [isEdit, setIsEdit] = useState(false)

  const {
    loading: detailLoading,
    data,
    run: getProjectDetail,
  } = useRequest(
    async () => {
      if (projectId) {
        const res = await axios.get(askBIApiUrls.auth.project.rest, {
          params: {
            id: projectId,
          },
        })
        if (res.data.code !== 0) throw new Error(res.data.msg)
        return res.data.data
      }
      return null
    },
    {
      onSuccess(data) {
        setNameValue(data?.name || '')
        setDescriptionValue(data?.description || '')
      },
    },
  )
  useEffect(() => {
    if (open) {
      getProjectDetail()
    }
  }, [open, getProjectDetail])

  const { loading: updateLoading, run: updateProject } = useRequest(
    async () => {
      return axios.put<{ name: string; description: string }, ProjectType>(askBIApiUrls.auth.project.rest, {
        id: projectId,
        name: nameValue,
        description: descriptionValue,
      })
    },
    {
      manual: true,
      onSuccess(data) {
        setIsEdit(false)
        message.success('修改成功')
        setNameValue(data?.name || '')
        setDescriptionValue(data?.description || '')
      },
    },
  )

  function cancelUpdate() {
    setIsEdit(false)
    setNameValue(data?.name || '')
    setDescriptionValue(data?.description || '')
  }

  function renderFooterButtons() {
    if (isEdit) {
      return (
        <>
          <Button
            className="font-[PingFang SC] mr-[6px] w-[74px] rounded text-[13px] font-medium leading-5"
            onClick={cancelUpdate}
          >
            取消
          </Button>
          <Button
            type="primary"
            className="font-[PingFang SC] w-[74px] rounded text-[13px] font-medium leading-5"
            loading={updateLoading}
            onClick={() => {
              updateProject()
            }}
          >
            保存
          </Button>
        </>
      )
    }

    return (
      <>
        <Button
          className="font-[PingFang SC] mr-[6px] w-[74px] rounded text-[13px] font-medium leading-5"
          onClick={() => setIsEdit(true)}
        >
          编辑
        </Button>
        <Button
          className="font-[PingFang SC] w-[74px] rounded text-[13px] font-medium leading-5"
          onClick={() => {
            onClose && onClose()
            cancelUpdate()
          }}
        >
          关闭
        </Button>
      </>
    )
  }

  return (
    <Modal
      onCancel={() => {
        onClose && onClose()
      }}
      className="grey-modal-footer"
      title={<div className="pl-5">项目详情</div>}
      open={open}
      footer={<div className="rounded-b bg-[#F4F4F4] px-5 py-3 dark:bg-gray-800">{renderFooterButtons()}</div>}
    >
      {detailLoading ? (
        <Skeleton active />
      ) : (
        <>
          {data ? (
            <div className="p-5">
              <div className="mb-2 flex justify-between align-middle">
                <div className="font-[PingFang SC] text-[13px] font-medium leading-5 text-[#A2A2A2]">项目名称</div>
              </div>
              {!isEdit && <div className="mb-4">{nameValue || '-'}</div>}
              {isEdit && (
                <Input
                  value={nameValue}
                  className="mb-4"
                  onChange={(e) => {
                    setNameValue(e.target.value)
                  }}
                />
              )}
              <div className="mb-2 flex justify-between align-middle">
                <div className="font-[PingFang SC] text-[13px] font-medium leading-5 text-[#A2A2A2]">描述</div>
              </div>
              {!isEdit && <div className="mb-2">{descriptionValue || '-'}</div>}
              {isEdit && (
                <Input
                  value={descriptionValue || ''}
                  onChange={(e) => {
                    setDescriptionValue(e.target.value)
                  }}
                />
              )}
            </div>
          ) : (
            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
          )}
        </>
      )}
    </Modal>
  )
}
