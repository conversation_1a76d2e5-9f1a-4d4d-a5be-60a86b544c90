import React, { useEffect } from 'react'
import { useRequest } from 'ahooks'
import { Drawer, Form } from 'antd'
import MetricModelTable from 'src/client/pages/MetricStore/components/MetricModelTable/MetricModelTable'
import DetailTableHeader from 'src/client/pages/MetricStore/components/MetricModelTable/DetailTableHeader'
import { formatMetricModelMetaToTable } from 'src/client/pages/MetricStore/components/MetricModelTable/conf'
import request from 'src/shared/xengine-axios'
import { MetricModelType } from 'src/shared/metric-types'
import { askBIApiUrls } from 'src/shared/url-map'

type PropsType = { name: string; open: boolean; onClose?: () => void }
export default function ReadingMetricModelDrawer(props: PropsType) {
  const { open, name, onClose } = props
  const {
    loading,
    data,
    run: getMetricModel,
  } = useRequest(async () => {
    if (name) {
      return request.get<{ name: string }, MetricModelType>(askBIApiUrls.model.meta, {
        params: {
          name,
        },
      })
    }
  })
  useEffect(() => {
    if (open) {
      getMetricModel()
    }
  }, [open, getMetricModel])
  const [timeColumnForm] = Form.useForm()

  if (!name) {
    return <></>
  }
  return (
    <Drawer
      placement="bottom"
      height="calc(100vh - 20px)"
      open={open}
      onClose={() => {
        onClose && onClose()
      }}
    >
      <DetailTableHeader
        data={data as MetricModelType}
        className="mt-4"
        onUpdateMetricModelSuccess={getMetricModel}
        onReplaceMetricModelModalSuccess={getMetricModel}
      />
      <MetricModelTable
        timeColumnForm={timeColumnForm}
        VTableInfo={{
          catalog: data?.catalogName || '',
          database: data?.databaseName || '',
          tableName: data?.name || '',
        }}
        initMetricModelTableData={formatMetricModelMetaToTable(data as MetricModelType)}
        loading={loading}
      />
    </Drawer>
  )
}
