import { MagnifyingGlassIcon } from '@heroicons/react/24/outline'
import { Button, Table, TableProps, Popconfirm, Form, Input, App, Upload, notification, UploadProps } from 'antd'
import dayjs from 'dayjs'
import { EditOutlined, EyeOutlined } from '@ant-design/icons'
import { useBoolean, useRequest } from 'ahooks'
import axios from 'axios'
import { useAtomValue } from 'jotai'
import React, { useState } from 'react'
import AdminCard from 'src/client/components/AdminCard'
import { ExternalEntityType } from 'src/shared/constants'
import AdminPage from 'src/client/components/AdminPage'
import { APIResponse } from 'src/shared/common-types'
import { askBIApiUrls } from 'src/shared/url-map'
import { ExternalReport } from 'src/shared/metric-types'
import { currentDatasetAtom } from '../../AskBI/askBIAtoms'
import { useCreateEdit } from './components/useCreateEdit'

export default function Management() {
  const { message } = App.useApp()
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([])
  const [form] = Form.useForm()
  const currentDataset = useAtomValue(currentDatasetAtom)
  const { data, loading, refresh } = useRequest(
    async () => {
      if (!currentDataset?.sceneId) throw 'unknown scene'
      const res = await axios.get<
        APIResponse<{
          list: ExternalReport[]
          total: number
        }>
      >(askBIApiUrls.externalReport.list(currentDataset?.sceneId ?? ''))
      if (res.data.code === 0) {
        return res.data.data
      } else {
        throw new Error(res.data.msg)
      }
    },
    {
      refreshDeps: [currentDataset],
      onError: (err: any) => {
        if (err !== 'unknown scene') {
          console.info(err)
        }
      },
    },
  )
  const { node: CreateEditNode, open } = useCreateEdit({ refresh })
  const columns: TableProps<ExternalReport>['columns'] = [
    {
      title: '报表ID',
      dataIndex: 'name',
      width: 120,
    },
    {
      title: '报表中文名',
      dataIndex: 'label',
      width: 120,
    },
    {
      title: '同义词',
      dataIndex: 'synonyms',
      width: 320,
      render(synonyms: string[], row) {
        return (
          <div className="flex gap-2">
            <div className="w-full truncate">{synonyms.join(',')}</div>
            <div className="flex flex-col items-center gap-2">
              <EyeOutlined
                className="cursor-pointer"
                onClick={() => open({ curMode: 'edit', synonymsMode: true, disabled: true, data: row })}
              />
              <EditOutlined
                className="cursor-pointer"
                onClick={() => open({ curMode: 'edit', synonymsMode: true, data: row })}
              />
            </div>
          </div>
        )
      },
    },
    {
      dataIndex: 'type',
      title: '类型',
      width: 120,
      ellipsis: true,
      render(data) {
        return <div>{ExternalEntityType.find((v) => v.value === data)?.label ?? 'unknown'}</div>
      },
    },
    {
      dataIndex: 'description',
      title: '描述',
      ellipsis: true,
      width: 320,
    },
    {
      dataIndex: 'createdAt',
      title: '创建时间',
      width: 120,
      render(data) {
        return dayjs(data).format('YYYY-MM-DD hh:mm:ss')
      },
    },
    {
      title: '操作',
      width: 120,
      render(_, row) {
        return (
          <div>
            <Popconfirm
              title={`确定删除${row.label}吗？`}
              onConfirm={async () =>
                axios.delete(askBIApiUrls.externalReport.delete(row.id)).then(() => {
                  message.success('删除成功')
                  refresh()
                })
              }
              okText="确定"
              cancelText="取消"
            >
              <Button type="link" danger>
                删除
              </Button>
            </Popconfirm>
            <Button
              type="link"
              onClick={() => {
                open({
                  curMode: 'edit',
                  data: row,
                })
              }}
            >
              编辑
            </Button>
          </div>
        )
      },
    },
  ]
  const search = Form.useWatch('search', form)
  const [notificationApi, notificationContextHolder] = notification.useNotification()
  const [uploading, uploadingOps] = useBoolean(false)
  function createUploadNode({
    action,
    label,
    method,
  }: {
    action: string
    label: string
    method?: UploadProps['method']
  }) {
    return (
      <Upload
        data={{ semanticProjectId: currentDataset?.projectId, semanticSceneId: currentDataset?.sceneId }}
        showUploadList={false}
        accept=".xlsx"
        method={method}
        action={action}
        onChange={({ file }) => {
          uploadingOps.set(file.status === 'uploading')
          if (file.status === 'done') {
            if (file.response) {
              const { code, msg } = file.response
              if (code === 500) {
                notificationApi.error({
                  message: `${label}指标失败`,
                  description: msg,
                })
              } else {
                notificationApi.success({
                  message: `${label}指标成功`,
                })
                refresh()
              }
            }
          }
        }}
      >
        <Button className="ml-1" type="primary" loading={uploading}>
          从XLSX文件{label}指标
        </Button>
      </Upload>
    )
  }
  return (
    <AdminPage title="外部报表管理">
      <AdminCard>
        <div className="mb-4 flex items-center justify-between">
          <div className="flex gap-[4px]">
            <div className="font-bold">全部({data?.total ?? 0})</div>
          </div>
          <div className="flex gap-[4px]">
            <Form form={form}>
              <Form.Item className="mb-0" name="search">
                <Input placeholder="搜索" prefix={<MagnifyingGlassIcon className="h-[18px] w-[18px]" />} />
              </Form.Item>
            </Form>
            <Popconfirm
              title={`确定删除这${selectedRowKeys.length}项吗？`}
              onConfirm={async () => {
                return axios
                  .delete(askBIApiUrls.externalReport.deleteBatch, { data: { ids: selectedRowKeys } })
                  .then(() => {
                    message.success('删除成功')
                    setSelectedRowKeys([])
                    refresh()
                  })
                  .catch((err) => {
                    message.error(err?.toString() ?? '删除失败')
                  })
              }}
              okText="确定"
              cancelText="取消"
            >
              <Button danger disabled={!selectedRowKeys.length}>
                批量删除
              </Button>
            </Popconfirm>
            <Button type="primary" onClick={() => open()}>
              创建外部实体
            </Button>
            {createUploadNode({ action: askBIApiUrls.externalReport.createFromFile, label: '创建' })}
            {createUploadNode({ action: askBIApiUrls.externalReport.updateFromFile, label: '更新', method: 'POST' })}
          </div>
        </div>
        <Table
          className="w-full"
          loading={loading}
          dataSource={(data?.list ?? []).filter(
            (v) =>
              !search ||
              v.name.includes(search) ||
              v.label.includes(search) ||
              v.synonyms.some((item) => item.includes(search)),
          )}
          columns={columns}
          rowKey="id"
          rowSelection={{
            type: 'checkbox',
            selectedRowKeys,
            onChange(selectedRowKeys) {
              setSelectedRowKeys(selectedRowKeys)
            },
          }}
        />
        {CreateEditNode}
        {notificationContextHolder}
      </AdminCard>
    </AdminPage>
  )
}
