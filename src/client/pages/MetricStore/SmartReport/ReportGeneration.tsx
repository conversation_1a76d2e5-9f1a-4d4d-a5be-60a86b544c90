/* eslint-disable react-hooks/exhaustive-deps */
import {
  App,
  <PERSON><PERSON>,
  <PERSON>lapse,
  DatePicker,
  Flex,
  Form,
  Input,
  Layout,
  message,
  Popover,
  Progress,
  Select,
  Space,
  Spin,
} from 'antd'
import React, { useEffect, useRef, useState } from 'react'
import {
  CaretRightOutlined,
  EditOutlined,
  MenuUnfoldOutlined,
  MinusCircleOutlined,
  PlusCircleOutlined,
  PlusOutlined,
} from '@ant-design/icons'
import { useAtom } from 'jotai'
import axios from 'axios'
import dayjs from 'dayjs'
import TextArea from 'antd/es/input/TextArea'
import { Content } from 'antd/es/layout/layout'
import Sider from 'antd/es/layout/Sider'
import { useRequest } from 'ahooks'
import clsx from 'clsx'
import { useNavigate, useSearchParams } from 'react-router-dom'
import AdminCard from 'src/client/components/AdminCard'
import AdminPage from 'src/client/components/AdminPage'
import './ReportGeneration.css'
import { askBIApiUrls, askDocApiUrls } from 'src/shared/url-map'
import { DOC_REPORT_TIMEOUT } from 'src/shared/constants'
import { Scenario } from 'src/shared/metric-types'
import {
  DataOperatorType,
  OutlineItemType,
  ReportDetailType,
  SectionConfigType,
  TextOperatorType,
} from 'src/shared/common-types'
import { currentReportTemplateAtom } from '../../AskBI/askBIAtoms'
import InfoItem from './components/InfoItem'
import ParagraphConfig from './components/ParagraphConfig'
import TextPreview from './components/TextPreview'
import OutlineTree from './components/OutlineTree'

interface Option {
  code: string
  name: string
}

interface OptionsList {
  options: Option[]
}

interface ColumnClassifyType {
  metrics: Option[]
  dimensions: Option[]
  timeDimensions: Option[]
}

interface SectionOperators {
  dataOp: DataOperatorType[]
  textOp: TextOperatorType[]
}

export default function ReportGeneration() {
  const [form] = Form.useForm()
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const reportId = searchParams.get('reportId') || undefined
  const templateId = searchParams.get('id') || undefined
  const reportGeneration = searchParams.get('generation') === 'true'
  const viewTemplate = searchParams.get('viewTemplate') === 'true'
  const editTemplate = searchParams.get('editTemplate') === 'true'
  const { message: antdMessage } = App.useApp()
  const [collapsed, setCollapsed] = useState(false)
  const [filterTypes, setFilterTypes] = useState<number[]>([])
  const [selectFieldsOptions, setSelectFieldsOptions] = useState<OptionsList[]>([])
  const [columnValues, setColumnValues] = useState<{ [key: string]: string[] }>({})
  const [isShowOutlineEdit, setIsShowOutlineEdit] = useState<boolean>(false)
  const [sceneList, setSceneList] = useState<Scenario[]>([])
  const [selectScene, setSelectScene] = useState<Scenario>()
  const [columnClassify, setColumnClassify] = useState<ColumnClassifyType>()
  // 当前选中的报告模板
  const [currentReportTemplate, _setCurrentReportTemplate] = useAtom(currentReportTemplateAtom)
  const [currentReportDetail, setCurrentReportDetail] = useState<ReportDetailType | null>(null)
  const [reportOpList, setReportOpList] = useState<{ key: string; value: string }[]>([])
  const [currentModelName, setCurrentModelName] = useState<string>('')
  const [selectMetricsFields, setSelectMetricsFields] = useState<Option[]>([])
  const [selectDimensionsFields, setSelectDimensionsFields] = useState<Option[]>([])
  // 是否显示段落配置
  const [isShowParagraphConfig, setIsShowParagraphConfig] = useState<boolean>(false)
  // 大纲是否正在加载中
  const [isOutlineLoading, setIsOutlineLoading] = useState<boolean>(false)
  // 报告id
  const [currentReportId, setCurrentReportId] = useState<number | undefined>(reportId ? Number(reportId) : undefined)
  // 大纲列表内容
  const [currentOutlineContent, setCurrentOutlineContent] = useState<OutlineItemType[]>([])
  // 点击大纲列表内容
  const [clickOutlineContent, setClickOutlineContent] = useState<OutlineItemType>()
  // 全文预览内容
  const [fullTextPreview, setFullTextPreview] = useState<string>(
    (currentReportTemplate?.outline &&
      currentReportTemplate.outline.map((item: { content: string }) => item.content).join('<br/><br/>')) ||
      '',
  )

  // 保存报告loading
  // const [saveReportLoading, setSaveReportLoading] = useState<boolean>(false)
  // 导出报告loading
  const [exportReportLoading, setExportReportLoading] = useState<boolean>(false)
  // 码值筛选loading
  const [columnValueLoading, setColumnValueLoading] = useState<{
    [key: string]: boolean // 键是字符串，值是 boolean
  }>({})

  // 使用此模板生成报告按钮点击后，显示确认生成按钮
  const [isShowReportGeneration, setIsShowReportGeneration] = useState<boolean>(false)
  // 是否编辑模板
  // const [isEditTemplate, setIsEditTemplate] = useState<boolean>(editTemplate)
  const defaultIntentionValue =
    '请生成一份电信集团签约数据分析报告，从省份、行业的常用维度切入，给出关键数据分析展示，如有异常需要及时识别。'

  const [columnClassifyLoading, setColumnClassifyLoading] = useState<boolean>(false)
  const [currentSectionConfig, setCurrentSectionConfig] = useState<SectionConfigType>()
  const [activeKey, setActiveKey] = useState<string | string[]>(viewTemplate ? ['2'] : ['1', '2'])
  const [chapterIndex, setChapterIndex] = useState<number>(0)
  const [progress, setProgress] = useState<number>(0)
  const [sectionOperators, setSectionOperators] = useState<SectionOperators>()

  const [initDataLoading, setInitDataLoading] = useState<boolean>(false)
  const [saveTemplateLoading, setSaveTemplateLoading] = useState<boolean>(false)
  const currentDate = dayjs()
  // 计算最大日期
  const maxDate = currentDate.subtract(1, 'month')
  const templateReport = '9999' // 模版管理中固定的经分报告

  useEffect(() => {
    let timer: NodeJS.Timeout
    if (isOutlineLoading && progress < 100) {
      const interval = 180000 / 100 // 2 minutes divided by 100 steps
      timer = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 100) {
            clearInterval(timer)
            return 100
          }
          return prev + 1
        })
      }, interval)
    } else if (!isOutlineLoading) {
      setProgress(0)
    }

    return () => clearInterval(timer)
  }, [isOutlineLoading, progress])

  const { data: departmentValue, run: getDepartmentValue } = useRequest(
    async (modeName: string) => {
      const response = await axios.get(askBIApiUrls.reportGenerate.getDepartmentValue, {
        params: { modelName: modeName || currentModelName || 'dianxin_report_0819' },
      })
      if (response.data.data) {
        return response.data.data.valueList
      }
    },
    {
      manual: true,
      onError: (error: any) => {
        message.error(`获取行业失败${error.message}`)
        console.error('getDepartmentValue = error', error)
      },
    },
  )

  const { data: provinceValue, run: getProvinceValue } = useRequest(
    async (modeName: string) => {
      const response = await axios.get(askBIApiUrls.reportGenerate.getProvinceValue, {
        params: { modelName: modeName || currentModelName || 'dianxin_report_0819' },
      })
      if (response.data.data) {
        return response.data.data.valueList
      }
    },
    {
      manual: true,
      onError: (error: any) => {
        message.error(`获取省份失败${error.message}`)
        console.error('getDepartmentValue = error', error)
      },
    },
  )

  /**
   * 获取场景列表
   * @returns 场景列表
   */
  const getSceneList = async () => {
    const res = await axios.get(askBIApiUrls.auth.scene.list, {
      params: { current: 1, pageSize: 1000 },
      timeout: DOC_REPORT_TIMEOUT,
    })
    if (res && res.data.data) {
      const localSceneList = res.data.data.list || []
      setSceneList(localSceneList)
      return localSceneList
    }
    return []
  }

  /**
   * 获取报告详情
   * @returns 报告详情
   */
  const getReportDetail = async () => {
    try {
      const res = await axios.get(askBIApiUrls.reportGenerate.getReportDetail, {
        params: { reportId: currentReportId },
        timeout: DOC_REPORT_TIMEOUT,
      })
      return res?.data?.data
    } catch (error: any) {
      console.error('获取报告详情Error', error)
      antdMessage.error(`获取报告详情${error.message}`, 2)
    }
  }

  /**
   * 获取模版详情
   * @returns 报告详情
   */
  const getTemplateDetail = async (templateId: string) => {
    try {
      const res = await axios.get(askBIApiUrls.reportGenerate.templateDetail, {
        params: { templateId },
        timeout: DOC_REPORT_TIMEOUT,
      })
      return res?.data?.data
    } catch (error: any) {
      console.error('获取模版详情Error', error)
      antdMessage.error(`获取模版详情${error.message}`, 2)
    }
  }

  /**
   * 获取场景列表
   * 获取报告详情接口
   * 显示form表单数据
   * 显示大纲列表和全文预览
   */
  const { run: initDetail } = useRequest(
    async () => {
      // 获取场景列表
      setFullTextPreview('')
      const localSceneList = await getSceneList()
      // 当当前报告id存在时，获取报告详情 ，不存在时 直接return
      if (!templateId && !currentReportId) return
      if (templateId === templateReport) return
      setInitDataLoading(true)

      // 获取报告详情
      let detail = templateId ? await getTemplateDetail(templateId) : await getReportDetail()
      // 因为模板和报告详情其中有一个字段不一样，报告是reportInfo，模板是templateInfo，这里前端为了统一，都转为reportInfo，对逻辑不影响
      detail = templateId ? { ...detail, reportInfo: detail.templateInfo } : detail
      setInitDataLoading(false)
      if (detail) {
        if (detail.reportInfo) {
          await getColumnClassify(detail.reportInfo.modelName)
          const selectedScene = localSceneList.find((item: { id: string }) => item.id === detail.reportInfo.sceneId)
          setCurrentModelName(detail.reportInfo.modelName || '')
          if (selectedScene) {
            setSelectScene(selectedScene)
          }
        }

        let tempDataFilterParams = []
        if (detail.dataFilterParams) {
          const newOptionsList = [...selectFieldsOptions]
          tempDataFilterParams = detail.dataFilterParams.map(
            (
              item: {
                columnName: string
                columnCode: string
                operator: string
                filterType: number
                values: string[] | string
              },
              index: number,
            ) => {
              // 判断 operator 是否为 "in"
              if (item.operator === 'in') {
                item = { ...item, filterType: item?.filterType ?? 1 }
                getReportColumnValue(item.columnCode, index, detail.reportInfo.modelName)
              } else {
                item = { ...item, filterType: item.filterType ?? 2 }
                // 将 values 数组转为字符串
                if (Array.isArray(item.values)) {
                  item.values = item.values.join(',')
                }
              }

              // 业务口径筛选 选择字段值的回显
              newOptionsList[index] = { options: item.operator === 'in' ? selectDimensionsFields : selectMetricsFields }
              setSelectFieldsOptions(newOptionsList)
              return item
            },
          )
        }
        setFilterTypes((tempDataFilterParams || []).map((items: { filterType: number }) => items.filterType))
        const intention = templateId ? detail?.reportInfo?.templateIntention : detail?.reportInfo?.reportIntention
        form.setFieldsValue({
          dataFilterParams: tempDataFilterParams.length > 0 ? tempDataFilterParams : [{}],
          reportTitle: templateId ? (detail?.reportInfo?.templateTitle ?? '') : (detail?.reportInfo?.reportTitle ?? ''),
          reportIntention: intention || defaultIntentionValue,
          sceneId: detail?.reportInfo?.sceneId ?? undefined,
          timeDimension: detail?.dataTimeParams?.timeColumn ?? undefined,
          timeRangeStart: detail?.dataTimeParams?.timeRangeStart ?? undefined,
          timeRangeEnd: detail?.dataTimeParams?.timeRangeEnd ?? undefined,
          timeRange:
            detail?.dataTimeParams?.timeRangeStart && detail?.dataTimeParams?.timeRangeEnd
              ? [
                  dayjs(detail?.dataTimeParams?.timeRangeStart, 'YYYY-MM-DD'),
                  dayjs(detail?.dataTimeParams?.timeRangeEnd, 'YYYY-MM-DD'),
                ]
              : undefined,
          timeStr: detail?.dataTimeParams?.timeRangeStart ? dayjs(detail?.dataTimeParams?.timeRangeStart) : undefined,
        })

        // 大纲中的content要加换行
        const combinedContent = (detail.outlineNodes || [])
          .map((item: { content: string }) => item.content.replace(/<br\/><br\/>/g, '\n\n'))
          .join('\n\n')
        setFullTextPreview(combinedContent ?? '')
        setCurrentOutlineContent(detail.outlineNodes ?? [])
        setCurrentReportDetail(detail)
        if (localSceneList && detail.reportInfo) {
          const scene = localSceneList.find((item: { id: string }) => item.id === detail.reportInfo.sceneId)
          setSelectScene(scene || undefined) // 如果找不到scene，可以选择传递undefined
        }
      }
    },
    {
      onError: (error: any) => {
        setInitDataLoading(false)
        console.error('获取报告详情失败', error)
        antdMessage.error(`获取报告详情失败${error?.message}`)
      },
    },
  )

  /**
   * 处理筛选类型改变事件
   *
   * @param value 筛选类型值
   * @param index 筛选类型在数组中的索引
   * @returns 无返回值
   */
  const handleFilterTypeChange = (value: number, index: number) => {
    const newFilterTypes = [...filterTypes]
    newFilterTypes[index] = value
    setFilterTypes(newFilterTypes)

    const currentValues = form.getFieldValue('dataFilterParams') || []
    if (currentValues.length > 0) {
      currentValues[index].values = []
      currentValues[index].columnName = undefined
      currentValues[index].operator = currentValues[index].filterType === 2 ? null : currentValues[index].operator
      form.setFieldsValue({
        dataFilterParams: currentValues,
      })
    }

    const newOptionsList = [...selectFieldsOptions]
    newOptionsList[index] = { options: value === 1 ? selectDimensionsFields : selectMetricsFields }
    setSelectFieldsOptions(newOptionsList)
    setColumnValueLoading((prev) => ({
      ...prev,
      [index]: false,
    }))
  }

  /**
   * 获取运算符列表_new
   */
  const getFilterOperator = async () => {
    try {
      const response = await axios.get(askBIApiUrls.reportGenerate.dataFilterOperator)
      setReportOpList(response.data.data.opList || [])
    } catch (error) {
      console.error(error)
    }
  }

  useEffect(() => {
    getFilterOperator()
  }, [])

  /**
   * 获取维度码值_new
   */
  const getReportColumnValue = async (columnCode: string, index: number, modelName?: string) => {
    try {
      const response = await axios.get(askBIApiUrls.reportGenerate.getColumnValue, {
        params: {
          columnCode,
          modelName: currentModelName || modelName,
        },
        timeout: DOC_REPORT_TIMEOUT,
      })
      setColumnValueLoading((prev) => ({
        ...prev,
        [index]: false,
      }))
      setColumnValues((prev) => ({
        ...prev,
        [index]: response.data.data.valueList || [],
      }))
    } catch (error: any) {
      setColumnValueLoading((prev) => ({
        ...prev,
        [index]: false,
      }))
      antdMessage.error(error?.message)
      console.error('获取码值列表失败', error)
    }
  }

  /**
   * 获取指标模型字段分类情况
   * @param columnName
   * @param index
   */
  const getColumnClassify = async (columnName: string): Promise<ColumnClassifyType | null> => {
    try {
      if (!columnName) return null
      setColumnClassifyLoading(true)
      const response = await axios.get(askBIApiUrls.reportGenerate.getColumnClassify, {
        params: { modelName: columnName },
        timeout: DOC_REPORT_TIMEOUT,
      })
      const result = response.data.data

      if (result) {
        setColumnClassify(result)
        setSelectMetricsFields(result.metrics)
        setSelectDimensionsFields(result.dimensions)
        setColumnClassifyLoading(false)
      }
      return result
    } catch (error: any) {
      setColumnClassifyLoading(false)
      console.error('getColumnClassify', error)
      antdMessage.error(`获取字段分类失败${error.message}`, 2)
      return null
    }
  }

  // 选择字段
  const onSelectFieldsChange = (value: string, index: number) => {
    const tempFieldsOptions = selectFieldsOptions[index]?.options || []
    const selectedScene = tempFieldsOptions.find((item) => item.code === value)
    // 清空筛选条件
    const dataFilterParams = form.getFieldValue('dataFilterParams')
    // if (dataFilterParams && dataFilterParams[index].filterType === 1) {
    //   getReportColumnValue(value, index)
    // }
    setColumnValueLoading((prev) => ({
      ...prev,
      [index]: true,
    }))
    setColumnValues((prev) => ({
      ...prev,
      [index]: [],
    }))
    getReportColumnValue(value, index)
    dataFilterParams[index].values = []
    dataFilterParams[index].columnName = selectedScene?.name
    dataFilterParams[index].columnCode = selectedScene?.code
    form.setFieldsValue({
      dataFilterParams,
    })
  }

  const toggleCollapsed = () => {
    setCollapsed(!collapsed)
  }

  /**
   * 场景切换函数
   *
   * @param value 切换到的场景值
   * @returns 无返回值
   */
  const onSceneChange = (value: string, isGetDepartmentAndProvince: boolean = false) => {
    form.setFieldsValue({
      dataFilterParams: [{}],
    })
    const selectedScene = sceneList.find((item) => item.id.toString() === value)
    if (selectedScene) {
      const modelName = selectedScene.modelNames?.[0]

      console.info('切换场景', selectedScene)
      setSelectScene(selectedScene)
      setCurrentModelName(modelName)
      getColumnClassify(modelName)
      if (isGetDepartmentAndProvince) {
        getDepartmentValue(modelName)
        getProvinceValue(modelName)
      }
    }
  }

  /**
   * 通用的字段处理函数
   */
  const processFields = (fields: any) => {
    if (fields && fields.dataFilterParams && fields.dataFilterParams.length > 0) {
      fields.dataFilterParams.forEach(
        (column: { filterType: number; operator: string; values: string[]; columnName: string | undefined | null }) => {
          column.columnName = column.columnName || null
          if (column.filterType === 1) {
            // 如果 filterType 为 1，设置 operator 为 "in"
            column.operator = 'in'
          } else if (column.filterType === 2 && typeof column.values === 'string') {
            // 如果 filterType 为 2，将 values 转为数组
            column.values = [column.values]
          }

          // 判断只有一条数据的情况
          if (fields.dataFilterParams && fields.dataFilterParams.length === 1) {
            if (!column.values || column.values.length === 0 || !column.columnName) {
              fields.dataFilterParams = null // 只有一条数据时，values 或 columnName 为空则设置为 null
            }
          }
        },
      )

      // 多条数据时，删除掉 values 或 columnName 为空的项
      if (fields && fields.dataFilterParams && fields.dataFilterParams.length > 1) {
        fields.dataFilterParams = fields.dataFilterParams.filter(
          (column: { values: string[]; columnName: string | undefined | null }) =>
            column.values && column.values.length > 0 && column.columnName,
        )
      }
    }
    // if (selectTimeRange.length > 0) {
    //   fields.timeRangeStart = selectTimeRange[0]
    //   fields.timeRangeEnd = selectTimeRange[1]
    // }
  }

  /**
   * 保存报告 - 并生成大纲
   */
  const handleCreateDocReport = async () => {
    await form.validateFields()
    setIsOutlineLoading(true)
    const fields = form.getFieldsValue()
    processFields(fields)
    const timeRangeStart = fields.timeStr.startOf('month').format('YYYY-MM-DD') // 当月1号
    const timeRangeEnd = fields.timeStr.endOf('month').format('YYYY-MM-DD') // 当月最后一天
    try {
      const reqParams = {
        reportTitle: fields.reportTitle,
        reportIntention: fields.reportIntention || defaultIntentionValue,
        sceneId: selectScene?.id,
        templateId: Number(currentReportTemplate?.id) || Number(currentReportDetail?.reportInfo.templateId),
        reportId: currentReportId || currentReportDetail?.reportInfo.reportId || undefined, // 报告id ，第一次为空
        modelName: currentModelName,
        creator: currentReportTemplate?.createUser || currentReportDetail?.reportInfo.creator || '',
        dataFilterParams: JSON.stringify(fields.dataFilterParams) === '[{}]' ? undefined : fields.dataFilterParams,
        dataTimeParams: {
          timeColumn: fields.timeDimension || currentReportDetail?.dataTimeParams.timeColumn || '',
          timeRangeStart: timeRangeStart || currentReportDetail?.dataTimeParams.timeRangeStart || '2020-01-01',
          timeRangeEnd: timeRangeEnd || currentReportDetail?.dataTimeParams.timeRangeEnd || '2024-03-31',
        },
        ...(fields.languageStyle ? { languageStyle: fields.languageStyle } : {}),
        ...(fields.province ? { province: fields.province } : {}),
        ...(fields.department ? { department: fields.department } : {}),
      }
      // 生成大纲接口调用 - 渲染大纲列表和全文预览
      const res = await axios.post(
        templateId === templateReport
          ? askBIApiUrls.reportGenerate.authTemplateCreateReport
          : askBIApiUrls.reportGenerate.postOutlineCreate,
        reqParams,
        {
          timeout: DOC_REPORT_TIMEOUT,
        },
      )
      if (res.data.data && res.data.data.outline) {
        const outline = res.data.data.outline
        // const content = outline.map((item: { content: string }) => item.content).join('<br/>')
        const combinedContent = (outline || [])
          .map((item: { content: string }) => item.content.replace(/<br\/><br\/>/g, '\n\n'))
          .join('\n\n')
        setIsShowOutlineEdit(true)
        toggleCollapse('1')
        setCurrentReportId(res.data.data.reportId)
        setCurrentOutlineContent(outline)
        setFullTextPreview(combinedContent)
        setIsOutlineLoading(false)
        antdMessage.success(`生成成功`)
      }
    } catch (error: any) {
      console.error('生成失败', error.message)
      setIsOutlineLoading(false)
      antdMessage.error(`生成失败${error.message}`)
    }
  }

  // 动态切换展开/收起的面板
  const toggleCollapse = (key: string) => {
    if (Array.isArray(activeKey)) {
      if (activeKey.includes(key)) {
        setActiveKey(activeKey.filter((k) => k !== key))
      } else {
        setActiveKey([...activeKey, key])
      }
    } else {
      setActiveKey([key])
    }
  }

  /**
   * 保存报告
   */
  // const saveReport = async () => {
  //   try {
  //     setSaveReportLoading(true)
  //     await axios.post(
  //       askBIApiUrls.reportGenerate.saveReport,
  //       { reportId: currentReportId },
  //       {
  //         timeout: DOC_REPORT_TIMEOUT,
  //       },
  //     )
  //     antdMessage.success(`报告保存成功`)
  //     setSaveReportLoading(false)
  //     navigate(askBIPageUrls.metricStore.smartReport.reportManagement)
  //   } catch (error: any) {
  //     console.error(error)
  //     setSaveReportLoading(false)
  //     antdMessage.error(`报告保存失败${error.message}`)
  //   }
  // }

  /**
   * 导出报告
   */
  const exportReport = async (type: 'PDF' | 'Word') => {
    try {
      if (exportReportLoading) return
      setExportReportLoading(true)
      const res = await axios.get(askBIApiUrls.reportGenerate.exportReport, { params: { reportId: currentReportId } })
      if (res.data.data) {
        const { pdfUrl, wordUrl } = res.data.data
        antdMessage.success(`导出报告成功`)
        setExportReportLoading(false)
        window.location.href = askDocApiUrls.downloadFileProxy(encodeURIComponent(type === 'PDF' ? pdfUrl : wordUrl))
      }
    } catch (error: any) {
      console.error(error)
      setExportReportLoading(false)
      antdMessage.error(`导出报告失败${error.message}`)
    }
  }

  /**
   * 处理段落配置
   *
   * @param item 段落项
   * @param index 段落索引
   * @returns 无返回值
   */
  const handleSectionConfig = async (item: OutlineItemType, index: number) => {
    try {
      setClickOutlineContent(item)
      setIsShowParagraphConfig(true)
      setChapterIndex(index)
      const res = await axios.get(
        templateId ? askBIApiUrls.reportGenerate.templateSectionConfig : askBIApiUrls.reportGenerate.sectionConfig,
        {
          params: templateId ? { sectionId: item.id, templateId } : { sectionId: item.id, reportId: currentReportId },
        },
      )
      getSectionOperators(item.id)
      setCurrentSectionConfig(res.data.data)
    } catch (error: any) {
      antdMessage.error(`段落配置获取失败${error.message}`)
    }
  }

  /**
   * 获取模版段落算子详情
   */
  const getSectionOperators = async (sectionId: number) => {
    try {
      const response = await axios.get(askBIApiUrls.reportGenerate.getSectionOperators, {
        params: { sectionId, templateId },
        timeout: DOC_REPORT_TIMEOUT,
      })
      const result = response.data.data
      result && setSectionOperators(result)
    } catch (error: any) {
      antdMessage.error(`获取模版段落算子详情失败${error.message}`, 2)
    }
  }

  /**
   * 更新完段落后刷新
   * @param current
   * @returns
   */
  const handleFinishUpdateSection = () => {
    initDetail()
    handleSectionConfig(clickOutlineContent as OutlineItemType, chapterIndex)
  }

  /**
   * 保存模板
   */
  const handleSaveTemplate = async () => {
    try {
      await form.validateFields()
      setSaveTemplateLoading(true)
      const fields = form.getFieldsValue()
      processFields(fields)
      const timeRangeStart = fields.timeStr.startOf('month').format('YYYY-MM-DD') // 当月1号
      const timeRangeEnd = fields.timeStr.endOf('month').format('YYYY-MM-DD') // 当月最后一天
      const reqParams = {
        templateTitle: fields.reportTitle,
        templateIntention: fields.reportIntention,
        sceneId: selectScene?.id,
        templateId: Number(currentReportTemplate?.id) || Number(currentReportDetail?.reportInfo.templateId),
        modelName: currentModelName,
        creator: currentReportTemplate?.createUser || currentReportDetail?.reportInfo.creator || '',
        dataFilterParams: fields.dataFilterParams && fields.dataFilterParams.length ? fields.dataFilterParams : [],
        dataTimeParams: {
          timeColumn: fields.timeDimension || currentReportDetail?.dataTimeParams.timeColumn || '',
          timeRangeStart: timeRangeStart
            ? timeRangeStart
            : currentReportDetail?.dataTimeParams?.timeRangeStart || '2020-01-01',
          timeRangeEnd: timeRangeEnd ? timeRangeEnd : currentReportDetail?.dataTimeParams?.timeRangeEnd || '2024-03-31',
        },
      }
      await axios.post(askBIApiUrls.reportGenerate.saveTemplate, reqParams, { timeout: DOC_REPORT_TIMEOUT })
      antdMessage.success(`保存成功`)
      setSaveTemplateLoading(false)
      navigate(-1)
    } catch (error: any) {
      setSaveTemplateLoading(false)
      antdMessage.error(`保存失败${error.message}`)
    }
  }

  // 生成大纲
  const renderGenerateAnOutlineContent = () => {
    return (
      <div>
        {templateId !== templateReport && (
          <div className="mb-4 flex">
            <InfoItem
              label="创建人："
              value={currentReportTemplate?.createUser || currentReportDetail?.reportInfo?.creator || ''}
            />
            <InfoItem
              label="创建日期："
              value={currentReportTemplate?.createAt || currentReportDetail?.reportInfo?.createAt || ''}
            />
          </div>
        )}
        <Form
          form={form}
          initialValues={{
            dataFilterParams: [{}],
            timeStr: maxDate.endOf('month'),
          }}
        >
          <Form.Item
            name="reportTitle"
            label="报告标题"
            rules={[
              {
                required: true,
                message: '请输入报告标题',
              },
            ]}
          >
            <Input placeholder="签约分析报告" />
          </Form.Item>
          <Form.Item name="reportIntention" label="报告需求" className="ml-3">
            <TextArea placeholder={defaultIntentionValue} disabled />
          </Form.Item>

          {templateId === templateReport && (
            <>
              <Form.Item
                name="sceneId"
                label="场景"
                className="ml-6"
                rules={[
                  {
                    required: true,
                    message: '请选择场景',
                  },
                ]}
              >
                {/* {reportGeneration ? (
                  <span className="font-bold">
                    {sceneList.find((item) => item.id === form.getFieldValue('sceneId'))?.label}
                  </span>
                ) : ( */}
                <Select
                  allowClear
                  showSearch
                  placeholder="请选择场景"
                  options={sceneList.filter((item) => item.modelNames && item.modelNames.length > 0)}
                  fieldNames={{ label: 'label', value: 'id' }}
                  filterOption={(input, option) => {
                    return option?.label?.toLowerCase().includes(input.toLowerCase()) ?? false
                  }}
                  onChange={(value) => {
                    onSceneChange(value, true)
                  }}
                />
                {/* )} */}
              </Form.Item>
              <Form.Item name="department" label="选择行业" className="ml-2">
                <Select
                  placeholder="请选择行业"
                  options={(departmentValue || []).map((d: string) => ({ name: d }))}
                  fieldNames={{ label: 'name', value: 'name' }}
                />
              </Form.Item>
              <Form.Item name="province" label="选择省份" className="ml-2">
                <Select
                  placeholder="请选择省份"
                  options={(provinceValue || []).map((d: string) => ({ name: d }))}
                  fieldNames={{ label: 'name', value: 'name' }}
                />
              </Form.Item>
            </>
          )}

          <Space className="block">
            <p className="mb-3 mt-1 text-sm text-[#000000] dark:text-white">分析时间范围：</p>
            <Form.Item
              name="timeDimension"
              label="时间维度"
              className="ml-5"
              rules={[
                {
                  required: true,
                  message: '请选择时间维度',
                },
              ]}
            >
              <Select
                placeholder="请选择时间维度"
                options={columnClassify?.timeDimensions || []}
                fieldNames={{ label: 'name', value: 'name' }}
              />
            </Form.Item>

            {/* <Form.Item
              name="timeRange"
              label="时间范围"
              className="ml-5"
              initialValue={[dayjs('2020-01-01'), dayjs('2024-03-31')]}
              rules={[
                {
                  required: true,
                  message: '请选择时间范围',
                },
              ]}
            >
              <RangePicker format="YYYY-MM-DD" onChange={onRangeChange} />
            </Form.Item> */}
            <Form.Item name="timeStr" label="日期" className="ml-14">
              <DatePicker size="middle" picker="month" maxDate={maxDate} allowClear={false} />
            </Form.Item>
            {(templateId === templateReport || reportGeneration) && (
              <Form.Item
                name="languageStyle"
                label="语言风格"
                className="ml-5"
                rules={[
                  {
                    required: true,
                    message: '请选择语言风格',
                  },
                ]}
              >
                <Select
                  placeholder="请选择语言风格"
                  options={[{ name: '第一人称' }, { name: '第三人称' }]}
                  fieldNames={{ label: 'name', value: 'name' }}
                />
              </Form.Item>
            )}
            <p className="text-sx text-[#797979]">注意：生成的报告内容依赖于大模型内生知识及输入素材，请谨慎参考。</p>
          </Space>
          {templateId !== templateReport && (
            <Collapse
              className="data-input-collapse"
              bordered={false}
              expandIcon={({ isActive }) => <CaretRightOutlined rotate={isActive ? 90 : 0} />}
              items={[
                {
                  key: '1',
                  label: '查看数据输入：',
                  children: (
                    <Space className="ml-3 block">
                      <Space className="ml-8 block">
                        <p className="mb-2 text-xs font-medium text-[#101828] dark:text-white">数据来源</p>
                        <Form.Item
                          name="sceneId"
                          label="场景"
                          className={`ml-6 ${reportGeneration ? 'mb-0' : 'mb-2'}`}
                          rules={[
                            {
                              required: true,
                              message: '请选择场景',
                            },
                          ]}
                        >
                          {reportGeneration ? (
                            <span className="font-bold">
                              {sceneList.find((item) => item.id === form.getFieldValue('sceneId'))?.label}
                            </span>
                          ) : (
                            <Select
                              allowClear
                              showSearch
                              placeholder="请选择场景"
                              options={sceneList.filter((item) => item.modelNames && item.modelNames.length > 0)}
                              fieldNames={{ label: 'label', value: 'id' }}
                              filterOption={(input, option) => {
                                return option?.label?.toLowerCase().includes(input.toLowerCase()) ?? false
                              }}
                              onChange={(value) => {
                                onSceneChange(value, false)
                              }}
                            />
                          )}
                        </Form.Item>
                      </Space>
                      {selectScene && (
                        <Space className="ml-8 block">
                          <p className="-ml-3 mb-2 text-xs font-medium text-[#101828] dark:text-white">业务口径筛选</p>
                          <Form.List name="dataFilterParams">
                            {(fields, { add }) => (
                              <>
                                {fields.map(({ key, name, ...restField }, index) => (
                                  <div key={key} className="filterFormListItem">
                                    <Space className="flex w-full" align="baseline">
                                      <Form.Item
                                        name={[name, 'filterType']}
                                        className={`flex ${reportGeneration ? 'mb-0' : 'mb-2'}`}
                                        label="筛选类型"
                                      >
                                        {reportGeneration ? (
                                          <span className="font-bold">
                                            {
                                              ['普通筛选', '特殊筛选'][
                                                form.getFieldValue(['dataFilterParams', name, 'filterType']) - 1
                                              ]
                                            }
                                          </span>
                                        ) : (
                                          <Select
                                            options={[
                                              { label: '普通筛选', value: 1 },
                                              { label: '特殊筛选', value: 2 },
                                            ]}
                                            placeholder="请选择筛选类型"
                                            onChange={(value) => handleFilterTypeChange(value, index)}
                                          />
                                        )}
                                      </Form.Item>
                                      <Form.Item
                                        {...restField}
                                        name={[name, 'columnName']}
                                        label="选择字段"
                                        className={`${reportGeneration ? 'mb-0' : 'mb-2'}`}
                                      >
                                        {reportGeneration ? (
                                          <span className="font-bold">
                                            {form.getFieldValue(['dataFilterParams', name, 'columnName'])}
                                          </span>
                                        ) : (
                                          <Select
                                            loading={columnClassifyLoading}
                                            showSearch
                                            style={{ width: 200 }}
                                            placeholder="请选择字段"
                                            options={selectFieldsOptions[index]?.options || []}
                                            fieldNames={{ label: 'name', value: 'code' }}
                                            onChange={(value) => onSelectFieldsChange(value, index)}
                                          />
                                        )}
                                      </Form.Item>
                                    </Space>
                                    <Space className="flex items-baseline">
                                      {filterTypes[index] === 1 && (
                                        <Form.Item
                                          {...restField}
                                          name={[name, 'values']}
                                          className={`flex ${reportGeneration ? 'mb-0' : 'mb-2'}`}
                                          label="筛选条件"
                                        >
                                          {reportGeneration ? (
                                            <span className="font-bold">
                                              {form.getFieldValue(['dataFilterParams', name, 'values']).join(', ')}
                                            </span>
                                          ) : (
                                            <Select
                                              loading={columnValueLoading[`values_${index}`]}
                                              style={{ width: 530 }}
                                              mode="multiple"
                                              options={(columnValues[name] || []).map((item) => ({
                                                label: item,
                                                value: item,
                                              }))}
                                              placeholder="请选择筛选条件"
                                            />
                                          )}
                                        </Form.Item>
                                      )}
                                      {filterTypes[index] === 2 && (
                                        <Space>
                                          <Form.Item
                                            {...restField}
                                            name={[name, 'operator']}
                                            className={`flex ${reportGeneration ? 'mb-0' : 'mb-2'}`}
                                            label="筛选条件"
                                          >
                                            <Select
                                              style={{ width: 170 }}
                                              options={reportOpList.filter((option) => option.key !== 'in')}
                                              fieldNames={{ label: 'value', value: 'key' }}
                                              placeholder="请选择特殊筛选条件"
                                            />
                                          </Form.Item>
                                          <Form.Item {...restField} name={[name, 'values']}>
                                            <Input placeholder="请输入" style={{ width: 350 }} />
                                          </Form.Item>
                                        </Space>
                                      )}

                                      {!reportGeneration && filterTypes[index] && (
                                        <Space>
                                          <PlusCircleOutlined onClick={() => add()} />
                                          <MinusCircleOutlined
                                            onClick={() => {
                                              if (fields.length === 1) {
                                                form.setFieldsValue({ dataFilterParams: [{}] })
                                              } else {
                                                const arr = form.getFieldValue('dataFilterParams')
                                                const newArr = arr.filter((_: any, idx: number) => idx !== index)
                                                form.setFieldsValue({
                                                  dataFilterParams: newArr,
                                                })
                                                setFilterTypes(
                                                  newArr.map((items: { filterType: number }) => items.filterType),
                                                )
                                              }
                                            }}
                                          />
                                        </Space>
                                      )}
                                    </Space>
                                  </div>
                                ))}
                              </>
                            )}
                          </Form.List>
                        </Space>
                      )}
                    </Space>
                  ),
                },
              ]}
            />
          )}
          {(reportGeneration || reportId || isShowReportGeneration) && (
            <Space className="mb-2 mt-2 w-full border-t pt-3">
              <Button
                type="primary"
                block
                loading={isOutlineLoading}
                onClick={() => {
                  handleCreateDocReport()
                }}
              >
                确认生成
              </Button>
              <Button
                type="default"
                block
                onClick={() => {
                  navigate(-1)
                }}
              >
                取消
              </Button>
            </Space>
          )}
        </Form>
      </div>
    )
  }
  const outlineTreeRef = useRef<{
    addNodeAtBaseRoot: () => void
    editAllNode: () => void
  }>(null)

  return (
    <AdminPage
      title={editTemplate ? '模板编辑' : isShowReportGeneration ? '报告生成' : viewTemplate ? '查看模板' : '报告生成'}
      className="relative"
    >
      {currentReportId && (
        <div className="absolute right-0 mb-3">
          {/* <Button
            loading={saveReportLoading}
            className="mr-2 font-bold"
            type="primary"
            onClick={() => {
              saveReport()
            }}
          >
            保存报告
          </Button> */}
          <Popover
            placement="top"
            content={
              <div className="flex flex-col">
                <Button
                  type="text"
                  onClick={() => {
                    exportReport('PDF')
                  }}
                  className="px-1"
                >
                  导出为PDF
                </Button>
                <Button
                  type="text"
                  onClick={() => {
                    exportReport('Word')
                  }}
                  className="px-1"
                >
                  导出为Word
                </Button>
              </div>
            }
          >
            <Button className="font-bold" type="primary" loading={exportReportLoading}>
              导出至本地
            </Button>
          </Popover>
        </div>
      )}
      {!currentReportId && viewTemplate && (
        <div className="absolute right-0 mb-3">
          <Button
            className="mr-2 font-bold"
            type="primary"
            onClick={() => {
              setIsShowReportGeneration(true)
              setActiveKey(['1', '2'])
            }}
          >
            使用此模板生成报告
          </Button>
        </div>
      )}
      {editTemplate && (
        <div className="absolute right-0 mb-3">
          <Button
            className="mr-2 font-bold"
            loading={saveTemplateLoading}
            type="primary"
            onClick={() => {
              handleSaveTemplate()
            }}
          >
            保存模板
          </Button>
          <Button
            className="mr-2 font-bold"
            onClick={() => {
              navigate(-1)
            }}
          >
            退出
          </Button>
        </div>
      )}

      <AdminCard>
        <Flex gap="middle" vertical>
          <Spin spinning={initDataLoading}>
            <div className="report-generation max-h-full">
              <Collapse
                className="mb-4"
                expandIconPosition="end"
                activeKey={activeKey}
                onChange={(keys) => setActiveKey(keys)}
                items={[
                  {
                    key: '1',
                    label: (
                      <div className="flex items-center">
                        <p className="font-medium text-[#171717] dark:text-white">基础信息</p>
                        {isShowOutlineEdit && (
                          <Button className="ml-3 border-[#6A58EC] font-medium text-[#6A58EC]">编辑</Button>
                        )}
                      </div>
                    ),
                    children: <>{renderGenerateAnOutlineContent()}</>,
                  },
                  {
                    key: '2',
                    collapsible: 'disabled',
                    label: <p className="font-medium text-[#171717] dark:text-white">报告编辑</p>,
                    children: (
                      <Layout>
                        <Sider
                          width={300}
                          trigger={null}
                          collapsible
                          collapsed={collapsed}
                          collapsedWidth={40}
                          style={{ background: collapsed ? '#fff' : '#fafafa' }}
                        >
                          <div>
                            <div
                              className={clsx(
                                'flex h-11 items-center justify-between px-4 py-3',
                                !collapsed && 'border-b',
                              )}
                            >
                              {!collapsed && (
                                <p className="text-base font-medium text-[#101828]">
                                  {currentReportId ? '大纲' : '大纲（示例）'}
                                </p>
                              )}

                              {/* {editTemplate && (
                                <Button
                                  className="mr-2"
                                  onClick={() => {
                                    handleSaveTemplate()
                                  }}
                                >
                                  推荐大纲
                                </Button>
                              )} */}

                              <div className="cursor-pointer">
                                {!collapsed && editTemplate && (
                                  <>
                                    <PlusOutlined
                                      onClick={() => {
                                        outlineTreeRef.current?.addNodeAtBaseRoot?.()
                                      }}
                                    />
                                    <EditOutlined
                                      className="ml-2"
                                      onClick={() => {
                                        outlineTreeRef.current?.editAllNode?.()
                                      }}
                                    />
                                  </>
                                )}
                                <MenuUnfoldOutlined className="ml-2" onClick={toggleCollapsed} />
                              </div>
                            </div>
                            <div>
                              {isOutlineLoading && (
                                <div className="flex h-96 items-center justify-center">
                                  <Spin spinning={isOutlineLoading} />
                                </div>
                              )}
                              <div className={clsx((isOutlineLoading || collapsed) && 'hidden')}>
                                <OutlineTree
                                  couldEdit={editTemplate}
                                  handleSectionConfig={handleSectionConfig}
                                  ref={outlineTreeRef}
                                  currentOutlineContent={currentOutlineContent}
                                />
                              </div>
                            </div>
                          </div>
                        </Sider>
                        <Layout>
                          <Content className="bg-white">
                            <div className="flex w-full">
                              {isShowParagraphConfig && (
                                <div className="w-[600px]">
                                  <ParagraphConfig
                                    currentModelName={currentModelName}
                                    currentOutlineContent={currentOutlineContent}
                                    finishUpdate={handleFinishUpdateSection}
                                    setIsShowParagraphConfig={setIsShowParagraphConfig}
                                    currentSectionConfig={currentSectionConfig as SectionConfigType}
                                    sectionOperators={sectionOperators as SectionOperators}
                                    clickOutlineContent={clickOutlineContent as OutlineItemType}
                                    title={'段落预览'}
                                    outlineNodes={clickOutlineContent as OutlineItemType}
                                    chapterIndex={chapterIndex}
                                  />
                                </div>
                              )}
                              {isOutlineLoading ? (
                                <div className="flex h-96 w-full items-center justify-center">
                                  <Progress type="circle" percent={progress} />
                                </div>
                              ) : (
                                <TextPreview fullTextPreview={fullTextPreview} currentReportId={currentReportId} />
                              )}
                            </div>
                          </Content>
                        </Layout>
                      </Layout>
                    ),
                  },
                ]}
              />
            </div>
          </Spin>
        </Flex>
      </AdminCard>
    </AdminPage>
  )
}
