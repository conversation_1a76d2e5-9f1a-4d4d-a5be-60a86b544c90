import { useRequest } from 'ahooks'
import { App, But<PERSON>, DatePicker, Form, Result, Select, Space, Spin } from 'antd'
import axios from 'axios'
import clsx from 'clsx'
import { getDocument, PageViewport, PDFDocumentProxy } from 'pdfjs-dist'
import React, { useEffect, useState } from 'react'
import { useSearchParams } from 'react-router-dom'
import dayjs from 'dayjs'
import { askBIApiUrls, askDocApiUrls } from 'src/shared/url-map'

// 定义缩略图数据类型
interface Thumbnail {
  id: number
  image: string
}

export default function PPTView() {
  const [form] = Form.useForm()
  const { message: antdMessage } = App.useApp()
  const [pptData, setPptData] = useState<Thumbnail[]>([])
  const [activeItem, setActiveItem] = useState<any>()
  const [loading, setLoading] = useState<boolean>(false)
  const [searchParams] = useSearchParams()
  const id = searchParams.get('id') || undefined
  const [pptDetail, setPPTDetail] = useState<{ pdfUrl: string; pptUrl: string }>()
  const currentDate = dayjs()
  // 获取当前日期的日
  const currentDay = currentDate.date()
  // 计算最大日期
  const maxDate = currentDay < 22 ? currentDate.subtract(2, 'month') : currentDate.subtract(1, 'month')
  const provinceTemplate = '2'

  const provinceValue = [
    '北京',
    '青海',
    '上海',
    '广东',
    '浙江',
    '江苏',
    '宁夏',
    '新疆',
    '重庆',
    '福建',
    '陕西',
    '甘肃',
    '西藏',
    '江西',
    '湖北',
    '广西',
    '安徽',
    '贵州',
    '湖南',
    '海南',
    '四川',
    '云南',
    '吉林',
    '河南',
    '辽宁',
    '天津',
    '黑龙江',
    '内蒙古',
    '河北',
    '山西',
    '山东',
  ]

  const generateThumbnails = async (url: string, getDocumentFn: typeof getDocument): Promise<void> => {
    try {
      const pdf: PDFDocumentProxy = await getDocumentFn(url).promise
      const thumbnails: Thumbnail[] = []
      let isFirstPageRendered = false // 标记是否已经设置了第一张
      for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
        const currentPageNum = pageNum
        const page = await pdf.getPage(pageNum)
        const viewport: PageViewport = page.getViewport({ scale: 2 })
        const canvas = document.createElement('canvas')
        const context = canvas.getContext('2d')
        canvas.width = viewport.width
        canvas.height = viewport.height
        canvas.style.width = '100%'
        canvas.style.height = '100%'

        if (context) {
          await page.render({
            canvasContext: context,
            viewport: viewport,
          }).promise

          const thumbnailUrl = canvas.toDataURL('image/jpeg')
          thumbnails.push({ id: pageNum - 1, image: thumbnailUrl })

          // 更新状态，确保每次都正确追加
          setPptData((prevThumbnails: Thumbnail[]) => {
            const existingIds = prevThumbnails.map((thumb) => thumb.id)
            if (!existingIds.includes(currentPageNum - 1)) {
              return [...prevThumbnails, { id: currentPageNum - 1, image: thumbnailUrl }]
            }
            return prevThumbnails
          })

          // 设置第一张图片为默认激活项
          if (!isFirstPageRendered) {
            setActiveItem({ id: pageNum - 1, image: thumbnailUrl })
            isFirstPageRendered = true
          }
        }
      }

      setLoading(false)
    } catch (error) {
      console.error('Error generating thumbnails:', error)
      setLoading(false)
    }
  }
  /**
   * 获取缩略图列表
   */
  const getThumbnails = (previewPath: string) => {
    setLoading(true)

    generateThumbnails(askDocApiUrls.downloadFileProxy(encodeURIComponent(previewPath)), getDocument).catch(() => {
      console.error('Failed to generate thumbnails')
      antdMessage.error('Failed to generate thumbnails')
      setLoading(false)
    })
  }

  const { run: handlePPTGenerate } = useRequest(
    async () => {
      const fields = form.getFieldsValue()
      const formattedDate = fields.timeStr ? fields.timeStr.format('YYYY-MM') : null
      setLoading(true)
      const res = await axios.post(askBIApiUrls.pptGenerate.pptGenerate, {
        timeStr: formattedDate,
        templateId: id,
        ...(id === provinceTemplate ? { province: fields.province } : {}),
      })
      if (res.data.data) {
        setPPTDetail(res.data.data)
        getThumbnails(res.data.data.pdfUrl)
      } else {
        antdMessage.error(`结果为空，请联系管理员`)
      }
    },
    {
      onError: (error: any) => {
        setLoading(false)
        antdMessage.error(`修改失败: ${error?.message}`)
      },
      manual: true,
    },
  )

  useEffect(() => {
    if (form && id !== provinceTemplate) {
      handlePPTGenerate()
    }
  }, [form, handlePPTGenerate, id])

  /**
   * 处理下载PPT文件的函数
   */
  const handleDownloadPPT = () => {
    if (pptDetail && pptDetail.pptUrl) {
      window.location.href = askDocApiUrls.downloadFileProxy(encodeURIComponent(pptDetail.pptUrl))
    }
  }

  const handleLeftClick = (item: { id: number; image: string }) => {
    setActiveItem(item)
  }

  return (
    <div className="flex h-full w-full flex-col">
      <div className="my-3">
        <Form
          form={form}
          layout="inline"
          initialValues={{
            timeStr: maxDate.endOf('month'),
          }}
        >
          <Form.Item name="timeStr" label="日期">
            <DatePicker
              size="middle"
              picker="month"
              maxDate={maxDate}
              allowClear={false}
              onChange={() => {
                if (id === provinceTemplate) return
                handlePPTGenerate()
              }}
            />
          </Form.Item>
          {id === provinceTemplate && (
            <Form.Item name="province" label="选择省份">
              <Select
                style={{ width: 180 }}
                placeholder="请选择省份"
                options={(provinceValue || []).map((d: string) => ({ name: d }))}
                fieldNames={{ label: 'name', value: 'name' }}
              />
            </Form.Item>
          )}
          <Space className="flex gap-2">
            {id === provinceTemplate && (
              <Button
                type="primary"
                block
                loading={loading}
                onClick={async () => {
                  await form.validateFields()
                  handlePPTGenerate()
                }}
              >
                确认生成
              </Button>
            )}
            {pptDetail && pptDetail.pptUrl && (
              <Button className="ml-2" onClick={handleDownloadPPT}>
                下载
              </Button>
            )}
          </Space>
        </Form>
      </div>

      <div className="h-full w-full flex-1 flex-shrink-0 flex-grow bg-[#f5f5f5]">
        <Spin tip="PPT生成中..." size="large" spinning={loading} className="mt-[20%]">
          {loading ? null : pptData && pptData.length > 0 ? (
            <div className="flex h-full w-full">
              <div
                className="left w-48 flex-shrink-0 overflow-y-auto bg-[#FAFAFA] p-3"
                style={{ maxHeight: 'calc(100vh - 200px)' }}
              >
                {(pptData || []).map((item, index) => (
                  <div className="mb-6 flex" key={index} onClick={() => handleLeftClick(item)}>
                    <p className="mr-2 text-[#797979]">{index + 1}</p>
                    <div
                      className={clsx(
                        'flex h-[91px] w-[154px] cursor-pointer items-center justify-center rounded-lg',
                        activeItem.id === item.id && 'border-[2px] border-[#6A58EC]',
                      )}
                    >
                      <div className="h-[83px] w-[146px] rounded-lg border bg-white">
                        <img src={item.image} alt="" className="h-full w-fit rounded-lg" />
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <div className="right flex h-full flex-1 items-center justify-center">
                <div className="content h-[500px] w-[1000px] bg-white">
                  {activeItem && <img src={activeItem.image} alt="" className="h-full w-fit rounded-lg" />}
                </div>
              </div>
            </div>
          ) : (
            <div className="flex h-full w-full items-center justify-center pt-[20%]">
              <Result
                status={id === provinceTemplate ? 'warning' : 'error'}
                title={id === provinceTemplate ? 'PPT生成' : 'PPT生成失败'}
                subTitle={id === provinceTemplate ? '请选择省份后生成PPT' : 'PPT生成失败，请点击下面按钮重新生成'}
                extra={[
                  <Button type="primary" key="refresh" onClick={handlePPTGenerate}>
                    重新生成
                  </Button>,
                ]}
              />
            </div>
          )}
        </Spin>
      </div>
    </div>
  )
}
