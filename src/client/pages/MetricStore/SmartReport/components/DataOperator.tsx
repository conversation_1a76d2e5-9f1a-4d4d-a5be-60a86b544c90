import React from 'react'
import { <PERSON><PERSON>r, Space, Button, Select, Checkbox, Tag } from 'antd'
import { CheckboxChangeEvent } from 'antd/es/checkbox'
import type { SelectProps } from 'antd'
import clsx from 'clsx'
import { length } from '@shared/common-utils'
import { dataOperatorConfig, SvgIcon } from 'src/client/components/SvgIcon'
import { DataOperatorType, TextOperatorType } from 'src/shared/common-types'
type TagRender = SelectProps['tagRender']

// 数据算子 和 文本算子都用这个组件
function DataOperator(props: {
  value?: string[]
  onChange?: (value: string[]) => any
  optionValueKey: 'dataOpId' | 'textOpId'
  options: (DataOperatorType | TextOperatorType)[]
  onEditOperator?: (id: string) => void
  onCreateNewOperator?: () => void
  showConfigIcon?: boolean
  showCreateButton?: boolean
  closable?: boolean
  disabled?: boolean
}) {
  const {
    value,
    showConfigIcon,
    showCreateButton,
    closable,
    optionValueKey,
    onChange,
    options,
    onEditOperator,
    onCreateNewOperator,
    disabled = false,
  } = props
  const getId = (option: DataOperatorType | TextOperatorType): number => {
    return optionValueKey === 'dataOpId' ? (option as DataOperatorType).dataOpId : (option as TextOperatorType).textOpId
  }
  const triggerChangeEvent = (value: string[]) => {
    onChange?.(value)
  }

  const optionsList = options?.map((v) => {
    return {
      label: v.name,
      value: String(getId(v)),
    }
  })
  const indeterminate = length(value) > 0 && length(value) < length(optionsList)
  const onCheckAllChange = (e: CheckboxChangeEvent) => {
    onChange?.(e.target.checked ? optionsList.map((e) => e.value) : [])
  }

  const tagRender: TagRender = (tagProps) => {
    const { label: tagLabel, onClose: onTagClose, value: tagValue } = tagProps
    return (
      <Tag
        className={clsx(
          'm-[4px] flex items-center gap-[4px] rounded-[2px] p-[3px] px-[8px] text-[14px] font-medium text-[#101828]',
          disabled ? 'bg-white' : 'bg-[#F2F3F5]',
        )}
        closable={closable}
        onClose={onTagClose}
        style={{ marginInlineEnd: 4 }}
        bordered={false}
      >
        {tagLabel}
        <span
          className={clsx('cursor-pointer', showConfigIcon ? 'block' : 'hidden')}
          onMouseDown={(e: React.MouseEvent<HTMLElement>) => {
            e.preventDefault()
            e.stopPropagation()
            onEditOperator?.(tagValue)
            if (e.defaultPrevented) {
              return
            }
          }}
        >
          <SvgIcon icon={dataOperatorConfig} className="flex h-[14px] items-center dark:text-white" />
        </span>
      </Tag>
    )
  }

  return (
    <Select
      value={value?.map(String)}
      style={{ width: '100%' }}
      showSearch
      allowClear
      disabled={disabled}
      placeholder="输入筛选"
      optionRender={(optionItem) => {
        const { label, value: optionValue } = optionItem
        return (
          <div
            style={{
              overflow: 'hidden',
              display: 'flex',
              justifyContent: 'space-between',
            }}
          >
            <Checkbox checked={!!optionValue && (value || []).map(String)?.indexOf?.(String(optionValue)) >= 0}>
              <span>{label}</span>
            </Checkbox>
          </div>
        )
      }}
      onChange={triggerChangeEvent}
      tagRender={tagRender}
      mode="multiple"
      menuItemSelectedIcon={null}
      dropdownRender={(list) => {
        return (
          <>
            <Space
              style={{
                padding: '8px 8px 4px 12px',
                width: '100%',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              <Checkbox
                indeterminate={indeterminate}
                onChange={onCheckAllChange}
                checked={length(value) === length(optionsList)}
                className="text-[14px] font-medium"
              >
                全选
                <span className='ml-2 font-["PingFang_SC"] text-[12px] font-normal not-italic leading-[20px]'>
                  {length(value)} / {length(optionsList)}
                </span>
              </Checkbox>
              {showCreateButton && (
                <Button
                  type="primary"
                  ghost
                  onClick={() => {
                    onCreateNewOperator?.()
                  }}
                  size="small"
                >
                  创建新算子
                </Button>
              )}
            </Space>
            <Divider style={{ margin: '4px 0' }} />
            {list}
            <Divider style={{ margin: '4px 0' }} />
            <Space className="flex justify-end">
              <Button
                htmlType="button"
                onClick={() => {
                  onChange?.([])
                }}
              >
                重置
              </Button>
            </Space>
          </>
        )
      }}
      options={optionsList}
    />
  )
}

export default DataOperator
