import React from 'react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeRaw from 'rehype-raw'

interface Props {
  title?: string
  className?: string
  fullTextPreview: string
  currentReportId?: number | undefined
}
export default function TextPreview(props: Props) {
  const { className, fullTextPreview, title, currentReportId } = props
  return (
    <div className="w-full flex-1 overflow-y-auto px-4">
      <div className="flex h-11 items-center justify-between border-b">
        <p className="text-base font-medium text-[#101828]">
          {title ? title : currentReportId ? '全文预览' : '全文预览（预览）'}
        </p>
      </div>
      <div className={`askbi-markdown py-3 ${className}`}>
        <ReactMarkdown remarkPlugins={[remarkGfm]} rehypePlugins={[rehypeRaw]}>
          {fullTextPreview}
        </ReactMarkdown>
      </div>
    </div>
  )
}
