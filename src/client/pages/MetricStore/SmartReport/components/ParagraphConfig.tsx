import { CloseOutlined } from '@ant-design/icons'
import { App, Button, Form, InputNumber, Space } from 'antd'
import TextArea from 'antd/es/input/TextArea'
import React, { useEffect, useState } from 'react'
import axios from 'axios'
import { useSearchParams } from 'react-router-dom'
import { DataOperatorType, OutlineItemType, SectionConfigType, TextOperatorType } from 'src/shared/common-types'
import { askBIApiUrls } from 'src/shared/url-map'
import TextPreview from './TextPreview'
import DataOperator from './DataOperator'
import TextOperatorModal from './TextOperatorModal'
import DataOperatorModal from './DataOperatorModal'

interface Props {
  currentModelName: string
  chapterIndex: number
  currentSectionConfig: SectionConfigType
  isShowEditor?: boolean
  title?: string
  outlineNodes: OutlineItemType
  currentOutlineContent: OutlineItemType[]
  sectionOperators: { dataOp: DataOperatorType[]; textOp: TextOperatorType[] }
  clickOutlineContent: OutlineItemType
  setIsShowParagraphConfig: (value: boolean) => void
  finishUpdate: () => void
}

/**
 * 段落配置组件
 *
 * @param props 组件属性
 * @returns 段落配置组件的React元素
 */
export default function ParagraphConfig(props: Props) {
  const {
    currentModelName,
    setIsShowParagraphConfig,
    outlineNodes,
    currentSectionConfig,
    finishUpdate,
    currentOutlineContent,
    sectionOperators,
    clickOutlineContent,
  } = props
  const [form] = Form.useForm()
  const { message: antdMessage } = App.useApp()
  const [updateSectionLoading, setUpdateSectionLoading] = useState<boolean>(false)
  const [saveSectionLoading, setSaveSectionLoading] = useState<boolean>(false)
  const [searchParams] = useSearchParams()
  const templateId = searchParams.get('id') || undefined
  const viewTemplate = searchParams.get('viewTemplate') === 'true'
  const reportGeneration = searchParams.get('generation') === 'true'
  // 文本算子弹窗
  const [textOperatorModal, setTextOperatorModal] = useState<boolean>(false)
  // 数据算子弹窗
  const [dataOperatorModal, setDataOperatorModal] = useState<boolean>(false)
  const [textOperatorDetail, setTextOperatorDetail] = useState<TextOperatorType | null>(null)
  // const [textOperatorDetail, setTextOperatorDetail] = useState<TextOpType | null>(null)
  const [dataOperatorDetail, setDataOperatorDetail] = useState<DataOperatorType | null>(null)
  const [currentSectionPreview, setCurrentSectionPreview] = useState<string>('')

  useEffect(() => {
    // 当 currentSectionConfig 更新时，设置表单的 initialValues
    if (currentSectionConfig && form) {
      form.setFieldsValue({
        sectionIntention: currentSectionConfig?.sectionIntention,
        maxWordLen: currentSectionConfig?.maxWordLen,
        minWordLen: currentSectionConfig?.minWordLen,
        textOpList: currentSectionConfig?.textOp?.map((op) => op.textOpId),
        dataOpList: currentSectionConfig?.dataOp?.map((op) => op.dataOpId),
      })
    } else {
      form.resetFields()
      setCurrentSectionPreview('')
    }
  }, [currentSectionConfig, form])

  /**
   * 更新段落 / 保存段落配置
   */
  const updateSectionConfig = async (isPreview: boolean) => {
    try {
      const values = form.getFieldsValue()
      const reqData = {
        ...values,
        dataOpList: values.dataOpList?.map((id: string) => Number(id)),
        textOpList: values.textOpList?.map((id: string) => Number(id)),
        templateId: Number(templateId),
        sectionId: clickOutlineContent.id,
        isPreview,
      }
      const res = await axios.post(askBIApiUrls.reportGenerate.updateSection, reqData)
      if (res.data.data) {
        isPreview && setCurrentSectionPreview(res.data.data.content)
      }
      isPreview ? setUpdateSectionLoading(false) : setSaveSectionLoading(false)
      finishUpdate()
      antdMessage.success(isPreview ? '段落配置更新成功' : '段落配置保存成功')
    } catch (error: any) {
      isPreview ? setUpdateSectionLoading(false) : setSaveSectionLoading(false)
      antdMessage.error(`段落配置${isPreview ? '更新' : '保存'}失败${error.message}`)
    }
  }

  return (
    <div className="w-full flex-1 overflow-y-auto border-r px-4">
      <div className="flex h-11 items-center justify-between border-b">
        <p className="text-base font-medium text-[#101828]">段落配置</p>
        <CloseOutlined
          className="cursor-pointer"
          onClick={() => {
            setIsShowParagraphConfig(false)
          }}
        />
      </div>
      <div className="py-3">
        <Form form={form}>
          <Space className="flex items-center">
            <Form.Item name="minWordLen" label="字数要求">
              <InputNumber
                min={0}
                controls={false} // 隐藏上下按钮
                style={{ width: 100 }}
                placeholder="请输入"
                disabled={reportGeneration}
              />
            </Form.Item>
            <Form.Item label="">
              <p>-</p>
            </Form.Item>
            <Form.Item name="maxWordLen" label="">
              <InputNumber
                min={0}
                controls={false} // 隐藏上下按钮
                style={{ width: 100 }}
                placeholder="请输入"
                disabled={reportGeneration}
              />
            </Form.Item>
          </Space>
          <Form.Item name="sectionIntention" label="章节需求">
            <TextArea disabled={reportGeneration} />
          </Form.Item>
          <Form.Item name="dataOpList" label="数据算子">
            {/* 根据reportId来判断算子上的icon和创建算子按钮要不要显示 ，有reportId的，不显示，有id的，是模板的，显示。 */}
            <DataOperator
              disabled={reportGeneration}
              closable={!viewTemplate && !reportGeneration}
              showConfigIcon
              showCreateButton={!viewTemplate && !reportGeneration}
              optionValueKey="dataOpId"
              onCreateNewOperator={() => {
                setTextOperatorDetail(null)
                setDataOperatorModal(true)
              }}
              onEditOperator={(key) => {
                setDataOperatorModal(true)
                setDataOperatorDetail(
                  (sectionOperators?.dataOp || []).find((v) => String(v.dataOpId) === String(key)) || null,
                )
              }}
              options={sectionOperators?.dataOp}
            />
          </Form.Item>
          <Form.Item name="textOpList" label="文本算子">
            <DataOperator
              disabled={reportGeneration}
              closable={!viewTemplate && !reportGeneration}
              showConfigIcon
              showCreateButton={!viewTemplate && !reportGeneration}
              onCreateNewOperator={() => {
                setTextOperatorDetail(null)
                setTextOperatorModal(true)
              }}
              onEditOperator={(key) => {
                setTextOperatorModal(true)
                setTextOperatorDetail(
                  (sectionOperators?.textOp || []).find((v) => String(v.textOpId) === String(key)) || null,
                )
              }}
              options={sectionOperators?.textOp}
              optionValueKey="textOpId"
            />
          </Form.Item>
          {templateId && !viewTemplate && !reportGeneration && (
            <Space className="mb-2 mt-2 flex justify-end">
              <Button
                loading={updateSectionLoading}
                block
                onClick={() => {
                  setUpdateSectionLoading(true)
                  updateSectionConfig(true)
                }}
              >
                更新目标段落
              </Button>
              <Button
                loading={saveSectionLoading}
                type="primary"
                block
                onClick={() => {
                  setSaveSectionLoading(true)
                  updateSectionConfig(false)
                }}
              >
                保存
              </Button>
            </Space>
          )}
        </Form>
      </div>
      <div className="bg-[#F9FBFD]">
        <TextPreview
          className="h-[300px] overflow-y-auto"
          fullTextPreview={
            currentSectionPreview || (outlineNodes.content ? outlineNodes.content.replace(/<br\/><br\/>/g, '\n\n') : '')
          }
          title={'段落预览'}
        />
      </div>

      {textOperatorModal && (
        <TextOperatorModal
          updateSectionConfig={() => {
            finishUpdate()
          }}
          showTextOperatorModal={textOperatorModal}
          setTextOperatorModal={setTextOperatorModal}
          templateId={templateId || ''}
          sectionId={Number(outlineNodes.id)}
          currentSectionConfig={currentSectionConfig as SectionConfigType}
          currentOutlineContent={currentOutlineContent}
          textOperatorDetail={textOperatorDetail}
          setTextOperatorDetail={setTextOperatorDetail}
          sectionOperators={sectionOperators}
        />
      )}
      {dataOperatorModal && (
        <DataOperatorModal
          updateSectionConfig={() => {
            finishUpdate()
          }}
          currentModelName={currentModelName}
          showDataOperatorModal={dataOperatorModal}
          setDataOperatorModal={setDataOperatorModal}
          templateId={templateId || ''}
          sectionId={Number(outlineNodes.id)}
          dataOperatorDetail={dataOperatorDetail}
          setDataOperatorDetail={setDataOperatorDetail}
        />
      )}
    </div>
  )
}
