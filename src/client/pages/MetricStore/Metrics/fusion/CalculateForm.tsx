import { App, But<PERSON>, Col, Form, Input, Modal, Row, Select, Space, Typography, Checkbox } from 'antd'
import React, { useEffect, useState } from 'react'
import { MinusOutlined, PlusOutlined } from '@ant-design/icons'
import clsx from 'clsx'
import { useRequest } from 'ahooks'
import axios from 'axios'
import { get } from 'lodash-es'
import { askBIApiUrls } from 'src/shared/url-map'
import CustomerHiddenWrap from 'src/client/components/CustomerHiddenWrap'
import { customerIsSupportType } from 'src/shared/customer-resolver'
const Option = Select.Option

const modelValueUnFormat = (formattedData: any, type = 'measures') => {
  const unformattedData = (formattedData && formattedData[type] ? formattedData[type] : [])
    .filter((typeItem: any) => typeItem?.function?.function !== undefined)
    .map((typeItem: any) => {
      const functionType = typeItem.function.function
      const params = typeItem.function.params
      const createMetric = typeItem.createMetric !== false
      const filterSwitch = typeItem.filterSwitch !== false

      switch (functionType) {
        case 'COUNT':
        case 'SUM':
        case 'AVG':
        case 'MAX':
        case 'MIN':
        case 'COUNT_DISTINCT':
        case 'YEAR':
        case 'MONTH':
        case 'DATE': {
          return {
            createMetric,
            function: functionType,
          }
        }
        case 'CASE_WHEN': {
          return {
            function: 'CASE_WHEN',
            filterSwitch,
            params: params
              .filter((param: any) => param.condition?.condition)
              .map((param: any) => ({
                operator: get(param, 'condition.condition.operator'),
                value: get(param, 'condition.condition.params[1].value'),
                functionDesc: {
                  value: get(param, 'condition.functionDesc.params[0].value'),
                },
              })),
          }
        }
        case 'CONCAT': {
          return {
            function: 'CONCAT',
            filterSwitch,
            params: {
              columnName: params[1].value,
            },
          }
        }

        case 'LEFT': {
          return {
            function: 'LEFT',
            filterSwitch,
            params: {
              length: params[1].value,
            },
          }
        }
        case 'DATE_FORMAT': {
          return {
            function: 'DATE_FORMAT',
            createMetric,
            params: {
              value: params[1].value,
            },
          }
        }
        case 'DATE_SUB': {
          return {
            function: 'DATE_SUB',
            createMetric,
            params: {
              value: params[1].value,
              unit: params[2].value,
            },
          }
        }
        case 'SUB_STRING3': {
          return {
            function: 'SUB_STRING3',
            filterSwitch,
            params: {
              substringStartPosition: params[1].value,
              length: params[2].value,
            },
          }
        }
        default: {
          return {}
        }
      }
    })

  return { [type]: unformattedData }
}

const formValueFormat = (
  type: 'measures' | 'dimensions' = 'measures',
  formValue: any,
  columnInfo: {
    catalog: string
    database: string
    tableName: string
    columnName: string
  },
): any => {
  const formattedData = formValue[type].map((typeItem: any) => {
    const createMetric = customerIsSupportType('calculateFormCreateMetricAlwaysFalse') ? false : !!typeItem.createMetric
    const filterSwitch = !!typeItem.filterSwitch
    switch (typeItem.function) {
      case 'COUNT':
      case 'SUM':
      case 'AVG':
      case 'MAX':
      case 'MIN':
      case 'COUNT_DISTINCT':
      case 'YEAR': // 时间类型
      case 'MONTH':
      case 'DATE': {
        return {
          createMetric,
          function: {
            function: typeItem.function,
            params: [
              {
                type: 'COLUMN',
                value: `${columnInfo.catalog}.${columnInfo.database}.${columnInfo.tableName}.${columnInfo.columnName}`,
                valueType: 'STRING',
              },
            ],
          },
        }
      }
      case 'DATE_FORMAT': {
        return {
          createMetric,
          function: {
            function: 'DATE_FORMAT',
            params: [
              {
                type: 'COLUMN',
                value: `${columnInfo.catalog}.${columnInfo.database}.${columnInfo.tableName}.${columnInfo.columnName}`,
                valueType: 'STRING',
              },
              {
                type: 'CONSTANT',
                value: typeItem.params.value,
                valueType: 'STRING',
              },
            ],
          },
        }
      }
      case 'DATE_SUB': {
        return {
          createMetric,
          function: {
            function: typeItem.function,
            params: [
              {
                type: 'COLUMN',
                value: `${columnInfo.catalog}.${columnInfo.database}.${columnInfo.tableName}.${columnInfo.columnName}`,
                valueType: 'STRING',
              },
              {
                type: 'CONSTANT',
                value: typeItem.params.value,
                valueType: 'NUMERIC',
              },
              {
                type: 'CONSTANT',
                value: typeItem.params.unit,
                valueType: 'NUMERIC',
              },
            ],
          },
        }
      }
      case 'CASE_WHEN': {
        const formattedDimension = {
          filterSwitch,
          function: {
            function: 'CASE_WHEN',
            params: typeItem.params.map((param: any) => {
              return {
                type: 'CONDITION_VALUE',
                condition: {
                  condition: {
                    type: 'ATOMIC',
                    operator: param.operator,
                    params: columnInfo
                      ? [
                          {
                            type: 'COLUMN',
                            value: `${columnInfo.catalog}.${columnInfo.database}.${columnInfo.tableName}.${columnInfo.columnName}`,
                            valueType: 'STRING',
                          },
                          {
                            type: 'CONSTANT',
                            value: param.value,
                            valueType: 'STRING',
                          },
                        ]
                      : [
                          {
                            type: 'CONSTANT',
                            value: param.value,
                            valueType: 'STRING',
                          },
                        ],
                  },
                  functionDesc: {
                    params: [
                      {
                        type: 'CONSTANT',
                        value: param.functionDesc.value,
                        valueType: 'STRING',
                      },
                    ],
                  },
                },
              }
            }),
          },
        }
        return formattedDimension
      }
      case 'SUB_STRING3': {
        const formattedDimension = {
          filterSwitch,
          function: {
            function: 'SUB_STRING3',
            params: [
              {
                type: 'COLUMN',
                value: `${columnInfo.catalog}.${columnInfo.database}.${columnInfo.tableName}.${columnInfo.columnName}`,
              },
              {
                type: 'CONSTANT',
                value: typeItem.params.substringStartPosition,
                valueType: 'NUMERIC',
              },
              {
                type: 'CONSTANT',
                value: typeItem.params.length,
                valueType: 'NUMERIC',
              },
            ],
          },
        }
        return formattedDimension
      }
      case 'LEFT': {
        const formattedDimension = {
          filterSwitch,
          function: {
            function: 'LEFT',
            params: [
              {
                type: 'COLUMN',
                value: `${columnInfo.catalog}.${columnInfo.database}.${columnInfo.tableName}.${columnInfo.columnName}`,
              },
              {
                type: 'CONSTANT',
                value: typeItem.params.length,
                valueType: 'NUMERIC',
              },
            ],
          },
        }
        return formattedDimension
      }
      case 'CONCAT': {
        const formattedDimension = {
          filterSwitch,
          function: {
            function: 'CONCAT',
            params: [
              {
                type: 'COLUMN',
                value: `${columnInfo.catalog}.${columnInfo.database}.${columnInfo.tableName}.${columnInfo.columnName}`,
              },
              {
                type: 'COLUMN',
                value: `${columnInfo.catalog}.${columnInfo.database}.${columnInfo.tableName}.${typeItem.params.columnName}`,
              },
            ],
          },
        }
        return formattedDimension
      }
      default: {
        return formValue
      }
    }
  })
  return { [type]: formattedData }
}
const comparisonOptions = [
  { label: '=', value: 'EQ' },
  { label: 'IS', value: 'IS' },
  { label: '!=', value: 'NEQ' },
  { label: '>', value: 'GT' },
  { label: '>=', value: 'GE' },
  { label: '<', value: 'LT' },
  { label: '<=', value: 'LE' },
  { label: 'LIKE', value: 'LIKE' },
  { label: 'IN', value: 'IN' },
  { label: 'NOT IN', value: 'NOT_IN' },
  { label: 'BETWEEN', value: 'BETWEEN' },
]

const measuresAggregationList = ['COUNT', 'SUM', 'AVG', 'MAX', 'MIN', 'COUNT_DISTINCT']
// const measuresDateList = ['YEAR', 'MONTH', 'DATE', 'DATE_SUB', 'DATE_FORMAT']
const measuresDateList = [] as const
// const measuresSpecialList = ['CASE_WHEN']
const measuresSpecialList = [] as const
const calculateMethod = {
  dimensions: [
    { label: 'SUB_STRING', value: 'SUB_STRING3' },
    { label: 'CONCAT', value: 'CONCAT' },
    { label: 'LEFT', value: 'LEFT' },
    { label: 'CASE_WHEN', value: 'CASE_WHEN' },
  ],

  measures: [...measuresAggregationList, ...measuresDateList, ...measuresSpecialList].map((i) => ({
    label: i,
    value: i,
  })),
}

const dateUnitOptions = [
  { label: '时', value: 'Hour' },
  { label: '分', value: 'Minute' },
  { label: '秒', value: 'Second' },
  { label: '年', value: 'Year' },
  { label: '月', value: 'Month' },
  { label: '日', value: 'Day' },
]
export const CalculateForm = (props: {
  readonly?: boolean
  initValue?: any
  columnName: string
  type: 'dimensions' | 'measures'
  createCalculateOpen: boolean
  setCreateCalculateOpen: React.Dispatch<React.SetStateAction<boolean>>
  catalog: string
  database: string
  tableName: string
  formValue?: any
  onOk: (val: any, formValue: any) => void
}) => {
  const calculateType = props.type ?? 'dimensions'
  const cfgPropName = calculateType === 'measures' ? 'createMetric' : 'filterSwitch'
  const [form] = Form.useForm()
  const { createCalculateOpen, setCreateCalculateOpen } = props
  const [singleDimensionFilterSwitch, setSingleDimensionFilterSwitch] = useState(true)

  useEffect(() => {
    try {
      form.setFieldsValue(modelValueUnFormat(props.initValue, calculateType))
      const dimensions = props.initValue?.[calculateType] || []
      if (calculateType === 'dimensions' && dimensions?.length <= 1) {
        setSingleDimensionFilterSwitch(dimensions[0]?.filterSwitch !== false)
      }
    } catch (error) {
      console.error(error, 'calculate form init value error')
    }
  }, [form, props.initValue, calculateType, createCalculateOpen])
  const { message } = App.useApp()

  const { data: tableDetail } = useRequest(
    async () => {
      const response = await axios.get(askBIApiUrls.xengine.vTableDetail, {
        params: { catalog: props.catalog, database: props.database, name: props.tableName },
      })
      return response.data.data
    },
    {
      ready: Boolean(props.catalog && props.database && props.tableName),
      onError: (error: any) => {
        if (error?.code === 348169) {
          message.error('无权限')
        } else {
          message.error('获取虚拟表详情失败，请重试！')
        }
        console.error('Load table detail error', error)
      },
    },
  )

  return (
    <Modal
      destroyOnClose
      width={500}
      footer={null}
      title="算子配置"
      open={createCalculateOpen}
      onOk={() => setCreateCalculateOpen(false)}
      okButtonProps={{ loading: false }}
      onCancel={() => setCreateCalculateOpen(false)}
    >
      <Form
        className="mt-2 w-full"
        form={form}
        onFinish={(val) => {
          if (calculateType === 'dimensions' && val[calculateType]?.length <= 0) {
            props.onOk(
              {
                dimensions: [{ filterSwitch: singleDimensionFilterSwitch }],
              },
              val,
            )
          } else {
            props.onOk(
              formValueFormat(calculateType, val, {
                catalog: props.catalog,
                database: props.database,
                tableName: props.tableName,
                columnName: props.columnName,
              }),
              val,
            )
          }
        }}
        initialValues={{}}
        layout="horizontal"
        labelCol={{ span: 24 }}
        wrapperCol={{ span: 24 }}
      >
        <Form.Item className="mb-0">
          <Form.List name={calculateType} initialValue={[null]}>
            {(outerFields, { add, remove }) => (
              <>
                {outerFields.map(({ key, name: outerFieldName }) => (
                  <div key={key} className="background mb-5 bg-white">
                    <Row wrap={false}>
                      <Col flex={1}>
                        <Form.Item
                          name={[outerFieldName, 'function']}
                          className="mb-[8px]"
                          rules={[{ required: true, message: '请选择算子' }]}
                        >
                          <Select placeholder="请选择算子" className="min-w-[200px]" disabled={props.readonly}>
                            {calculateMethod[calculateType].map((i) => (
                              <Option key={i.value} value={i.value}>
                                {i.label}
                              </Option>
                            ))}
                          </Select>
                        </Form.Item>
                        <Form.Item shouldUpdate noStyle>
                          {({ getFieldValue, resetFields }) => {
                            const functionName = getFieldValue([calculateType, outerFieldName, 'function'])
                            const params = getFieldValue([calculateType, outerFieldName, 'params'])
                            switch (functionName) {
                              case 'COUNT':
                              case 'SUM':
                              case 'AVG':
                              case 'MAX':
                              case 'MIN':
                              case 'COUNT_DISTINCT':
                              case 'YEAR': // 时间类型
                              case 'MONTH':
                              case 'DATE': {
                                return false
                              }
                              case 'CASE_WHEN':
                                if (!Array.isArray(params)) {
                                  // 其他有params是对象，这里的params是数据，不做处理会进行报错
                                  resetFields([[calculateType, outerFieldName, 'params']])
                                }
                                return (
                                  <Form.Item className="mb-0">
                                    <Form.List name={[outerFieldName, 'params']} initialValue={[null]}>
                                      {(fields, { add, remove }) => (
                                        <div className="p-4">
                                          {fields.map(({ key, name, ...restField }) => (
                                            <div key={key}>
                                              <Row wrap={false}>
                                                <Col flex={1}>
                                                  <Form.Item label="条件" className="mb-[8px] flex-1">
                                                    <Space.Compact className="w-full">
                                                      <Form.Item
                                                        name={[name, 'operator']}
                                                        noStyle
                                                        rules={[{ required: true, message: '请选择条件' }]}
                                                      >
                                                        <Select
                                                          placeholder="条件"
                                                          className="!w-[182px]"
                                                          disabled={props.readonly}
                                                        >
                                                          {comparisonOptions.map((i) => (
                                                            <Option key={i.value} value={i.value}>
                                                              {i.label}
                                                            </Option>
                                                          ))}
                                                        </Select>
                                                      </Form.Item>
                                                      <Form.Item
                                                        name={[name, 'value']} // 自定义的值后端给的结构太复杂了，很难直接拼接
                                                        noStyle
                                                        rules={[
                                                          {
                                                            required: true,
                                                            message: '请输入条件值，范围值，请用 , 分割',
                                                          },
                                                        ]}
                                                      >
                                                        <Input
                                                          placeholder="请输入条件值，范围值，请用 , 分割"
                                                          disabled={props.readonly}
                                                        />
                                                      </Form.Item>
                                                    </Space.Compact>
                                                  </Form.Item>
                                                </Col>
                                                {fields.length > 1 && (
                                                  <Col
                                                    className={clsx('mt-[5px] flex items-baseline justify-around', {
                                                      'w-[45px]': fields.length > 1,
                                                      'w-[30px]': fields.length === 1,
                                                    })}
                                                  >
                                                    <Space direction="horizontal" className="justify-between">
                                                      {fields.length > 1 ? (
                                                        <Typography.Link disabled={props.readonly}>
                                                          <MinusOutlined
                                                            className="cursor-pointer"
                                                            onClick={() => remove(name)}
                                                          />
                                                        </Typography.Link>
                                                      ) : null}
                                                    </Space>
                                                  </Col>
                                                )}
                                              </Row>
                                              <Row>
                                                <Col flex={1}>
                                                  <Form.Item
                                                    {...restField}
                                                    label="结果"
                                                    className="mb-[16px] flex-1"
                                                    style={{ flex: 1 }}
                                                    name={[name, 'functionDesc', 'value']} // 自定义的值后端给的结构太复杂了，很难直接拼接
                                                    rules={[{ message: '请输入结果值' }]}
                                                  >
                                                    <Input placeholder="请输入结果值" disabled={props.readonly} />
                                                  </Form.Item>
                                                </Col>
                                                {fields.length > 1 && (
                                                  <Col
                                                    className={clsx('mt-[5px] flex items-baseline justify-around', {
                                                      'w-[45px]': fields.length > 1,
                                                      'w-[30px]': fields.length === 1,
                                                    })}
                                                  >
                                                    <Space direction="horizontal" className="hidden justify-between">
                                                      {fields.length > 1 ? (
                                                        <Typography.Link disabled={props.readonly}>
                                                          <MinusOutlined
                                                            className="cursor-pointer"
                                                            onClick={() => remove(name)}
                                                          />
                                                        </Typography.Link>
                                                      ) : null}
                                                    </Space>
                                                  </Col>
                                                )}
                                              </Row>
                                            </div>
                                          ))}
                                          <Typography.Link onClick={() => add()} disabled={props.readonly}>
                                            <PlusOutlined className="mr-2 cursor-pointer" /> 条件
                                          </Typography.Link>
                                        </div>
                                      )}
                                    </Form.List>
                                  </Form.Item>
                                )
                              case 'CONCAT':
                                return (
                                  <Form.Item
                                    name={[outerFieldName, 'params', 'columnName']}
                                    className="mb-[8px]"
                                    rules={[{ required: true, message: '请选择字段' }]}
                                  >
                                    <Select
                                      placeholder="请选择字段"
                                      className="min-w-[200px]"
                                      disabled={props.readonly}
                                    >
                                      {tableDetail.columns.map((i: { name: string }) => (
                                        <Option key={i.name} value={i.name}>
                                          {i.name}
                                        </Option>
                                      ))}
                                    </Select>
                                  </Form.Item>
                                )
                              case 'LEFT':
                                return (
                                  <>
                                    <Form.Item
                                      name={[outerFieldName, 'params', 'length']}
                                      rules={[{ required: true, message: '请输入截取长度' }]}
                                    >
                                      <Input placeholder="请输入截取长度" disabled={props.readonly} />
                                    </Form.Item>
                                  </>
                                )
                              case 'DATE_FORMAT':
                                return (
                                  <>
                                    <Form.Item
                                      name={[outerFieldName, 'params', 'value']}
                                      rules={[{ required: true, message: '请输入格式' }]}
                                    >
                                      <Input placeholder="请输入格式" disabled={props.readonly} />
                                    </Form.Item>
                                  </>
                                )
                              case 'DATE_SUB':
                                return (
                                  <div>
                                    <Space.Compact>
                                      <Form.Item
                                        name={[outerFieldName, 'params', 'value']}
                                        rules={[{ required: true, message: '请输入需要减去时间的值' }]}
                                      >
                                        <Input placeholder="请输入需要减去时间的值" disabled={props.readonly} />
                                      </Form.Item>
                                      <Form.Item
                                        name={[outerFieldName, 'params', 'unit']}
                                        noStyle
                                        rules={[{ required: true, message: '请选择单位' }]}
                                      >
                                        <Select
                                          placeholder="请选择单位"
                                          className="min-w-[200px]"
                                          disabled={props.readonly}
                                        >
                                          {dateUnitOptions.map((i) => (
                                            <Option key={i.value} value={i.value}>
                                              {i.label}
                                            </Option>
                                          ))}
                                        </Select>
                                      </Form.Item>
                                    </Space.Compact>
                                  </div>
                                )
                              case 'SUB_STRING3':
                                return (
                                  <>
                                    <Form.Item
                                      name={[outerFieldName, 'params', 'substringStartPosition']}
                                      label={'开始位置'}
                                      rules={[{ required: true }]}
                                    >
                                      <Input placeholder="开始位置" disabled={props.readonly} />
                                    </Form.Item>
                                    <Form.Item
                                      name={[outerFieldName, 'params', 'length']}
                                      label={'截取长度'}
                                      rules={[{ required: true, message: '请输入截取长度' }]}
                                    >
                                      <Input placeholder="请输入截取长度" disabled={props.readonly} />
                                    </Form.Item>
                                  </>
                                )
                              default:
                                null
                            }
                          }}
                        </Form.Item>
                      </Col>

                      <Col flex="none" className={clsx('mt-[5px] inline-flex items-baseline justify-between')}>
                        <CustomerHiddenWrap type="calculateFormCheckCfgHide">
                          <div className="ml-6 flex flex-none items-center">
                            <Form.Item noStyle name={[outerFieldName, cfgPropName]} valuePropName="checked">
                              <Checkbox disabled={props.readonly} />
                            </Form.Item>
                            <span className="ml-1">{calculateType === 'measures' ? '是否配置指标' : '是否召回'}</span>
                          </div>
                        </CustomerHiddenWrap>
                        <Typography.Link disabled={props.readonly} className="flex-none">
                          <MinusOutlined className="ml-4 cursor-pointer" onClick={() => remove(outerFieldName)} />
                        </Typography.Link>
                      </Col>
                    </Row>
                  </div>
                ))}
                <div className="my-5 flex items-center justify-between">
                  <Typography.Link
                    disabled={props.readonly}
                    onClick={() => {
                      add()
                      form.setFieldValue([calculateType, outerFields.length, cfgPropName], true)
                    }}
                  >
                    <PlusOutlined className="mr-1 cursor-pointer" /> 算子
                  </Typography.Link>
                  <CustomerHiddenWrap type="calculateFormCheckCfgHide">
                    <Form.Item noStyle shouldUpdate>
                      {({ getFieldsValue, setFieldsValue }) => (
                        <div className="flex items-center">
                          <Checkbox
                            disabled={props.readonly}
                            onChange={(e) => {
                              const toChecked = e.target.checked
                              const values = getFieldsValue()[calculateType] || []
                              if (calculateType === 'dimensions' && values.length <= 0) {
                                setSingleDimensionFilterSwitch(toChecked)
                              } else {
                                setFieldsValue({
                                  [calculateType]: values.map((item: Record<string, string>) => ({
                                    ...item,
                                    [cfgPropName]: toChecked,
                                  })),
                                })
                              }
                            }}
                            indeterminate={(() => {
                              const values = getFieldsValue()[calculateType] || []
                              return (
                                values?.some(
                                  (v: { createMetric: boolean; filterSwitch: boolean }) => !v?.[cfgPropName],
                                ) &&
                                values?.some((v: { createMetric: boolean; filterSwitch: boolean }) => v?.[cfgPropName])
                              )
                            })()}
                            checked={(() => {
                              const values = getFieldsValue()[calculateType] || []
                              if (calculateType === 'dimensions' && values.length <= 0) {
                                return singleDimensionFilterSwitch
                              }
                              return values.every(
                                (v: { createMetric: boolean; filterSwitch: boolean }) => v?.[cfgPropName],
                              )
                            })()}
                          />
                          <span className="ml-1">
                            {calculateType === 'measures'
                              ? '全选配置指标'
                              : getFieldsValue()[calculateType]?.length > 0
                                ? '全选召回'
                                : '是否召回'}
                          </span>
                        </div>
                      )}
                    </Form.Item>
                  </CustomerHiddenWrap>
                </div>
              </>
            )}
          </Form.List>
        </Form.Item>

        <Row justify="end" gutter={12}>
          <Col span={12}>
            <Button
              className="w-full"
              onClick={() => {
                setCreateCalculateOpen(false)
              }}
            >
              取消
            </Button>
          </Col>
          <Col span={12}>
            <Button className="w-full" type="primary" onClick={form.submit} disabled={props.readonly}>
              确定
            </Button>
          </Col>
        </Row>
      </Form>
    </Modal>
  )
}
