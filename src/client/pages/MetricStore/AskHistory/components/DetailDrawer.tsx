import { Drawer } from 'antd'
import React from 'react'
// import { format as sqlFormatter } from 'sql-formatter'
// import SyntaxHighlighter from 'react-syntax-highlighter'
// import { PlanData } from 'src/shared/agent'
import { MultiAgentChat, transformChat } from 'src/client/components/chats'
import './DetailDrawer.css'
// import AnswerView from 'src/client/pages/AskDoc/DocDetail/AnswerView'
// import { readyResponseToChatAns } from 'src/client/pages/AskBI/Chat/utils'
// import { AssistantChatItem } from 'src/shared/common-types'
import { AskHistoryItem } from './Table'
import DetailBIResult from './DetailBIResult'

type Props = { visible: boolean; onClose: () => void; chatData: AskHistoryItem | null }

const topBarHeight = 62

export default function DetailDrawer({ visible, onClose, chatData }: Props) {
  const chat = transformChat(chatData?.response)
  // const chatResponse = chatData?.response
  // const docResponse = chatData?.docResponse

  // const data = useMemo(() => {
  //   const result = chatResponse?.[0] || {}
  //   return result
  // }, [chatResponse])

  // const {
  //   // projectId,
  //   // sceneId,
  //   chatData: newChatData,
  // } = useMemo(() => {
  //   if (chatData) {
  //     const projectId = chatData.conver.semanticProjectId
  //     const sceneId = chatData.conver.semanticSceneId
  //     let ansData: any = null
  //     const response = chatData.response
  //     if (response?.length > 0 && !!response[0].taskType) {
  //       ansData = readyResponseToChatAns(response[0], sceneId)
  //       const content = ansData.content[0]
  //       if (content && content?.type === 'multi-agent') {
  //         // 如果是multi-agent, 则强制将inferStatus转为done, 即不展示为推理中
  //         content.data.inferStatus = 'done'
  //       }
  //     }
  //     return {
  //       projectId,
  //       sceneId,
  //       chatData: ansData ? ansData : {},
  //     }
  //   } else {
  //     return {
  //       projectId: '',
  //       sceneId: '',
  //       chatData: {},
  //     }
  //   }
  // }, [chatData])

  // const sql = useMemo(() => {
  //   const content = newChatData.content?.[0]
  //   if (content && content?.type === 'multi-agent') {
  //     // 兼容multi-agent的sql取值
  //     const result = findFirstInMap<string, PlanData>(content.data.agentMap, (item) => {
  //       if (item.earlyAnsItem) {
  //         return item?.earlyAnsItem?.length > 0
  //       } else {
  //         return false
  //       }
  //     })
  //     if (!result) {
  //       return null
  //     }
  //     return result.earlyAnsItem?.[0]?.content?.find((item: AssistantChatItem) => item.type === 'sql')?.sql
  //   } else {
  //     return data?.sql
  //   }
  // }, [newChatData, data?.sql])
  const title = chatData?.ask

  // const currentChat: Chat = {
  //   id: newChatData?.id,
  //   ask: {
  //     role: 'user',
  //     content: newChatData.ask,
  //     jsonContent: newChatData.ask,
  //   },
  //   docAns: {
  //     content: [{ text: '正在为您查询这个问题...', type: 'text' }],
  //     role: 'assistant',
  //     status: 'pending',
  //   },
  //   selectedSceneId: sceneId,
  //   askTime: newChatData.createdAt,
  //   ans: [{ content: newChatData.content, role: 'assistant', sceneId, status: 'success' }],
  // }

  return (
    <Drawer
      className="ask-history-detail-drawer"
      open={visible}
      placement="bottom"
      height={`calc(100% - ${topBarHeight}px)`}
      onClose={onClose}
      title={title}
      maskClosable
      // 关闭后不销毁,会导致图表渲染出问题
      destroyOnClose
    >
      {/* {docResponse && docResponse.length > 0 && (
        <div className="response-doc">
          <div className="mb-2">Doc回答结果:</div>
          <div className="rounded border px-6 py-4">
            <AnswerView content={docResponse[0]} status={'success'} isViewMode={true} />
          </div>
        </div>
      )} */}
      {chat && (
        <div className="flex gap-4">
          <div className="response-data w-1/2">
            <div className="mb-2">数据结果:</div>
            <div className="rounded border p-0">
              <DetailBIResult currentChat={chat as MultiAgentChat} />
            </div>
          </div>
          {/* {sql ? (
          <div className="response-sql w-1/2">
            <div className="mb-2">SQL:</div>
            <div className="rounded border px-6 py-4">
              <div>
                <SyntaxHighlighter
                  language="sql"
                  customStyle={{ background: 'bg-slate-50 dark:bg-slate-700', padding: 0 }}
                >
                  {sqlFormatter(sql, { indentStyle: 'tabularLeft' })}
                </SyntaxHighlighter>
              </div>
            </div>
          </div>
        ) : null} */}
        </div>
      )}
    </Drawer>
  )
}

// function findFirstInMap<K, V>(map: Map<K, V>, predicate: (value: V, key: K) => boolean): V | undefined {
//   for (const key of map.keys()) {
//     const value = map.get(key)! // 非空断言，因为我们知道key存在
//     if (predicate(value, key)) {
//       return value
//     }
//   }
//   return undefined
// }
