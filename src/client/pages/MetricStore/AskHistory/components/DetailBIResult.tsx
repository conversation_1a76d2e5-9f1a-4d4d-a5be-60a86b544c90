import { useAtomValue } from 'jotai/react'
import React, { useRef, useState } from 'react'
import { themeAtom } from 'src/client/pages/AskBI/askBIAtoms'
import { BlobWithRatio } from 'src/shared/common-types'
import BiResultContent from 'src/client/components/ChatHistory/BiResultContent'
import { ChatHistoryItemContext } from 'src/client/components/ChatHistory/ChatHistoryItemContext'
import { MultiAgentChat } from 'src/client/components/chats'
import { MULTI_SCENE_CHAT_MOCK_SCENE_ID } from 'src/shared/constants'
import { AnsChatItem, ChatStatus } from 'src/client/utils'

export default function DetailBIResult({ currentChat }: { currentChat: MultiAgentChat }) {
  const theme = useAtomValue(themeAtom)

  const chartWrapperRef = useRef<{
    downloadPNG: () => void
    copyPNG: () => void
    getBlob: () => Promise<BlobWithRatio | string>
  }>(null)

  const [ansItem] = useState<AnsChatItem>({
    role: 'assistant',
    content: [
      {
        type: 'multi-agent' as const,
        data: currentChat?.data,
      },
    ],
    status: ChatStatus.success,
    sceneId: MULTI_SCENE_CHAT_MOCK_SCENE_ID,
  })

  return (
    <ChatHistoryItemContext.Provider value={{ chat: currentChat, chatProgress: null, theme }}>
      <BiResultContent
        currentSelectedSceneId={MULTI_SCENE_CHAT_MOCK_SCENE_ID}
        chartWrapperRef={chartWrapperRef}
        currentChat={currentChat}
        isViewMode={true}
        chatAnsItem={ansItem}
      />
    </ChatHistoryItemContext.Provider>
  )
}
