import React from 'react'
import { Form, Input, Select } from 'antd'
import axios from 'axios'
import { useRequest } from 'ahooks'
import { askBIApiUrls } from 'src/shared/url-map'
export default function KafkaForm() {
  const { data: listKafkaSecurityProtocol = [], loading: listKafkaSecurityProtocolLoading } = useRequest(() =>
    axios.get(askBIApiUrls.xengine.datasource.listKafkaSecurityProtocol).then((res) => res.data?.data || []),
  )
  const { data: listKafkaSASLMechanism = [], loading: listKafkaSASLMechanismLoading } = useRequest(() =>
    axios.get(askBIApiUrls.xengine.datasource.listKafkaSASLMechanism).then((res) => res.data?.data || []),
  )
  return (
    <>
      <Form.Item
        label="Broker地址"
        name="brokers"
        required
        rules={[{ required: true }]}
        tooltip="配置多个Broker地址，使用;进行分割，例如：***********:9092;***********:9092"
      >
        <Input placeholder="请输入" />
      </Form.Item>
      <Form.Item label="用户名" name="userName">
        <Input placeholder="请输入" />
      </Form.Item>
      <Form.Item label="密码" name="passWord">
        <Input.Password placeholder="请输入" />
      </Form.Item>
      <Form.Item name="kafkaSecurityProtocol" label="安全协议">
        <Select
          allowClear
          placeholder="请选择"
          loading={listKafkaSecurityProtocolLoading}
          options={listKafkaSecurityProtocol.map((v: string) => ({ label: v, value: v }))}
        />
      </Form.Item>
      <Form.Item name="kafkaSASLMechanism" label="SASL机制">
        <Select
          allowClear
          placeholder="请选择"
          loading={listKafkaSASLMechanismLoading}
          options={listKafkaSASLMechanism.map((v: string) => ({ label: v, value: v }))}
        />
      </Form.Item>
    </>
  )
}
