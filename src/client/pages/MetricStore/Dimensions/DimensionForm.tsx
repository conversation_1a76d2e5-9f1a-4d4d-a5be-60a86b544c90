import { Button, Form, Input, App, Select, Tabs, Space, Row, Col, Tag, Radio } from 'antd'
import React, { useEffect, useState } from 'react'
import { useForm } from 'antd/es/form/Form'
import clsx from 'clsx'
import axios from 'axios'
import { useRequest } from 'ahooks'
import { SemanticScene } from '@prisma/client'
import { ArrowLeftOutlined } from '@ant-design/icons'
import {
  Dimension,
  DimensionTypeWithoutVirtual,
  DimensionTypeNames,
  DimensionTypeWithoutVirtualNames,
} from '@shared/metric-types'
import { askBIApiUrls } from 'src/shared/url-map'
import { APIResponse, DateTypes } from 'src/shared/common-types'
import { SynonymsForm } from '../components/SynonymsForm'
import { NullComponent } from '../components/NullComponent'

const Option = Select.Option

const dimensionOptions: { label: string; value: DimensionTypeWithoutVirtual }[] = Object.entries(
  DimensionTypeWithoutVirtualNames,
).map(([key, value]) => ({
  label: value,
  value: key as DimensionTypeWithoutVirtual,
}))

type ActiveKey = 'dimensionForm' | 'selectDimensionType'

export default function DimensionForm({
  dimensionId,
  className,
  updateList,
  onOk,
  initialActiveKey,
}: {
  initialActiveKey?: ActiveKey
  onOk?: () => void
  className?: string
  updateList?: () => void
  dimensionId?: string
}) {
  const [form] = useForm()

  const { message: antdMessage } = App.useApp()
  const [dimensionType, setDimensionType] = useState<DimensionTypeWithoutVirtual>('categorical')
  const { data: semanticList, loading: isSemanticListLoading } = useRequest(
    async () => {
      const response = await axios.get<APIResponse<{ list: SemanticScene[]; total: number }>>(askBIApiUrls.model.list, {
        params: { current: 1, pageSize: 1000 },
      })
      return response.data.data?.list
    },
    {
      onError: (error: any) => {
        console.error('get currentDimensionInfo =', error)
      },
    },
  )
  const [originalDimensionInfo, setOriginalDimensionInfo] = useState()
  const { data: editDimensionInfo } = useRequest(
    async () => {
      if (dimensionId) {
        const response = await axios.get(askBIApiUrls.dimension.detail(dimensionId))
        return response.data.data
      }
    },
    {
      ready: !!dimensionId,
      refreshDeps: [dimensionId],
      onSuccess: (data) => {
        const synonyms = data.synonyms
          .filter((item: string[]) => item != null)
          .map((i: string) => {
            return { name: i }
          })

        const tempFormData = { ...data, synonyms: synonyms.length ? synonyms : [null] }
        setDimensionType(data.type)
        setOriginalDimensionInfo(tempFormData)
        data && form.setFieldsValue(tempFormData)
      },
      onError: (error: any) => {
        console.error('get currentDimensionInfo =', error)
      },
    },
  )
  const { run: createOrUpdateDimension } = useRequest(
    (arg) => {
      const { id, semanticModelId, synonyms } = arg
      const dimension = {
        ...arg,
        type: dimensionId ? editDimensionInfo.type : dimensionType,
        synonyms: synonyms.filter((item: string[]) => item != null).map((i: { name: string }) => i.name),
        semanticModelId,
      }
      if (id) {
        return axios.put(askBIApiUrls.dimension.update(id), dimension)
      } else {
        return axios.post(askBIApiUrls.dimension.create, dimension)
      }
    },
    {
      manual: true,
      onSuccess: (_, arg) => {
        updateList?.()
        if (arg[0].id) {
          antdMessage.success('修改维度成功！', 0.5)
        } else {
          antdMessage.success('新增维度成功！', 0.5)
        }
        form.resetFields()
        onOk?.()
      },
      onError: (error, arg) => {
        if (arg[0].id) {
          antdMessage.error(error.message || '修改维度失败！')
        } else {
          antdMessage.error(error.message || '新增维度失败！')
        }
      },
    },
  )
  const [activeKey, setActiveKey] = useState<ActiveKey>('selectDimensionType')

  useEffect(() => {
    if (initialActiveKey) {
      setActiveKey(initialActiveKey)
    }
  }, [initialActiveKey])
  return (
    <div
      className={clsx(
        'metric-list-page mx-auto flex h-full w-full flex-grow flex-col justify-start gap-2 overflow-y-auto bg-white p-3 pt-1 text-base text-black dark:bg-slate-900 dark:text-slate-100 md:max-w-screen-lg md:p-6 md:pt-3',
        className,
      )}
    >
      <Tabs
        activeKey={activeKey}
        onChange={(key) => {
          switch (key) {
            // TODO 这个写法要研究一下
            case 'dimensionForm':
            case 'selectDimensionType':
              setActiveKey(key)
              return
            default:
              return
          }
        }}
        renderTabBar={() => <NullComponent />}
        className="rounded-tremor-default flex min-h-[360px] w-full max-w-full flex-col rounded-xl bg-white dark:bg-slate-700"
        centered
        items={
          [
            {
              label: 'selectDimensionType',
              key: 'selectDimensionType',
              children: (
                <div className="flex h-fit w-full cursor-pointer justify-center gap-10 md:p-4 md:pt-0">
                  {dimensionOptions.map(({ label, value }) => {
                    return (
                      <div
                        key={value}
                        onClick={() => {
                          setDimensionType(value)
                          setActiveKey('dimensionForm')
                        }}
                        className="mr-2 mt-1 flex h-20 w-20 items-center justify-center rounded-lg bg-primary text-white"
                      >
                        {label}
                      </div>
                    )
                  })}
                </div>
              ),
            },
            {
              label: 'dimensionForm',
              key: 'dimensionForm',
              children: (
                <div className="flex flex-col flex-wrap justify-center md:p-4 md:pt-0">
                  {/* 新建模式才有返回 */}
                  {!dimensionId && (
                    <div
                      className="cursor-pointer p-1"
                      onClick={() => {
                        setActiveKey('selectDimensionType')
                        form.resetFields()
                      }}
                    >
                      <ArrowLeftOutlined />
                      <span>上一步</span>
                    </div>
                  )}
                  <Form
                    className="mt-2 w-full"
                    form={form}
                    onFinish={(val: Dimension) => {
                      createOrUpdateDimension({ ...val, id: dimensionId })
                    }}
                    layout={dimensionId ? 'vertical' : 'horizontal'}
                    labelCol={{ span: dimensionId ? 24 : 4 }}
                    wrapperCol={{ span: dimensionId ? 24 : 16 }}
                  >
                    <Form.Item label="类型">
                      <Tag className="font-bold">{DimensionTypeNames[dimensionType]}</Tag>
                    </Form.Item>
                    <Form.Item label="场景" name="semanticModelId" rules={[{ required: true, message: '请选择场景' }]}>
                      <Select placeholder="选择场景" loading={isSemanticListLoading}>
                        {semanticList?.map(({ id, label }) => (
                          <Option key={id} value={id}>
                            {label}
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                    <Form.Item label="标识符" name="name" rules={[{ required: true, message: '请输入标识符' }]}>
                      <Input placeholder="请输入英文标识" />
                    </Form.Item>
                    <SynonymsForm />
                    <Form.Item label="中文名" name="label" rules={[{ required: true, message: '请输入中文名' }]}>
                      <Input placeholder="中文名" />
                    </Form.Item>
                    <Form.Item label="描述" name="description">
                      <Input.TextArea placeholder="描述" />
                    </Form.Item>
                    <Form.Item
                      label="表达式"
                      name="expr"
                      extra="SQL 字段的表达式"
                      rules={[{ required: true, message: '请输入表达式' }]}
                    >
                      <Input placeholder="表达式" />
                    </Form.Item>
                    {dimensionType === 'time' && (
                      <Form.Item
                        label="时间粒度"
                        name="timeGranularity"
                        rules={[{ required: true, message: '请选择时间粒度' }]}
                      >
                        <Radio.Group>
                          {DateTypes.map((i) => {
                            return (
                              <Radio value={i.value} key={i.value}>
                                {i.label}
                              </Radio>
                            )
                          })}
                        </Radio.Group>
                      </Form.Item>
                    )}
                    <Row justify="end">
                      <Col pull={4}>
                        <Space style={{ display: 'flex', marginBottom: 8 }} align="end">
                          <Button
                            onClick={() => {
                              // 编辑情况下重置为原来的数据， 新增情况下重置为空数据
                              form.setFieldsValue(originalDimensionInfo)
                            }}
                          >
                            重置
                          </Button>
                          <Button
                            type="primary"
                            onClick={() => {
                              form.submit()
                            }}
                          >
                            确认
                          </Button>
                        </Space>
                      </Col>
                    </Row>
                  </Form>
                </div>
              ),
            },
          ] as const
        }
      />
    </div>
  )
}
