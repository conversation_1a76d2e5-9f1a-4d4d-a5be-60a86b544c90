import axios from 'axios'
import { useAtomValue, useSetAtom } from 'jotai/react'
import { APIResponse } from 'src/shared/common-types'
import { ConversationTimeSlices } from 'src/shared/types/conversationTypes'
import { askBIApiUrls } from 'src/shared/url-map'
import { currentDatasetAtom, isProjectChosenAtom } from '../pages/AskBI/askBIAtoms'
import { conversationsAtom } from '../pages/AskBI/conver-atoms/converAtoms'

export function useConversation() {
  const isProjectChosen = useAtomValue(isProjectChosenAtom)
  const currentDataset = useAtomValue(currentDatasetAtom)
  const setConversation = useSetAtom(conversationsAtom)

  const loadConversationList = async () => {
    if (!currentDataset) {
      return
    }
    try {
      const params = {
        projectId: currentDataset?.projectId,
        sceneId: isProjectChosen ? undefined : currentDataset?.sceneId,
      }
      const response = await axios.get<APIResponse<ConversationTimeSlices>>(askBIApiUrls.convers.list, { params })
      setConversation(response.data.data || null)
      return response.data.data
    } catch (error: any) {
      // message.error(`获取历史会话列表失败：${error?.message}`, 0.5)
      console.error('Conversations error =', error)
    }
  }

  return { loadConversationList }
}
