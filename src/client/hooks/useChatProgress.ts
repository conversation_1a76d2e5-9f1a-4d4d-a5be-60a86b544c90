/* eslint-disable @typescript-eslint/naming-convention */
import EventEmitter from 'eventemitter3'
import axios from 'axios'
import { useCallback, useMemo, useRef, useState } from 'react'
import { ChatResponse } from 'src/shared/common-types'
import { askBIApiUrls } from 'src/shared/url-map'
import { QueryParams } from 'src/shared/metric-types'
import { getThrottleLog } from 'src/shared/common-utils'
import { MatchedDataGroups } from '../components/agent/match'

export type ToolType =
  | 'lookup_data'
  | 'select'
  | 'filter'
  | 'join'
  | 'orderby'
  | 'auto_join'
  | 'calculator'
  | 'time_range'
  | 'time_series_service'

export interface PlanNode {
  data: {
    table: Record<string, any>[]
    schema: {
      column_name: string
      column_type: 'metric' | 'dimension' | 'calculate' | 'unknown' | (string & {})
      metric_name?: string
      dimension_name?: string
      expression?: string
      original_name?: string
      original_param_key?: string
    }[]
  }
  is_leaf: boolean
  meta?: {
    query_metric_result: Record<
      string,
      {
        query_metric: QueryParams
      }
    >
    'metric-query-response': any
  }
  param_key: string
  parents: string[]
  tool: ToolType
  tool_params: string[]
  tool_kw_params?: {
    description: string
    expression: string
    mapping: Record<string, string>
  }
}

export type Nl2agentStatusToolType =
  | 'bi'
  | 'chat'
  | 'code'
  | 'judge'
  | 'brain'
  | 'fast_lookup'
  | 'early_stop'
  | 'metric_meta'
  | 'metric_attr'
  | 'python_code_tool'
  | 'match'
  | 'run_tools_after_manual_select'

export interface Nl2agentStatusPlan {
  breadcrumbs: string[]
  args: any
  name: Nl2agentStatusToolType
}

export interface Nl2agentStatus {
  breadcrumbs: string[]
  cot: string
  cot_uuid?: string
  tool_type: Nl2agentStatusToolType | null
  plan: Nl2agentStatusPlan[] | null
  data?: string
  status?: 'succeed' | 'failed' | 'running' | 'aborted'
  code?: string
  // only bi tool type
  nl2agent_build_msg?: string
  nl2agent_steps_list?: Record<string, [2, PlanNodeFailed | string] | [1, PlanNode] | [3, MatchedDataGroups]>
  nl2agent_delta_builds?: Record<string, [1, 'table_tools', any[]] | [2, 'table_tools']>
}

export interface ChatProgress {
  close: boolean
  nl2agent_status: string
  task_id: string
  nl2intent: number
  nl2intent_msg: string // { "intent": "归因" }
  nl2time_query: number
  nl2time_query_msg: string
  nl2attr_params: number
  nl2attr_params_msg: string
  nl2time_attr: number
  nl2time_attr_msg: string
  nl2meta: number
  nl2meta_msg: string
  nl2metric_group_bys: number
  nl2metric_group_bys_msg: string // { "groupBys": ["value"], "notExistGroupBys": [] }
  nl2metric_where: number
  nl2metric_where_msg: string
  nl2metric_metrics: number
  nl2metric_metrics_msg: string
  nl2metirc_order_bys: number
  nl2metirc_order_bys_msg: string
  nl2calculator_get_expression_msg: string
  nl2calculator_steps_list: [number, any, string][]
  nl2agent_build_msg?: string
  nl2agent_steps_list?: [number, any][]
  nl2agent_delta_builds?: [number, string, string][]
}

export interface Nl2IntentMsg {
  thought: string
  steps: [PlanNode['tool'], PlanNode['param_key'], PlanNode['tool_params']][]
  result: PlanNode[]
}

export interface PlanNodeFailed {
  param_key: string
  metric_names: string[]
  query_metric: QueryParams
  row: null
  message: string
  metric_query_resp: ChatResponse
}

export type Nl2agentDeltaBuild = [PlanNode['tool'], PlanNode['param_key'], PlanNode['tool_params']]

function convertNl2IntentMsg({ data }: { data: ChatProgress[] }) {
  if (data.length === 0 || !data.at(-1)?.nl2intent_msg) return null
  try {
    const intentData = JSON.parse(JSON.parse(data.at(-1)!.nl2intent_msg)) as {
      intent: string
      askbi: boolean
      askdoc: boolean
    }
    return {
      ...intentData,
      askbi: intentData.askbi ?? true,
      askdoc: intentData.askdoc ?? true,
    }
  } catch (e) {
    return null
  }
}

function checkClose({ data }: { data: ChatProgress[] }) {
  return data.some((v) => v.close)
}

function convertNl2agentStatus({ data }: { data: ChatProgress[] }): Nl2agentStatus[] | null {
  try {
    return JSON.parse(data[0].nl2agent_status)
  } catch (_) {
    return null
  }
}

function useUpdate() {
  const [_, setState] = useState(false)
  return useCallback(() => {
    setState((v) => !v)
  }, [])
}

async function fetchChatProgress(taskId: string) {
  if (!taskId) return []
  const res = await axios.get(askBIApiUrls.chatProgress, {
    params: {
      taskId: taskId,
    },
  })
  if (res.data.code !== 0) throw new Error('ChatProgress: fetch fail', { cause: res })
  if (!Array.isArray(res.data.data)) throw new Error('ChatProgress: Unknown data type', { cause: res })
  const data: ChatProgress[] = res.data.data
  return data
}

export interface ChatProgressHooks {
  onAfterFetch: (v: ChatProgress) => void
}

const throttleLog = getThrottleLog()

export function useChatProgress({ interval = 1000, taskId }: { interval?: number | null; taskId?: string }) {
  const dataRef = useRef({
    mounted: false,
    intervalId: null as NodeJS.Timeout | null,
    infoList: [] as ChatProgress[],
    hooks: new EventEmitter<ChatProgressHooks>(),
    running: false,
  })

  const startRequest = useCallback(() => {
    if (interval === null) return
    if (dataRef.current.intervalId) clearInterval(dataRef.current.intervalId)
    console.info('ChatProgress Start Request')
    dataRef.current.intervalId = setInterval(() => {
      if (dataRef.current.running) return
      dataRef.current.running = true
      fetchChatProgress(taskId ?? '')
        .then((data) => {
          if (data.length) {
            const list = dataRef.current.infoList
            list.length = 0
            list.push(data.at(-1)!)
            const newData = dataRef.current.infoList.at(-1)
            if (newData) {
              throttleLog('ChatProgress Emit New Data', transformChatProgressData(newData))
              return dataRef.current.hooks.emit('onAfterFetch', newData)
            }
          }
        })
        .catch((err) => {
          if (err?.name === 'CanceledError') return
          console.info('===> useChatProgress Error', err)
        })
        .finally(() => {
          dataRef.current.running = false
        })
    }, interval)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [interval, taskId])

  const cancelRequest = useCallback(() => {
    console.info('ChatProgress Cancel Request')
    if (dataRef.current.intervalId) {
      clearInterval(dataRef.current.intervalId)
    }
  }, [])

  const transformChatProgressData = useCallback(
    (data: ChatProgress) => ({
      close: checkClose({ data: [data] }),
      nl2IntentMsg: convertNl2IntentMsg({ data: [data] }),
      nl2agentStatus: convertNl2agentStatus({ data: [data] }),
    }),
    [],
  )

  const chatProgress = useMemo(() => {
    return {
      data: dataRef.current,
      getLatestData: () => dataRef.current.infoList.at(-1),
      useUpdate,
      startRequest,
      cancelRequest,
      transformChatProgressData,
    }
  }, [cancelRequest, startRequest, transformChatProgressData])

  if (!taskId) return null

  return chatProgress
}
