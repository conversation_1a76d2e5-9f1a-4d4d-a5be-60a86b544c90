import { useLayoutEffect, useState } from 'react'
import { ThemeConfig } from 'antd'
import { useAtomValue } from 'jotai/react'
import { appearanceAtom } from '../pages/System/Appearance/appearanceSettingAtom'

function hasClassWithPrefix(classList: string[], prefix: string) {
  return classList.some((className) => className.startsWith(prefix))
}

const hexToFull = (hex: string) => {
  if (/^#.{3}$/.test(hex)) {
    // 移除 # 并扩展 3 位 HEX 到 6 位（如 #123 -> #112233）
    const fullHex = hex.replace(/^#?([a-f\d])([a-f\d])([a-f\d])$/i, (_, r, g, b) => `#${r}${r}${g}${g}${b}${b}`)

    return fullHex // 返回空格分隔的 RGB 值
  } else {
    return hex
  }
}

const defaultTheme = {
  cssVar: { prefix: '' },
  token: {
    colorPrimary: '#6A58EC',
    colorPrimaryActive: '#503CE4',
    colorPrimaryHover: '#503CE4',
    borderRadius: 4,
    colorLink: '#5542E1',
  },
}
export function useTheme() {
  const [theme, setTheme] = useState<ThemeConfig>(defaultTheme)
  const appearanceConfig = useAtomValue(appearanceAtom)
  // todo 临时方案,后续可以改成更通用的方式
  useLayoutEffect(() => {
    const newTheme: any = { ...defaultTheme }
    if (appearanceConfig) {
      if (appearanceConfig.primaryColor) {
        const primaryColor = hexToFull(appearanceConfig.primaryColor)
        newTheme.token = {
          colorPrimary: primaryColor,
          colorPrimaryActive: primaryColor + 'ee',
          colorPrimaryHover: primaryColor + 'ee',
          borderRadius: 4,
          colorLink: primaryColor,
        }
      }
    }

    const htmlEl = document.querySelector('html')
    const hasBaowuPrefix = hasClassWithPrefix([...(htmlEl?.classList || [])], 'baowu')
    if (hasBaowuPrefix) {
      // 宝武主题
      setTheme({
        cssVar: { prefix: '' },
        token: {
          colorPrimary: '#2c6add',
          colorPrimaryActive: '#2c6add',
          colorPrimaryHover: '#2c6add',
          borderRadius: 4,
          colorLink: '#2c6add',
        },
      })
    } else {
      // 数巅及其他自定义主题
      setTheme({
        ...newTheme,
      })
    }
  }, [appearanceConfig])
  return [theme]
}
