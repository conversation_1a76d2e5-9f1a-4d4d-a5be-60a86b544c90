// @ts-nocheck
import {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    DiengineDatasourceApi,
    DiengineMetricsApi,
    DiengineVtableApi,
    <PERSON>nginePufaApi,
    DiengineMvApi,
    DiengineMetaApi,
    DiengineApiEngineV1MvApi,
    DiengineApiEngineV1PtableApi,
    DiengineApiEngineV1VtableApi,
    DiengineApiEngineV1CatalogApi,
    DiengineApiEngineV1EditorApi,
    DiengineApiEngineV1DatabaseApi,
    DiengineApiEngineV1MetaApi,
    DiengineApiEngineV1SqlApi,
    DiengineApiEngineV1MetricsApi,
    DiengineApiEngineV1SmartApi,
    DiengineApiEngineV1IndexApi,
    DiengineApiSysApi,
    DiengineApiEngineV1SupersetApi,
    DiengineApiAuthApi,
    DiengineApiEngineV1LogApi,
    DiengineApiEngineV1DatasourceApi,
    DiengineApiEngineV1Rep<PERSON><PERSON><PERSON>,
    DiengineApiEngineV1StackApi,
    DiengineApiEngineV1ThreadApi,
    DiengineApiEngineV1TaskApi,
    DiengineApiEngineV1SettingApi,
    DiengineApiEngineV1NoticeApi,
    DiengineApiEngineV1SchedulerApi,
    DiengineApiEngineV1DiagnosisNodeApi,
    DiengineApiRangerApi,
    DiengineApiEngineV1MetricsModelApi
} from './generated-api'

export * from './generated-api'

interface IApi
    extends DiengineApi,
        DiengineDatasourceApi,
        DiengineMetricsApi,
        DiengineVtableApi,
        DienginePufaApi,
        DiengineMvApi,
        DiengineMetaApi,
        DiengineApiEngineV1MvApi,
        DiengineApiEngineV1PtableApi,
        DiengineApiEngineV1VtableApi,
        DiengineApiEngineV1CatalogApi,
        DiengineApiEngineV1EditorApi,
        DiengineApiEngineV1DatabaseApi,
        DiengineApiEngineV1MetaApi,
        DiengineApiEngineV1SqlApi,
        DiengineApiEngineV1MetricsApi,
        DiengineApiEngineV1IndexApi,
        DiengineApiSysApi,
        DiengineApiEngineV1SupersetApi,
        DiengineApiAuthApi,
        DiengineApiEngineV1DatasourceApi,
        DiengineApiEngineV1LogApi,
        DiengineApiEngineV1SmartApi,
        DiengineApiEngineV1ReplicaApi,
        DiengineApiEngineV1StackApi,
        DiengineApiEngineV1ThreadApi,
        DiengineApiEngineV1TaskApi,
        DiengineApiEngineV1NoticeApi,
        DiengineApiEngineV1SchedulerApi,
        DiengineApiEngineV1DiagnosisNodeApi,
        DiengineApiRangerApi,
        DiengineApiEngineV1SettingApi,
        DiengineApiEngineV1MetricsModelApi{ }

class ApiC
    implements
        DiengineApi,
        DiengineDatasourceApi,
        DiengineMetricsApi,
        DiengineVtableApi,
        DienginePufaApi,
        DiengineMvApi,
        DiengineMetaApi,
        DiengineApiEngineV1MvApi,
        DiengineApiEngineV1PtableApi,
        DiengineApiEngineV1VtableApi,
        DiengineApiEngineV1CatalogApi,
        DiengineApiEngineV1EditorApi,
        DiengineApiEngineV1DatabaseApi,
        DiengineApiEngineV1MetaApi,
        DiengineApiEngineV1SqlApi,
        DiengineApiEngineV1MetricsApi,
        DiengineApiEngineV1SmartApi,
        DiengineApiSysApi,
        DiengineApiEngineV1SupersetApi,
        DiengineApiAuthApi,
        DiengineApiEngineV1DatasourceApi,
        DiengineApiEngineV1LogApi,
        DiengineApiEngineV1IndexApi,
        DiengineApiEngineV1ReplicaApi,
        DiengineApiEngineV1StackApi,
        DiengineApiEngineV1ThreadApi,
        DiengineApiEngineV1TaskApi,
        DiengineApiEngineV1NoticeApi,
        DiengineApiEngineV1SchedulerApi,
        DiengineApiEngineV1DiagnosisNodeApi,
        DiengineApiRangerApi,
        DiengineApiEngineV1SettingApi,
        DiengineApiEngineV1MetricsModelApi{ }

applyMixins(ApiC, [
    DiengineApi,
    DiengineDatasourceApi,
    DiengineMetricsApi,
    DiengineVtableApi,
    DienginePufaApi,
    DiengineApiEngineV1MvApi,
    DiengineApiEngineV1MvApi,
    DiengineMvApi,
    DiengineMetaApi,
    DiengineApiEngineV1MvApi,
    DiengineApiEngineV1PtableApi,
    DiengineApiEngineV1VtableApi,
    DiengineApiEngineV1CatalogApi,
    DiengineApiEngineV1EditorApi,
    DiengineApiEngineV1DatabaseApi,
    DiengineApiEngineV1MetaApi,
    DiengineApiEngineV1SqlApi,
    DiengineApiEngineV1MetricsApi,
    DiengineApiEngineV1SmartApi,
    DiengineApiSysApi,
    DiengineApiEngineV1SupersetApi,
    DiengineApiEngineV1IndexApi,
    DiengineApiEngineV1DatasourceApi,
    DiengineApiEngineV1LogApi,
    DiengineApiAuthApi,
    DiengineApiEngineV1ReplicaApi,
    DiengineApiEngineV1StackApi,
    DiengineApiEngineV1ThreadApi,
    DiengineApiEngineV1TaskApi,
    DiengineApiEngineV1SettingApi,
    DiengineApiEngineV1NoticeApi,
    DiengineApiEngineV1DiagnosisNodeApi,
    DiengineApiEngineV1SchedulerApi,
    DiengineApiRangerApi,
    DiengineApiEngineV1MetricsModelApi
])

function applyMixins(derivedCtor: any, baseCtors: any[]) {
    baseCtors.forEach((baseCtor) => {
        Object.getOwnPropertyNames(baseCtor.prototype).forEach((name) => {
            derivedCtor.prototype[name] = baseCtor.prototype[name]
        })
    })
}

export const Api: IApi = new ApiC()
