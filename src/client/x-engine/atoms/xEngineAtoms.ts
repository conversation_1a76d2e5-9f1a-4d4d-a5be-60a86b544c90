/**
 * xengine 的全局状态管理
 */
import { atom } from 'jotai'

export const tableInfoListWithWindowId = atom([
    // {
    //     tableData: {
    //         id: 277,
    //         catalogName: 'dipeak',
    //         tag: null,
    //         user: null,
    //         databaseName: 'default',
    //         name: 'vt_CUSTOMER',
    //         columns: [
    //             {
    //                 id: 0,
    //                 name: 'C_CUSTKEY',
    //                 columnType: 'DECIMAL',
    //                 columnPrecision: 0,
    //                 columnScale: 0,
    //                 comment: '',
    //                 not_allow_null: false,
    //                 displayList: null,
    //             }
    //         ],
    //         query: 'SELECT * FROM `oracle_datasource`.`TPCH`.`CUSTOMER`',
    //         virtualTableType: 'LIKE',
    //         hot: 0,
    //         timeColumn: null,
    //         like: 'CUSTOMER',
    //         computeType: null,
    //         settings: {},
    //         modification: {
    //             creator: '',
    //             gmtCreated: 1703512916198,
    //             modifier: '',
    //             gmtModified: 1703512916198,
    //         },
    //         displayList: null,
    //         joinVO: null,
    //         unionVO: null,
    //         streamBatchVO: null,
    //         exampleSql: null,
    //         labels: {
    //             database: 'TPCH',
    //             catalog: 'oracle_datasource',
    //         },
    //         dataModelDesc: null,
    //         createTime: 1703512916198,
    //         creator: '',
    //         partitionKey: [],
    //     },
    //     windowId: 'jsjdfjsdjf',
    // },
])
// 控制默认激活 tab
export const activeTabKeyInSearchResultTab = atom<{ [windowId: string]: string }>({})

export const envAtom = atom<Record<string, string | number>>({
    MV_QUERY_DAYS: 30,
})
