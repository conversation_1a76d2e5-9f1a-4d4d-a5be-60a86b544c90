import {  FormInstance } from 'antd'
import { atom } from 'jotai'
import { DataModelDescFilterType, ErFlowEdgeDataType } from 'src/shared/xengine-types'

export type EtlPaintInfoAtomType = {
  type: string
  dataSceneId: string 
  isDrawer: boolean
  createNodeStepShowOpen: boolean
  virtualTable?: any
  isReadyToCreate?: boolean
  file?: Blob | File | string | FormData
  nodeInfo?: any
  commonScene?: {
    sceneId: string
    sceneName: string
    projectId: string
    projectName: string
  }
}

// 判断用户有没有引入事实表

export const hasFactAtom = atom(false)

// 标识 用户当前正在构建的是什么类型的 业务虚拟表 STREAM: 流式er Batch：批式er BATCH_STREAM：批流一体
export const dataModelDescTypeAtom = atom('')

export const erFlowDataAtom = atom<{
  timeLimitModalForm: FormInstance<any> | null
  filterData:  DataModelDescFilterType | null
}>({
  timeLimitModalForm: null,
  filterData: null,
})

export const etlPaintInfoAtom = atom<EtlPaintInfoAtomType>({
  dataSceneId: '',
  type: '',
  createNodeStepShowOpen: false,
  isDrawer: false,
})

export const erFlowEdgesAtom = atom<ErFlowEdgeDataType[]>([])
