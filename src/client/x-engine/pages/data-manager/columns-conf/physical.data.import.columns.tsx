// @ts-nocheck
import { Form, Popconfirm, Button, Modal, message, Tooltip, Tag, InputNumber } from 'antd'
import {
    SyncOutlined,
    ExclamationCircleOutlined,
    CheckCircleOutlined,
    MinusCircleOutlined,
    ClockCircleOutlined,
} from '@ant-design/icons'
import { ColumnsType } from 'antd/lib/table/interface'
import { Api } from '@api'
import moment from 'moment'
import Broadcast from '@libs/broadcast'
import React from 'react'

import '../data-manager.module.scss'

interface Record {
    id: any
    projectId: any
    storageType: string
    mysqlDatabase: string
    mysqlTable: string
    mysqlUrl: string
    mysqlUser: string
    mysqlVersion: string
    kafkaBrokerList: string
    kafkaFormat: string
    kafkaGroupName: string
    kafkaNumConsumers: string
    kafkaTopicList: string
    readisIp: string
    radisPort: number
    radisAuth: string
}

// 数据导入
const loadData = (record: Record, importThreadCount: number) => {
    const { projectId, id } = record
    const loadDatasourceParam: any = []
    loadDatasourceParam.push({
        projectId,
        datasourceId: id,
        importThreadCount,
    })

    message.loading({ content: '数据导入中……', key: 'dataimport', duration: 1 })
    return Api.apiLoadDatasourcePost({ loadDatasourceParam: loadDatasourceParam }).then((data: any) => {
        if (data === true) {
            message.success({ content: '数据导入完成，刷新列表', key: 'dataimport', duration: 2 })
            Broadcast.trigger('PHYSICAL_DATA_IMPORT', { refresh: true })
        }
    })
}

const LeadInButton = ({ text, record }) => {
    const [leadFormInfo] = Form.useForm()
    return (
        <Popconfirm
            placement='topRight'
            icon={null}
            overlayClassName={'lead-in-pop'}
            style={{
                marginLeft: 0,
            }}
            title={
                <Form
                    preserve={false}
                    form={leadFormInfo}
                    onFinish={(val) => {
                        console.log(val)
                    }}
                    labelCol={{ span: 10 }}
                    wrapperCol={{ span: 14 }}
                >
                    <Form.Item
                        label='并行数'
                        name='importThreadCount'
                        rules={[
                            {
                                required: true,
                                message: '请输入',
                            },
                        ]}
                    >
                        <InputNumber style={{ marginRight: 20 }} max={100} min={1} placeholder='请输入' />
                    </Form.Item>
                </Form>
            }
            onConfirm={() => {
                // 校验如果出错了 弹窗不会消失
                return leadFormInfo.validateFields().then((values) => {
                    return loadData(record, values.importThreadCount)
                })
            }}
            okText='确认'
            cancelText='取消'
        >
            {(() => {
                switch (text) {
                    case 'CREATED':
                        return (
                            <Button size='large' type='primary'>
                                <em className='btn-m'>导入数据</em>
                                <i className='btn-v'>(完成智能加速)</i>
                            </Button>
                        )
                    default:
                        return <Button>重新导入</Button>
                }
            })()}
        </Popconfirm>
    )
}

const showDetail = (record: Record) => {
    const { storageType } = record
    const {
        mysqlDatabase,
        mysqlTable,
        mysqlUrl,
        mysqlUser,
        mysqlVersion,
        kafkaBrokerList,
        kafkaFormat,
        kafkaGroupName,
        kafkaNumConsumers,
        kafkaTopicList,
        readisIp,
        radisPort,
        radisAuth,
    } = record

    const content =
        storageType === 'MYSQL' ? (
            <>
                <p>数据库: {mysqlDatabase} </p>
                <p>数据表: {mysqlTable} </p>
                <p>IP: {mysqlUrl} </p>
                <p>用户名: {mysqlUser} </p>
                <p>密码: ****** </p>
                <p>版本: {mysqlVersion}</p>
            </>
        ) : storageType === 'KAFKA' ? (
            <>
                <p>brocker地址: {kafkaBrokerList} </p>
                <p>消息体格式: {kafkaFormat} </p>
                <p>消费组名称: {kafkaGroupName} </p>
                <p>topic列表: {kafkaTopicList}</p>
                <p>partition数目: {kafkaNumConsumers} </p>
            </>
        ) : (
            <>
                <p>redis ip: {readisIp} </p>
                <p>redis port: {radisPort} </p>
                <p>redis auth: {radisAuth} </p>
            </>
        )

    Modal.success({
        title: '数据源详情',
        width: 600,
        content: content,
    })
}

const data: ColumnsType[number][] = [
    {
        title: '表ID',
        dataIndex: 'physicalTableId',
        width: 100,
    },
    {
        title: '表名称',
        dataIndex: 'name',
        width: 200,
        render: (value) => {
            return (
                <Tooltip title={value} mouseEnterDelay={1}>
                    <div className='text-ellipsis' style={{ maxWidth: '200px' }}>
                        {value}
                    </div>
                </Tooltip>
            )
        },
    },
    {
        title: '计算类型',
        align: 'center',
        width: 100,
        dataIndex: 'computeType',
        render: (text) => (text === 'FLOW_COMPUTE' ? '流计算' : text === 'BATCH_COMPUTE' ? '批量计算' : '-'),
    },
    {
        title: '表类型',
        align: 'center',
        width: 120,
        dataIndex: 'tableType',
        render: (text) =>
            text === 'FACT_TYPE' ? (
                <Tag color='#108ee9'>事实表</Tag>
            ) : text === 'DIMENSION_TYPE' ? (
                <Tag color='#87d068'>维度表</Tag>
            ) : (
                '-'
            ),
    },
    {
        title: '导入状态',
        dataIndex: 'importerStatus',
        align: 'center',
        width: 150,
        render: (text) => {
            switch (text) {
                case 'CREATED':
                    return <Tag icon={<ClockCircleOutlined />}>已创建</Tag>
                case 'DROPPED':
                    return <Tag icon={<MinusCircleOutlined spin />}>已删除</Tag>
                case 'LOADING_COMPLETED':
                    return (
                        <Tag color='green' icon={<CheckCircleOutlined />}>
                            导入完成
                        </Tag>
                    )
                case 'LOADING_ERROR':
                    return (
                        <Tag color='red' icon={<ExclamationCircleOutlined />}>
                            导入出错
                        </Tag>
                    )
                case 'LOADING':
                    return (
                        <Tag color='cyan' icon={<SyncOutlined spin />}>
                            导入中
                        </Tag>
                    )
                case 'LONG_TERM':
                    return (
                        <Tag color='cyan' icon={<SyncOutlined spin />}>
                            导入中
                        </Tag>
                    )
                case 'STOPPED':
                    return (
                        <Tag color='gold' icon={<MinusCircleOutlined spin />}>
                            已停止
                        </Tag>
                    )
                case 'UNKNOWN':
                    return (
                        <Tag color='orange' icon={<ExclamationCircleOutlined spin />}>
                            未知错误
                        </Tag>
                    )
                default:
                    return '-'
            }
        },
    },
    {
        title: '导入行数',
        align: 'center',
        width: 100,
        dataIndex: 'importerRows',
        render: (text) => {
            return text === 0 ? '-' : text
        },
    },
    {
        title: '更新频率',
        align: 'center',
        width: 100,
        dataIndex: 'frequency',
    },
    {
        title: '主键',
        dataIndex: 'primaryKeys',
        align: 'center',
        width: 150,
        render: (text) => {
            const parseText = (text && JSON.parse(text)) || []
            return parseText.join(',') || '-'
        },
    },
    {
        title: '存储类型',
        align: 'center',
        width: 120,
        dataIndex: 'tableEngine',
    },
    {
        title: '建表语句',
        align: 'center',
        dataIndex: 'ddlSql',
        width: 120,
        render: (text) => {
            return (
                <Popconfirm placement='right' title={text} showCancel={false} okText='确认'>
                    <a href='#!'>查看SQL</a>
                </Popconfirm>
            )
        },
    },
    {
        title: '数据源',
        align: 'center',
        width: 150,
        dataIndex: 'mysqlUrl',
        render: (text, record: any) => {
            return (
                <Button type='link' onClick={() => showDetail(record)}>
                    查看源信息
                </Button>
            )
        },
    },
    {
        title: '创建时间',
        dataIndex: ['gmtCreated'],
        render: (text) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
        width: 200,
    },
    {
        title: '更新时间',
        dataIndex: 'importerUpdateTime',
        width: 200,
        render: (text) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
        title: '操作',
        dataIndex: 'importerStatus',
        fixed: 'right',
        align: 'center',
        width: 150,
        render: (text, record: any) => {
            return <LeadInButton text={text} record={record} />
        },
    },
]

export default data
