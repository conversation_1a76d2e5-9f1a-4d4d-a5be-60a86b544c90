// @ts-nocheck
import React from 'react'
import { useAntdTable } from 'ahooks'
import { PageHeader } from '@ant-design/pro-layout'
import { Form, Table, message } from 'antd'
import { Api } from '@api'
import dataColumns from './columns-conf/physical.data.import.columns'
import Broadcast from '@libs/broadcast'
import './data-manager.module.scss'

let listenTimer: any = null

interface ImportData {
    refresh: boolean
}

const getListTableData: any = ({ current, pageSize }) => {
    return Api.apiListPTablesPost({ current, pageSize }).then((data) => ({
        list: data,
        total: data?.length,
    }))
}

function physicalDataImport() {
    const [form] = Form.useForm()
    const { tableProps: leadIntableProps, run: getAccRes } = useAntdTable(getListTableData, {
        defaultPageSize: 100,
        form,
    })

    Broadcast.listen('PHYSICAL_DATA_IMPORT', (data: ImportData) => {
        if (data.refresh === true && listenTimer === null) {
            listenTimer = setTimeout(() => {
                clearTimeout(listenTimer)
                listenTimer = null
                getAccRes({
                    current: 1,
                    pageSize: 100,
                })
                message.loading({ content: '数据列表已刷新', key: 'dataimport', duration: 1 })
            }, 500)
        }
    })

    return (
        <div>
            <PageHeader title='数据导入' />
            <Table
                size='middle'
                columns={dataColumns}
                rowKey='physicalTableId'
                {...leadIntableProps}
                scroll={{ x: '120%' }}
            />
        </div>
    )
}

export default physicalDataImport
