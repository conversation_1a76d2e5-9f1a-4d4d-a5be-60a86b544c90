// @ts-nocheck
import React from 'react'
import { useAntdTable } from 'ahooks'
import { Form, Table } from 'antd'
import { PageHeader } from '@ant-design/pro-layout'
import { getColumns } from './columns-conf/virtual.data.manager.columns'
import { Api } from '@api'
import { useNavigate } from 'react-router-dom'
import { routerMap } from '@XEngineRouter/routerMap'

const getListTableData = ({ current, pageSize }) =>
  // /api/engine/v1/vtable/list
  Api.apiEngineV1VtableListGet({
    database: 'default',
    catalog: 'default',
    current,
    pageSize,
  }).then((data: any) => ({
    list: data?.data,
    total: data?.length,
  }))

function virtualDataManager() {
  const [form] = Form.useForm()
  const navigate = useNavigate()
  const jump = (data: any) => {
    const query = Object.keys(data)
      .map((key) => `${key}=${data[key]}`)
      .join('&')
    navigate(`${routerMap.dataManager.offlineLargeTable.path}?${query}`)
  }
  const { tableProps: leadIntableProps } = useAntdTable(getListTableData, {
    defaultPageSize: 20,
    form,
  })

  return (
    <div>
      <PageHeader title="虚拟表管理" />
      <Table size="middle" columns={getColumns(jump)} rowKey="id" {...leadIntableProps} scroll={{ x: '100%' }} />
    </div>
  )
}

export default virtualDataManager
