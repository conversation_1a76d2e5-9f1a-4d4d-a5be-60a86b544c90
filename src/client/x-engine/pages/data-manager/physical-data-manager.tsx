// @ts-nocheck
import React from 'react'
import { useAntdTable } from 'ahooks'
import { Form, Table } from 'antd'
import { PageHeader } from '@ant-design/pro-layout'
import { Api } from '@api'
import dataColumns from './columns-conf/physical.data.manager.columns'
import Drawers from './columns-conf/physical.data.draw'
import './data-manager.module.scss'

interface Page {
    current: number
    pageSize: number
}

const getListTableData = ({ current, pageSize }: Page) => {
    const data = Api.apiEngineV1PtableListGet({
        database: 'default',
        catalog: 'default',
        current,
        pageSize,
    }).then((data: any) => ({
        list: data?.data,
        total: data?.length,
    }))
    return data
}

function physicalDataManager() {
    const [form] = Form.useForm()

    const { tableProps: leadIntableProps, run: getAccRes } = useAntdTable(getListTableData, {
        defaultPageSize: 10,
        form,
    })

    // 创建table成功，刷新列表到第一页
    const createTableSucc = () => {
        return getAccRes({
            current: 1,
            pageSize: 100,
        })
    }

    return (
        <div>
            <PageHeader title='物理表管理' extra={[<Drawers succ={createTableSucc} />]} />
            <Table
                size='middle'
                columns={dataColumns}
                rowKey='physicalTableId'
                {...leadIntableProps}
                scroll={{ x: '100%' }}
            />
        </div>
    )
}

export default physicalDataManager
