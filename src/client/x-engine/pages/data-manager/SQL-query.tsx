// @ts-nocheck
import React, { useEffect, useRef, useState } from 'react'
import { message, Tabs, Layout, Card } from 'antd'
import { Api } from '@api'
import { useRequest, useThrottleFn } from 'ahooks'
import { useImmer } from 'use-immer'
import { findItemById, getRandomString, getTime, supplementCode } from '@model/sql/method'
import { ButtonInput } from '@model/sql/ButtonInput'
import { TabContent } from '@model/sql/TabContent'
import styles from './data-manager.module.scss'
import SQLQueryDetail from '@pages/data-manager/SQL-query-detail'
import Broadcast from '@libs/broadcast'
import { SearchTableTree } from '@model/sql'
import { ReflexContainer, ReflexSplitter, ReflexElement, ReflexElementProps } from 'react-reflex'
import { LeftOutlined } from '@ant-design/icons'
import { useAtom } from 'jotai/index'
import { tableInfoListWithWindowId } from '@atoms/xEngineAtoms'
import { PageHeader } from '@ant-design/pro-layout'
import { DndContext, closestCenter, PointerSensor, useSensor } from '@dnd-kit/core'
import { SortableContext, horizontalListSortingStrategy, useSortable, arrayMove } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'

type ReflexElementPropsWidthChildren = ReflexElementProps & {
  children?: React.ReactNode
  id: string
  setFlexInfo: (arg: Partial<flexInfo>) => void
}

interface DraggableTabPaneProps extends React.HTMLAttributes<HTMLDivElement> {
  'data-node-key': string
}

interface flexInfo {
  pane1: number
  pane1_1: number
  pane1_2: number
  pane2: number
}

const PANE_1_MIN_SIZE = 265

enum operateType {
  grow = 'grow',
  shrink = 'shrink',
}

interface IChangeSizeData {
  name: string
  operate: keyof operateType
  number: number
}

const DraggableTabNode: React.FC<Readonly<DraggableTabPaneProps>> = ({ className, ...props }) => {
  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({
    id: props['data-node-key'],
  })

  const style: React.CSSProperties = {
    ...props.style,
    transform: CSS.Translate.toString(transform),
    transition,
    cursor: 'move',
  }

  return React.cloneElement(props.children as React.ReactElement<any>, {
    ref: setNodeRef,
    style,
    ...attributes,
    ...listeners,
  })
}

const getPaneDirection = (flexSize = Infinity, stadarValue: number) => {
  return flexSize > stadarValue ? operateType.shrink : operateType.grow
}

const CustomCom = (props) => {
  useEffect(() => {
    Broadcast.listen('changeSize', (data: IChangeSizeData) => {
      if (data.name === props.name) {
        if (data.operator === operateType.grow) {
          onMaximize(data.number)
        } else {
          onMinimize(data.number)
        }
      }
    })
  }, [])

  const cur = useRef()

  const [state, setState] = useState({
    size: -1,
  })

  const onMinimize = (endSize = 0) => {
    if (cur.current) {
      cur.current.style.overflow = 'hidden'
    }
    setState(() => ({
      size: endSize,
    }))
  }

  const onMaximize = (endSize = 400) => {
    setState({
      size: endSize,
    })
  }

  const { run } = useThrottleFn(
    () => {
      props.setFlexInfo((draft) => {
        draft[props.name] = props.flex
      })
    },
    { wait: 100 },
  )
  useEffect(() => {
    return run()
  }, [props.flex])
  return (
    <ReflexElement ref={cur} size={state.size} {...props}>
      <div className="pane-content" ref={cur}>
        <div className="ctrl-pane-content">{props.children}</div>
      </div>
    </ReflexElement>
  )
}

const ControlledElement = React.forwardRef((props: ReflexElementPropsWidthChildren, ref) => {
  return <CustomCom innerRef={ref} {...props} />
})

const App: React.FC = () => {
  const toggleClosable = (key: string, value: boolean) => {
    setTabItems((pre) => {
      return pre.map((item) => {
        if (item.key === key) {
          if (value !== undefined) {
            return { ...item, closable: value }
          } else {
            return { ...item, closable: !item.closable }
          }
        } else {
          return item
        }
      })
    })
  }

  const [initKey] = useState(getRandomString())
  const [activeKey, setActiveKey] = useState(initKey)
  const [windowInfoList, setWindowInfoList] = useImmer([])

  const pushNewWindow = (draft) => {
    draft.push({
      editor: null,
      title: `SQL-${getTime()}`,
      windowId: undefined,
      key: initKey,
      closable: true,
    })
  }
  useRequest(Api.apiEngineV1EditorListGet, {
    onSuccess(data) {
      if (data.length === 0) {
        setWindowInfoList(pushNewWindow)
      } else {
        const windowList = data.map((i) => {
          return {
            queryEngine: i.queryEngine,
            title: i.windowName,
            editor: null,
            key: getRandomString(),
            windowId: i.windowId,
            sqlContent: i.sqlContent,
            closable: true,
          }
        })

        setWindowInfoList((draft) => {
          draft.push(...windowList)
        })
        setActiveKey(windowList[0].key)
      }
    },
    onError() {
      setWindowInfoList(pushNewWindow)
      message.error('获取窗口信息失败')
    },
  })

  // windowarr
  const [tabItems, setTabItems] = useState([])
  useEffect(() => {
    const x = windowInfoList.map((item) => ({
      label: <ButtonInput itemKey={item.key} setWindowInfoList={setWindowInfoList} title={item.title} />,
      children: (
        <TabContent
          queryEngine={item.queryEngine}
          title={item.title}
          setWindowInfoList={setWindowInfoList}
          windowId={item.windowId}
          sqlContent={item.sqlContent}
          itemKey={item.key}
          toggleClosable={toggleClosable}
          closable={item.closable}
        />
      ),
      key: item.key,
      closable: item.closable,
    }))
    if (x.length === 1) {
      x[0].closable = false
    }
    setTabItems(x)
  }, [windowInfoList])

  const onChange = (newActiveKey: string) => {
    setActiveKey(newActiveKey)
  }

  const addWindowInfoListItem = () => {
    const newActiveKey = getRandomString()
    const title = getTime()
    setWindowInfoList((draft) => {
      draft.push({
        queryEngine: 'XEngine',
        key: newActiveKey,
        title,
        editor: null,
        windowId: undefined,
      })
    })
    setActiveKey(newActiveKey)
  }

  const removeWindowInfoListItem = (targetKey: string) => {
    let newActiveKey = activeKey
    let lastIndex = -1
    tabItems.forEach((item, i) => {
      if (item.key === targetKey) {
        lastIndex = i - 1
      }
    })
    const newPanes = tabItems.filter((item) => item.key !== targetKey)
    if (newPanes.length && newActiveKey === targetKey) {
      if (lastIndex >= 0) {
        newActiveKey = newPanes[lastIndex].key
      } else {
        newActiveKey = newPanes[0].key
      }
    }
    setWindowInfoList((draft) => {
      return draft.filter((item) => {
        return item.key !== targetKey
      })
    })
    setActiveKey(newActiveKey)
  }
  const { run: runDeleteWindow } = useRequest(Api.apiEngineV1EditorDelete, {
    manual: true,
    retryCount: 3,
  })

  const onEdit = (targetKey: string, action: 'add' | 'remove') => {
    if (action === 'add') {
      addWindowInfoListItem()
    } else {
      const { windowId } = windowInfoList.find((item) => {
        return item.key === targetKey
      })
      runDeleteWindow({ windowId })
      removeWindowInfoListItem(targetKey)
    }
  }

  const {
    run: getQueryInfo,
    data: queryInfo,
    mutate: changeQueryInfo,
  } = useRequest(Api.apiEngineV1SqlQueryInfoGet, { manual: true })

  useEffect(() => {
    Broadcast.listen(
      'OPERATE_SQL_QUERY_DETAIL',
      ({ operate, queryId }: { operate: 'close' | 'open'; queryId?: string }) => {
        if (operate === 'open') {
          getQueryInfo({ queryId })
        } else {
          changeQueryInfo(undefined)
        }
      },
    )
    return Broadcast.definedChannel('OPERATE_SQL_QUERY_DETAIL', [])
  }, [])
  const [pane1Flex, setPane1Flex] = useImmer<number>(0.1)
  const [flexInfo, setFlexInfo] = useImmer<Partial<flexInfo>>({})
  const [flexInfoBack, setFlexInfoBack] = useImmer<Partial<flexInfo>>({})
  const [flexConfig, setFlexConfig] = useImmer<Record<string, ReflexElementPropsWidthChildren>>({
    pane1: {
      name: 'pane1',
      direction: 1,
      id: 'pane1',
      minSize: PANE_1_MIN_SIZE,
      setFlexInfo,
    },
    pane1_1: {
      name: 'pane1_1',
      direction: 1,
      id: 'pane1_1',
      minSize: 0,
      setFlexInfo,
    },
    pane1_2: {
      flex: 0,
      name: 'pane1_2',
      direction: -1,
      id: 'pane1_2',
      minSize: 0,
      setFlexInfo,
    },
    pane2: {
      name: 'pane2',
      direction: -1,
      id: 'pane2',
      minSize: 200,
      setFlexInfo,
    },
  })

  const queryContainer = useRef(null)
  const [domHeight, setDomHeight] = useState<string>()
  useEffect(() => {
    if (queryContainer.current?.ref?.current) {
      const dom = queryContainer.current.ref.current
      const top = dom.getBoundingClientRect().top
      setDomHeight(`calc(100vh - ${top + 26}px)`)
      //  [hack method], initially adjust pane1_2 height as 0.
      Broadcast.trigger('changeSize', {
        name: 'pane1_2',
        operator: operateType.shrink,
        number: 0,
      })
    }
  }, [queryContainer.current])
  const [, setTableInfoListWithWindowId] = useAtom(tableInfoListWithWindowId)

  const sensor = useSensor(PointerSensor, { activationConstraint: { distance: 10 } })

  const onDragEnd = ({ active, over }: DragEndEvent) => {
    if (active.id !== over?.id) {
      setTabItems((prev) => {
        const activeIndex = prev.findIndex((i) => i.key === active.id)
        const overIndex = prev.findIndex((i) => i.key === over?.id)
        return arrayMove(prev, activeIndex, overIndex)
      })
    }
  }
  return (
    <Layout className={styles.queryPage}>
      <div style={{ display: queryInfo ? 'none' : 'block' }}>
        <PageHeader title="SQL 编辑器" />

        <ReflexContainer
          ref={queryContainer}
          orientation="vertical"
          className={styles.queryPageContent}
          style={{
            height: domHeight,
            background: 'white',
          }}
        >
          <ControlledElement flex={pane1Flex} {...flexConfig.pane1}>
            <ReflexContainer
              orientation="horizontal"
              style={{
                height: domHeight,
                background: 'white',
              }}
            >
              <ControlledElement flex={1} {...flexConfig.pane1_1}>
                <SearchTableTree
                  onClickTreeItem={(i, originData) => {
                    if (i.isLeaf) {
                      setTableInfoListWithWindowId(() => {
                        return [
                          {
                            tableData: findItemById(originData, i.id),
                            windowId: windowInfoList.find((w) => {
                              return w.key === activeKey
                            })?.windowId,
                          },
                        ]
                      })
                      if (flexInfo.pane1_2 < 0.03) {
                        Broadcast.trigger('changeSize', {
                          name: 'pane1_2',
                          operator: operateType.grow,
                          number: 300,
                        })
                      }
                    }
                  }}
                  onSupplement={(node) => {
                    const windowInfoItem = windowInfoList.find((i) => {
                      return i.key === activeKey
                    })
                    const { editor } = windowInfoItem
                    const preCode = editor.getValue()?.trim()?.length === 0 ? '' : '\n\n'
                    supplementCode(editor, `${preCode}${node.displayList[0] ?? ''}`)
                  }}
                  onSupplementExampleSQL={(str) => {
                    const windowInfoItem = windowInfoList.find((i) => {
                      return i.key === activeKey
                    })
                    const { editor } = windowInfoItem
                    const preCode = editor.getValue()?.trim()?.length === 0 ? '' : '\n\n'
                    supplementCode(editor, `${preCode}${str ?? ''}`)
                  }}
                />
              </ControlledElement>
            </ReflexContainer>
          </ControlledElement>

          <ReflexSplitter id={styles.reflexSpliter}>
            {/* hidden operate button for until next version */}
            <LeftOutlined
              style={{
                transform: getPaneDirection(flexInfo?.pane1, 0.05) === operateType.grow ? 'rotate(180deg)' : '',
              }}
              className={styles.justi}
              onClick={() => {
                if (getPaneDirection(flexInfo?.pane1, 0.05) === operateType.grow) {
                  const value = flexInfoBack.pane1 <= 0.05 ? 0.3 : flexInfoBack.pane1
                  setFlexConfig((draft) => {
                    draft.pane1.minSize = PANE_1_MIN_SIZE
                  })
                  setPane1Flex(value)
                } else {
                  setFlexInfoBack(flexInfo)
                  setFlexConfig((draft) => {
                    draft.pane1.minSize = 0
                  })
                  setPane1Flex(0)
                }
              }}
            />
          </ReflexSplitter>
          <ControlledElement
            className={`${styles.queryPageContentTabCon} queryPageContentTabContainer`}
            flex={1 - pane1Flex}
            {...flexConfig.pane2}
          >
            <Tabs
              className={`${styles.queryPageTab} pane-content`}
              type="editable-card"
              onChange={onChange}
              activeKey={activeKey}
              onEdit={onEdit}
              items={tabItems}
              renderTabBar={(tabBarProps, DefaultTabBar) => (
                <DndContext sensors={[sensor]} onDragEnd={onDragEnd} collisionDetection={closestCenter}>
                  <SortableContext items={tabItems.map((i) => i.key)} strategy={horizontalListSortingStrategy}>
                    <DefaultTabBar {...tabBarProps}>
                      {(node) => (
                        <DraggableTabNode {...(node as React.ReactElement<DraggableTabPaneProps>).props} key={node.key}>
                          {node}
                        </DraggableTabNode>
                      )}
                    </DefaultTabBar>
                  </SortableContext>
                </DndContext>
              )}
            />
          </ControlledElement>
        </ReflexContainer>
      </div>
      {queryInfo && (
        <Card>
          <SQLQueryDetail queryInfo={queryInfo} />
        </Card>
      )}
    </Layout>
  )
}

export default App
