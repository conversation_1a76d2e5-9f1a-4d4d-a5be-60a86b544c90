import React, { useCallback } from 'react'
import { Result, Button, Empty, message, Table, Tooltip } from 'antd'
import type { TabsProps } from 'antd'
import { routerMap } from '@XEngineRouter/routerMap'
import { LikeParamsProps } from './interface'
import { Link } from 'react-router-dom'
import { Api } from '@api'
import { useAntdTable } from 'ahooks'
import { getUnitId } from '@libs'
import cs from '../data-model.module.scss'

export const virtualTableTypeList = [
  { value: 'LIKE', desc: '贴源虚拟表' },
  { value: 'AS', desc: '业务虚拟表' },
]

export const computeTypeList = [
  { value: 'STREAM', desc: '流计算' },
  { value: 'BATCH', desc: '批计算' },
  { value: 'STREAM_AND_BATCH', desc: '流批一体' },
]

// hash function
export const extraHashFunctionList = [
  'sipHash64',
  'sipHash128',
  'halfMD5',
  'cityHash64',
  'farmFingerprint64',
  'farmHash64',
  'metroHash64',
  'gccMurmurHash',
  'murmurHash232',
  'murmurHash264',
  'murmurHash332',
  'murmurHash364',
  'murmurHash3128',
  'javaHash',
  'javaHashUTF16LE',
  'hiveHash',
  'xxHash32',
  'xxHash64',
  'wyHash64',
  'crc32',
  'sip',
  'unknown',
]

// usage mode
export const usageModeList = [
  { value: 'mergetree', desc: '明细' },
  { value: 'summing_mergetree', desc: '聚合' },
  { value: 'distore', desc: '服务' },
]

export const importImmediatelyList = [
  { value: true, desc: '是' },
  { value: false, desc: '否' },
]

export const tableEngineTypeList = [
  { value: 'hive', desc: 'Hive' },
  { value: 'kafka', desc: 'Kafka' },
  { value: 'external', desc: '外部调度' },
]

export const tableTypeList = {
  hive: 'EXTERNAL_TABLE',
  kafka: 'EXTERNAL_TABLE',
  external: 'MANAGED_TABLE',
}

export const stepItemsText = [{ title: '数据源配置' }, { title: '高级查询配置' }, { title: '虚拟表配置' }]

export const createBusinessVirtualTableStepItemsText = [
  { title: '选择表类型' },
  { title: '构建模型' },
  { title: '表配置' },
]

export const columnTypeList = [
  { value: 'CHAR', desc: 'CHAR' },
  { value: 'VARCHAR', desc: 'VARCHAR' },
  { value: 'DECIMAL', desc: 'DECIMAL' },
  { value: 'FLOAT', desc: 'FLOAT' },
  { value: 'DOUBLE', desc: 'DOUBLE' },
  { value: 'REAL', desc: 'REAL' },
  { value: 'BOOLEAN', desc: 'BOOLEAN' },
  { value: 'TINYINT', desc: 'TINYINT' },
  { value: 'SMALLINT', desc: 'SMALLINT' },
  { value: 'INTEGER', desc: 'INTEGER' },
  { value: 'BIGINT', desc: 'BIGINT' },
  { value: 'TIMESTAMP', desc: 'TIMESTAMP' },
  { value: 'DATE', desc: 'DATE' },
]

export const CREATE_LIKE_TABLE_STEPS = {
  CONFIG_DATASOURCE: 0,
  QUERY_ADVANCED: 1,
  CONFIG_TABLE: 2,
  CREATE_SUCCESS: 3,
}

export const createLikeTabItems: any = [
  { key: '0', children: <></> },
  { key: '1', children: <></> },
  { key: '2', children: <></> },
  {
    key: '3',
    children: (
      <Result
        status="success"
        title={'创建成功'}
        subTitle="你的贴源虚拟表创建成功，点击「查看虚拟表列表」查看所有的虚拟表"
        extra={[
          <Link to={routerMap.dataModel.virtualTable.path} key={'redirect_table_list'}>
            <Button key={'table_list'} type="primary">
              查看虚拟表列表
            </Button>
          </Link>,
        ]}
      />
    ),
  },
]

// virtual table detail conf
export const VIRTUAL_TABLE_DETAIL_PANELS = {
  COLUMN_TYPE: 0, // 字段类型
  DATA_PREVIEW: 1, //数据预览
  INDEX_RECOMMEND: 2, //关联指标
  ER_RELATION: 3, //关联模型
  BLOOD_RELATION: 4, // 血缘关系
  QUALITY_MONITOR: 5, //质量监控
  AUTH_MANAGE: 6, // 权限管理
  RELATED_MV: 7, // 关联物化
}

export const detailTabItems: Exclude<TabsProps['items'], undefined> = [
  {
    key: `${VIRTUAL_TABLE_DETAIL_PANELS.COLUMN_TYPE}`,
    label: `字段类型`,
    children: <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />,
  },
  {
    key: `${VIRTUAL_TABLE_DETAIL_PANELS.DATA_PREVIEW}`,
    label: `数据预览`,
    children: <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />,
  },
  {
    key: `${VIRTUAL_TABLE_DETAIL_PANELS.INDEX_RECOMMEND}`,
    label: `关联指标`,
    children: <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />,
  },
  {
    key: `${VIRTUAL_TABLE_DETAIL_PANELS.ER_RELATION}`,
    label: `关联模型`,
    children: <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />,
  },

  {
    key: `${VIRTUAL_TABLE_DETAIL_PANELS.BLOOD_RELATION}`,
    label: `血缘关系`,
    children: <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />,
  },
  {
    key: `${VIRTUAL_TABLE_DETAIL_PANELS.QUALITY_MONITOR}`,
    label: '质量监控',
    children: <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />,
  },
  {
    key: `${VIRTUAL_TABLE_DETAIL_PANELS.AUTH_MANAGE}`,
    label: '权限管理',
    children: <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />,
  },
  {
    key: `${VIRTUAL_TABLE_DETAIL_PANELS.RELATED_MV}`,
    label: `关联物化`,
    children: <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />,
  },
]

export const columnTypeColumns = [
  {
    title: 'ID',
    dataIndex: 'id',
  },
  {
    title: '字段名称',
    dataIndex: 'name',
  },
  {
    title: '数据类型',
    dataIndex: 'columnType',
  },
  {
    title: '字段说明',
    dataIndex: 'comment',
  },
  {
    title: '字段口径',
    dataIndex: 'customSql',
    render(customSql: string) {
      return <Tooltip title={customSql}>{customSql}</Tooltip>
    },
  },
]

export const indexCommendColumns = [
  {
    title: 'ID',
    dataIndex: 'id',
  },
  {
    title: '指标名称',
    dataIndex: 'name',
  },
  {
    title: '引用字段 ',
    dataIndex: 'business',
  },
  {
    title: '算子',
    dataIndex: 'metricsFunction',
  },
  {
    title: '操作 ',
    dataIndex: 'frequency',
  },
]

export const authUserListColumns = [
  {
    title: '用户名',
    dataIndex: 'userName',
  },
  {
    title: '权限',
    dataIndex: 'privilegeName',
  },
  {
    title: '授权时间',
    dataIndex: 'authorizationTime',
  },
  {
    title: '权限到期时间',
    dataIndex: 'expireDate',
  },
  {
    title: '授权者',
    dataIndex: 'authorizer',
  },
  {
    title: '操作',
    dataIndex: 'actions',
    render: () => (
      <>
        <Button type="link">
          <span data-type="update">编辑</span>
        </Button>
        <Button type="link">
          <span data-type="delete">去除权限</span>
        </Button>
      </>
    ),
  },
]

export const taskListColumns = [
  {
    title: '任务名称',
    dataIndex: 'taskName',
  },
  {
    title: '任务状态',
    dataIndex: 'taskStatus',
  },
  {
    title: '任务创建时间',
    dataIndex: 'createTime',
  },
  {
    title: '任务CPU资源',
    dataIndex: 'cpuResource',
  },
  {
    title: '任务内存资源',
    dataIndex: 'memoryResource',
  },
  {
    title: '任务磁盘资源',
    dataIndex: 'diskResource',
  },
  {
    title: '任务创建者',
    dataIndex: 'creator',
  },
  {
    title: '操作',
    dataIndex: 'actions',
    render: () => (
      <Button type="link">
        <span data-type="detail">详情</span>
      </Button>
    ),
  },
]

export const defaultLikeParams: LikeParamsProps = {
  catalog: '',
  database: '',
  tableEngine: '',
  timeColumn: '',
  name: '',
  columns: [],
  hiveParams: {
    metastore: '',
    dbName: '',
    hiveTableName: '',
  },
  kafkaParams: {
    'kafka.broker.list': '',
    'kafka.topic.list': '',
    'kafka.group.name': '',
    'kafka.format': '',
    'kafka.num.consumers': '',
  },
  externalParams: {
    computeType: '',
    primaryKeys: [],
    storageEngine: '',
  },
  postParams: {},
}

export function DataPreviewTabItem({
  computeType,
  catalog,
  database,
  tableName,
}: {
  computeType: string
  catalog: string
  database: string
  tableName: string
}) {
  // data preview
  const { data: previewData, tableProps: previewDataProps } = useAntdTable(
    useCallback(
      () =>
        Api.apiEngineV1VtablePreviewGet({
          vtableName: tableName,
          catalog,
          database,
          limit: 50,
        }).then((res) => ({
          list: res,
          total: res.length ?? 0,
        })),
      [tableName, catalog, database],
    ),
    {
      ready: Boolean(computeType === 'BATCH'),
      onError(e: any) {
        message.error(e?.msg || '获取虚拟表数据出错')
      },
      defaultPageSize: 10,
    },
  )
  return (
    <Table
      key={VIRTUAL_TABLE_DETAIL_PANELS.DATA_PREVIEW}
      className={cs.dataPreviewTableWidth}
      {...previewDataProps}
      dataSource={previewData?.list}
      columns={Object.keys(previewData?.list[0] ?? []).map((e) => ({
        title: e,
        dataIndex: e,
        width: 300,
      }))}
      scroll={{ x: 'max-content' }}
      rowKey={() => getUnitId()}
    />
  )
}
// INDEX_RECOMMEND: 2, //关联指标
// export function IndexRecommendTabItem({
//   catalog,
//   database,
//   tableName,
// }: {
//   catalog: string
//   database: string
//   tableName: string
// }) {
//   const { tableProps: metricsList } = useAntdTable(
//     () => {
//       return Api.apiEngineV1MetricsListPost({
//         vtableMeta: {
//           catalogName: catalog,
//           databaseName: database,
//           virtualTableName: tableName,
//         },
//         current: 1,
//         pageSize: -1,
//       })
//     },
//     {
//       defaultPageSize: 10,
//       defaultCurrent: 1,
//     },
//   )
//   return <Table key={VIRTUAL_TABLE_DETAIL_PANELS.INDEX_RECOMMEND} columns={columns} rowKey={'id'} {...metricsList} />
// }

// BLOOD_RELATION: 4, // 血缘关系
// export function BloodRelationTabItem({
//   catalog,
//   database,
//   tableName,
// }: {
//   catalog: string
//   database: string
//   tableName: string
// }) {
//   const { data: lineageData } = useRequest(Api.apiEngineV1VtableGetTableLineageGet, {
//     defaultParams: [
//       {
//         catalogName: catalog,
//         databaseName: database,
//         tableName: tableName,
//       },
//     ],
//   })
//   return <GLineage typeKey="type" data={formatVTLineageData(lineageData)} />
// }
// QUALITY_MONITOR: 5, //质量监控
// AUTH_MANAGE: 6, // 权限管理
// RELATED_MV: 7, // 关联物化

export const simpleCronUnitOptions = [
  {
    label: '小时',
    value: 'HOUR',
  },
  {
    label: '天',
    value: 'DAY',
  },
]
