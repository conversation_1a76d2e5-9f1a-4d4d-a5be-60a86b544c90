import React, { useState } from 'react'
import { routerMap } from '@XEngineRouter/routerMap'
import { get } from 'lodash-es'
import Columns from '@ui/table/Columns'
import { message, Popover, Switch, Tag, Typography, Modal, Form, Input, type TableProps, Tooltip } from 'antd'
import { Link } from 'react-router-dom'
import { Broadcast } from '@libs'
import { computeTypeList } from '@pages/data-model/forms-conf/constant-conf'
import axios from 'axios'
import { useAtomValue } from 'jotai'
import { isOpenTenantAtom } from 'src/client/pages/AskBI/askBIAtoms'
import { askBIApiUrls } from 'src/shared/url-map'
import { SvgIcon, editIcon } from 'src/client/components/SvgIcon'
import { streamTaskMap, streamTaskTagColorMap } from '@constant/enums'
import type { AnyObject } from 'src/shared/xengine-types'
import LoadingText from 'src/client/components/LoadingText'
import { customerFilterValue } from 'src/shared/customer-resolver'
enum ITableType {
  STREAM = 'STREAM',
  BATCH = 'BATCH',
}
export const deleteVTableBtn = (record: any) => {
  Broadcast.trigger('@PAGE/DATAMODEL/VIRTUAL_TABLE', record)
}

/**
 * Form items conf
 */
const formItems = [
  {
    key: 'catalog',
    tag: 'Select',
    label: '虚拟表目录',
    name: 'catalog',
  },
  {
    tag: 'Select',
    rules: [{ required: false, message: '请选择虚拟表库' }],
    label: '虚拟表库',
    name: 'database',
    allowClear: true,
  },
  {
    tag: 'Input',
    rules: [{ required: false, message: '请输入表名称' }],
    label: '虚拟表名称',
    name: 'name',
    allowClear: true,
  },
  {
    tag: 'buttons',
    wrapperCol: { span: 16 },
    children: [
      {
        tag: 'Button',
        name: 'reset',
        label: '重置',
        htmlType: 'button',
      },
      {
        tag: 'Button',
        name: 'submit',
        label: '查询',
        type: 'primary',
        htmlType: 'submit',
      },
    ],
  },
]

const computeItems = computeTypeList.map((i) => {
  return { ...i, text: i.desc }
})

computeItems.push({
  value: '--',
  text: '--',
  desc: '',
})

const columns = [
  {
    title: '虚拟表名称',
    sorter: true,
    fixed: 'left' as const,
    width: 200,
    ellipsis: true,
    dataIndex: 'name',
    render: (text: string, record: { name: string; catalogName: string; databaseName: string }) => {
      return (
        <Popover content={text} placement="top">
          <Link
            to={`${routerMap.dataModel.virtualTableDetail.path}?name=${record.name}&catalog=${record.catalogName}&database=${record.databaseName}`}
          >
            {text}
          </Link>
        </Popover>
      )
    },
  },
  {
    title: '虚拟表目录',
    width: 120,
    dataIndex: 'catalogName',
    filterSearch: true,
  },
  {
    title: '虚拟表状态',
    width: 120,
    dataIndex: 'available',
    render: (status: boolean) => {
      return status ? (
        <Tag color="green">正常</Tag>
      ) : (
        <Tooltip title="表异常">
          <Tag color="red">异常</Tag>
        </Tooltip>
      )
    },
  },
  {
    title: '虚拟表库',
    sorter: true,
    width: 120,
    dataIndex: 'databaseName',
  },
  {
    title: '创建时间',
    sorter: true,
    width: 200,
    dataIndex: 'gmtCreated',
    render: Columns({ type: 'gmtCreated' }),
  },
  {
    title: '创建人',
    dataIndex: ['modification', 'creator'],
    width: 150,
    ellipsis: true,
    sorter: true,
    render: (creator: string) => {
      return <Popover content={creator}>{creator || '-'}</Popover>
    },
  },
]

const EditableComment = ({
  text,
  record,
  refreshList,
}: {
  text: string
  record: AnyObject
  refreshList: () => void
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [form] = Form.useForm()

  const showModal = () => {
    form.setFieldsValue({ comment: text })
    setIsModalVisible(true)
  }

  const handleOk = async () => {
    try {
      const values = await form.validateFields()
      await axios.put(askBIApiUrls.xengine.VTable.updateComment, {
        catalog: record.catalogName,
        database: record.databaseName,
        name: record.name,
        comment: values.comment,
      })
      message.success('描述更新成功')
      setIsModalVisible(false)
      refreshList()
    } catch (error) {
      message.error('更新失败：' + ((error as Error)?.message || '未知错误'))
    }
  }

  return (
    <>
      <div className="flex items-center gap-2">
        <div className="group flex w-full items-center justify-between">
          <span className="max-w-[200px] truncate">{text || '-'}</span>
          <Typography.Link
            onClick={showModal}
            className="ml-2 flex items-center whitespace-nowrap opacity-0 group-hover:opacity-100"
          >
            <SvgIcon icon={editIcon} className="h-4 w-4" />
          </Typography.Link>
        </div>
      </div>
      <Modal title="修改表描述" open={isModalVisible} onOk={handleOk} onCancel={() => setIsModalVisible(false)}>
        <Form form={form} initialValues={{ comment: text }}>
          <Form.Item name="comment" label="表描述" rules={[{ required: true, message: '请输入表描述' }]}>
            <Input.TextArea rows={4} placeholder="请输入表描述" />
          </Form.Item>
        </Form>
      </Modal>
    </>
  )
}

const businessTableColumns = ({
  publishVTable,
  offlineStreamVtableSuccess,
  streamVtableActSuccess,
  navigateDetail,
  materializeTable,
  goMVDetail,
  offMaterializeTable,
  refreshList,
}: any) => {
  const isOpenTenant = useAtomValue(isOpenTenantAtom)
  return customerFilterValue('materialViewListColumnsFilter', [
    {
      title: '虚拟表名称',
      sorter: true,
      fixed: 'left',
      width: 200,
      ellipsis: true,
      dataIndex: 'name',
      render: (text: string, record: { name: string; catalogName: string; databaseName: string }) => {
        return (
          <Popover content={text} placement="top">
            <Link
              to={`${routerMap.dataModel.businessVirtualTableDetail.path}?name=${record.name}&catalog=${record.catalogName}&database=${record.databaseName}`}
            >
              {text}
            </Link>
          </Popover>
        )
      },
    },

    {
      title: '虚拟表库',
      sorter: true,
      width: 120,
      dataIndex: 'databaseName',
    },
    {
      title: '虚拟表状态',
      width: 120,
      dataIndex: 'available',
      render: (status: boolean) => {
        return status ? (
          <Tag color="green">正常</Tag>
        ) : (
          <Tooltip title="上游依赖表不完整，存在被删除的情况，请检查所有链路">
            <Tag color="red">异常</Tag>
          </Tooltip>
        )
      },
    },
    {
      title: '关联事实表名称',
      sorter: true,
      width: 200,
      // dataIndex: 'databaseName',
      render(record) {
        return get(record, 'dataModelDesc.factTable', '-')
      },
    },
    {
      title: '创建时间',
      sorter: true,
      width: 200,
      dataIndex: 'gmtCreated',
      render: Columns({ type: 'gmtCreated' }),
    },
    {
      title: '上线',
      dataIndex: 'status',
      width: 100,
      render: (status: string, record) => {
        switch (status) {
          case 'CREATED':
          case 'RUNNING':
          case 'SUSPENDED':
          case 'COMPLETED':
          case 'FAILED': {
            const isPaused = status === 'SUSPENDED'
            return (
              <>
                <Tag bordered={false} color={streamTaskTagColorMap[status]}>
                  {streamTaskMap[status]}
                </Tag>
                {(status === 'SUSPENDED' || status === 'CREATED' || status === 'RUNNING') && (
                  <LoadingText
                    api={() =>
                      axios.put(askBIApiUrls.xengine.VTable.operateStreamTask(isPaused ? 'RESUME' : 'PAUSE'), {
                        catalog: record.catalogName,
                        database: record.databaseName,
                        table: record.name,
                      })
                    }
                    onSuccess={() => streamVtableActSuccess?.()}
                    onFail={(err) => message.error(err?.message || '操作失败')}
                    type="link"
                    className="mr-2"
                    popconfirmProps={{
                      title: `确认${isPaused ? '重启' : '暂停'}流任务？`,
                    }}
                  >
                    {isPaused ? '重启' : '暂停'}
                  </LoadingText>
                )}
                <LoadingText
                  api={() =>
                    axios.delete(askBIApiUrls.xengine.VTable.offlineStreamVtable, {
                      data: {
                        catalog: record.catalogName,
                        database: record.databaseName,
                        table: record.name,
                      },
                    })
                  }
                  onSuccess={() => offlineStreamVtableSuccess?.()}
                  onFail={(err) => message.error(err?.message || '下线失败')}
                  type="link"
                  className="mr-2"
                  popconfirmProps={{
                    title: '确认下线该表？',
                  }}
                >
                  下线
                </LoadingText>
                <Typography.Link onClick={() => navigateDetail(record)}>运行详情</Typography.Link>
              </>
            )
          }
          default: {
            if (record.computeType === ITableType.BATCH) {
              return '-'
            }
            return <Typography.Link onClick={() => publishVTable(record)}>上线业务虚拟表(流)</Typography.Link>
          }
        }
      },
    },
    {
      title: '流批属性',
      dataIndex: '',
      width: 100,
      render: (_, record) => {
        if (record.computeType === ITableType.BATCH) {
          return '批表'
        } else if (record.computeType === ITableType.STREAM) {
          return '流表'
        } else {
          return ''
        }
      },
    },
    {
      title: '物化',
      dataIndex: 'settings',
      width: 150,
      render(settings: Record<string, string>, record: any) {
        const isMaterialize = 'materialized.view.name' in settings
        const lastTenantSelection = JSON.parse(localStorage.getItem('lastTenantSelection') || '{}')
        const tenantCatalogName = lastTenantSelection?.catalogName
        return (
          <div className="flex items-center">
            <LoadingText
              type="link"
              api={async () => {
                if (!tenantCatalogName && isOpenTenant) {
                  message.info('请先从页面左上角点击选择租户')
                } else {
                  return isMaterialize ? offMaterializeTable(record) : materializeTable(record)
                }
              }}
              popconfirmProps={isMaterialize && { title: '确认删除物化视图？' }}
              onFail={(err) => message.error(err.message || '操作失败')}
              onSuccess={() => tenantCatalogName && refreshList()}
            >
              <Switch size="small" checked={isMaterialize} className="mr-1" />
            </LoadingText>
            {isMaterialize && (
              <Typography.Link
                onClick={() => {
                  const [catalogName, dbName, mvName] = settings['materialized.view.name']?.split('.') || []
                  goMVDetail({
                    catalogName,
                    dbName,
                    mvName,
                  })
                }}
              >
                物化视图
              </Typography.Link>
            )}
          </div>
        )
      },
    },
    {
      title: '虚拟表描述',
      dataIndex: 'comment',
      render: (text: string, record) => <EditableComment text={text} record={record} refreshList={refreshList} />,
    },
  ] as Exclude<TableProps['columns'], undefined>)
}

export { formItems, columns, businessTableColumns }
