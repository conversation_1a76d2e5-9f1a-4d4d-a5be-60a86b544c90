import { CommonProps } from '../create-virtual-table'
import { Form, Row, Input, Col, Button, Select, Space, message, Divider, Typography, InputNumber } from 'antd'
import { SaveOutlined, MinusCircleOutlined, PlusOutlined } from '@ant-design/icons'
import React, { useState, useRef } from 'react'
import {
  CREATE_LIKE_TABLE_STEPS,
  columnTypeList,
  usageModeList,
  computeTypeList,
  importImmediatelyList,
  extraHashFunctionList,
} from './constant-conf'
import type { InputRef } from 'antd'
import cs from '../data-model.module.scss'
import { CornComp } from '@model/CornComp'
import { partitionFormatOptions } from '@constant'
import DBSelect from '@model/DBSelect'

const { Title } = Typography

function TableConfig(props: CommonProps) {
  const { requestParams, setRequestParams, setStep, setOriginData, loading } = props

  const [form] = Form.useForm()

  const onSubmit = async (name: string, instance: any) => {
    // 等待虚拟表目录表单验证通过
    await instance.forms.base.validateFields()
    const baseData = instance.forms.base.getFieldValue()
    if (name === 'hiveTable') {
      await instance.forms.hive.validateFields()
      setOriginData?.({
        ...baseData,
        tableParams: instance.forms.hive.getFieldValue(),
        isSubmit: true,
      })
    } else {
      //
      await instance.forms.other.validateFields()
      const data = instance.forms.other.getFieldValue()
      const columns = data.virtualTableFields.map((e: any) => ({
        name: e.name,
        comment: e.comment || '',
        ...parseColumnType(e.columnType),
      }))
      setRequestParams?.({ ...requestParams, columns, ...baseData })
      setOriginData?.({
        columns,
        ...baseData,
        tableParams: data,
        isSubmit: false,
      })
      setStep?.(CREATE_LIKE_TABLE_STEPS.CONFIG_TABLE)
    }
  }

  return (
    <Form.Provider onFormFinish={onSubmit}>
      <Form name="base" layout="vertical" form={form} autoComplete="off">
        <Row gutter={[16, 16]} style={{ flexDirection: 'column' }}>
          <Col span={8}>
            <Title level={5}>选择虚拟表目录/虚拟表库</Title>
          </Col>
          <DBSelect span={8} setFieldValue={form.setFieldValue} />
        </Row>
      </Form>
      {requestParams?.tableEngine === 'hive' ? (
        <HiveTable requestParams={requestParams} />
      ) : (
        <OtherTable requestParams={requestParams} />
      )}
      <Form name={requestParams?.tableEngine + 'Table'}>
        <div className={cs.btnWrapper}>
          <Button onClick={() => setStep?.(CREATE_LIKE_TABLE_STEPS.QUERY_ADVANCED)}>上一步</Button>
          <Button htmlType="submit" type="primary" icon={<SaveOutlined />} loading={loading} disabled={loading}>
            {requestParams?.tableEngine === 'hive' ? '创建虚拟表' : '下一步'}
          </Button>
        </div>
      </Form>
    </Form.Provider>
  )
}

const HiveTable = (props: CommonProps) => {
  const { requestParams } = props
  const [form] = Form.useForm()

  const handleCornChange = (value: any) => {
    form.setFieldValue('corn', value) // set corn value to form
  }

  return (
    <Form
      name="hive"
      layout="vertical"
      form={form}
      autoComplete="off"
      initialValues={{
        corn: {
          frequency: 0,
          timeUnit: 'day',
          time: {},
        },
      }}
    >
      <Row gutter={[16, 16]}>
        <Col className={cs.gutter_row} span={8}>
          <Form.Item
            name="name"
            label="虚拟表名称"
            rules={[
              {
                required: true,
                message: '请输入虚拟表名称',
              },
            ]}
          >
            <Input placeholder="请输入虚拟表名称" />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={[16, 16]}>
        <Col className={cs.gutter_row} span={8}>
          <Form.Item name="timeColumn" label="时间列" rules={[{ required: false, message: '请选择时间列' }]}>
            <Select
              placeholder={'请选择时间列'}
              options={requestParams?.columns.map((e) => ({
                label: e.name,
                value: e.name,
              }))}
            />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={[16, 16]}>
        <Col className={cs.gutter_row} span={8}>
          <Form.Item name="distributedKeys" label="分片键" rules={[{ required: true, message: '请选择分片键' }]}>
            <Select
              placeholder={'请选择分片键'}
              mode="multiple"
              options={requestParams?.columns.map((e) => ({
                label: e.name,
                value: e.name,
              }))}
            />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={[16, 16]}>
        <Col className={cs.gutter_row} span={24}>
          <Form.Item
            name="corn"
            label="corn表达式"
            rules={[
              { required: true, message: '请填写corn' },
              {
                validator: (_, value, callback) => {
                  if (Object.keys(value.time).length === 0) {
                    return callback('时间为空，请确认')
                  } else {
                    return callback()
                  }
                },
              },
            ]}
          >
            <CornComp value={form.getFieldValue('corn')} onChange={handleCornChange} />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={[16, 16]}>
        <Col className={cs.gutter_row} span={8}>
          <Form.Item
            style={{ marginBottom: 0 }}
            name="importImmediately"
            label="是否立即导入"
            rules={[{ required: true, message: '请选择是否立即导入' }]}
          >
            <Select
              placeholder={'请选择是否立即导入'}
              options={importImmediatelyList.map((e) => ({
                label: e.desc,
                value: e.value,
              }))}
            />
          </Form.Item>
        </Col>
      </Row>
    </Form>
  )
}

let index = 1

const OtherTable = (props: CommonProps) => {
  const { requestParams } = props

  const [form] = Form.useForm()
  // column type select&input config
  const [items, setItems] = useState(columnTypeList)
  const [inputVal, setInputVal] = useState('')
  const inputRef = useRef<InputRef>(null)

  const onCtChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setInputVal(event.target.value)
  }

  const addItem = (e: React.MouseEvent<HTMLButtonElement | HTMLAnchorElement>) => {
    e.preventDefault()
    setItems([
      ...items,
      {
        desc: inputVal || `New item ${index++}`,
        value: inputVal || `New item ${index++}`,
      },
    ])
    setInputVal('')
    setTimeout(() => {
      inputRef.current?.focus()
    }, 0)
  }
  // Used to save the current columns [for external ,not kafka]
  const [formField, setFormField] = useState([])

  return (
    <Form name="other" layout="horizontal" form={form} autoComplete="off">
      <Row gutter={[16, 16]}>
        <Col className={cs.gutter_row} span={8}>
          <Form.Item
            name="virtualTableName"
            label="虚拟表名称"
            rules={[{ required: true, message: '请输入虚拟表名称' }]}
          >
            <Input placeholder="请输入虚拟表名称" />
          </Form.Item>
        </Col>
      </Row>
      <Row>
        <Col className={cs.gutter_row} span={12}>
          <Form.List name="virtualTableFields" initialValue={[{ name: '' }]}>
            {(fields, { add, remove }) => {
              return (
                <>
                  {fields.map(({ key, name, ...restField }) => (
                    <Space key={key} align="baseline">
                      <Row gutter={[16, 16]} wrap={false}>
                        <Col flex={'0 0 27%'}>
                          <Form.Item noStyle shouldUpdate>
                            {() => (
                              <Form.Item
                                {...restField}
                                label="字段名"
                                name={[name, 'name']}
                                rules={[
                                  {
                                    required: true,
                                    message: '请输入字段名',
                                  },
                                ]}
                              >
                                <Input />
                              </Form.Item>
                            )}
                          </Form.Item>
                        </Col>
                        <Col flex={'0 0 45%'}>
                          <Form.Item
                            {...restField}
                            label="字段类型"
                            name={[name, 'columnType']}
                            rules={[
                              {
                                required: true,
                                message: '请选择字段类型',
                              },
                              {
                                pattern: new RegExp(/^[A-Z]+((\(\d+,\d+\))|((\([\d]+\))))?$/, 'g'),
                                message: '请正确输入字段类型',
                              },
                            ]}
                          >
                            <Select
                              placeholder={'字段类型'}
                              options={items.map((e) => ({
                                label: e.desc,
                                value: e.value,
                              }))}
                              dropdownRender={(menu) => (
                                <>
                                  {menu}
                                  <Divider style={{ margin: '8px 0' }} />
                                  <Space style={{ padding: '0 8px 4px' }}>
                                    <Input
                                      placeholder="Enter type"
                                      ref={inputRef}
                                      value={inputVal}
                                      onChange={onCtChange}
                                    />
                                    <Button onClick={addItem}>Add</Button>
                                  </Space>
                                </>
                              )}
                            />
                          </Form.Item>
                        </Col>
                        <Col flex={'0 0 28%'}>
                          <Form.Item
                            {...restField}
                            label="注释"
                            name={[name, 'comment']}
                            rules={[
                              {
                                required: false,
                                message: '请输入注释',
                              },
                            ]}
                          >
                            <Input.TextArea rows={1} />
                          </Form.Item>
                        </Col>
                      </Row>
                      <MinusCircleOutlined
                        onClick={() => {
                          if (fields.length > 1) {
                            remove(name)
                            if (requestParams?.tableEngine === 'external') {
                              form.setFieldValue('timeCol', null)
                              form.setFieldValue('primaryKeys', [])
                            }
                          } else {
                            message.error('最少需要一个字段')
                          }
                        }}
                      />
                    </Space>
                  ))}
                  <Row>
                    <Col span={16}>
                      <Form.Item>
                        <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>
                          添加字段
                        </Button>
                      </Form.Item>
                    </Col>
                  </Row>
                </>
              )
            }}
          </Form.List>
        </Col>
      </Row>
      {requestParams?.tableEngine === 'kafka' && (
        <>
          <Row>
            <Col className={cs.gutter_row} span={8}>
              <Form.Item name="distributedKeys" label="分片键" rules={[{ required: true, message: '请选择分片键' }]}>
                <Select
                  placeholder={'请选择分片键'}
                  mode="multiple"
                  onFocus={() => setFormField(form.getFieldValue('virtualTableFields'))}
                  options={formField.map((e: any) => ({
                    label: e.name,
                    value: e.name,
                    key: e.name,
                  }))}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col className={cs.gutter_row} span={8}>
              <Form.Item
                name="importImmediately"
                label="是否立即导入"
                rules={[{ required: true, message: '请选择是否立即导入' }]}
              >
                <Select
                  placeholder={'请选择是否立即导入'}
                  options={importImmediatelyList.map((e) => ({
                    label: e.desc,
                    value: e.value,
                  }))}
                />
              </Form.Item>
            </Col>
          </Row>
        </>
      )}

      {requestParams?.tableEngine === 'external' && (
        <>
          <Row>
            <Col className={cs.gutter_row} span={8}>
              <Form.Item
                name="primaryKeys"
                label="主键/联合主键"
                rules={[
                  {
                    required: true,
                    message: '请选择主键/联合主键',
                  },
                ]}
              >
                <Select
                  placeholder={'请选择主键/联合主键'}
                  mode="multiple"
                  onFocus={() => setFormField(form.getFieldValue('virtualTableFields'))}
                  options={formField.map((e: any) => ({
                    label: e.name,
                    value: e.name,
                    key: e.name,
                  }))}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col className={cs.gutter_row} span={8}>
              <Form.Item name="distributedKeys" label="分片键" rules={[{ required: true, message: '请选择分片键' }]}>
                <Select
                  placeholder={'请选择分片键'}
                  mode="multiple"
                  onFocus={() => setFormField(form.getFieldValue('virtualTableFields'))}
                  options={formField.map((e: any) => ({
                    label: e.name,
                    value: e.name,
                    key: e.name,
                  }))}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col className={cs.gutter_row} span={8}>
              <Form.Item
                name="hashFunc"
                label="分片hash方法"
                rules={[{ required: true, message: '请选择分片hash方法' }]}
              >
                <Select
                  placeholder={'请选择分片hash方法'}
                  options={extraHashFunctionList.map((e) => ({ label: e, value: e }))}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col className={cs.gutter_row} span={8}>
              <Form.Item
                name="bucketNum"
                label="分片数"
                rules={[{ required: true, type: 'number', message: '分片数不能小于1', min: 1 }]}
              >
                <InputNumber />
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col className={cs.gutter_row} span={8}>
              <Form.Item name="computeType" label="计算类型" rules={[{ required: true, message: '请输入计算类型' }]}>
                <Select
                  placeholder="请选择计算类型"
                  options={computeTypeList
                    .filter((i) => {
                      return i.value !== 'STREAM_AND_BATCH'
                    })
                    .map((e) => ({
                      label: e.desc,
                      value: e.value,
                    }))}
                />
              </Form.Item>
            </Col>
          </Row>
        </>
      )}
      <Row>
        <Col className={cs.gutter_row} span={8}>
          <Form.Item name="timeCol" label="时间列" rules={[{ required: false, message: '请输入时间列' }]}>
            <Select
              placeholder={'请选择时间列'}
              onFocus={() => setFormField(form.getFieldValue('virtualTableFields'))}
              options={formField.map((e: any) => ({
                label: e.name,
                value: e.name,
                key: e.name,
              }))}
            />
          </Form.Item>
        </Col>
      </Row>
    </Form>
  )
}

const QueryAdvance = (props: CommonProps) => {
  const { setStep, requestParams, setOriginData, loading } = props
  const [form] = Form.useForm()

  const onSubmit = (data: any) => {
    setOriginData?.({
      queryParams: data,
      isSubmit: requestParams?.tableEngine !== 'hive',
    })

    setStep?.(CREATE_LIKE_TABLE_STEPS.CONFIG_TABLE)
  }

  const colSetting = {
    span: 8,
  }
  return (
    <Form layout="vertical" form={form} autoComplete="off" onFinish={onSubmit}>
      <Row gutter={[16, 16]} style={{ flexDirection: 'column' }}>
        <Col {...colSetting}>
          <Form.Item
            style={{ marginBottom: 0 }}
            name="usageMode"
            label="常见使用方式"
            rules={[{ required: true, message: '请选择常见使用方式' }]}
          >
            <Select
              placeholder={'请选择常见使用方式'}
              options={usageModeList.map((e) => ({
                label: e.desc,
                value: e.value,
              }))}
            />
          </Form.Item>
        </Col>
        <Col {...colSetting}>
          <Form.Item
            style={{ marginBottom: 0 }}
            name="sortColumn"
            label="排序列"
            rules={[{ required: true, message: '请选择排序列' }]}
          >
            <Select
              placeholder={'请选择排序列'}
              options={requestParams?.columns.map((e) => ({
                label: e.name,
                value: e.name,
              }))}
            />
          </Form.Item>
        </Col>

        <Col {...colSetting}>
          <Form.Item
            style={{ marginBottom: 0 }}
            name="partitionKey"
            label="分区列"
            rules={[{ required: false, message: '请选择分区列' }]}
          >
            <Select
              placeholder={'请选择分区列'}
              options={requestParams?.columns.map((e) => ({
                label: e.name,
                value: e.name,
              }))}
            />
          </Form.Item>
        </Col>

        <Col {...colSetting}>
          <Form.Item
            style={{ marginBottom: 0 }}
            name="partitionFormat"
            label="分区列格式"
            rules={[{ required: true, message: '请选择分区列格式' }]}
          >
            <Select
              placeholder={'请选择分区列格式'}
              options={partitionFormatOptions.map((i) => ({
                label: i,
                value: i,
              }))}
            />
          </Form.Item>
        </Col>
      </Row>
      <div className={cs.btnWrapper}>
        <Button onClick={() => setStep?.(CREATE_LIKE_TABLE_STEPS.CONFIG_DATASOURCE)}>上一步</Button>
        <Button htmlType="submit" type="primary" icon={<SaveOutlined />} loading={loading} disabled={loading}>
          {requestParams?.tableEngine === 'hive' ? '下一步' : '创建虚拟表'}
        </Button>
      </div>
    </Form>
  )
}

const psReg = /^[A-Z]+(\(\d+,\d+\))$/
const pReg = /^[A-Z]+(\([\d]+\))$/
const Reg = /^[A-Z]+$/

function parseColumnType(data: string) {
  if (pReg.test(data)) {
    const pDataNum = data.replace(/[^\d]/g, ' ')
    return {
      columnType: data.replace(/[^A-Z]/g, ' ').trim(),
      columnPrecision: parseInt(pDataNum),
    }
  } else if (psReg.test(data)) {
    const numArr = data
      .replace(/[^\d,]/g, ' ')
      .split(',')
      .map((e) => parseInt(e))
    return {
      columnType: data.replace(/[^A-Z]/g, ' ').trim(),
      columnPrecision: numArr[0],
      columnScale: numArr[1],
    }
  } else if (Reg.test(data)) {
    return { columnType: data }
  } else {
    return { columnType: data.replace(/[^A-Z]/g, ' ').trim() }
  }
}

export { HiveTable, OtherTable, QueryAdvance, TableConfig }
