// @ts-nocheck
import React, { useState, useRef } from 'react'
import DBSelect from '@model/DBSelect'
import GLineage from '@model/GLineage'
import { Col, Row, Form, Select, Button, message, Space } from 'antd'
import { type FormInstance } from 'antd'
import CreateBusinessVTBtn from './createBusinessVTBtn'
import { useRequest } from 'ahooks'
import { Api } from '@api'
import NodeModal from './NodeModal'
import { getUnitId, getCharFinalStr } from '@libs/util'
import { PageHeader } from '@ant-design/pro-layout'
import LayoutCard from '@ui/layoutCard/LayoutCard'
import { useNavigate } from 'react-router-dom'
import { routerMap } from '@XEngineRouter/routerMap'
import {
  type VTableSelectParamsType,
  type FactTableFormPropsType,
  type ERDataType,
  type ERNodesTreeNodeType,
  type NodeType,
} from './conf'
import {
  getNodeMoalInitFormValuesFromERNodesTreeNode,
  getColumnsAfterVTable,
  PopConfirmSelectFormItem,
  formatERDataToLineage,
  formatERDataToExactJoinDag,
  getParentNodes,
  updateERTreeNode,
  transformERNodesTreeToERData,
} from './conf'

function Main() {
  const navigate = useNavigate()
  const [VTableSelectParams, setVTableSelectParams] = useState<VTableSelectParamsType>({
    catalog: '',
    database: '',
    vTable: '',
  })
  const [nodeModalOpen, setNodeModalOpen] = useState(false)
  const [factTableForm] = Form.useForm<FactTableFormPropsType>()
  const [dimensionsForm] = Form.useForm()
  const [ERData, setERData] = useState<ERDataType | Record<string, never>>({})

  const ERNodesTree = useRef<ERNodesTreeNodeType | Record<string, never>>({})

  const curEditNodeId = useRef('')

  const exitedEdgesRef = useRef([])

  const { run: getVtableListRun, data: vTableList } = useRequest(
    async (args) => {
      const vTableListAns = await Api.apiEngineV1VtableListGet({
        current: 1,
        pageSize: -1,
        ...args,
      })
      return vTableListAns.list
    },
    {
      manual: true,
    },
  )

  function handleFormValueChange(changedValues: Record<string, any>, allValues: Record<string, any>) {
    const changedValueKey = Object.keys(changedValues)[0]
    if (!changedValueKey) {
      return
    }
    // 根据顺序清空值
    const feildOrder = ['catalog', 'database', 'vTable', 'dimensionsColumns', 'metricsColumns']
    const hasOrderKeys = ['catalog', 'database', 'vTable']
    if (hasOrderKeys.includes(changedValueKey)) {
      const index = feildOrder.findIndex((field) => field === changedValueKey)
      factTableForm.resetFields(feildOrder.slice(index + 1))
    }

    // 按着顺序请求值
    switch (changedValueKey) {
      case 'database': {
        if (allValues.catalog && allValues.database) {
          getVtableListRun({ catalog: allValues.catalog, database: allValues.database })
        }
      }
    }
  }

  // 获取维度列和指标列的select的options值
  function getSelectColumnsAfterVTable(excludeColumns: string[]) {
    const columns = getColumnsAfterVTable(vTableList, VTableSelectParams.vTable)
    const columnsOptions = columns.filter((e) => !excludeColumns.includes(e.name))
    return columnsOptions.map((e) => ({
      label: e.name,
      value: e.name,
      ...(e.name === 'dt' ? { disabled: true } : {}),
    }))
  }

  // 构建事实表
  async function buildFactVTable() {
    await factTableForm.validateFields()
    const dimensionsColumns = factTableForm.getFieldValue('dimensionsColumns')
    const metricsColumns = factTableForm.getFieldValue('metricsColumns')

    exitedEdgesRef.current = []
    const table = `${VTableSelectParams.database}.${VTableSelectParams.vTable}`
    const id = `${VTableSelectParams.catalog}.${table}`
    const n = {
      id: id,
      table: table,
      kind: 'FACT',
      dimensionsColumns: Array.isArray(dimensionsColumns) ? dimensionsColumns : [],
      metricsColumns: Array.isArray(metricsColumns) ? metricsColumns : [],
    } as ERDataType['vertices'][0]
    ERNodesTree.current = {
      ...n,
      database: VTableSelectParams.database,
      children: [],
      primaryKeys: [],
      foreignKeys: [],
    }

    setERData({ vertices: [n], edges: [] })
    message.success('事实表选择完成，可点击事实表右上角编辑图标进行子节点选择')
  }

  // 提交NodeModal表单进行转换为api的ER格式数据
  function convertFormDataToJoinDag({
    curVTable,
    nodeFormData,
    factTableForm,
    dimensionsForm,
    ERNodesTree,
  }: {
    curVTable: string
    nodeFormData: NodeType
    factTableForm: FormInstance<FactTableFormPropsType>
    dimensionsForm: FormInstance<any>
    ERNodesTree: ERNodesTreeNodeType | Record<string, never>
  }) {
    const nodes = nodeFormData.nodes || []
    const primaryKeys = Array.isArray(nodeFormData.primaryKeys) ? nodeFormData.primaryKeys : [nodeFormData.primaryKeys]
    const foreignKeys = Array.isArray(nodeFormData.foreignKeys) ? nodeFormData.foreignKeys : [nodeFormData.foreignKeys]

    const {
      vTable: factVTable,
      catalog,
      database,
      dimensionsColumns: factDimensionsColumns,
      metricsColumns: factMetricsColumns,
    } = factTableForm.getFieldsValue()

    const changeNodes: Record<string, ERDataType['vertices'][0]> = {}
    const changeEdges: ERDataType['edges'] = []

    const selfVTable = `${database}.${curVTable}`
    const selfId = `${catalog}.${selfVTable}`
    // todo: 后面改同一个目录需要改造
    const dimColums =
      curVTable === factVTable
        ? factDimensionsColumns
        : dimensionsForm.getFieldValue(`${curVTable}____dimensionsColumns`)
    const selfNode = {
      id: selfId,
      table: selfVTable,
      kind: curVTable === factVTable ? 'FACT' : 'DIM',
      metricsColumns: curVTable === factVTable ? factMetricsColumns : [],
      dimensionsColumns: Array.isArray(dimColums) ? dimColums : [],
      primaryKeys: primaryKeys,
      foreignKeys: foreignKeys,
    } as ERDataType['vertices'][0]
    changeNodes[selfId] = selfNode
    const originChildEdges = ERData.edges.filter((e) => e.from === selfId)

    nodes.forEach((n) => {
      const childNode = n.childNode
      const kind = childNode === factVTable ? 'FACT' : 'DIM'
      const table = `${database}.${childNode}`
      const id = `${catalog}.${table}`
      const dimColums =
        kind === 'FACT' ? factDimensionsColumns : dimensionsForm.getFieldValue(`${childNode}____dimensionsColumns`)
      const newVertice = {
        id: id,
        table: table,
        kind: kind,
        dimensionsColumns: Array.isArray(dimColums) ? dimColums : [],
        metricsColumns: kind === 'FACT' ? factMetricsColumns : [],
        primaryKeys: [],
        foreignKeys: [],
      } as ERDataType['vertices'][0]

      // 处理edges
      const joinKeys = Array.isArray(n.joinKeys) ? n.joinKeys : []
      const pkColumns = joinKeys.map(({ sourceKey, targetKey }) => {
        changeEdges.push({
          id: `e${getUnitId()}`,
          from: selfId,
          to: id,
          joinType: 'LEFT',
          primaryKey: `${table}.${targetKey}`,
          foreignKey: `${selfVTable}.${sourceKey}`,
        })
        return targetKey
      })
      changeNodes[id] = newVertice
      changeNodes[id] = {
        ...newVertice,
        primaryKeys: pkColumns,
      }
    })

    // todo：比对changeEdges和originChildEdges找出新增/删除的字段/节点
    // 获取edgesMap即是 {}
    function getEdgesToNodePrimaryKeysMap(edges: ERDataType['edges']) {
      if (!Array.isArray(edges)) {
        return {}
      }
      const map: Record<string, ERDataType['edges']> = {}
      const length = edges.length
      for (let i = 0; i < length; ++i) {
        const { to: childNodeId, primaryKey, foreignKey, from, joinType } = edges[i]
        if (!map[childNodeId]) {
          map[childNodeId] = []
        }
        map[childNodeId].push({
          from: from,
          to: childNodeId,
          primaryKey: getCharFinalStr(primaryKey, '.'),
          foreignKey: getCharFinalStr(foreignKey, '.'),
          joinType,
        })
      }
      return map
    }

    const changeEdgesToNodePrimaryKeysMap = getEdgesToNodePrimaryKeysMap(changeEdges)
    // 处理dt
    Object.keys(changeEdgesToNodePrimaryKeysMap).forEach((nodeId) => {
      const joinKeys = changeEdgesToNodePrimaryKeysMap[nodeId]
      const hasTDLink = joinKeys.find((e) => e.primaryKey === 'dt' && e.foreignKey === 'dt')
      if (!hasTDLink) {
        joinKeys.push({
          from: selfId,
          to: nodeId,
          primaryKey: 'dt',
          foreignKey: 'dt',
          joinType: 'LEFT',
        })
      }
    })

    const originChildEdgesToPrimaryKeysMap = getEdgesToNodePrimaryKeysMap(originChildEdges)
    const deleteChildNodeJoinKeys = [] as Partial<ERNodesTreeNodeType>[]
    const addChildNodesJoinKeys = [] as ERNodesTreeNodeType[]

    Object.keys(changeEdgesToNodePrimaryKeysMap).forEach((childNodId) => {
      const changeJoinKeys = changeEdgesToNodePrimaryKeysMap[childNodId] || []
      const originJoinKeys = originChildEdgesToPrimaryKeysMap[childNodId] || []
      const childNodeAddJoinKeys = changeJoinKeys.filter(
        (n) => !originJoinKeys.find((joinK) => joinK.primaryKey === n.primaryKey && joinK.foreignKey === n.foreignKey),
      )
      if (childNodeAddJoinKeys && childNodeAddJoinKeys.length > 0) {
        addChildNodesJoinKeys.push({
          ...changeNodes[childNodId],
          id: childNodId,
          parentJoinKeys: childNodeAddJoinKeys,
        })
      }
    })
    Object.keys(originChildEdgesToPrimaryKeysMap).forEach((childNodId) => {
      const changeJoinKeys = changeEdgesToNodePrimaryKeysMap[childNodId] || []
      const originJoinKeys = originChildEdgesToPrimaryKeysMap[childNodId] || []
      const childNodeDeleteJoinKeys = originJoinKeys.filter(
        (n) => !changeJoinKeys.find((joinK) => joinK.primaryKey === n.primaryKey && joinK.foreignKey === n.foreignKey),
      )
      if (childNodeDeleteJoinKeys && childNodeDeleteJoinKeys.length > 0) {
        deleteChildNodeJoinKeys.push({
          id: childNodId,
          parentJoinKeys: childNodeDeleteJoinKeys,
        })
      }
    })

    const updateERNodesTreeNode = {
      ...selfNode,
      deleteChildNodeJoinKeys: deleteChildNodeJoinKeys,
      addChildNodesJoinKeys: addChildNodesJoinKeys,
    }
    const newERNodesTree = updateERTreeNode(ERNodesTree, updateERNodesTreeNode)
    const newERData = transformERNodesTreeToERData(newERNodesTree)
    const { vertices: vs, edges: es } = newERData

    // 根据updateERTreeNode取值赋值更新
    // 对象改变会改变值
    const ER = {
      vertices: Array.isArray(vs) ? vs : [],
      edges: Array.isArray(es) ? es : [],
    }
    setERData(ER)
  }

  function handleDimensionsFormFinish(values) {
    const ER = {
      vertices: ERData.vertices || [],
      edges: ERData.edges || [],
    }
    const dimensionsColumnsKeys = Object.keys(values) || []
    dimensionsColumnsKeys.forEach((key) => {
      const dimensionsColumns = Array.isArray(values[key]) ? values[key] : []
      const [table] = key.split('____')
      const curId = `${VTableSelectParams.catalog}.${VTableSelectParams.database}.${table}`
      const changeIdx = ER.vertices.findIndex((v) => v.id === curId)
      if (changeIdx !== -1) {
        const changeItem = ER.vertices[changeIdx]
        ER.vertices[changeIdx] = {
          ...changeItem,
          dimensionsColumns: dimensionsColumns,
        }
      }
    })
    setERData(ER)
    return ER
  }

  return (
    <>
      <PageHeader title="创建业务虚拟表" />
      <NodeModal
        initFormValues={getNodeMoalInitFormValuesFromERNodesTreeNode(
          ERNodesTree.current as ERNodesTreeNodeType,
          curEditNodeId.current,
        )}
        parentNodes={getParentNodes(ERNodesTree.current as ERNodesTreeNodeType, curEditNodeId.current)}
        vTableList={vTableList}
        modalOpen={nodeModalOpen}
        setModalOpen={setNodeModalOpen}
        handleFormFinsh={(values) => {
          convertFormDataToJoinDag({
            curVTable: getCharFinalStr(curEditNodeId.current, '.'),
            nodeFormData: values,
            factTableForm: factTableForm,
            dimensionsForm: dimensionsForm,
            ERNodesTree: ERNodesTree.current,
          })
        }}
      />
      <LayoutCard>
        <Row gutter={8}>
          <Col
            span={6}
            style={{
              overflow: 'auto',
              height: '80vh',
            }}
          >
            <Form
              form={factTableForm}
              layout="vertical"
              onValuesChange={(changedValues, allValues) => {
                handleFormValueChange(changedValues, allValues)
              }}
              initialValues={{ dimensionsColumns: ['dt'], metricsColumns: [] }}
            >
              <DBSelect
                span={24}
                setFieldValue={factTableForm.setFieldValue}
                params={VTableSelectParams as { catalog: string; database: string }}
                setParams={setVTableSelectParams}
              />
              <Col span={24}>
                <Form.Item
                  name="vTable"
                  label="虚拟表名"
                  rules={[
                    {
                      required: true,
                      message: '请选择虚拟表名',
                    },
                  ]}
                >
                  <Select
                    placeholder="请选择虚拟表名"
                    showSearch
                    options={
                      vTableList &&
                      vTableList
                        .filter(
                          (vt) =>
                            vt.virtualTableType &&
                            (vt.virtualTableType === 'like' ||
                              vt.virtualTableType === 'LIKE' ||
                              vt.virtualTableType === '贴源虚拟表'),
                        )
                        .map((e) => ({ label: e.name, value: e.name }))
                    }
                    onChange={(vTable) => {
                      setVTableSelectParams((params) => ({ ...params, vTable: vTable }))
                    }}
                  />
                </Form.Item>
              </Col>

              <Col>
                <Form.Item
                  noStyle
                  shouldUpdate={(preValues, curValues) => {
                    return preValues.metricsColumns !== curValues.metricsColumns
                  }}
                >
                  {() => (
                    <Form.Item name="dimensionsColumns" label="维度列候选集">
                      <PopConfirmSelectFormItem
                        popConfirmSelectProps={{
                          title: '维度候选集选择',
                          mode: 'multiple',
                          buttonPlaceholder: '维度候选集选择',
                          options: getSelectColumnsAfterVTable(factTableForm.getFieldValue('metricsColumns')),
                        }}
                      />
                    </Form.Item>
                  )}
                </Form.Item>
              </Col>

              <Col>
                <Form.Item
                  noStyle
                  shouldUpdate={(preValues, curValues) => {
                    return preValues.dimensionsColumns !== curValues.dimensionsColumns
                  }}
                >
                  {() => (
                    <Form.Item
                      name="metricsColumns"
                      label="指标列候选集"
                      rules={[
                        {
                          required: true,
                          message: '请选择指标候选集',
                        },
                      ]}
                    >
                      <PopConfirmSelectFormItem
                        popConfirmSelectProps={{
                          title: '指标候选集选择',
                          mode: 'multiple',
                          buttonPlaceholder: '指标候选集选择',
                          options: getSelectColumnsAfterVTable(factTableForm.getFieldValue('dimensionsColumns')),
                        }}
                      />
                    </Form.Item>
                  )}
                </Form.Item>
              </Col>

              <Col>
                <Button
                  block
                  type="primary"
                  onClick={() => {
                    buildFactVTable()
                  }}
                >
                  确定选择事实表
                </Button>
              </Col>
            </Form>
          </Col>

          <Col span={18}>
            <div
              style={{
                width: '100%',
                height: '80vh',
                border: '1px solid #ccc',
                borderRadius: '4px',
              }}
            >
              <GLineage
                key="GLinegae"
                data={formatERDataToLineage(ERData)}
                typeKey="type"
                rightTopIconType="edit"
                handleClickRightTopIcon={function (n) {
                  const nodeData = n.origin
                  const id = nodeData.id
                  curEditNodeId.current = id
                  setNodeModalOpen(true)
                }}
              />
            </div>
          </Col>
        </Row>

        {/* 选维度列 */}
        {Array.isArray(ERData.vertices) && ERData.vertices.length > 1 && (
          <>
            <h3 style={{ margin: '20px 0 10px 0' }}>维度表维度候选集选择：</h3>
            <Form form={dimensionsForm} layout="vertical" onFinish={handleDimensionsFormFinish}>
              {(ERData.vertices || []).map((item) => {
                if (!item || !item.table) return <></>
                const [, table] = item.table.split('.')
                if (!table) return <></>
                if (table === VTableSelectParams.vTable) return <></>
                return (
                  <Form.Item name={`${table}____dimensionsColumns`} label={`${table}维度候选集：`}>
                    <Select
                      mode="multiple"
                      placeholder="请选择"
                      showSearch
                      options={
                        vTableList &&
                        (vTableList.find((vt) => vt.name === table)?.columns || []).map((e) => ({
                          label: e.name,
                          value: e.name,
                        }))
                      }
                    ></Select>
                  </Form.Item>
                )
              })}
            </Form>
          </>
        )}

        <div
          style={{
            margin: `20px 0 0 0`,
            display: 'flex',
            justifyContent: 'flex-end',
          }}
        >
          <Space>
            {Array.isArray(ERData.vertices) && ERData.vertices.length > 1 && (
              <Button
                type="primary"
                onClick={() => {
                  dimensionsForm.submit()
                }}
              >
                确定选择维度候选集
              </Button>
            )}
            <CreateBusinessVTBtn
              handleClickBtn={async (setModalOpen) => {
                factTableForm.validateFields().then(
                  () => {
                    if (!ERData || !Array.isArray(ERData.vertices) || ERData.vertices.length < 1) {
                      message.info('请点击确定选择事实表')
                      return
                    }
                    setModalOpen(true)
                  },
                  () => {
                    message.info('请先完成事实表的选择')
                  },
                )
              }}
              onFinishForm={(values, form, setModalOpen) => {
                const ER = handleDimensionsFormFinish(dimensionsForm.getFieldsValue())
                const joinDag = formatERDataToExactJoinDag(ER)
                Api.apiEngineV1VtablePost({
                  table: {
                    name: `${values.modelName}`,
                    databaseName: VTableSelectParams.database,
                    catalogName: VTableSelectParams.catalog,
                    dataModelDesc: {
                      catalog: VTableSelectParams.catalog,
                      modelName: values.modelName,
                      factTable: `${VTableSelectParams.database}.${VTableSelectParams.vTable}`,
                      joinDag: joinDag,
                      measures: [],
                    },
                  },
                })
                  .then(() => {
                    form.resetFields()
                    message.success('创建业务虚拟表成功')
                    setModalOpen?.(false)
                    navigate(`${routerMap.dataModel.businessVirtualTable.path}?catalog=${VTableSelectParams.catalog}`)
                  })
                  .catch((e) => {
                    message.error(e?.msg || '创建业务虚拟表失败')
                  })
              }}
            />
          </Space>
        </div>
      </LayoutCard>
    </>
  )
}
export default Main
