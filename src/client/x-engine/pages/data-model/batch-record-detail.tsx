// @ts-nocheck
import { PageHeader } from '@ant-design/pro-layout'
import { Card, Table, Tag } from 'antd'
import { useState } from 'react'
import { useRequest } from 'ahooks'
import { Api } from '@api'
import { useSearchParams } from 'react-router-dom'
import dayjs from 'dayjs'
import { ColumnType } from 'antd/es/table'
import React from 'react'

const columns: ColumnType<any>[] = [
    {
        key: 'tableName',
        dataIndex: 'tableName',
        title: '表名称',
    },
    {
        key: 'createdTime',
        dataIndex: 'createdTime',
        render: (time: string) => (time ? dayjs(new Date(time)).format('YYYY-MM-DD HH:mm:ss') : '-'),
        title: '创建时间',
    },
    {
        key: 'result',
        dataIndex: 'result',
        title: '操作结果',
        render: (text: string) => <Tag color={text === '成功' ? 'success' : 'error'}>{text}</Tag>,
    },
    {
        key: 'reason',
        dataIndex: 'reason',
        title: '原因分析',
        ellipsis: true,
    },
]

interface TableItemType {
    catalogName: string
    createdTime: string
    databaseName: string
    info: string
    reason: string
    result: string
    tableName: string
    virtualTableType: string
}

const defaultPageSize = 20

export default function BatchRecordDetail() {
    const [searchParams] = useSearchParams()

    const [list, setList] = useState<TableItemType[]>([])

    const [total, setTotal] = useState<number>(0)

    const [pageNumber, setPageNumber] = useState<number>(1)

    const { loading } = useRequest(
        () =>
            Api.apiEngineV1VtableListCreationDetailGet({
                id: searchParams.get('id'),
                current: pageNumber,
                pageSize: defaultPageSize,
            }),
        {
            refreshDeps: [pageNumber],
            onSuccess: (res: any) => {
                setList(res.list)
                setTotal(res.total)
            },
        },
    )

    return (
        <>
            <PageHeader title='虚拟表创建详情' onBack={() => window.history.back()} />
            <Card>
                <Table
                    loading={loading}
                    columns={columns}
                    rowKey='id'
                    pagination={{
                        pageSize: defaultPageSize,
                        total,
                        showSizeChanger: false,
                        onChange: (page: number) => setPageNumber(page),
                    }}
                    dataSource={list}
                />
            </Card>
        </>
    )
}
