import React, { useEffect, useState } from 'react'
import { PageHeader } from '@ant-design/pro-layout'
import {
  App,
  Button,
  Form,
  Modal,
  Typography,
  Popover,
  Row,
  Col,
  Switch,
  Dropdown,
  Input,
  Table,
  Card,
  Empty,
} from 'antd'
import { useBoolean, useRequest } from 'ahooks'
import { Api, ResourceDesc } from '@api'
import { businessTableColumns, deleteVTableBtn } from './forms-conf/form-items'
import { Broadcast } from '@libs'
import { routerMap } from '@XEngineRouter/routerMap'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { getWinInnerSize } from '@libs/util'
import {
  DataProcessFormItem as DataResolveFormItem,
  DetailOutputSettingsFormItem,
  ResourceSettingsFormItem,
  StreamSettingsFormItem as StreamSettingFormItem,
  resolveStreamingDescParams,
  CustomConfigFormItem,
} from '@model/dataModelDesc'
import { askBIApiUrls } from 'src/shared/url-map'
import request from 'src/shared/xengine-axios'
import { DataModelForStreamVirtualTable, OutputDesc as IOutputDesc } from './type'
import DataMaskModal from 'src/client/components/DataMasking/DataMaskModal'
import { type OriginColumnsType } from 'src/client/components/TableFilter/TableFilterDrawer'
import CreateFilterTable from 'src/client/components/TableFilter/CreateFilterTable'
import { customAlphabet, nanoid } from 'nanoid'
import { MoreOutlined } from '@ant-design/icons'
import SelectColumnsCreateNewTableByTransfer, {
  type SelectColumnsCreateNewTablePropsType,
} from 'src/client/components/SelectColumnsCreateNewTableByTransfer'
import { VTableOperateActions as ACTIONS, VTableOperateButtons, Customer } from 'src/shared/constants'
// import { type SelectColumnsCreateNewTablePropsType } from './components/SelectColumnsCreateNewTable'
// import { VTableOperateActions as ACTIONS, VTableOperateButtons } from 'src/shared/constants'
import ValidatedUploadFile from 'src/client/components/ValidatedUploadFile'
import MaterializeVirtualTableDrawer, {
  type MaterializeVirtualTableDrawerProps,
} from './components/MaterializeVirtualTableDrawer'
import type { SmartXMaterialViewDetailQueryParams } from 'src/shared/xengine-types'
import { get } from 'lodash-es'
import { EditorInDrawer } from '../../widget/model/sql/EditorInDrawer'
import { message } from 'antd/lib'
import OutputDesc from './components/OutputDesc'
import { CurrentVtableContext } from './data-model-context'
import type { ResponseErrorType } from 'src/shared/metric-types'
import { editor as IEditorType } from 'monaco-editor/esm/vs/editor/editor.api'
import type { AnyObject, VTableBaseInfo } from 'src/shared/xengine-types'
import { customerFilterValue } from 'src/shared/customer-resolver'
import { VTable } from 'src/shared/common-types'
import CatalogDatabaseTree from 'src/client/components/CatalogDatabaseTree'
import Search from 'antd/es/input/Search'
import styles from './virtual-table.module.scss'
import TimeColumnSettingModal from 'src/client/components/TimeColumnSettingModal'
import clsx from 'clsx'

const numericalID = customAlphabet('1234567890', 10)
const PageSize = 10

export type DimensionType = { select: [string, string]; description?: string }
export type MeasureType = {
  measuresAgg: string
  measuresColumn: string
  measuresValue: string
  measuresDescription?: string
}

export interface ModelFormValueType {
  catalog: string
  database: string
  mainVTable: string
  windowTimeColumn: string
  measures: MeasureType[]
  dimensions: DimensionType[]
  scanStartupMode?: string
  outputTarget?: string
  defaultDtPartition: boolean
}

type CatalogItem = {
  id: string
  name: string
  type: string
}

type DatabaseItem = {
  id: string
  name: string
  catalogType: string
  catalogName: string
}

type OptionalDrawerProps = Partial<Omit<MaterializeVirtualTableDrawerProps, 'open'>> & {
  open: boolean
}

const { Text } = Typography

const VirtualTable = () => {
  const [dbDataMap, setDbDataMap] = useState<Record<string, DatabaseItem[]>>({})
  const [tableListData, setTableListData] = useState<any[]>([])
  const [searchValue, setSearchValue] = useState('')
  const [searchTableValue, setSearchTableValue] = useState('')
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([])
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([])
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: PageSize,
    total: 0,
  })
  const [currentSelection, setCurrentSelection] = useState<{
    catalog: string
    database: string
  }>({
    catalog: '',
    database: '',
  })
  const [searchParams] = useSearchParams()
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [publishStreamVTableModalOpen, setPublishStreamVTableModalOpen] = useState(false)
  const [currentTable, setCurrentTable] = useState<any>({})
  const [currentPublishTable, setCurrentPublishTable] = useState<any>({})
  const [xWidth, setXWidth] = useState('100%')
  const [currentVTableInfo, setCurrentVTableInfo] = useState<VTable | undefined>(undefined)
  const navigate = useNavigate()
  const { message: antdMessage } = App.useApp()
  const [createFilterTableProps, setCreateFilterTableProps] = useState<{
    open: boolean
    tableData?: any
  }>({
    open: false,
  })
  const [dataMaskProps, setDataMaskProps] = useState<{
    open: boolean
    tableData: any
  }>({
    open: false,
    tableData: {},
  })
  const [materializeVirtualTableDrawerProps, setMaterializeVirtualTableDrawerProps] = useState<OptionalDrawerProps>({
    open: false,
  })
  const [timeColumnSettingProps, setTimeColumnSettingProps] = useState<{
    open: boolean
    tableData: any
  }>({
    open: false,
    tableData: {},
  })
  const [createCEPTableProps, setCreateCEPTableProps] = useState<{
    open: boolean
    tableData?: any
  }>({
    open: false,
  })

  const [editor, setEditor] = useState<IEditorType.IStandaloneCodeEditor | null>(null)
  const [currentDbAction, setCurrentDbAction] = useState<'create' | 'delete' | null>(null)
  const [catalogORDatabaseDetail, setCatalogORDatabaseDetail] = useState('')
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false)
  const [selectColumnsCreateNewTableProps, setSelectColumnsCreateNewTableProps] = useState<
    Pick<SelectColumnsCreateNewTablePropsType, 'open' | 'originColumns' | 'currentTable' | 'computeType' | 'tableData'>
  >({ open: false, originColumns: [], currentTable: '', computeType: '', tableData: undefined })
  const [CSVUploadModalOpen, CSVUploadModalOpenOps] = useBoolean(false)
  const [CreateCatalogModalOpen, setCreateCatalogModalOpen] = useState(false)
  const [createType, setCreateType] = useState<'catalog' | 'database'>()
  const [deleteType, setDeleteType] = useState<'catalog' | 'database'>()
  const [curDeleteInfo, setCurDeleteInfo] = useState<{ catalog: string; database?: string } | null>(null)
  const [createCatalogForm] = Form.useForm()

  const { run: createDatabase, loading: createDatabaseLoading } = useRequest(
    Api.apiEngineV1DatabaseCreateVtableDatabasePost,
    {
      manual: true,
      onSuccess() {
        antdMessage.success('创建成功')
        handleCloseCreateCatalogModal()
        getCatalogData()
      },
    },
  )

  const { run: createCatalog, loading: createCatalogLoading } = useRequest(Api.apiEngineV1CatalogVtcatalogCreatePost, {
    manual: true,
    onSuccess() {
      antdMessage.success('创建成功')
      handleCloseCreateCatalogModal()
      getCatalogData()
    },
  })

  const { run: deleteCatalog, loading: deleteCatalogLoading } = useRequest(Api.apiEngineV1CatalogVtcatalogDropDelete, {
    manual: true,
    onSuccess() {
      antdMessage.success('删除成功')
      getCatalogData()
      setIsDeleteModalOpen(false)
      setCurDeleteInfo(null)
    },
  })

  const { run: deleteDatabase, loading: deleteDatabaseLoading } = useRequest(
    Api.apiEngineV1DatabaseDropVtableDatabaseDelete,
    {
      manual: true,
      onSuccess() {
        getCatalogData()
        antdMessage.success('删除成功')
        setIsDeleteModalOpen(false)
        setCurDeleteInfo(null)
      },
    },
  )

  const publishVTable = (record: OriginColumnsType) => {
    setCurrentPublishTable(record)
    setPublishStreamVTableModalOpen(true)
  }

  const { runAsync: deleteVTable } = useRequest(Api.apiEngineV1VtableDeleteDelete, {
    manual: true,
    onSuccess() {
      getTableList(currentSelection.catalog, currentSelection.database, 1, pagination.pageSize)
    },
  })

  const { run: getCatalogData, data: catalogData } = useRequest(() =>
    request
      .get<{}, CatalogItem[]>(askBIApiUrls.xengine.catalogList, { params: { current: 1, pageSize: -1 } })
      .then((res) => res.filter((item) => item.type === 'INTERNAL')),
  )

  useEffect(() => {
    if (catalogData && catalogData.length > 0) {
      catalogData.forEach((catalog) => {
        getDatabaseRun(catalog.name)
      })
    }
  }, [catalogData])
  const getDatabaseRun = async (catalog: string) => {
    try {
      const data = await request.get<{ catalog: string }, DatabaseItem[]>(askBIApiUrls.xengine.databaseList, {
        params: { catalog },
      })

      setDbDataMap((prev) => ({
        ...prev,
        [catalog]: data,
      }))
    } catch (e: any) {
      message.error(e?.message || '获取数据库失败')
    }
  }

  const { run: getTableList, loading: tableListLoading } = useRequest(
    async (catalog?: string, database?: string, page = 1, pageSize = PageSize, table?: string) => {
      return request.get(askBIApiUrls.xengine.VTable.search, {
        params: {
          catalog,
          database,
          current: page,
          pageSize,
          name: table,
          virtualTableType: 'AS',
        },
      })
    },
    {
      manual: true,
      onSuccess: (res: any) => {
        setTableListData(res?.list || [])
        setPagination((prev) => ({
          ...prev,
          total: res?.total || 0,
        }))
      },
      onError: (e: any) => {
        setTableListData([])
        setPagination((prev) => ({
          ...prev,
          total: 0,
        }))
        message.error(e?.message || '获取虚拟表列表失败')
      },
    },
  )

  const handleTableChange = (newPagination: any) => {
    setPagination((prev) => ({
      ...prev,
      current: newPagination.current,
      pageSize: newPagination.pageSize,
    }))
    getTableList(
      currentSelection.catalog || catalogData?.[0]?.name,
      currentSelection.database,
      newPagination.current,
      newPagination.pageSize,
      searchTableValue,
    )
  }

  const handleOk = () => {
    const { catalogName, databaseName, name } = currentTable
    setIsModalOpen(false)
    antdMessage.loading({
      content: `虚拟表正在删除，请稍候……`,
      key: 'table_delete_ing',
    })
    deleteVTable({
      catalog: catalogName,
      database: databaseName,
      name: name,
      cascadeDropLikeTable: true,
    })
      .then(() => {
        antdMessage.success({
          content: `虚拟表删除成功`,
          key: 'table_delete_ing',
        })
      })
      .catch((e) => {
        antdMessage.loading({
          content: e?.message || `虚拟表删除失败`,
          key: 'table_delete_ing',
          duration: 0.01,
        })
      })
      .finally(() => getTableList(currentSelection.catalog, currentSelection.database, 1, PageSize))
  }

  const handleCancel = () => {
    setIsModalOpen(false)
  }

  const { run: publishStreamVTable, loading: isPublishStreamVTable } = useRequest(
    (data) => request.post(askBIApiUrls.xengine.publishStreamVtable, data),
    {
      manual: true,
      onSuccess() {
        antdMessage.success('上线成功')
        setPublishStreamVTableModalOpen(false)
        configForm.resetFields()
        getTableList(currentSelection.catalog, currentSelection.database, 1, PageSize)
      },
      onError(e: ResponseErrorType) {
        const msg = e.msg || e.message || e.error || ''
        message.error(msg || '上线失败')
      },
    },
  )
  const { runAsync: getPublishInfo } = useRequest(
    (params: VTableBaseInfo) =>
      request.get<
        VTableBaseInfo,
        {
          jobId?: string
          jobUrl?: string
        }
      >(askBIApiUrls.xengine.businessVTablePublishInfo, { params: params }),
    {
      manual: true,
      onError(e: ResponseErrorType) {
        const msg = e.msg || e.message || e.error || ''
        message.error(msg || '获取上线信息失败')
      },
    },
  )
  const navigateDetail = async (record: AnyObject) => {
    try {
      let res = await getPublishInfo({
        catalog: record.catalogName,
        database: record.databaseName,
        table: record.name,
      })
      if (res.jobUrl) {
        window.open(res.jobUrl, '_blank')
      } else {
        message.warning('任务生成中，请稍后再试')
      }
    } catch (e) {
      message.error('获取上线信息失败')
      console.error(e)
    }
  }
  const publishStreamVTableModalOk = async () => {
    const value = await configForm.validateFields()
    const isJDBC = value.outputTarget === 'JDBC'
    const IS_UPDATE_MODEL = currentPublishTable?.dataModelDesc?.modelType === 'UPDATE_MODEL'
    const outputDesc: IOutputDesc = {
      outputTarget: value.outputTarget,
      kafkaCatalogName: value.kafkaCatalogName,
      topic: value.topic,
      outputFormat: value.outputFormat,
      catalog: isJDBC ? value.catalog : value.outputCatalog, // 兼容旧版本字段
      database: isJDBC ? value.database : value.outputDatabase,
      table: isJDBC ? value.table : value.outputTable,
      // primaryKeys: isJDBC && value.isPrimaryKeysCfg ? value.primaryKeys : null,
      // updateStrategy: isJDBC && value.isPrimaryKeysCfg ? value.updateStrategy : null,
      // indexes: isJDBC && value.isPrimaryKeysCfg ? value.indexes : null,
    }
    if (value.isPrimaryKeysCfg && isJDBC) {
      Object.assign(outputDesc, {
        primaryKeys: value.primaryKeys || [],
        updateStrategy: value.updateStrategy,
        indexes: (value.indexes || []).filter((v: { indexColumns: string[] }) => v?.indexColumns?.length > 0),
      })
    }
    const resourceDesc: ResourceDesc | {} = value.isConfigResource
      ? {
          cpu: value.cpu,
          memory: value.memory,
          disk: value.disk,
        }
      : {}
    const configs = value.isCustomConfig ? value.configList : []
    const dataModel: DataModelForStreamVirtualTable = {
      dataProcessDesc:
        value.outputTarget === 'ASKDI'
          ? {
              strategyList: (
                value.dataProcessDesc as { strategyType: string; primaryKeys: string[]; version: string[] }[]
              ).map((i: { strategyType: string; primaryKeys: string[]; version: string[] }) => ({
                strategyType: i.strategyType,
                primaryKeys: i.primaryKeys.map((j: string) => ({
                  name: j,
                  vertexId: j,
                })),
                version: i.version.map((j: string) => ({
                  name: j,
                  vertexId: j,
                }))[0],
              })),
            }
          : {},
      outputDesc: IS_UPDATE_MODEL ? {} : outputDesc,
      resourceDesc,
      streamingDesc: resolveStreamingDescParams(currentPublishTable.computeType, value as any),
      configs,
    }

    return publishStreamVTable({
      catalog: currentPublishTable.catalogName,
      database: currentPublishTable.databaseName,
      table: currentPublishTable.name,
      publishMv: true,
      dataModel,
    })
  }

  const cancelPublishStreamVTableModal = () => {
    configForm.resetFields()
    setPublishStreamVTableModalOpen(false)
  }

  const setTableWidth = (width: number) => {
    if (width < 1330) {
      setXWidth('110%')
    } else {
      setXWidth('100%')
    }
  }
  useEffect(() => {
    Broadcast.listen('@PAGE/DATAMODEL/VIRTUAL_TABLE', (record: any) => {
      setCurrentTable(record)
      setIsModalOpen(true)
    })

    setTableWidth(getWinInnerSize().innerWidth)

    // Window resize set table scroll x value
    window.addEventListener('resize', (e: UIEvent) => {
      const width = (e.target as Window).innerWidth
      setTableWidth(width)
    })
  }, [location.href])

  const [configForm] = Form.useForm<DataModelForStreamVirtualTable>()

  const { run: getVTableList } = useRequest(
    async (args: { catalog: string; database: string }) => {
      if (!args.database || !args.catalog) {
        return []
      }
      const vTableListAns = await Api.apiEngineV1VtableSearchGet({
        current: 1,
        pageSize: -1,
        ...args,
      })
      return (vTableListAns?.list || []).filter((vt) => vt?.virtualTableType === 'AS')
    },
    {
      manual: true,
    },
  )
  function handleFormValuesChange(changeValue: Record<string, any>, values: Record<string, any>) {
    const changeKey = Object.keys(changeValue)[0]
    switch (changeKey) {
      case 'catalog':
      case 'database': {
        const { database, catalog, outputTarget } = values
        // 内部数据源才能进行请求 否则后端会报错。
        if (outputTarget === 'ASKDI') {
          getVTableList({
            catalog,
            database: changeKey === 'catalog' ? undefined : database,
          })
          configForm.setFieldsValue({
            database,
            catalog,
          })
        }
        break
      }
      case 'mainVTable': {
        const { database, catalog, mainVTable } = values
        configForm.setFieldsValue({
          database,
          catalog,
          mainVTable,
        })
        break
      }
    }
  }
  const [_drawerLabel, setDrawerLabel] = useState('')
  const [currentAction, setCurrentAction] = useState<keyof typeof ACTIONS>()
  // 抽象的操作按钮组件
  const handleAction = (record: AnyObject, actionType: keyof typeof ACTIONS) => {
    setCurrentAction(actionType)
    switch (actionType) {
      case ACTIONS.DELETE:
        deleteVTableBtn(record)
        break
      case ACTIONS.VISUAL_FILTER:
        setDrawerLabel(VTableOperateButtons.find((btn) => btn.action === actionType)?.label || '')
        setCreateFilterTableProps({
          open: true,
          tableData: { ...record, id: numericalID() },
        })
        break
      case ACTIONS.SQL_CLEANING:
        setDrawerLabel(VTableOperateButtons.find((btn) => btn.action === actionType)?.label || '')
        setCurrentVTableInfo(record as VTable)
        setSelectColumnsCreateNewTableProps({
          open: true,
          originColumns: (record?.columns || []).map((col: { name: string }) => {
            const id = nanoid()
            return {
              id,
              key: id,
              type: 'COLUMN',
              columnName: col.name,
            }
          }),
          currentTable: `${record.catalogName}.${record.databaseName}.${record.name}`,
          computeType: record.computeType,
          tableData: record,
        })
        break
      case ACTIONS.CREATE_VIRTUAL_TABLE:
        setDrawerLabel(VTableOperateButtons.find((btn) => btn.action === actionType)?.label || '')
        setCurrentVTableInfo(record as VTable)
        setSelectColumnsCreateNewTableProps({
          open: true,
          originColumns: (record?.columns || []).map((col: { name: string }) => {
            const id = nanoid()
            return {
              id,
              key: id,
              type: 'COLUMN',
              columnName: col.name,
            }
          }),
          currentTable: `${record.catalogName}.${record.databaseName}.${record.name}`,
          computeType: record.computeType,
          tableData: record,
        })
        break
      case ACTIONS.CREATE_CEP_TABLE:
        setDrawerLabel(VTableOperateButtons.find((btn) => btn.action === actionType)?.label || '')
        editor?.setValue?.('')
        setCreateCEPTableProps({
          open: true,
          tableData: { ...record, id: nanoid() },
        })
        break
      case ACTIONS.DATA_MASKING:
        setDrawerLabel(VTableOperateButtons.find((btn) => btn.action === actionType)?.label || '')
        setDataMaskProps({
          open: true,
          tableData: record,
        })
        break
      case ACTIONS.TIME_COLUMN_SETTING:
        setTimeColumnSettingProps({
          open: true,
          tableData: record,
        })
        break
      default:
        break
    }
  }

  const renderPublishMVCtx = () => {
    const IS_UPDATE_MODEL = currentPublishTable?.dataModelDesc?.modelType === 'UPDATE_MODEL'
    if (IS_UPDATE_MODEL) {
      return (
        <>
          <Form.Item label="输出选项" required>
            <Input disabled value="直接上线（仅流批一体虚拟表可用）" />
          </Form.Item>
          <StreamSettingFormItem type={currentPublishTable.computeType} isShowUnit={false} />
          <Row align="middle" gutter={[14, 0]} className="mb-2">
            <Col>
              <Form.Item
                name="isConfigResource"
                label="资源配置"
                className="my-0"
                labelCol={{
                  flex: 'none',
                }}
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item
            noStyle
            shouldUpdate={({ isConfigResource: preIsConfigResource }, { isConfigResource: curIsConfigResource }) =>
              preIsConfigResource !== curIsConfigResource
            }
          >
            {({ getFieldValue }) => <>{getFieldValue('isConfigResource') && <ResourceSettingsFormItem />}</>}
          </Form.Item>
          <CustomConfigFormItem />
        </>
      )
    }
    return (
      <>
        <DetailOutputSettingsFormItem form={configForm} isUseKafkaCatalogName />
        <DataResolveFormItem
          columns={
            currentPublishTable?.columns?.map((j: any) => {
              const str = `${currentPublishTable.catalogName}.${currentPublishTable.databaseName}.${currentPublishTable.name}.${j.name}`
              return {
                label: str,
                value: str,
              }
            }) || []
          }
        />
        <StreamSettingFormItem type={currentPublishTable.computeType} isShowUnit={false} />
        <Form.Item noStyle shouldUpdate>
          {({ getFieldValue }) => {
            return (
              getFieldValue('outputTarget') === 'JDBC' && (
                <OutputDesc
                  allowPrimaryKeysCfg
                  table={currentPublishTable}
                  form={configForm}
                  queryParams={{
                    OutputTarget: 'JDBC',
                  }}
                />
              )
            )
          }}
        </Form.Item>
        <Row align="middle" gutter={[14, 0]} className="mb-5">
          <Col>
            <Form.Item
              name="isConfigResource"
              label="资源配置"
              className="my-0"
              labelCol={{
                flex: 'none',
              }}
            >
              <Switch />
            </Form.Item>
          </Col>
        </Row>
        <Form.Item
          noStyle
          shouldUpdate={({ isConfigResource: preIsConfigResource }, { isConfigResource: curIsConfigResource }) =>
            preIsConfigResource !== curIsConfigResource
          }
        >
          {({ getFieldValue }) => <>{getFieldValue('isConfigResource') && <ResourceSettingsFormItem />}</>}
        </Form.Item>
        <CustomConfigFormItem />
      </>
    )
  }

  useEffect(() => {
    // 从 URL 获取参数
    const catalog = searchParams.get('catalog')
    const database = searchParams.get('database')

    if (catalog && database) {
      // 设置展开的节点
      setExpandedKeys([catalog])
      // 设置选中的节点
      setSelectedKeys([`${catalog}.${database}`])
      // 设置当前选择
      setCurrentSelection({ catalog, database })
      // 加载表格数据
      getTableList(catalog, database, 1, PageSize)
    }
  }, [searchParams]) // 依赖 searchParams 变化

  function handleCloseCreateCatalogModal() {
    setCreateCatalogModalOpen(false)
    createCatalogForm.resetFields()
  }

  return (
    <div className="flex h-full w-full gap-4">
      <div className="mx-3 w-60 shrink-0">
        <CatalogDatabaseTree
          catalogData={catalogData}
          dbDataMap={dbDataMap}
          searchValue={searchValue}
          onSearchChange={setSearchValue}
          expandedKeys={expandedKeys}
          onExpandedKeysChange={setExpandedKeys}
          selectedKeys={selectedKeys}
          onCurrentDbAction={setCurrentDbAction}
          onCatalogORDatabaseDetail={setCatalogORDatabaseDetail}
          onSelectedKeysChange={setSelectedKeys}
          onSelectionChange={({ catalog, database }) => {
            setCurrentSelection({ catalog, database })
            setPagination((prev) => ({
              ...prev,
              current: 1,
            }))
            getTableList(catalog, database, 1, pagination.pageSize)
          }}
        />
      </div>
      <div className="flex w-full flex-col">
        <CurrentVtableContext.Provider value={currentVTableInfo}>
          <PageHeader
            className={`mb-4 py-0 ${styles.pageHeader}`}
            title={
              <>
                <Search
                  placeholder="搜索虚拟表"
                  allowClear
                  onSearch={(e) => {
                    setSearchTableValue(e)
                    getTableList(
                      currentSelection.catalog || catalogData?.[0]?.name,
                      currentSelection.database,
                      1,
                      PageSize,
                      e,
                    )
                  }}
                />
              </>
            }
            extra={
              <div className="flex items-center gap-4">
                <Popover
                  placement="bottomRight"
                  content={
                    <>
                      <div className="flex flex-col rounded">
                        <Button
                          type="link"
                          onClick={() => {
                            setCreateType('catalog')
                            setCreateCatalogModalOpen(true)
                          }}
                        >
                          创建虚拟表目录
                        </Button>
                        {currentDbAction === 'delete' ? (
                          <Button
                            type="link"
                            danger
                            onClick={() => {
                              setIsDeleteModalOpen(true)
                              setDeleteType('database')
                              setCurDeleteInfo({
                                catalog: currentSelection.catalog,
                                database: catalogORDatabaseDetail,
                              })
                            }}
                          >
                            删除数据库
                          </Button>
                        ) : currentDbAction === 'create' ? (
                          <>
                            <Button
                              type="link"
                              danger
                              onClick={
                                catalogORDatabaseDetail === 'dipeak'
                                  ? undefined
                                  : () => {
                                      setIsDeleteModalOpen(true)
                                      setDeleteType('catalog')
                                      setCurDeleteInfo({
                                        catalog: catalogORDatabaseDetail,
                                      })
                                    }
                              }
                              className={clsx({
                                'cursor-not-allowed opacity-50': catalogORDatabaseDetail === 'dipeak',
                              })}
                            >
                              删除虚拟表目录
                            </Button>
                            <Button
                              type="link"
                              onClick={() => {
                                setCreateCatalogModalOpen(true)
                                setCreateType('database')
                                createCatalogForm.setFieldValue('catalog', catalogORDatabaseDetail)
                              }}
                            >
                              新增数据库
                            </Button>
                          </>
                        ) : null}
                      </div>
                    </>
                  }
                >
                  <MoreOutlined className="rotate-90 cursor-pointer text-2xl" />
                </Popover>

                <Dropdown
                  placement="bottomRight"
                  arrow
                  menu={{
                    items: [
                      {
                        key: 'visual-create',
                        label: (
                          <span onClick={() => navigate(routerMap.dataModel.createBusinessVirtualTable.path)}>
                            可视化创建
                          </span>
                        ),
                      },
                      {
                        key: 'csv-upload-create',
                        label: <span onClick={CSVUploadModalOpenOps.setTrue}>CSV上传创建</span>,
                      },
                    ],
                  }}
                >
                  <Button type="primary">创建业务虚拟表</Button>
                </Dropdown>
              </div>
            }
          />
          <EditorInDrawer
            virtualTableCreateType="SQL_CREATE"
            tableData={createCEPTableProps?.tableData}
            onClose={() => setCreateCEPTableProps((pre) => ({ ...pre, open: false }))}
            onOk={() => setCreateCEPTableProps((pre) => ({ ...pre, open: false }))}
            editor={editor}
            showFormatBtn
            setEditor={setEditor}
            title={'SQL创建虚拟表'}
            open={createCEPTableProps.open}
          ></EditorInDrawer>
          <Card>
            <Table
              columns={businessTableColumns({
                publishVTable,
                offlineStreamVtableSuccess: () => {
                  message.success('下线成功')
                  getTableList(currentSelection.catalog, currentSelection.database, 1, pagination.pageSize)
                },
                streamVtableActSuccess: () => {
                  message.success('操作成功')
                  getTableList(currentSelection.catalog, currentSelection.database, 1, pagination.pageSize)
                },
                navigateDetail,
                materializeTable(record: any) {
                  setMaterializeVirtualTableDrawerProps({
                    open: true,
                    columns: record.columns,
                    catalog: record.catalogName,
                    database: record.databaseName,
                    table: record.name,
                  })
                },
                offMaterializeTable(record: any) {
                  const [, , mvName] = get(record, ['settings', 'materialized.view.name'])?.split('.') || []
                  return request.delete(`${askBIApiUrls.xengine.mv.dropMv}?mvName=${mvName}&displayName=${mvName}`)
                },
                goMVDetail(record: SmartXMaterialViewDetailQueryParams) {
                  navigate(`${routerMap.smartx.materialViewDetail.path}?${new URLSearchParams(record).toString()}`)
                },
                refreshList: () => {
                  getTableList(currentSelection.catalog, currentSelection.database, 1, pagination.pageSize)
                },
              }).concat([
                {
                  title: '操作',
                  dataIndex: 'vtable-control',
                  width: 60,
                  fixed: 'right',
                  render: (_: any, record: any) => {
                    return (
                      <Popover
                        placement="bottomLeft"
                        content={
                          <div className="w-[145px]">
                            {customerFilterValue('vTableOperateButtonsFilter', VTableOperateButtons).map((btn) => (
                              <Button
                                key={btn.action}
                                block
                                type="text"
                                danger={btn.danger}
                                icon={<btn.icon />}
                                onClick={() => handleAction(record, btn.action)}
                                className="flex items-center justify-start"
                                style={{ marginLeft: 0, paddingLeft: 2 }}
                              >
                                {btn.label}
                              </Button>
                            ))}
                          </div>
                        }
                      >
                        <MoreOutlined className="cursor-pointer" />
                      </Popover>
                    )
                  },
                },
              ])}
              rowKey="id"
              scroll={{ x: xWidth }}
              dataSource={tableListData}
              loading={tableListLoading}
              pagination={{
                ...pagination,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条`,
              }}
              locale={{
                emptyText: <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="该数据库下未获取到数据" />,
              }}
              onChange={handleTableChange}
            />
          </Card>

          <Modal
            title={'删除虚拟表'}
            open={isModalOpen}
            onOk={handleOk}
            onCancel={handleCancel}
            okText={'确认删除'}
            cancelText={'取消'}
          >
            <p>
              确认删除 <Text type="danger">{currentTable?.name}</Text> 表吗？
            </p>
          </Modal>
          <Modal
            title={'上线业务虚拟表'}
            open={publishStreamVTableModalOpen}
            onOk={publishStreamVTableModalOk}
            onCancel={cancelPublishStreamVTableModal}
            okText={'确定'}
            okButtonProps={{
              loading: isPublishStreamVTable,
            }}
            cancelText={'取消'}
          >
            <Form
              form={configForm}
              layout="vertical"
              initialValues={{
                dataProcessDesc: [{}],
                updateStrategy: 'NEW',
                outputTarget: 'KAFKA',
              }}
              onValuesChange={handleFormValuesChange}
            >
              {renderPublishMVCtx()}
            </Form>
          </Modal>
          {/* 复制表 和 可视化Filter清洗*/}
          <CreateFilterTable
            {...createFilterTableProps}
            onClose={() =>
              setCreateFilterTableProps({
                open: false,
              })
            }
          />
          {/* 以该表创建新表 和自定义 SQL 清洗 */}
          {/* <SelectColumnsCreateNewTable
        {...selectColumnsCreateNewTableProps}
        onSuccess={() =>
          setRefreshTrigger((pre) => {
            return pre + 1
          })
        }
        mode={currentAction === ACTIONS.CREATE_VIRTUAL_TABLE ? ACTIONS.CREATE_VIRTUAL_TABLE : ACTIONS.SQL_CLEANING}
        closeController={() =>
          setSelectColumnsCreateNewTableProps((pre) => ({
            ...pre,
            open: false,
          }))
        }
      /> */}
          <SelectColumnsCreateNewTableByTransfer
            {...selectColumnsCreateNewTableProps}
            mode={currentAction === ACTIONS.CREATE_VIRTUAL_TABLE ? ACTIONS.CREATE_VIRTUAL_TABLE : ACTIONS.SQL_CLEANING}
            closeController={() =>
              setSelectColumnsCreateNewTableProps((pre) => ({
                ...pre,
                open: false,
              }))
            }
          />
          {/* 创建数据目录 */}
          <Modal
            width={540}
            title={createType === 'database' ? '创建虚拟表数据库' : '创建虚拟表目录'}
            open={CreateCatalogModalOpen}
            onCancel={() => {
              handleCloseCreateCatalogModal()
            }}
            okButtonProps={{
              loading: createCatalogLoading || createDatabaseLoading,
              async onClick() {
                await createCatalogForm.validateFields()
                const values = createCatalogForm.getFieldsValue()
                switch (createType) {
                  case 'catalog': {
                    createCatalog(values)
                    break
                  }
                  case 'database': {
                    createDatabase(values)
                    break
                  }
                }
              },
            }}
          >
            <Form
              form={createCatalogForm}
              labelCol={{
                flex: '120px',
              }}
            >
              <Form.Item
                name="catalog"
                label="数据目录名称"
                validateFirst
                rules={[
                  { required: true },
                  {
                    async validator(_, value) {
                      const testReg = /[^a-zA-Z_0-9]/
                      if (testReg.test(value)) {
                        return Promise.reject('请输入符合格式的数据目录名称')
                      }
                    },
                  },
                ]}
              >
                <Input placeholder="支持英文、数字、下划线" readOnly={createType === 'database'} />
              </Form.Item>

              <Form.Item
                name="databases"
                label="数据库名称"
                validateFirst
                validateDebounce={400}
                rules={[
                  { required: true },
                  {
                    async validator(_, value) {
                      const testReg = /[^a-zA-Z,_0-9]/
                      if (testReg.test(value)) {
                        return Promise.reject('请输入符合格式的数据库名称')
                      }
                    },
                  },
                  {
                    async validator(_, value) {
                      const dbs = (value || '').split(',')
                      const dbSet = [...new Set(dbs)]
                      if (dbs.length > dbSet.length) {
                        return Promise.reject('有重复的数据库名称输入')
                      }
                    },
                  },
                ]}
              >
                <Input placeholder="支持英文、数字、下划线，可填写多个，用','分隔" />
              </Form.Item>
            </Form>
          </Modal>
          {/* 删除目录 */}
          <Modal
            title={`确认删除数据${deleteType === 'catalog' ? '目录' : '库'}${
              (deleteType === 'catalog' ? curDeleteInfo?.catalog : curDeleteInfo?.database) || ''
            }`}
            open={isDeleteModalOpen}
            onCancel={() => {
              setCurDeleteInfo(null)
              setIsDeleteModalOpen(false)
            }}
            okButtonProps={{
              loading: deleteCatalogLoading || deleteDatabaseLoading,
              onClick() {
                if (curDeleteInfo) {
                  switch (deleteType) {
                    case 'catalog': {
                      if (curDeleteInfo) {
                        const { catalog } = curDeleteInfo
                        deleteCatalog({
                          catalog,
                        })
                      }
                      break
                    }
                    case 'database': {
                      if (curDeleteInfo && curDeleteInfo.database) {
                        const { catalog, database } = curDeleteInfo
                        deleteDatabase({ catalog, database })
                      }
                      break
                    }
                  }
                }
              },
            }}
          >
            <p>
              {`删除当前数据${deleteType === 'catalog' ? '目录' : '库'}会同步删除数据${
                deleteType === 'catalog' ? '目录' : '库'
              }内虚拟表，是否确定删除？`}
            </p>
          </Modal>
          <DataMaskModal
            open={dataMaskProps.open}
            tableData={dataMaskProps.tableData}
            onCancel={() => setDataMaskProps((pre) => ({ ...pre, open: false }))}
          />
          <TimeColumnSettingModal
            open={timeColumnSettingProps.open}
            tableData={timeColumnSettingProps.tableData}
            onCancel={() => setTimeColumnSettingProps((pre) => ({ ...pre, open: false }))}
          />
          <ValidatedUploadFile
            modalProps={{
              title: 'CSV上传创建业务虚拟表',
              open: CSVUploadModalOpen,
              onCancel: CSVUploadModalOpenOps.setFalse,
            }}
            uploadApi={(file) => {
              const formData = new FormData()
              formData.append('file', file)
              return request.post(askBIApiUrls.xengine.VTable.createErModelVtableByCsv, formData, {
                headers: {
                  'Content-Type': 'multipart/form-data',
                },
              })
            }}
            acceptTypes={['.csv']}
            samples={[
              {
                fileName: '创建业务虚拟表示例.csv',
                fileUrl: `${askBIApiUrls.model.CSVUploadTemplate}?type=ER_MODEL_VIRTUAL_TABLE`,
              },
            ]}
            onSuccess={() => {
              message.success('上传成功')
              CSVUploadModalOpenOps.setFalse()
              getTableList(currentSelection.catalog, currentSelection.database, 1, pagination.pageSize)
            }}
          />
          <MaterializeVirtualTableDrawer
            open={materializeVirtualTableDrawerProps.open}
            columns={materializeVirtualTableDrawerProps.columns}
            catalog={materializeVirtualTableDrawerProps.catalog}
            database={materializeVirtualTableDrawerProps.database}
            table={materializeVirtualTableDrawerProps.table}
            closeController={() => setMaterializeVirtualTableDrawerProps({ open: false })}
            onSuccess={() => {
              getTableList(currentSelection.catalog, currentSelection.database, 1, pagination.pageSize)
            }}
          />
        </CurrentVtableContext.Provider>
      </div>
    </div>
  )
}

export default VirtualTable
