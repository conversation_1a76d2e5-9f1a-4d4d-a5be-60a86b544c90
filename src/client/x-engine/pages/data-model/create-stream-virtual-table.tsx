import {
  Button,
  Col,
  Form,
  Input,
  InputNumber,
  message,
  Result,
  Row,
  Select,
  Tooltip,
  Typography,
  Pagination,
} from 'antd'
import React, { useEffect, useState } from 'react'
import LayoutCard from '@ui/layoutCard/LayoutCard'
import { useLocation, useNavigate, useSearchParams } from 'react-router-dom'
import DbWithTable from '../../../pages/MetricStore/ExternalDatasource/components/DbWithTable'
import { MinusCircleOutlined, PlusOutlined, ExclamationCircleOutlined } from '@ant-design/icons'
import { useRequest, useBoolean } from 'ahooks'
import { Api } from '@api'
import KafkaPropertiesFormItem from './forms-conf/KafkaPropertiesFormItem'
import FileSystemPropertiesFormItem from './forms-conf/FileSystemPropertiesFormItem'
import AdminPage from 'src/client/components/AdminPage'
import { SQLIcon, SvgIcon } from 'src/client/components/SvgIcon'
import { EditorInDrawer } from '../../widget/model/sql/EditorInDrawer'
import { IntNotZeroReg, IntReg } from 'src/shared/constants'
import ValidatedUploadFile from '../../../components/ValidatedUploadFile'
import { askBIApiUrls } from 'src/shared/url-map'
import request from 'src/shared/xengine-axios'
import { omitBy } from 'lodash-es'
import { TableCreateColumnType } from 'src/shared/xengine-types'
import { editor } from 'monaco-editor'

interface SQLInFormProps {
  id?: string
  value?: string
  onChange?: (value: string) => void
  onClick: (e: { value: string; onChange: SQLInFormProps['onChange']; currentId: string }) => void
  currentId?: string
}

const TypeInfoMap = {
  KAFKA: {
    title: '流式表',
    formItem: KafkaPropertiesFormItem,
  },
  FILESYSTEM: {
    title: '文件表',
    formItem: FileSystemPropertiesFormItem,
  },
}

// 字段校验，由于使用的Form.List不能设置validateTrigger造成多次渲染，改为手动message提示
function columnsValidate(columns: TableCreateColumnType[]) {
  let msg = ''
  const valid = columns.every((col) => {
    if (!col.name) {
      msg = '存在没有填写字段名的字段'
      return false
    }
    if (!col.columnType) {
      msg = '存在没有选择字段类型的字段'
      return false
    }
    if (col.columnType === 'DECIMAL' && (col.columnPrecision == null || col.columnScale == null)) {
      msg = '字段类型为DECIMAL，需填写精度和小数位'
      return false
    }
    if (col.columnType === 'TIMESTAMP' && col.columnPrecision == null) {
      msg = '字段类型为DECIMAL，需填写精度'
      return false
    }
    return true
  })
  if (!valid) {
    message.error(msg)
  }
  return valid
}

function resolveRequestSpecialTableProp(type: string) {
  switch (type) {
    case 'KAFKA': {
      return {
        tableEngine: 'Kafka',
        tableType: 'stream',
      }
    }
    case 'FILESYSTEM': {
      return {
        tableEngine: 'FileSystem',
      }
    }
    default: {
      return {}
    }
  }
}
const Option = Select.Option

// 格式化表单properties的值
function formatPropertiesValues(properties: { [key: string]: string | number | boolean | string[] }) {
  const trulyProperties = omitBy(properties, (val) => val === '' || val === undefined)
  return Object.keys(trulyProperties).reduce((pre, key) => {
    switch (key) {
      case 'file.system.csv.dynamic.schedule': {
        const schedule = Math.floor((trulyProperties[key] as number) / 1000)
        return schedule > 0 ? Object.assign(pre, { [key]: schedule }) : pre
      }
      case 'kafka.time.column.options': {
        if (Array.isArray(trulyProperties[key])) {
          return Object.assign(pre, { [key]: trulyProperties[key].join(',') })
        }
        return pre
      }
      case 'file.system.time.column.options': {
        if (Array.isArray(trulyProperties[key])) {
          return Object.assign(pre, { [key]: trulyProperties[key].join(',') })
        }
        return pre
      }
      default: {
        return Object.assign(pre, { [key]: trulyProperties[key] })
      }
    }
  }, {})
}

export default function CatalogUpdate() {
  const location = useLocation()
  const [searchParams] = useSearchParams()
  const mode = searchParams.get('mode')
  const catalog = searchParams.get('catalog') || ''
  const database = searchParams.get('database') || ''
  const name = searchParams.get('table') || ''
  const type = searchParams.get('type')?.toLocaleUpperCase() || 'KAFKA'
  const info = TypeInfoMap[type as keyof typeof TypeInfoMap]
  const PropertiesFormItemTag = info.formItem
  const [tableColsUploadOpen, tableColsUploadOpeOpts] = useBoolean(false)
  const [columnsPagination, setColumnsPagination] = useState({
    current: 1,
    pageSize: 10,
  })

  const navigate = useNavigate()

  const [form] = Form.useForm()
  const [catalogName, setCatalogName] = useState<string>()
  const [success, setSuccess] = useState<boolean>(false)

  const [editor, setEditor] = useState<editor.IStandaloneCodeEditor | null>(null)

  const { data: columnTypeList, loading: getColumnTypeListLoading } = useRequest(() =>
    request.get<{}, string[]>(askBIApiUrls.xengine.ptable.columnType),
  )

  const continueTask = () => {
    setSuccess(false)
    if (!location.state.catalogName) {
      form.resetFields()
      setCatalogName('')
    }
  }
  const { data: tableDetail } = useRequest(
    () =>
      Api.apiEngineV1PtableDetailGet({
        catalog,
        database,
        name,
      }),
    {
      ready: mode === 'edit',
      onSuccess: (data) => {
        form.setFieldsValue({
          catalogName: data.catalogName,
          properties: data?.properties,
          name: data.name,
          columns: data.columns,
        })
      },
    },
  )
  const { run: updateTable } = useRequest(
    () => {
      const data = form.getFieldsValue()
      return Api.apiEngineV1PtablePut({
        table: Object.assign(
          {
            id: tableDetail?.id,
            catalogName: data.catalogName,
            databaseName: 'default',
            name: data.name,
            columns: data.columns?.map((col: TableCreateColumnType) => omitBy(col, (val) => val === null)),
            properties: formatPropertiesValues(data.properties),
          },
          resolveRequestSpecialTableProp(type),
        ) as any,
      })
    },
    {
      manual: true,
      ready: mode === 'edit',
    },
  )

  const { data: datasourceList } = useRequest(() => Api.apiEngineV1DatasourceCatalogListGet({ type }), {})
  const { run: createStreamTable, loading: isCreateStreamTableLoading } = useRequest(
    (data) => {
      return Api.apiEngineV1PtablePost({
        table: Object.assign(
          {
            catalogName: data.catalogName,
            databaseName: 'default',
            name: data.name,
            comment: data.comment,
            columns: data.columns?.map((col: TableCreateColumnType) => omitBy(col, (val) => val === null)),
            properties: formatPropertiesValues(data.properties),
          },
          resolveRequestSpecialTableProp(type),
        ) as any,
        cascadeCreateVTable: false,
      })
    },
    {
      manual: true,
      onSuccess: () => {
        message.success('创建成功')
        navigate(-1)
      },
    },
  )
  const [currentSQLEditorInfo, innerSetCurrentSQLEditorInfo] = useState<{
    open: boolean
    value: string
    currentId: string
    onChange?: (value: string) => void
  }>({
    open: false,
    value: '',
    currentId: '',
    onChange: () => {},
  })
  const setCurrentSQLEditorInfo = ({
    open,
    value,
    currentId,
    onChange,
  }: {
    open: boolean
    value: string
    currentId: string
    onChange?: (value: string) => void
  }) => {
    open && editor?.setValue?.(value)
    innerSetCurrentSQLEditorInfo({
      open,
      value,
      currentId,
      onChange,
    })
  }

  useEffect(() => {
    if (currentSQLEditorInfo.open && editor) {
      editor.setValue(currentSQLEditorInfo.value)
    }
  }, [currentSQLEditorInfo.open, currentSQLEditorInfo.value, editor])

  const SQLIconInForm: React.FC<SQLInFormProps> = ({ value, onChange, onClick, currentId = '' }) => {
    return (
      <span
        onClick={() => {
          onClick?.({
            value: value || '',
            onChange,
            currentId,
          })
          return
        }}
      >
        <SvgIcon icon={SQLIcon} className="ml-2 h-6 w-6 cursor-pointer dark:text-white" />
      </span>
    )
  }
  return (
    <>
      <AdminPage title={`${mode === 'edit' ? '编辑' : '创建'}${info.title}`} onBack={() => window.history.back()}>
        <LayoutCard>
          {success ? (
            <Result
              status="success"
              title={(location.state?.catalogName ? '修改' : '创建') + '数据源成功'}
              extra={[
                <Button type="primary" onClick={() => history.back()}>
                  返回上级
                </Button>,
                <Button onClick={continueTask}>{location.state?.catalogName ? '继续修改' : '继续创建'}</Button>,
              ]}
            />
          ) : (
            <Form
              initialValues={{
                properties: {
                  'file.system.csv.ignore.parse.errors': true,
                  'file.system.csv.allow.comments': true,
                },
              }}
              form={form}
              labelCol={{ span: 6 }}
              wrapperCol={{ span: 16 }}
              style={{ maxWidth: 600 }}
              onFinish={(val) => {
                if (columnsValidate(val.columns)) {
                  mode === 'edit' ? updateTable() : createStreamTable(val)
                }
              }}
            >
              <Form.Item label={`数据源`} name="catalogName" rules={[{ required: true, message: '请选择数据源' }]}>
                <Select placeholder="请选择">
                  {datasourceList?.list?.map?.((i) => (
                    <Option value={i.name} key={i.name}>
                      {i.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
              <Form.Item
                label="表名称"
                name="name"
                rules={[
                  { required: true, message: '请输入虚拟表名称' },
                  {
                    pattern: new RegExp(/[A-Za-z_0-9]+$/, 'g'),
                    message: '支持英文、数字、下划线',
                  },
                ]}
              >
                <Input placeholder="请输入" />
              </Form.Item>
              <Form.Item label="表描述" name="comment" initialValue={''}>
                <Input placeholder="请输入" />
              </Form.Item>
              <Form.Item shouldUpdate noStyle>
                {({ getFieldValue }) => (
                  <PropertiesFormItemTag catalogName={getFieldValue('catalogName')}>
                    <Form.Item
                      label="字段"
                      required
                      shouldUpdate={({ columns: preColumns }, { columns: curColumns }) =>
                        preColumns?.length !== curColumns?.length
                      }
                    >
                      {({ getFieldValue }) => (
                        <>
                          <Form.List name="columns" initialValue={[{ name: '' }]}>
                            {(fields, { add, remove }) => {
                              const { current, pageSize } = columnsPagination
                              return (
                                <>
                                  {fields.map(({ name, key, ...restField }, index) => {
                                    const isShow = index >= (current - 1) * pageSize && index < current * pageSize
                                    if (isShow) {
                                      return (
                                        <div key={key}>
                                          <Row
                                            style={{ width: '100%', display: 'flex', marginBottom: 8 }}
                                            gutter={[10, 10]}
                                          >
                                            <Col flex={1}>
                                              <Form.Item noStyle shouldUpdate>
                                                {() => (
                                                  <Form.Item
                                                    {...restField}
                                                    name={[name, 'name']}
                                                    rules={[
                                                      {
                                                        required: true,
                                                        message: '请输入字段名',
                                                      },
                                                    ]}
                                                  >
                                                    <Input placeholder="请输入字段名" />
                                                  </Form.Item>
                                                )}
                                              </Form.Item>
                                            </Col>
                                            <Col flex={1}>
                                              <Form.Item
                                                {...restField}
                                                name={[name, 'columnType']}
                                                rules={[
                                                  {
                                                    required: true,
                                                    message: '请选择字段类型',
                                                  },
                                                ]}
                                              >
                                                <Select
                                                  loading={getColumnTypeListLoading}
                                                  showSearch
                                                  placeholder={'字段类型'}
                                                  dropdownStyle={{
                                                    width: 220,
                                                  }}
                                                  options={columnTypeList?.map((type) => ({
                                                    label: type,
                                                    value: type,
                                                  }))}
                                                />
                                              </Form.Item>
                                            </Col>
                                            <Col flex={1}>
                                              <Form.Item {...restField} name={[name, 'asExpr']} rules={[]}>
                                                <SQLIconInForm
                                                  onClick={({ value, onChange, currentId }) => {
                                                    setCurrentSQLEditorInfo({
                                                      open: true,
                                                      value: value || '',
                                                      currentId,
                                                      onChange,
                                                    })
                                                  }}
                                                />
                                              </Form.Item>
                                            </Col>
                                          </Row>
                                          {/* 如果上边 columnType 选择的是decimal 则展示 Precision 和 scale 表单 */}
                                          <Form.Item shouldUpdate noStyle>
                                            {({ getFieldValue }) => {
                                              const columnType = getFieldValue(['columns', name, 'columnType'])
                                              return (
                                                <>
                                                  {columnType === 'DECIMAL' && (
                                                    <>
                                                      <Row
                                                        style={{ width: '100%', display: 'flex', marginBottom: 8 }}
                                                        gutter={[10, 10]}
                                                      >
                                                        <Col flex={1}>
                                                          <Form.Item
                                                            {...restField}
                                                            name={[name, 'columnPrecision']}
                                                            rules={[
                                                              {
                                                                required: true,
                                                                message: '请输入精度',
                                                              },
                                                              { pattern: IntNotZeroReg, message: '请输入整数' },
                                                            ]}
                                                          >
                                                            <InputNumber
                                                              style={{ width: '100%' }}
                                                              min={0}
                                                              placeholder="请输入 precision"
                                                            />
                                                          </Form.Item>
                                                        </Col>
                                                        <Col flex={1}>
                                                          <Form.Item
                                                            {...restField}
                                                            name={[name, 'columnScale']}
                                                            rules={[
                                                              {
                                                                required: true,
                                                                message: '请输入小数位',
                                                              },
                                                              { pattern: IntReg, message: '请输入整数' },
                                                            ]}
                                                          >
                                                            <InputNumber
                                                              style={{ width: '100%' }}
                                                              min={0}
                                                              placeholder="请输入 scale"
                                                            />
                                                          </Form.Item>
                                                        </Col>
                                                      </Row>
                                                    </>
                                                  )}
                                                  {(columnType === 'TIMESTAMP' ||
                                                    columnType === 'TIMESTAMP_WITH_LOCAL_TIME_ZONE' ||
                                                    columnType === 'CHAR' ||
                                                    columnType === 'VARCHAR') && (
                                                    <>
                                                      <Row
                                                        style={{ width: '100%', display: 'flex', marginBottom: 8 }}
                                                        gutter={[10, 10]}
                                                      >
                                                        <Col flex={1}>
                                                          <Form.Item
                                                            {...restField}
                                                            initialValue={0}
                                                            name={[name, 'columnPrecision']}
                                                            rules={[
                                                              {
                                                                required: true,
                                                                message: '请输入精度',
                                                              },
                                                              { pattern: IntReg, message: '请输入整数' },
                                                            ]}
                                                          >
                                                            <InputNumber
                                                              style={{ width: '100%' }}
                                                              min={0}
                                                              placeholder="请输入 precision"
                                                            />
                                                          </Form.Item>
                                                        </Col>
                                                      </Row>
                                                    </>
                                                  )}
                                                </>
                                              )
                                            }}
                                          </Form.Item>
                                          <Row style={{ width: '100%', display: 'flex', marginBottom: 8 }}>
                                            <Col flex={'90%'}>
                                              <Form.Item {...restField} name={[name, 'comment']}>
                                                <Input.TextArea rows={1} placeholder="请输入注释" />
                                              </Form.Item>
                                            </Col>
                                            <Col flex={'10%'} className="mt-[8px] flex items-baseline justify-center">
                                              {fields.length > 1 && (
                                                <MinusCircleOutlined
                                                  className="ml-1"
                                                  onClick={() => {
                                                    remove(name)
                                                  }}
                                                />
                                              )}
                                            </Col>
                                          </Row>
                                        </div>
                                      )
                                    }
                                  })}
                                  <Row className="w-full" gutter={[12, 0]} align="middle">
                                    <Col flex={1}>
                                      <Button type="dashed" onClick={() => add({})} icon={<PlusOutlined />} block>
                                        添加字段
                                      </Button>
                                    </Col>
                                    <Col flex={'180px'}>
                                      <Typography.Link className="mr-1" onClick={tableColsUploadOpeOpts.setTrue}>
                                        上传文件方式新增字段
                                      </Typography.Link>
                                      <Tooltip title="上传后页面添加的字段将被删除，页面/文件添加方式只能2选1">
                                        <ExclamationCircleOutlined className="cursor-pointer" />
                                      </Tooltip>
                                    </Col>
                                  </Row>
                                </>
                              )
                            }}
                          </Form.List>
                          <Pagination
                            align="end"
                            className="mt-4"
                            size="small"
                            simple
                            total={getFieldValue('columns')?.length}
                            onChange={(page, pageSize) =>
                              setColumnsPagination({
                                current: page,
                                pageSize,
                              })
                            }
                            {...columnsPagination}
                          />
                        </>
                      )}
                    </Form.Item>
                  </PropertiesFormItemTag>
                )}
              </Form.Item>

              <Form.Item wrapperCol={{ offset: 6, span: 14 }}>
                <Button type="primary" loading={isCreateStreamTableLoading} onClick={form.submit}>
                  {mode === 'edit' ? '保存' : '创建'}
                </Button>
              </Form.Item>
            </Form>
          )}
        </LayoutCard>

        <DbWithTable catalogName={catalogName || ''} />
        <EditorInDrawer
          showSide={false}
          tableData={[]}
          onClose={() => {
            setCurrentSQLEditorInfo({ ...currentSQLEditorInfo, open: false })
          }}
          editor={editor}
          setEditor={setEditor}
          customHandleConfirm={() => {
            currentSQLEditorInfo.onChange?.(editor?.getValue?.() || '')
            setCurrentSQLEditorInfo({ ...currentSQLEditorInfo, open: false })
          }}
          title={'自定义SQL数据清洗'}
          open={currentSQLEditorInfo.open}
        />
        {/* 上传文件创建字段 */}
        <ValidatedUploadFile
          modalProps={{
            title: '从CSV创建字段',
            open: tableColsUploadOpen,
            onCancel: tableColsUploadOpeOpts.setFalse,
          }}
          uploadApi={(file) => {
            const formData = new FormData()
            formData.append('file', file)
            return request
              .post<FormData, TableCreateColumnType[]>(askBIApiUrls.xengine.ptable.parseColumns, formData, {
                headers: {
                  'Content-Type': 'multipart/form-data',
                },
              })
              .then((data) => {
                message.success(`上传成功解析出${data.length}个字段`)
                form.setFieldValue('columns', data)
              })
          }}
          acceptTypes={['.csv']}
          samples={[
            {
              fileName: 'CSV创建字段示例.csv',
              fileUrl: `${askBIApiUrls.model.CSVUploadTemplate}?type=TABLE_COLUMN`,
            },
          ]}
          onSuccess={tableColsUploadOpeOpts.setFalse}
        />
      </AdminPage>
    </>
  )
}
