import React, { useState, useEffect } from 'react'
import { PageHeader } from '@ant-design/pro-layout'
import { Tabs, Steps, Layout, message } from 'antd'
import { DataSourceForm } from './forms-conf/datasource-form-conf'
import { QueryAdvance, TableConfig } from './forms-conf/virtual-table-conf'
import {
  stepItemsText,
  defaultLikeParams,
  createLikeTabItems,
  CREATE_LIKE_TABLE_STEPS,
} from './forms-conf/constant-conf'
import { LikeParamsProps } from './forms-conf/interface'
import { CreateBusinessVirtualTable } from './components/create-business-virtual-table'
import { useLocation } from 'react-router-dom'
import { useRequest } from 'ahooks'
import { Api } from '@api'
import { toCorn } from '@model/CornComp'
import LayoutCard from '@ui/layoutCard/LayoutCard'

const { Content } = Layout

const CreateVirtualTable = () => {
  const location = useLocation()
  const isAsTable = location?.state?.type === 'as'
  return <>{isAsTable ? <CreateBusinessVirtualTable /> : <CreateLikeTable />}</>
}

interface OriginDataType {
  isSubmit?: boolean
  tableEngine?: string
  [key: string]: any
}

function CreateLikeTable() {
  const [step, setStep] = useState(0)
  const [requestParams, setRequestParams] = useState<LikeParamsProps>(defaultLikeParams)
  // 原始数据，将3个大表单中的数据全部提升处理
  const [originData, setOriginData] = useState<OriginDataType | null>(null)

  const dataChange = (data: any) => {
    setOriginData({
      ...(originData || {}),
      ...data,
    })
  }

  const setStepHandle = (index: number) => {
    const special = (index === step && index === 1) || Math.abs(index - step) > 1
    setStep(special ? step - 1 : index)
  }

  const disposeRequest = {
    manual: true,
    onSuccess() {
      setStep(CREATE_LIKE_TABLE_STEPS.CREATE_SUCCESS)
    },
    onError() {
      setOriginData({
        ...originData,
        isSubmit: false,
      })
      message.error('创建虚拟表失败')
    },
  }

  const { loading: pTableLoading, run: createPTable } = useRequest(Api.apiEngineV1PtablePost, disposeRequest)

  useEffect(() => {
    if (originData?.isSubmit) {
      createPTable(getFinalParams(originData))
    }
  }, [originData])

  createLikeTabItems[CREATE_LIKE_TABLE_STEPS.CONFIG_DATASOURCE].children = (
    <DataSourceForm
      setStep={setStep}
      setRequestParams={setRequestParams}
      requestParams={requestParams}
      setOriginData={dataChange}
    />
  )

  let stepList = stepItemsText
  // change order
  if (requestParams.tableEngine && requestParams.tableEngine !== 'hive') {
    createLikeTabItems[CREATE_LIKE_TABLE_STEPS.QUERY_ADVANCED].children = (
      <TableConfig
        loading={pTableLoading}
        setStep={setStepHandle}
        setRequestParams={setRequestParams}
        requestParams={requestParams}
        setOriginData={dataChange}
      />
    )
    createLikeTabItems[CREATE_LIKE_TABLE_STEPS.CONFIG_TABLE].children = (
      <QueryAdvance
        loading={pTableLoading}
        setStep={setStepHandle}
        setRequestParams={setRequestParams}
        requestParams={requestParams}
        setOriginData={dataChange}
      />
    )
    stepList = [stepItemsText[0], stepItemsText[2], stepItemsText[1]]
  } else {
    createLikeTabItems[CREATE_LIKE_TABLE_STEPS.QUERY_ADVANCED].children = (
      <QueryAdvance
        loading={pTableLoading}
        setStep={setStepHandle}
        setRequestParams={setRequestParams}
        requestParams={requestParams}
        setOriginData={dataChange}
      />
    )
    createLikeTabItems[CREATE_LIKE_TABLE_STEPS.CONFIG_TABLE].children = (
      <TableConfig
        loading={pTableLoading}
        setStep={setStepHandle}
        setRequestParams={setRequestParams}
        requestParams={requestParams}
        setOriginData={dataChange}
      />
    )
  }

  return (
    <>
      <PageHeader title={`创建贴源虚拟表`} onBack={() => window.history.back()} />
      <LayoutCard>
        <Steps direction="horizontal" current={step} items={stepList} />
      </LayoutCard>
      <LayoutCard>
        <div style={{ display: 'flex' }}>
          <Content>
            <Tabs
              activeKey={step.toString()}
              items={createLikeTabItems}
              tabBarStyle={{ display: 'none' }}
              style={{ width: '100%' }}
            />
          </Content>
        </div>
      </LayoutCard>
    </>
  )
}

export interface CommonProps {
  step?: number
  loading?: boolean
  form?: any
  setStep?: (step: number) => void
  requestParams?: LikeParamsProps
  setRequestParams?: (data: LikeParamsProps) => void
  setOriginData?: (data: any) => void
}

function getFinalParams(originData: any) {
  console.log('originData is :', originData)
  const mirrorTableConfigVO: any = {
    distributedKeys: originData.tableParams.distributedKeys,
    partitionFormat: originData.queryParams.partitionFormat,
    partitionKey: originData.queryParams.partitionKey,
    sortColumn: originData.queryParams.sortColumn,
    usageMode: originData.queryParams.usageMode,
  }
  if (originData.tableEngine === 'hive') {
    mirrorTableConfigVO.corn = toCorn(originData.tableParams.corn)
  }
  if (originData.tableEngine !== 'external') {
    mirrorTableConfigVO.importImmediately = originData.tableParams.importImmediately
  }
  let properties: any = {}
  if (originData.tableEngine === 'hive') {
    properties = {
      'hive.metastore.uris': originData.hiveParams.metastore,
      'hive.database': originData.hiveParams.dbName,
      // 'hive.table': requestParams.hiveParams.hiveTableName,
      //  todo 上面 hiveTableName 没有 不知道为什么得对一下
      'hive.table': originData.hiveParams.tableName,
      username: originData.hiveParams.username,
      password: originData.hiveParams.password,
      kvValue: originData.hiveParams.kvValue,
      tableName: originData.hiveParams.tableName,
    }
  }
  if (originData.tableEngine === 'kafka') {
    properties = { ...originData.kafkaParams }
  }
  const commonParams: any = {
    catalogName: originData.catalog,
    databaseName: originData.database,
    columns: originData.columns,
    computeType: getComputeText(originData),
    tableEngine: getTableEngine(originData.tableEngine),
    name: originData.tableParams.name || originData.tableParams.virtualTableName,
    tableType: getTableType(originData.tableEngine),
    partitionKeys: originData.queryParams.partitionKey ? [originData.queryParams.partitionKey] : [], // originData.queryParams.partitionKey might be null
    timeColumn: originData.tableParams.timeColumn || originData.tableParams.timeCol,
  }

  if (originData.tableEngine === 'hive') {
    commonParams.like = originData.hiveParams.tableName
  }

  if (originData.tableEngine === 'external') {
    commonParams.distributedByHash = {
      hashFunc: originData.tableParams.hashFunc,
      keys: originData.tableParams.distributedKeys,
      bucketNum: originData.tableParams.bucketNum,
    }
    commonParams.primaryKeys = originData.tableParams.primaryKeys
  }

  return {
    table: {
      ...commonParams,
      properties,
      mirrorTableConfigVO,
    },
    cascadeCreateVTable: true,
  }
}

const getComputeText = (data: any) => {
  if (data.tableEngine === 'hive') {
    return 'BATCH'
  }
  if (data.tableEngine === 'kafka') {
    return 'STREAM'
  }
  return data.tableParams.computeType
}

const getTableEngine = (tableEngine: string) => {
  if (tableEngine === 'external') {
    return 'MergeTree'
  }
  return `${tableEngine[0].toUpperCase()}${tableEngine.slice(1)}`
}

const getTableType = (tableEngine: string) => {
  switch (tableEngine) {
    case 'hive':
      return 'extenalTable'
    case 'kafka':
      return 'EXTERNAL_TABLE'
    default:
      return 'MANAGED_TABLE'
  }
}

export default CreateVirtualTable
