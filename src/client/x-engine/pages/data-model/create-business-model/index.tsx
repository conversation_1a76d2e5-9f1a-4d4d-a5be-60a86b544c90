// @ts-nocheck
import Er<PERSON>low from '@model/er-flow'
import { PageHeader } from '@ant-design/pro-layout'
import { Button, Flex, Form, Input, message, Modal } from 'antd'
import React, { useRef, useState } from 'react'
import DBSelect from '@model/DBSelect'
import { erFlowDataAtom } from 'src/client/x-engine/atoms/er'
import { getUnitId } from '@libs'
import { Api } from '@api'
import { useRequest } from 'ahooks'
import { routerMap } from '@XEngineRouter/routerMap'
import { useNavigate } from 'react-router-dom'
import { useAtomValue, useAtom } from 'jotai'
import { TimeLimitTimeOffsetTimeOptions } from 'src/shared/constants'
import { isAlphaNumberAndAlphaStart } from '@shared/common-utils'
import { etlPaintInfoAtom } from '@atoms/er'

type TimeLimitTimeOffsetTimeOption = (typeof TimeLimitTimeOffsetTimeOptions)[number]
type TimeLimitTimeOffsetTimeOptionValue = TimeLimitTimeOffsetTimeOption['value']

// 创建 流批一体的 业务虚拟表 模型
export default function CreateBusinessModel() {
  const erFlowData = useAtomValue(erFlowDataAtom)
  const originData = useRef<any>()

  const navigate = useNavigate()

  const [form] = Form.useForm()

  const [show, setShow] = useState<boolean>(false)

  const [etlPaintInfo, setEtlPaintInfo] = useAtom(etlPaintInfoAtom)

  const [selectColumns, setSelectColumns] = useState<string>('')

  const { loading, run } = useRequest(
    async (data) => {
      if (!etlPaintInfo.createNodeStepShowOpen) {
        return Api.apiEngineV1VtablePost(data)
      }
    },
    {
      manual: true,
      onSuccess: (_: unknown, [tableInfo]) => {
        if (etlPaintInfo.createNodeStepShowOpen) {
          setEtlPaintInfo((res) => ({
            ...res,
            createNodeStepShowOpen: false,
            isReadyToCreate: true,
            virtualTable: tableInfo?.table,
          }))
        } else {
          void message.success('创建业务虚拟表成功')
          navigate(
            `${routerMap.dataModel.businessVirtualTable.path}?catalog=${tableInfo.table.catalogName}&database=${tableInfo.table.databaseName}&name=${tableInfo.table.name}`,
          )
        }
      },
      onFinally: () => {
        closeHandle()
      },
    },
  )

  const changeHandle = (d: any) => {
    originData.current = d
  }

  const submit = () => {
    const { nodes = [], edges = [], dataModelDescType } = originData.current
    if (nodes.length === 0) {
      void message.error('请选择主表构建模型')
      return
    }
    if (nodes.length > 1 && edges.length === 0 && dataModelDescType !== 'STREAM_BATCH') {
      void message.error('请构建ER模型的join关系')
      return
    }
    if (etlPaintInfo.createNodeStepShowOpen) {
      okHandle()
    } else {
      setShow(true)
    }
  }

  const closeHandle = () => {
    form.resetFields()
    setShow(false)
  }

  const okHandle = async () => {
    const res = etlPaintInfo.createNodeStepShowOpen ? {} : await form.validateFields()
    let factTable = ''
    let unionDagDescUnionType = ''
    const vertices = originData.current.nodes.map((d: any) => {
      const item = d.data
      if (item.unionType) {
        // STREAM_BATCH把TableConfigModel传递出来的unionType放在node里面，需要提取出来给后面请求使用
        unionDagDescUnionType = item.unionType
      }
      if (item.isFact) {
        factTable = `${item.catalogName}.${item.databaseName}.${item.name}`
      }
      return {
        dummy: false,
        id: d.id,
        table: `${item.catalogName}.${item.databaseName}.${item.name}`,
        kind: item.isFact ? 'FACT' : 'DIM',
        alias: item.isFact ? 't1' : `t${item.tableIndex}`,
        timeColumn: item.timeColumn,
        dimensionsColumns: item.columns.map((col) => ({
          name: `${item.catalogName}.${item.databaseName}.${item.name}.${col.name}`,
          vertexId: d.id,
        })),
        tableType: item?.tableEngine === 'Kafka' ? 'STREAM' : item?.computeType || 'BATCH',
      }
    })

    const body = {
      catalogName: res.catalog,
      databaseName: res.database,
      name: res.name,
      comment: res.comment,
      virtualTableCreateType: 'JOIN',
      dataModelDesc: {
        dataModelDescType: originData.current.dataModelDescType,
        catalog: res.catalog,
        modelName: res.name,
        measures: [],
        factTable,
        extraInfoDesc: {
          selectColumns: selectColumns,
        },
      },
    }

    if (originData.current.dataModelDescType === 'STREAM_BATCH') {
      body.dataModelDesc.unionDagDesc = { vertices }
      body.dataModelDesc.unionDagDesc.unionType = unionDagDescUnionType
      if (unionDagDescUnionType === 'UNION_UPSERT') {
        body.dataModelDesc.modelType = 'UPDATE_MODEL'
      }
    } else {
      body.dataModelDesc.joinDag = {
        edges: formatEdges(originData.current.edges),
        vertices,
      }
    }

    const { timeLimitModalForm, filterData } = erFlowData
    const timeLimitModalFormData = timeLimitModalForm?.getFieldsValue()
    const changeTimeUnitToMs = ({ val, timeUnit }: { val: number; timeUnit: TimeLimitTimeOffsetTimeOptionValue }) => {
      switch (timeUnit) {
        case 'HOURS':
          return val * 1000 * 60 * 60
        case 'MINUTES':
          return val * 1000 * 60
        case 'SECONDS':
          return val * 1000
        default:
          return val
      }
    }
    if (body.dataModelDesc.joinDag?.edges?.length > 0) {
      body.dataModelDesc.joinDag.edges[0].joinConditions = { conditions: [] }
      if (timeLimitModalFormData?.['need-time-limit']) {
        const conditions = [
          {
            alias: 'timeFilter1',
            operator: timeLimitModalFormData[`time-limit1-logic-compare`],
            params: [
              {
                type: 'TIME_INTERVAL_EXPR',
                timeIntervalExprDesc: {
                  timeColumn: {
                    name: timeLimitModalFormData['time-limit1-input1-time-limit-select'].split('~')[0],
                    vertexId: timeLimitModalFormData['time-limit1-input1-time-limit-select'].split('~')[1],
                  },
                  ...(timeLimitModalFormData['time-limit1-input1-offset-select'] === 'NONE'
                    ? {}
                    : {
                        timeIntervalOperator: timeLimitModalFormData['time-limit1-input1-offset-select'],
                        intervalSize: changeTimeUnitToMs({
                          val: timeLimitModalFormData['time-limit1-input1-time-num'],
                          timeUnit: timeLimitModalFormData['time-limit1-input1-time-select'],
                        }),
                        timeUnit: 'MILLISECONDS',
                      }),
                },
              },
              {
                type: 'TIME_INTERVAL_EXPR',
                timeIntervalExprDesc: {
                  timeColumn: {
                    name: timeLimitModalFormData['time-limit1-input2-time-limit-select'].split('~')[0],
                    vertexId: timeLimitModalFormData['time-limit1-input2-time-limit-select'].split('~')[1],
                  },
                  ...(timeLimitModalFormData['time-limit1-input2-offset-select'] === 'NONE'
                    ? {}
                    : {
                        timeIntervalOperator: timeLimitModalFormData['time-limit1-input2-offset-select'],
                        intervalSize: changeTimeUnitToMs({
                          val: timeLimitModalFormData['time-limit1-input2-time-num'],
                          timeUnit: timeLimitModalFormData['time-limit1-input2-time-select'],
                        }),
                        timeUnit: 'MILLISECONDS',
                      }),
                },
              },
            ],
          },
        ]
        if (timeLimitModalFormData['need-add']) {
          conditions.push({
            alias: 'timeFilter2',
            operator: timeLimitModalFormData[`time-limit2-logic-compare`],
            params: [
              {
                type: 'TIME_INTERVAL_EXPR',
                timeIntervalExprDesc: {
                  timeColumn: {
                    name: timeLimitModalFormData['time-limit2-input1-time-limit-select'].split('~')[0],
                    vertexId: timeLimitModalFormData['time-limit2-input1-time-limit-select'].split('~')[1],
                  },
                  ...(timeLimitModalFormData['time-limit2-input1-offset-select'] === 'NONE'
                    ? {}
                    : {
                        timeIntervalOperator: timeLimitModalFormData['time-limit2-input1-offset-select'],
                        intervalSize: changeTimeUnitToMs({
                          val: timeLimitModalFormData['time-limit2-input1-time-num'],
                          timeUnit: timeLimitModalFormData['time-limit2-input1-time-select'],
                        }),
                        timeUnit: 'MILLISECONDS',
                      }),
                },
              },
              {
                type: 'TIME_INTERVAL_EXPR',
                timeIntervalExprDesc: {
                  timeColumn: {
                    name: timeLimitModalFormData['time-limit2-input2-time-limit-select'].split('~')[0],
                    vertexId: timeLimitModalFormData['time-limit2-input2-time-limit-select'].split('~')[1],
                  },
                  ...(timeLimitModalFormData['time-limit2-input2-offset-select'] === 'NONE'
                    ? {}
                    : {
                        timeIntervalOperator: timeLimitModalFormData['time-limit2-input2-offset-select'],
                        intervalSize: changeTimeUnitToMs({
                          val: timeLimitModalFormData['time-limit2-input2-time-num'],
                          timeUnit: timeLimitModalFormData['time-limit2-input2-time-select'],
                        }),
                        timeUnit: 'MILLISECONDS',
                      }),
                },
              },
            ],
          })
        }
        if (conditions) {
          body.dataModelDesc.joinDag.edges[0].joinConditions.conditions.push(...conditions)
        }
      }
      if (filterData?.conditions) {
        body.dataModelDesc.joinDag.edges[0].joinConditions.conditions.push(...filterData.conditions)
      }
      body.dataModelDesc.joinDag.edges[0].joinConditions.filterExpr = filterData?.filterExpr || ''
    }
    run({ table: body })
  }

  const formatEdges = (edges: any) => {
    if (!edges || edges.length === 0) {
      return []
    }
    const sourceTargetIds: string[] = []
    const list: any = []
    edges.forEach((item: any) => {
      const sourceTargetId = item.source.cell + '-' + item.target.cell
      const index = sourceTargetIds.indexOf(sourceTargetId)
      const sourceInfo = item.source.port.split('~')
      const targetInfo = item.target.port.split('~')
      const from = sourceInfo[2]
      const to = targetInfo[2]
      const foreignKey = {
        name: `${item?.sourceData?.catalogName}.${item?.sourceData?.databaseName}.${item?.sourceData?.name}.${sourceInfo[4]}`,
        vertexId: from,
      }
      const primaryKey = {
        name: `${item?.targetData?.catalogName}.${item?.targetData?.databaseName}.${item?.targetData?.name}.${targetInfo[4]}`,
        vertexId: to,
      }
      if (index < 0) {
        sourceTargetIds.push(sourceTargetId)
        list.push({
          foreignKeys: [foreignKey],
          from,
          to,
          id: `e${getUnitId()}`,
          joinType: item?.data?.joinType,
          primaryKeys: [primaryKey],
          properties: {},
        })
      } else {
        list[index].foreignKeys.push(foreignKey)
        list[index].primaryKeys.push(primaryKey)
      }
    })
    return list
  }

  return (
    <Flex vertical style={{ width: '100%', height: '100%' }}>
      <PageHeader
        title="创建业务虚拟表"
        onBack={
          etlPaintInfo.createNodeStepShowOpen
            ? undefined
            : () => navigate(routerMap.dataModel.businessVirtualTable.path)
        }
        extra={
          <Button type="primary" onClick={submit}>
            创建
          </Button>
        }
      />
      <Flex flex={1}>
        <ErFlow
          onChange={changeHandle}
          onEtlPaintInfoChange={(info) => {
            setSelectColumns(info.selectColumns ?? '')
          }}
        />
      </Flex>
      <Modal
        open={show}
        okButtonProps={{
          loading,
        }}
        title={'选择业务虚拟表配置'}
        width={600}
        destroyOnClose
        onCancel={closeHandle}
        onOk={okHandle}
      >
        <Form layout="vertical" form={form}>
          <DBSelect span={24} required={{ catalog: true, database: true }} setFieldValue={form.setFieldValue} />
          <Form.Item
            label="虚拟表名称"
            name="name"
            required
            validateFirst
            rules={[
              {
                required: true,
                message: '请输入业务虚拟表名称',
              },
              {
                validator(_, value) {
                  return isAlphaNumberAndAlphaStart(value)
                    ? Promise.resolve('success')
                    : Promise.reject('需以数字字母下划线组成，并以字母开头')
                },
              },
            ]}
          >
            <Input placeholder={'请输入虚拟表的名称'} />
          </Form.Item>
          <Form.Item label="虚拟表描述" name="comment">
            <Input placeholder={'请输入虚拟表的描述'} />
          </Form.Item>
        </Form>
      </Modal>
    </Flex>
  )
}
