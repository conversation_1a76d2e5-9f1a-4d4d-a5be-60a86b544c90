import React, { useEffect, useState } from 'react'
import { PageHeader } from '@ant-design/pro-layout'
import { Button, message, Modal, Typography, Popover, Space, Switch, Card, Table, Empty } from 'antd'
import { useRequest } from 'ahooks'
import { Api } from '@api'
import { columns, deleteVTableBtn } from './forms-conf/form-items'
import { Broadcast } from '@libs'
import { getWinInnerSize } from '@libs/util'
import BatchUpload from '@model/batchUpload'
import { useSearchParams, useNavigate } from 'react-router-dom'
import CreateFilterTable from 'src/client/components/TableFilter/CreateFilterTable'
import { MoreOutlined } from '@ant-design/icons'
import { customAlphabet, nanoid } from 'nanoid'
// import CSVUpload from 'src/client/components/CSVUpload/CSVUpload'
import { type SelectColumnsCreateNewTablePropsType } from './components/SelectColumnsCreateNewTable' // TODO change import path
import { askBIApiUrls, askBIPageUrls } from 'src/shared/url-map'
import { VTableOperateActions as ACTIONS, VTableOperateButtons } from 'src/shared/constants'
import { customerFilterValue } from 'src/shared/customer-resolver'

import { EditorInDrawer } from '../../widget/model/sql/EditorInDrawer'
import { editor as EditorType } from 'monaco-editor'
import { VTable } from 'src/shared/common-types'
// import CustomerHiddenWrap from 'src/client/components/CustomerHiddenWrap'
import SelectColumnsCreateNewTableByTransfer from 'src/client/components/SelectColumnsCreateNewTableByTransfer'
import { useAtom, useAtomValue } from 'jotai'
import { isOpenTenantAtom } from 'src/client/pages/AskBI/askBIAtoms'
import { etlPaintInfoAtom } from '@atoms/er'
import DataMaskModal from 'src/client/components/DataMasking/DataMaskModal'
import { pick } from 'lodash-es'
import { CurrentVtableContext } from './data-model-context'
import MaterializeVirtualTableDrawer, {
  type MaterializeVirtualTableDrawerProps,
} from './components/MaterializeVirtualTableDrawer'
import LoadingText from 'src/client/components/LoadingText'
import { routerMap } from '@XEngineRouter/routerMap'
import axios from 'axios'
import { get } from 'lodash-es'
import CatalogDatabaseTree, { DatabaseItem, CatalogItem } from 'src/client/components/CatalogDatabaseTree'
import request from 'src/shared/xengine-axios'
import Search from 'antd/es/input/Search'
import styles from './virtual-table.module.scss'
import TimeColumnSettingModal from 'src/client/components/TimeColumnSettingModal'

const { Text } = Typography
const numericalID = customAlphabet('1234567890', 10)
const PageSize = 10

const VirtualTable = () => {
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const [editor, setEditor] = useState<EditorType.IStandaloneCodeEditor | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [currentTable, setCurrentTable] = useState<any>({})
  const [show, setShow] = useState<boolean>(false)
  const [xWidth, setXWidth] = useState('100%')
  const [currentVTableInfo, setCurrentVTableInfo] = useState<VTable>()
  const isOpenTenant = useAtomValue(isOpenTenantAtom)
  // const [CSVUploadOpen, setCSVUploadOpen] = useState(false)
  const [dbDataMap, setDbDataMap] = useState<Record<string, DatabaseItem[]>>({})
  const [tableListData, setTableListData] = useState<any[]>([])
  const [searchValue, setSearchValue] = useState('')
  const [searchTableValue, setSearchTableValue] = useState('')
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([])
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([])
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: PageSize,
    total: 0,
  })
  const [currentSelection, setCurrentSelection] = useState<{
    catalog: string
    database: string
  }>({
    catalog: '',
    database: '',
  })
  const [createFilterTableProps, setCreateFilterTableProps] = useState<{
    open: boolean
    tableData?: any
  }>({
    open: false,
  })
  const [dataMaskProps, setDataMaskProps] = useState<{
    open: boolean
    tableData: any
  }>({
    open: false,
    tableData: {},
  })
  const [timeColumnSettingProps, setTimeColumnSettingProps] = useState<{
    open: boolean
    tableData: any
  }>({
    open: false,
    tableData: {},
  })
  const [createCEPTableProps, setCreateCEPTableProps] = useState<{
    open: boolean
    tableData?: any
  }>({
    open: false,
  })
  const [selectColumnsCreateNewTableProps, setSelectColumnsCreateNewTableProps] = useState<
    Pick<SelectColumnsCreateNewTablePropsType, 'open' | 'originColumns' | 'currentTable' | 'computeType'>
  >({
    open: false,
    originColumns: [],
    currentTable: '',
    computeType: '',
  })
  const [etlPaintInfo, setEtlPaintInfo] = useAtom(etlPaintInfoAtom)
  const [materializeVirtualTableDrawerProps, setMaterializeVirtualTableDrawerProps] = useState<
    Partial<MaterializeVirtualTableDrawerProps>
  >({
    open: false,
  })

  const { runAsync: deleteVTable } = useRequest(Api.apiEngineV1VtableDeleteDelete, {
    manual: true,
    onSuccess: () => {
      getTableList(currentSelection.catalog, currentSelection.database, 1, PageSize)
    },
  })

  const { data: catalogData } = useRequest(() =>
    request
      .get<{}, CatalogItem[]>(askBIApiUrls.xengine.catalogList, { params: { current: 1, pageSize: -1 } })
      .then((res) => res.filter((item) => item.type === 'INTERNAL')),
  )

  useEffect(() => {
    if (catalogData && catalogData.length > 0) {
      catalogData.forEach((catalog) => {
        getDatabaseRun(catalog.name)
      })
    }
  }, [catalogData])

  const getDatabaseRun = async (catalog: string) => {
    try {
      const data = await request.get<{ catalog: string }, DatabaseItem[]>(askBIApiUrls.xengine.databaseList, {
        params: { catalog },
      })

      setDbDataMap((prev) => ({
        ...prev,
        [catalog]: data,
      }))
    } catch (e: any) {
      message.error(e?.message || '获取数据库失败')
    }
  }

  const { run: getTableList, loading: tableListLoading } = useRequest(
    async (catalog?: string, database?: string, page = 1, pageSize = PageSize, table?: string) => {
      return request.get(askBIApiUrls.xengine.VTable.search, {
        params: {
          catalog,
          database,
          current: page,
          pageSize,
          name: table,
          virtualTableType: 'LIKE',
        },
      })
    },
    {
      manual: true,
      onSuccess: (res: any) => {
        setTableListData(res?.list || [])
        setPagination((prev) => ({
          ...prev,
          total: res?.total || 0,
        }))
      },
      onError: (e: any) => {
        setTableListData([])
        setPagination((prev) => ({
          ...prev,
          total: 0,
        }))
        message.error(e?.message || '获取虚拟表列表失败')
      },
    },
  )

  const handleTableChange = (newPagination: any) => {
    setPagination((prev) => ({
      ...prev,
      current: newPagination.current,
      pageSize: newPagination.pageSize,
    }))
    getTableList(
      currentSelection.catalog || catalogData?.[0]?.name,
      currentSelection.database,
      newPagination.current,
      newPagination.pageSize,
      searchTableValue,
    )
  }

  const handleOk = () => {
    const { catalogName, databaseName, name } = currentTable
    setIsModalOpen(false)
    message.loading({
      content: `虚拟表正在删除，请稍候……`,
      key: 'table_delete_ing',
    })
    deleteVTable({
      catalog: catalogName,
      database: databaseName,
      name: name,
      cascadeDropLikeTable: true,
    })
      .then(() => {
        message.success({
          content: `虚拟表正在删除，请稍候……`,
          key: 'table_delete_ing',
        })
      })
      .catch(() => {
        message.loading({
          content: `虚拟表正在删除，请稍候……`,
          key: 'table_delete_ing',
          duration: 0.01,
        })
      })
      .finally(() => getTableList(currentSelection.catalog, currentSelection.database, 1, PageSize))
  }

  const handleCancel = () => {
    setIsModalOpen(false)
  }

  const setTableWidth = (width: number) => {
    if (width < 1330) {
      setXWidth('110%')
    } else {
      setXWidth('100%')
    }
  }

  const batchCreateHandle = () => {
    getTableList(currentSelection.catalog, currentSelection.database, 1, PageSize)
  }

  useEffect(() => {
    Broadcast.listen('@PAGE/DATAMODEL/VIRTUAL_TABLE', (record: any) => {
      setCurrentTable(record)
      setIsModalOpen(true)
    })

    setTableWidth(getWinInnerSize().innerWidth)

    // Window resize set table scroll x value
    window.addEventListener('resize', (e: any) => {
      setTableWidth(e?.target?.innerWidth)
    })
  }, [location.href])

  const [_drawerLabel, setDrawerLabel] = useState('')
  const [currentAction, setCurrentAction] = useState<keyof typeof ACTIONS>()
  // 抽象的操作按钮组件
  const handleAction = (record: VTable, actionType: keyof typeof ACTIONS) => {
    setCurrentAction(actionType)
    const id = nanoid()
    switch (actionType) {
      case ACTIONS.DELETE:
        deleteVTableBtn(record)
        break
      case ACTIONS.VISUAL_FILTER:
        setDrawerLabel(VTableOperateButtons.find((btn) => btn.action === actionType)?.label || '')
        setCreateFilterTableProps({
          open: true,
          tableData: { ...record, id: numericalID() },
        })
        break
      case ACTIONS.SQL_CLEANING:
        setDrawerLabel(VTableOperateButtons.find((btn) => btn.action === actionType)?.label || '')
        setCurrentVTableInfo(record)
        setSelectColumnsCreateNewTableProps({
          open: true,
          originColumns: (record?.columns || []).map((col: { name: string }) => ({
            id,
            key: id,
            type: 'COLUMN',
            columnName: col.name,
          })),
          currentTable: `${record.catalogName}.${record.databaseName}.${record.name}`,
          computeType: record.computeType,
        })
        break
      case ACTIONS.CREATE_VIRTUAL_TABLE:
        setDrawerLabel(VTableOperateButtons.find((btn) => btn.action === actionType)?.label || '')
        setCurrentVTableInfo(record)
        setSelectColumnsCreateNewTableProps({
          open: true,
          originColumns: (record?.columns || []).map((col: { name: string }) => ({
            id,
            key: id,
            type: 'COLUMN',
            columnName: col.name,
          })),
          currentTable: `${record.catalogName}.${record.databaseName}.${record.name}`,
          computeType: record.computeType,
        })
        break
      case ACTIONS.CREATE_CEP_TABLE:
        setDrawerLabel(VTableOperateButtons.find((btn) => btn.action === actionType)?.label || '')
        editor?.setValue?.('')
        setCreateCEPTableProps({
          open: true,
          tableData: { ...record, id },
        })
        break
      case ACTIONS.DATA_MASKING:
        setDrawerLabel(VTableOperateButtons.find((btn) => btn.action === actionType)?.label || '')
        setDataMaskProps({
          open: true,
          tableData: record,
        })
        break
      case ACTIONS.TIME_COLUMN_SETTING:
        setTimeColumnSettingProps({
          open: true,
          tableData: record,
        })
        break
      default:
        break
    }
  }

  const columnsFilter = customerFilterValue('materialViewListColumnsFilter', [
    ...columns,
    {
      title: '物化',
      dataIndex: 'settings',
      width: 120,
      render(settings: Record<string, string>, record: any) {
        const isMaterialize = 'materialized.view.name' in settings
        const lastTenantSelection = JSON.parse(localStorage.getItem('lastTenantSelection') || '{}')
        const tenantCatalogName = lastTenantSelection?.catalogName
        return (
          <div className="flex items-center">
            <LoadingText
              type="link"
              api={async () => {
                if (!tenantCatalogName && isOpenTenant) {
                  message.info('请先从页面左上角点击选择租户')
                } else {
                  if (isMaterialize) {
                    const [, , mvName] = get(record, ['settings', 'materialized.view.name'])?.split('.') || []
                    return axios.delete(`${askBIApiUrls.xengine.mv.dropMv}?mvName=${mvName}&displayName=${mvName}`)
                  }
                  setMaterializeVirtualTableDrawerProps({
                    open: true,
                    columns: record.columns,
                    catalog: record.catalogName,
                    database: record.databaseName,
                    table: record.name,
                  })
                }
              }}
              popconfirmProps={isMaterialize && { title: '确认删除物化视图？' }}
              onFail={(err) => message.error(err.message || '操作失败')}
              onSuccess={() =>
                tenantCatalogName && getTableList(currentSelection.catalog, currentSelection.database, 1, PageSize)
              }
            >
              <Switch size="small" checked={isMaterialize} className="mr-1" />
            </LoadingText>
            {isMaterialize && (
              <Typography.Link
                onClick={() => {
                  const [catalogName, dbName, mvName] = settings['materialized.view.name']?.split('.') || []
                  navigate(
                    `${routerMap.smartx.materialViewDetail.path}?${new URLSearchParams({
                      catalogName,
                      dbName,
                      mvName,
                    }).toString()}`,
                  )
                }}
              >
                物化视图
              </Typography.Link>
            )}
          </div>
        )
      },
    },
    {
      title: '操作',
      dataIndex: 'vtable-control',
      width: 60,
      fixed: 'right' as const,
      render: (_: any, record: any) => {
        if (etlPaintInfo.createNodeStepShowOpen) {
          return (
            <Typography.Link
              onClick={() =>
                setEtlPaintInfo((res) => ({
                  ...res,
                  virtualTable: pick(record, ['catalogName', 'databaseName', 'name', 'like']),
                  isReadyToCreate: true,
                  createNodeStepShowOpen: false,
                }))
              }
            >
              选择
            </Typography.Link>
          )
        }
        return (
          <Popover
            placement="bottomLeft"
            content={
              <div className="w-[145px]">
                {customerFilterValue('vTableOperateButtonsFilter', VTableOperateButtons).map((btn) => (
                  <Button
                    key={btn.action}
                    block
                    type="text"
                    danger={btn.danger}
                    onClick={() => handleAction(record, btn.action)}
                    icon={<btn.icon />}
                    className="flex items-center justify-start"
                    style={{ marginLeft: 0, paddingLeft: 2 }}
                  >
                    {btn.label}
                  </Button>
                ))}
              </div>
            }
          >
            <MoreOutlined className="cursor-pointer" />
          </Popover>
        )
      },
    },
  ])

  useEffect(() => {
    // 从 URL 获取参数
    const catalog = searchParams.get('catalog')
    const database = searchParams.get('database')

    if (catalog && database) {
      // 设置展开的节点
      setExpandedKeys([catalog])
      // 设置选中的节点
      setSelectedKeys([`${catalog}.${database}`])
      // 设置当前选择
      setCurrentSelection({ catalog, database })
      // 加载表格数据
      getTableList(catalog, database, 1, PageSize)
    }
  }, [searchParams])

  return (
    <div className="flex h-full w-full gap-4">
      <div className="mx-3 w-60 shrink-0">
        <CatalogDatabaseTree
          catalogData={catalogData}
          dbDataMap={dbDataMap}
          searchValue={searchValue}
          onSearchChange={setSearchValue}
          expandedKeys={expandedKeys}
          onExpandedKeysChange={setExpandedKeys}
          selectedKeys={selectedKeys}
          onSelectedKeysChange={setSelectedKeys}
          onSelectionChange={({ catalog, database }) => {
            setCurrentSelection({ catalog, database })
            setPagination((prev) => ({
              ...prev,
              current: 1,
            }))
            getTableList(catalog, database, 1, pagination.pageSize)
          }}
        />
      </div>
      <div className="flex w-full flex-col">
        <CurrentVtableContext.Provider value={currentVTableInfo}>
          <PageHeader
            className={`mb-4 py-0 ${styles.pageHeader}`}
            title={
              <>
                <Search
                  placeholder="搜索虚拟表"
                  allowClear
                  onSearch={(e) => {
                    setSearchTableValue(e)
                    getTableList(
                      currentSelection.catalog || catalogData?.[0]?.name,
                      currentSelection.database,
                      1,
                      PageSize,
                      e,
                    )
                  }}
                />
              </>
            }
            extra={
              <Space>
                <Button type="primary" onClick={() => setShow(true)}>
                  批量创建贴源虚拟表
                </Button>
                {/* <CustomerHiddenWrap type="CSVCreateTableHide">
              <Button type="primary" onClick={() => setCSVUploadOpen(true)}>
                上传csv文件
              </Button>
            </CustomerHiddenWrap> */}
              </Space>
            }
          />
          <EditorInDrawer
            virtualTableCreateType="SQL_CREATE"
            tableData={createCEPTableProps?.tableData}
            onClose={() => {
              setCreateCEPTableProps((pre) => ({ ...pre, open: false }))
            }}
            onOk={() => {
              setCreateCEPTableProps((pre) => ({ ...pre, open: false }))
            }}
            showFormatBtn
            editor={editor}
            setEditor={setEditor}
            title={'SQL创建虚拟表'}
            open={createCEPTableProps.open}
          ></EditorInDrawer>
          <Card>
            <Table
              columns={columnsFilter}
              rowKey="id"
              scroll={{ x: xWidth }}
              dataSource={tableListData}
              loading={tableListLoading}
              pagination={{
                ...pagination,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条`,
              }}
              locale={{
                emptyText: <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="该数据库下未获取到数据" />,
              }}  
              onChange={handleTableChange}
            />
          </Card>
          <Modal
            title={'删除虚拟表'}
            open={isModalOpen}
            onOk={handleOk}
            onCancel={handleCancel}
            okText={'确认删除'}
            cancelText={'取消'}
          >
            <p>
              确认删除 <Text type="danger">{currentTable?.name}</Text> 表吗？
            </p>
          </Modal>
          <BatchUpload
            title="批量创建贴源虚拟表"
            sampleType="likeVT"
            open={show}
            onClose={() => setShow(false)}
            uploadUrl={askBIApiUrls.xengine.VTable.createBatchVT}
            onConfirm={batchCreateHandle}
          />
          <CreateFilterTable
            {...createFilterTableProps}
            onClose={() =>
              setCreateFilterTableProps({
                open: false,
              })
            }
          />
          {/* <CSVUpload
        open={CSVUploadOpen}
        setOpen={setCSVUploadOpen}
        viewDataAfterSuccessUpload={() => {
          setSearchParams({
            catalog: 'dipeak',
            database: 'csv',
          })
          setReload((reload) => !reload)
          setCSVUploadOpen(false)
        }}
      /> */}
          <SelectColumnsCreateNewTableByTransfer
            {...selectColumnsCreateNewTableProps}
            onSuccess={() => navigate(askBIPageUrls.manage.xengine.businessDataModel)}
            mode={currentAction === ACTIONS.CREATE_VIRTUAL_TABLE ? ACTIONS.CREATE_VIRTUAL_TABLE : ACTIONS.SQL_CLEANING}
            closeController={() =>
              setSelectColumnsCreateNewTableProps((pre) => ({
                ...pre,
                open: false,
              }))
            }
          />
          <DataMaskModal
            open={dataMaskProps.open}
            tableData={dataMaskProps.tableData}
            onCancel={() => setDataMaskProps((pre) => ({ ...pre, open: false }))}
          />
          <TimeColumnSettingModal
            open={timeColumnSettingProps.open}
            tableData={timeColumnSettingProps.tableData}
            onCancel={() => setTimeColumnSettingProps((pre) => ({ ...pre, open: false }))}
          />
          {/* <SelectColumnsCreateNewTable
        {...selectColumnsCreateNewTableProps}
        onSuccess={() => navigate(askBIPageUrls.manage.xengine.businessDataModel)}
        mode={currentAction === ACTIONS.CREATE_VIRTUAL_TABLE ? ACTIONS.CREATE_VIRTUAL_TABLE : ACTIONS.SQL_CLEANING}
        closeController={() =>
          setSelectColumnsCreateNewTableProps((pre) => ({
            ...pre,
            open: false,
          }))
        }
      /> */}
          <MaterializeVirtualTableDrawer
            open={Boolean(materializeVirtualTableDrawerProps.open)}
            columns={materializeVirtualTableDrawerProps.columns}
            catalog={materializeVirtualTableDrawerProps.catalog}
            database={materializeVirtualTableDrawerProps.database}
            table={materializeVirtualTableDrawerProps.table}
            closeController={() => setMaterializeVirtualTableDrawerProps({ open: false })}
            onSuccess={() => getTableList(currentSelection.catalog, currentSelection.database, 1, PageSize)}
          />
        </CurrentVtableContext.Provider>
      </div>
    </div>
  )
}

export default VirtualTable
