export interface Request {
  /**
   * 虚拟表目录
   */
  catalog: string
  /**
   * 虚拟表数据库
   */
  database: string
  /**
   * 发布参数
   */
  dataModel: DataModelForStreamVirtualTable
  /**
   * 是否发布，前端写死为true，false为后端测试使用
   */
  publishMv: boolean
  /**
   * 虚拟表名称
   */
  table: string
  [property: string]: any
}

/**
 * 发布参数
 *
 * DataModelForStreamVirtualTable
 */
export interface DataModelForStreamVirtualTable {
  dataProcessDesc: DataProcessDesc | {}
  outputDesc: OutputDesc | {}
  resourceDesc: ResourceDesc | {}
  streamingDesc: StreamingDesc | {}
  [property: string]: any
}

/**
 * DataProcessDesc
 */
export interface DataProcessDesc {
  strategyList?: StrategyList[]
  /**
   * 虚拟表可视化数据处理配置，2024-04-28新增
   */
  virtualTableProcessDesc?: VirtualTableProcessDesc
  /**
   * 虚拟表SQL数据处理配置，2024-04-28新增
   */
  virtualTableSqlDesc?: VirtualTableSQLDesc
  [property: string]: any
}

export interface StrategyList {
  primaryKeys: ColumnDesc[]
  strategyType: StrategyType
  version: ColumnDesc
  [property: string]: any
}

/**
 * ColumnDesc
 */
export interface ColumnDesc {
  alias?: string
  /**
   * 字段描述
   */
  comment?: string
  /**
   * 字段名称，database.table.column
   */
  name: string
  /**
   * 字段类型
   */
  type?: string
  vertexId: string
  [property: string]: any
}

export enum StrategyType {
  Update = 'UPDATE',
}

/**
 * 虚拟表可视化数据处理配置，2024-04-28新增
 *
 * VirtualTableProcessDesc
 */
export interface VirtualTableProcessDesc {
  cases: CaseWhenDesc[]
  expressions: ExpressionDesc[]
  filterDesc: string
  type: string
  [property: string]: any
}

/**
 * CaseWhenDesc
 */
export interface CaseWhenDesc {
  caseValue: string
  whenFilter: VirtualTableFilter
  [property: string]: any
}

/**
 * VirtualTableFilter
 */
export interface VirtualTableFilter {
  column: ColumnDesc
  condition: AtomicCondition
  [property: string]: any
}

/**
 * AtomicCondition
 */
export interface AtomicCondition {
  operator: Operator
  params: ParameterDesc[]
  [property: string]: any
}

export enum Operator {
  Between = 'BETWEEN',
  Eq = 'EQ',
  Ge = 'GE',
  Gt = 'GT',
  In = 'IN',
  Is = 'IS',
  LE = 'LE',
  Like = 'LIKE',
  Lt = 'LT',
  Neq = 'NEQ',
  NotIn = 'NOT_IN',
}

/**
 * ParameterDesc
 */
export interface ParameterDesc {
  function: FunctionDesc
  type: Type
  valueType: FluffyValueType
  vertexId: string
  [property: string]: any
}

/**
 * 条件对象
 */
export interface FluffyCondition {
  /**
   * AtomicCondition或者CombinedCondition
   */
  condition: Condition
  functionDesc: FunctionDesc
  [property: string]: any
}

/**
 * ParamDesc
 */
export interface ParamDesc {
  /**
   * 条件对象
   */
  condition?: FluffyCondition
  function?: { [key: string]: any }
  type: Type
  /**
   * 如果type是COLUMN，value为字段值；如果type是CONSTANT，value为常量
   */
  value: string
  valueType?: PurpleValueType
  /**
   * 主要是ER模型使用
   */
  vertexId?: string
  [property: string]: any
}

/**
 * AtomicCondition或者CombinedCondition
 *
 * Condition
 */
export interface Condition {
  conditions?: { [key: string]: any }[]
  operator: string
  params?: ParamDesc[]
  [property: string]: any
}

/**
 * 条件对象
 */
export interface PurpleCondition {
  /**
   * AtomicCondition或者CombinedCondition
   */
  condition: Condition
  functionDesc: FunctionDesc
  [property: string]: any
}

export interface Param {
  /**
   * 条件对象
   */
  condition?: PurpleCondition
  function?: { [key: string]: any }
  type: Type
  /**
   * 如果type是COLUMN，value为字段值；如果type是CONSTANT，value为常量
   */
  value: string
  valueType?: PurpleValueType
  /**
   * 主要是ER模型使用
   */
  vertexId?: string
  [property: string]: any
}

/**
 * FunctionDesc
 */
export interface FunctionDesc {
  function: Function
  params: Param[]
  [property: string]: any
}

export enum Type {
  Column = 'COLUMN',
  ConditionValue = 'CONDITION_VALUE',
  Constant = 'CONSTANT',
  Function = 'FUNCTION',
}

export enum PurpleValueType {
  String = 'STRING',
}

export enum Function {
  Avg = 'AVG',
  CaseWhen = 'CASE_WHEN',
  Count = 'COUNT',
  CountDistinct = 'COUNT_DISTINCT',
  Date = 'DATE',
  Left = 'LEFT',
  Max = 'MAX',
  Min = 'MIN',
  SubString = 'SUB_STRING',
  Sum = 'SUM',
}

export enum FluffyValueType {
  Boolean = 'BOOLEAN',
  Date = 'DATE',
  Numeric = 'NUMERIC',
  String = 'STRING',
}

/**
 * ExpressionDesc
 */
export interface ExpressionDesc {
  column: ColumnDesc
  expression: string
  unit: string
  [property: string]: any
}

/**
 * 虚拟表SQL数据处理配置，2024-04-28新增
 *
 * VirtualTableSqlDesc
 */
export interface VirtualTableSQLDesc {
  sql: string
  [property: string]: any
}

/**
 * OutputDesc
 */
export interface OutputDesc {
  /**
   * Kafka 专用
   */
  brokers?: string
  /**
   * AskDI 专用
   */
  catalog: string
  /**
   * AskDI 专用
   */
  database: string
  /**
   * Kafka 专用
   */
  outputFormat: string
  /**
   * 计算结果输出目标
   */
  outputTarget: OutputTarget
  /**
   * AskDI 专用
   */
  table: string
  /**
   * Kafka 专用
   */
  topic: string
  [property: string]: any
}

/**
 * 计算结果输出目标
 */
export enum OutputTarget {
  Askdi = 'ASKDI',
  Kafka = 'KAFKA',
}

/**
 * ResourceDesc
 */
export interface ResourceDesc {
  /**
   * Core
   */
  cpu: number
  /**
   * GB
   */
  disk: number
  /**
   * MB
   */
  memory: number
  [property: string]: any
}

/**
 * StreamingDesc
 */
export interface StreamingDesc {
  /**
   * EARLIEST,LATEST,SPECIFIC
   */
  scanStartupMode?: string
  specificTime?: number
  timeBoundary: number
  /**
   * STREAM_FIRST,BATCH_FIRST,SPECIFIC
   */
  timeBoundaryStrategy?: string
  [property: string]: any
}
