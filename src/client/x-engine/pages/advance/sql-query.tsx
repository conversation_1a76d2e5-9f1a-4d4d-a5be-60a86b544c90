// @ts-nocheck
import { Card, Select, Button, Table, Form, Modal } from 'antd'
import { useAntdTable, useRequest } from 'ahooks'
import { Api } from '@api'
import React, { useEffect, useRef, useState } from 'react'

import editorWorker from 'monaco-editor/esm/vs/editor/editor.worker?worker'
self.MonacoEnvironment = {
  getWorker() {
    return new editorWorker()
  },
}
import { PageHeader } from '@ant-design/pro-layout'
import { Editor } from '../../widget/model/sql/Editor'

function Home() {
  const { run: killQuery } = useRequest(Api.apiEngineV1SqlKillQueryPost, {
    manual: true,
    onSuccess: () => {
      return
    },
    onError: () => {
      return
    },
  })

  const [editorValue, setEditorValue] = useState('')
  const columns = [
    {
      title: '查询ID',
      dataIndex: 'queryId',
      width: 230,
    },
    {
      width: 230,
      title: '查询 SQL 脚本',
      dataIndex: 'sqlStatement',
      render: (data) => {
        return (
          <Button
            onClick={() => {
              setEditorValue(data)
              setOpen(true)
            }}
            type={'link'}
          >
            查看 SQL 脚本详情
          </Button>
        )
      },
    },
    {
      title: '开始时间',
      dataIndex: 'startTime',
    },
    {
      width: 160,
      title: '操作',
      render(record) {
        return (
          <Button
            onClick={() => {
              killQuery({ queryId: record.queryId })
            }}
            danger
          >
            KILL
          </Button>
        )
      },
    },
  ]

  const [editor, setEditor] = useState<monaco.editor.IStandaloneCodeEditor | null>(null)
  const [open, setOpen] = useState(false)
  const savedTableData = useRef({})
  const { tableProps, run: loadTable } = useAntdTable(
    (arg) => {
      const recentCount = form.getFieldValue('recentCount')
      if (arg?.action !== 'refreshTable') {
        return Promise.resolve(savedTableData.current.slice(0, recentCount))
      }
      return Api.apiEngineV1SqlGetBigQueryListGet(arg).then((data) => {
        data.list = data.list.slice(0, recentCount)
        return data
      })
    },
    {
      manual: true,
      onSuccess: (data) => {
        savedTableData.current = data
      },
      defaultCurrent: 1,
      defaultPageSize: 10,
    },
  )
  useEffect(() => {
    if (editor && open) {
      editor.setValue(editorValue)
    }
  }, [editorValue, editor, open])

  useEffect(() => {
    loadTable({ action: 'refreshTable' })
  }, [])
  const [form] = Form.useForm()

  return (
    <>
      <Modal
        title="SQL 详情"
        open={open}
        okText="确定"
        width={600}
        confirmLoading={false}
        onCancel={() => {
          setOpen(false)
        }}
        cancelButtonProps={{ style: { display: 'none' } }}
        onOk={() => {
          setOpen(false)
        }}
      >
        <Editor showFormatBtn={true} showRunBtn={false} editor={editor} setEditor={setEditor} />
      </Modal>
      <PageHeader title="实时查询状态" />
      <Card>
        <Form
          initialValues={{ recentCount: 50 }}
          layout="inline"
          style={{ margin: '16px 0 32px 0' }}
          onFinish={() => {
            return loadTable({ action: 'refreshTable' })
          }}
          form={form}
        >
          <Form.Item name="recentCount" label={'最近 SQL 条数'}>
            <Select
              placeholder="请选择"
              allowClear
              onChange={() => {
                form.submit()
              }}
              style={{ width: 128 }}
              options={
                [
                  {
                    value: 50,
                    label: '50',
                  },
                  {
                    value: 100,
                    label: '100',
                  },
                  {
                    value: 150,
                    label: '150',
                  },
                ] as { label: string; value: number }[]
              }
            />
          </Form.Item>
          <Form.Item>
            <Button
              loading={tableProps.loading}
              type="primary"
              htmlType="submit"
              onClick={() => loadTable({ action: 'refreshTable' })}
            >
              查询
            </Button>
          </Form.Item>
        </Form>

        <Table
          rowKey={'queryId'}
          columns={columns}
          {...tableProps}
          onChange={(arg) => {
            loadTable({ ...arg, action: 'changePage' })
          }}
        />
      </Card>
    </>
  )
}

export default Home
