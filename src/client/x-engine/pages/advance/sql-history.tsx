import { Search } from '@ui/form'
import Broadcast from '@libs/broadcast'
import { tableColumns, searchItems, SQLDetailDescItems } from './SQLHistory'
import React, { useEffect, useState, useRef } from 'react'
import {
  Button,
  Collapse,
  Descriptions,
  App,
  Modal,
  Popover,
  Table,
  Typography,
  Space,
  Empty,
  type TableProps,
} from 'antd'
import { PageHeader } from '@ant-design/pro-layout'
import dayjs, { type Dayjs } from 'dayjs'
import { pickBy, omit } from 'lodash'
import { askBIApiUrls } from 'src/shared/url-map'
import request from 'src/shared/xengine-axios'
import GreyFooterModal from '@components/GreyFooterModel'
import { SvgIcon, warningIcon } from 'src/client/components/SvgIcon'
import { useBoolean, useRequest } from 'ahooks'
import { MoreOutlined } from '@ant-design/icons'

type ExecutionInfo = {
  name: string // 名称
  jobs: number // 任务数
  maxExecutionTime: number // 最大执行时间
  maxPreparationTime: number // 最大准备时间
  inputsRows: number // 输入行数
  inputsBytes: number // 输入字节
  outputsRows: number // 输出行数
  outputsBytes: number // 输出字节
}
type SQLExplainDataType = {
  stageId: string
  parallelism: number
  taskInfos: { executionInfos: ExecutionInfo[] }[]
  subStageInfo: SQLExplainDataType[] | null
}

type TableSearchType = {
  queryTime: [Dayjs, Dayjs]
}

type SQLHistoryTableDataType = {
  queryId: string
  relatedTables: string[]
  result: 'success' | 'failed'
  sysCost: string
  cpuCost: string
  userName: string
  startTime: string
  endTime: string
}

const SQL_QUERY_TIME_SESSION_KEY = `sql_history_query_time`

const descriptionBox = (record: Record<string, string>) => {
  Broadcast.trigger('@page-sql-history', record)
}

const SQLTimeCostColumns = [
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '层级',
    dataIndex: 'locate',
    key: 'locate',
  },
  {
    title: '任务数',
    dataIndex: 'jobs',
    key: 'jobs',
  },
  {
    title: '最大执行时间',
    dataIndex: 'maxExecutionTime',
    key: 'maxExecutionTime',
    render(time: number) {
      return time ? `${time} ms` : '-'
    },
  },
  {
    title: '最大准备时间',
    dataIndex: 'maxPreparationTime',
    key: 'maxPreparationTime',
    render(time: number) {
      return time ? `${time} ms` : '-'
    },
  },
  {
    title: '输入行数',
    dataIndex: 'inputsRows',
    key: 'inputsRows',
  },
  {
    title: '输入字节',
    dataIndex: 'inputsBytes',
    key: 'inputsBytes',
  },
  {
    title: '输出行数',
    dataIndex: 'outputsRows',
    key: 'outputsRows',
  },
  {
    title: '输出字节',
    dataIndex: 'outputsBytes',
    key: 'outputsBytes',
  },
]

// 分阶段查询耗时步骤map
const TIME_COST_STEPS = {
  CONFIRM: 0,
  RESULT: 1,
} as const

const TIME_COST_STEPS_MODEL_WIDTH = {
  [TIME_COST_STEPS['CONFIRM']]: 600,
  [TIME_COST_STEPS['RESULT']]: '80%',
}

type TIME_COST_STEPS_VALUE_TYPE = (typeof TIME_COST_STEPS)[keyof typeof TIME_COST_STEPS]

function SQLExplainDataShow({
  data,
  defaultOpenFirst,
}: {
  data: SQLExplainDataType[]
  defaultOpenFirst?: boolean
  parentLevel?: string
}) {
  const labelItems = [
    {
      key: 'stageId',
      label: '阶段查询ID：',
    },
    {
      key: 'parallelism',
      label: '并发数：',
    },
  ]
  const items = data?.map((d) => {
    return {
      key: d.stageId,
      label: (
        <Space size="large">
          {labelItems.map((i) => (
            <div key={i.key}>
              <Typography.Text strong>{i.label}</Typography.Text>
              {(d[i.key as keyof typeof d] as string) || '-'}
            </div>
          ))}
        </Space>
      ),
      children: (
        <>
          {d.taskInfos?.map((task, index) => (
            <React.Fragment key={index}>
              <Typography.Text type="secondary" mark italic className="inline-block [.peer+&]:mt-2">
                Task{index + 1}：
              </Typography.Text>
              <Table
                columns={SQLTimeCostColumns}
                dataSource={task?.executionInfos}
                pagination={false}
                size="small"
                className="peer"
              />
            </React.Fragment>
          ))}
          {d.subStageInfo?.length && <SQLExplainDataShow data={d.subStageInfo} />}
        </>
      ),
    }
  })
  const defaultActiveKey = [defaultOpenFirst ? items[0].key : ''].filter(Boolean)

  return (
    <>
      {items?.length ? (
        <Collapse items={items} bordered={false} defaultActiveKey={defaultActiveKey} />
      ) : (
        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
      )}
    </>
  )
}

const parseQueryTimeSession = () => {
  try {
    const queryTimeSession = sessionStorage.getItem(SQL_QUERY_TIME_SESSION_KEY)
    const initQueryTimeArr = queryTimeSession ? JSON.parse(queryTimeSession) : null
    return Array.isArray(initQueryTimeArr) && initQueryTimeArr.length === 2 && initQueryTimeArr.map(dayjs)
  } catch {
    return null
  }
}

const SQLHistory = () => {
  const { message } = App.useApp()
  const [infoModal, setInfoModal] = useState(false)
  const [descItemsInfo, setDescItemsInfo] = useState<Record<string, string>>({})
  const [SQLTimeCostModalOpen, SQLTimeCostModalOpenOps] = useBoolean(false)
  const [currentSQLTimeCostStep, setCurrentSQLTimeCostStep] = useState<TIME_COST_STEPS_VALUE_TYPE>(
    TIME_COST_STEPS['CONFIRM'],
  )
  const currentSQL = useRef('')

  const {
    loading: SQLTimeCostLoading,
    run: explainSQLTimeCost,
    data: SQLExplainData,
    cancel: SQLExplainCancel,
  } = useRequest(
    () =>
      request.post<unknown, SQLExplainDataType>(askBIApiUrls.xengine.sql.explain, {
        sql: currentSQL.current,
      }),
    {
      manual: true,
      onSuccess() {
        message.success('查询分阶段耗时成功')
        setCurrentSQLTimeCostStep(TIME_COST_STEPS['RESULT'])
      },
      onError(err) {
        message.error(err.message || '查询分阶段耗时失败')
      },
    },
  )

  const setInfoModalStatus = (type: boolean) => {
    setInfoModal(type)
  }

  useEffect(() => {
    Broadcast.listen('@page-sql-history', (data: Record<string, string>) => {
      setDescItemsInfo(data)
      setInfoModal(true)
    })
  }, [location])

  const handleTimeCostModalOk = () => {
    if (currentSQLTimeCostStep === TIME_COST_STEPS['CONFIRM']) {
      explainSQLTimeCost()
    }
    if (currentSQLTimeCostStep === TIME_COST_STEPS['RESULT']) {
      SQLTimeCostModalOpenOps.setFalse()
    }
  }

  const renderSQLTimeCostCtx = () => {
    if (currentSQLTimeCostStep === TIME_COST_STEPS['CONFIRM']) {
      return (
        <>
          <div className="my-4 flex items-center">
            <SvgIcon icon={warningIcon} className="mr-2 h-6 w-6" />
            <span>查询SQL分阶段耗时需要重新对SQL发起查询，会消耗系统资源，请谨慎操作</span>
          </div>
          <p className="ml-8">若确定进行重跑SQL查询分段阶段耗时，请点击下方按钮</p>
        </>
      )
    }
    if (currentSQLTimeCostStep === TIME_COST_STEPS['RESULT']) {
      return <SQLExplainDataShow data={SQLExplainData ? [SQLExplainData] : []} defaultOpenFirst />
    }
  }

  return (
    <>
      <PageHeader title="SQL 执行历史" />
      <Search
        key={'p-sql-history'}
        items={searchItems}
        table={{
          columns: tableColumns.concat([
            {
              title: '操作',
              width: 70,
              render: (record) => {
                return (
                  <Popover
                    placement="top"
                    content={
                      <div className="w-[80px]">
                        <Button type="text" block onClick={() => descriptionBox(record)}>
                          查看详情
                        </Button>
                        {record.type === 'DQL' && (
                          <Button
                            type="text"
                            block
                            onClick={() => {
                              currentSQL.current = record.originalSql
                              setCurrentSQLTimeCostStep(TIME_COST_STEPS['CONFIRM'])
                              SQLTimeCostModalOpenOps.setTrue()
                            }}
                          >
                            分阶段耗时
                          </Button>
                        )}
                      </div>
                    }
                  >
                    <MoreOutlined className="cursor-pointer" />
                  </Popover>
                )
              },
            },
          ] as Exclude<TableProps['columns'], undefined>),
          rowKey: 'queryId',
          api: (params: TableSearchType) => {
            const queryTimeStartValue = params.queryTime[0].valueOf()
            const queryTimeEndValue = params.queryTime[1].valueOf()
            const queryParams = {
              ...omit(params, ['queryTime']),
              queryTimeStart: queryTimeStartValue,
              queryTimeEnd: queryTimeEndValue,
            }
            sessionStorage.setItem(SQL_QUERY_TIME_SESSION_KEY, JSON.stringify([queryTimeStartValue, queryTimeEndValue]))
            return request.get<unknown, { list: SQLHistoryTableDataType[]; total: number }>(
              askBIApiUrls.xengine.sql.audit,
              {
                params: pickBy(queryParams, Boolean),
              },
            )
          },
        }}
        initialValues={{
          queryTime: parseQueryTimeSession() || [dayjs().subtract(1, 'day'), dayjs()],
        }}
      />
      <Modal
        title="SQL 详情"
        open={infoModal}
        width={'80%'}
        onOk={() => setInfoModalStatus(false)}
        onCancel={() => setInfoModalStatus(false)}
      >
        <Descriptions bordered={true} className="max-h-[600px] overflow-y-auto">
          {SQLDetailDescItems.map((item) => {
            return (
              <Descriptions.Item label={item.label} key={item.key} span={item.span}>
                {item.render ? item.render(descItemsInfo[item.key]) : descItemsInfo[item.key] || '-'}
              </Descriptions.Item>
            )
          })}
        </Descriptions>
      </Modal>
      <GreyFooterModal
        title="分阶段耗时"
        open={SQLTimeCostModalOpen}
        width={TIME_COST_STEPS_MODEL_WIDTH[currentSQLTimeCostStep]}
        okButtonProps={{
          loading: SQLTimeCostLoading,
        }}
        maskClosable={false}
        onCancel={() => {
          if (SQLTimeCostLoading) {
            Modal.confirm({
              content: '正在重跑SQL查询分段阶段耗时，确定取消重跑，关闭弹框吗?',
              onOk() {
                SQLExplainCancel()
                SQLTimeCostModalOpenOps.setFalse()
              },
            })
          } else {
            SQLTimeCostModalOpenOps.setFalse()
          }
        }}
        onOk={handleTimeCostModalOk}
        okText={currentSQLTimeCostStep === TIME_COST_STEPS['CONFIRM'] ? '重跑SQL查询分段阶段耗时' : '确定'}
        {...(currentSQLTimeCostStep === TIME_COST_STEPS['RESULT'] ? { footer: null } : {})}
      >
        <div className="overflow-auto px-5 pb-4 pt-2">{renderSQLTimeCostCtx()}</div>
      </GreyFooterModal>
    </>
  )
}

export default SQLHistory
