import React from 'react'
import { Tag, Tooltip, type TableProps } from 'antd'
import dayjs from 'dayjs'
import { QuestionCircleOutlined } from '@ant-design/icons'

const tableColumns = [
  {
    title: 'ID',
    dataIndex: 'queryId',
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 60,
    render: (text: string) => {
      return text?.toLocaleLowerCase() === 'success' ? <Tag color="green">成功</Tag> : <Tag color="red">失败</Tag>
    },
  },
  {
    title: '类型',
    dataIndex: 'type',
    width: 80,
  },

  {
    title: '查询耗时',
    dataIndex: 'timeCost',
    width: 120,
    render(timeCost: number) {
      return timeCost ? `${timeCost}ms` : '-'
    },
  },
  {
    title: (
      <span>
        CPU
        <Tooltip title="单位为 micro" className="ml-1 cursor-pointer font-normal">
          <QuestionCircleOutlined />
        </Tooltip>
      </span>
    ),
    dataIndex: 'cpuCost',
  },
  {
    title: '来源',
    dataIndex: 'userSource',
    width: 70,
  },
  {
    title: '用户',
    dataIndex: 'username',
    render: (text: string) => (text == 'null' ? '-' : text),
  },
  {
    title: '开始时间',
    dataIndex: 'startTime',
    width: 160,
    render: (startTime: string) => {
      return <Tag>{startTime ? dayjs(startTime).format('YYYY-MM-DD HH:mm:ss') : '-'}</Tag>
    },
  },
] as Exclude<TableProps['columns'], undefined>

const searchItems = [
  {
    label: '关键字',
    tag: 'Input',
    name: 'keyWord',
    allowClear: true,
  },
  {
    label: '状态',
    tag: 'Select',
    name: 'status',
    allowClear: true,
    options: [
      {
        label: '成功',
        value: 'SUCCESS',
      },
      {
        label: '失败',
        value: 'FAILED',
      },
    ],
  },
  {
    tag: 'Input',
    label: '用户',
    name: 'username',
    allowClear: true,
  },
  {
    tag: 'Input',
    label: '耗时大于',
    name: 'timeCost',
    placeholder: '100，以毫秒为单位',
    allowClear: true,
  },
  {
    showTime: true,
    label: '查询时间',
    tag: 'RangePicker',
    name: 'queryTime',
    format: 'YYYY-MM-DD HH:mm:ss',
    rules: [{ required: true }],
  },
  {
    tag: 'Select',
    label: '查询类型',
    name: 'type',
    allowClear: true,
    options: [
      {
        label: 'DML',
        value: 'DML',
      },
      {
        label: 'DDL',
        value: 'DDL',
      },
      {
        label: 'DQL',
        value: 'DQL',
      },
      {
        label: 'DCL',
        value: 'DCL',
      },
    ],
  },
  {
    tag: 'Select',
    label: '来源',
    name: 'source',
    allowClear: true,
    options: [
      {
        label: 'JDBC',
        value: 'JDBC',
      },
      {
        label: 'WEB',
        value: 'WEB',
      },
    ],
  },
  {
    tag: 'Input',
    label: '用户IP',
    name: 'userIp',
    allowClear: true,
    placeholder: '127.0.0.1',
  },
  {
    tag: 'Input',
    label: '错误信息',
    name: 'errMsg',
    allowClear: true,
  },
  {
    tag: 'InputNumber',
    label: '影响行数',
    name: 'rowNum',
    placeholder: '0',
    allowClear: true,
    tooltip: '影响行数大于所填写的行数',
  },
  {
    tag: 'buttons',
    wrapperCol: { span: 16 },
    children: [
      {
        tag: 'Button',
        name: 'reset',
        label: '重置',
        htmlType: 'button',
      },
      {
        tag: 'Button',
        name: 'submit',
        label: '查询',
        type: 'primary',
        htmlType: 'submit',
      },
    ],
  },
]

export const SQLDetailDescItems = [
  {
    label: '查询ID',
    key: 'queryId',
    span: 3,
  },
  {
    label: '用户名',
    key: 'username',
  },
  {
    label: '用户IP',
    key: 'userIp',
  },
  {
    label: '来源',
    key: 'userSource',
  },
  {
    label: '类型',
    key: 'type',
  },
  {
    label: '状态',
    key: 'status',
  },
  {
    label: '耗时',
    key: 'timeCost',
    render(timeCost: string) {
      return timeCost ? timeCost + 'ms' : '-'
    },
  },
  {
    label: '开始时间',
    key: 'startTime',
  },
  {
    label: 'CPU消耗',
    key: 'cpuCost',
    render(cpuCost: string) {
      return cpuCost ? cpuCost + 'micro' : '-'
    },
  },
  {
    label: '影响行数',
    key: 'rowNum',
  },
  {
    label: '错误信息',
    key: 'errMsg',
    span: 3,
  },
  {
    label: '原始SQL',
    key: 'originalSql',
    span: 3,
  },
]

export { searchItems, tableColumns }
