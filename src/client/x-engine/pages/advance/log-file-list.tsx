// @ts-nocheck
import React, { useState } from 'react'
import { useAntdTable } from 'ahooks'
import { Card, Button, Form, Col, Row, Table } from 'antd'
import axios from 'axios'
import { PageHeader } from '@ant-design/pro-layout'
import { Api } from '@api'
import { PREFIX } from '@constant'
import dayjs from 'dayjs'
import cs from './advance.module.scss'
import { useSearchParams } from 'react-router-dom'
import HostSelect from './components/HostSelect'
import { BASE_URL } from 'src/shared/constants'

function sortListByName(data) {
  if (!data || !data.list || !Array.isArray(data.list)) {
    // 输入数据不符合预期，直接返回
    return data
  }

  // 根据 name 属性进行排序
  data.list.sort((a, b) => {
    const nameA = a.name.toLowerCase()
    const nameB = b.name.toLowerCase()

    // 使用 localeCompare 进行字符串比较，确保排序的正确性
    return nameA.localeCompare(nameB)
  })

  return data
}

const downloadFile = async (url) => {
  try {
    // 发起GET请求，指定响应类型为流
    const response = await axios.get(url, { responseType: 'blob' })

    // 获取Content-Disposition中的文件名
    const contentDisposition = response.headers['content-disposition']
    const match = contentDisposition && contentDisposition.match(/filename=(.+)/)
    const filename = match ? match[1] : 'downloaded-file'

    // 创建Blob对象并生成URL
    const blob = new Blob([response.data], { type: response.headers['content-type'] })
    const fileUrl = window.URL.createObjectURL(blob)

    // 创建a标签并模拟点击下载
    const link = document.createElement('a')
    link.href = fileUrl
    link.download = filename
    document.body.appendChild(link)
    link.click()

    // 移除临时创建的a标签
    document.body.removeChild(link)
    window.URL.revokeObjectURL(fileUrl)
  } catch (error) {
    console.error('下载文件出错:', error)
  }
}

function Home() {
  const [searchParams] = useSearchParams()
  const ip = searchParams.get('ip') || ''
  const [form] = Form.useForm()

  const [host, setHost] = useState('')

  const {
    tableProps: logsTableProps,
    run: getFileList,
    loading: getLogsLoading,
  } = useAntdTable(
    (arg) => {
      return Api.apiEngineV1LogListFilesGet(arg).then((res) => {
        try {
          return sortListByName(res)
        } catch (e) {
          console.log(e, 'error')
        }
      })
    },
    {
      defaultPageSize: 1000,
      manual: true,
    },
  )

  const columns = [
    {
      key: 'name',
      title: '名称',
      render: (item) => (
        <Button
          type={'link'}
          onClick={() => {
            const fileUrl = BASE_URL + `/api/engine/v1/log/getFile?file=${item.name}&ip=${host}`
            downloadFile(fileUrl)
          }}
        >
          {item?.name}
        </Button>
      ),
    },
    {
      dataIndex: 'lastModified',
      key: 'lastModified',
      title: '上次修改',
      render: (value: number) => (value ? dayjs(value).format('YYYY-MM-DD HH:mm:ss') : '_'),
    },
    {
      dataIndex: 'size',
      key: 'size',
      title: '大小',
    },
    {
      key: 'operation',
      title: '操作',
      render: (item) => (
        <>
          <Button
            style={{
              padding: 0,
            }}
            type={'link'}
            onClick={() => {
              const fileUrl = BASE_URL + `/api/engine/v1/log/getFile?file=${item.name}&ip=${host}`
              downloadFile(fileUrl)
            }}
          >
            下载
          </Button>
          <Button
            type={'link'}
            onClick={() => {
              const fileUrl = BASE_URL + `/api/engine/v1/log/getFile?file=${item.name}&compress=zip&ip=${host}`
              downloadFile(fileUrl)
            }}
          >
            下载压缩包
          </Button>
        </>
      ),
    },
  ]

  return (
    <>
      <PageHeader title="日志"></PageHeader>
      <Card className={cs.host}>
        <Form
          initialValues={{
            host: ip.trim(),
          }}
          layout="horizontal"
          form={form}
          wrapperCol={{ span: 24 }}
          onFinish={({ host: tempHost }) => {
            setHost(tempHost)
            getFileList({ address: tempHost.trim() })
          }}
        >
          <Row gutter={[20, 0]}>
            <Col span={8}>
              <Form.Item label="地址" name="host">
                <HostSelect nodeTypes={['compute_nodes', 'xengine_nodes']} />
              </Form.Item>
            </Col>
            <Col flex={'30px'}>
              <Form.Item>
                <Button type="primary" htmlType="submit" loading={getLogsLoading}>
                  查询
                </Button>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Card>
      <br />
      <Table columns={columns} rowKey="id" {...logsTableProps} />
    </>
  )
}

export default Home
