export interface MLDataType {
    key: string | number
    displayName: string
    mvName: string
    creator: string
    viewType: string
    scheduleType: string
    scheduleFrequency: string
    revenue: number
}

export interface MLPageParamsType {
    current: number
    pageSize: number
    total: number
}

export interface MLSearchOptionsType {
    catalogName?: string
    dbName?: string
    displayName?: string
    scheduleType?: string
    viewType?: string
    creator?: string
    current?: number
    pageSize?: number
}


export interface NewRecommendationItemVO {
    avgQueryCostTimeMs?: number
    id: string
    modelName: string
    mvSizeBytes?: number
    projectName: string
    queryCount: number
    recommendationTime: string
    roi: number
    type: string
    mvSizeRows?: number
    mvSizeRowsAfter?: number
    mvToMergeList: {
        avgQueryCostTimeMs?: number
        modelName: string
        mvName: string
        projectName: string
        queryCount: number
        sizeBytes: number
        sizeRows: number
    }[]
    [property: string]: any
}
export interface AcceptedRecommendationItemVO {
    acceptTime: string
    avgQueryCostTimeMsAfter?: number
    avgQueryCostTimeMsBefore?: number
    id: string
    modelName: string
    mvName: string
    projectName: string
    type: string
    mvToMergeList: {
        avgQueryCostTimeMs?: number
        modelName: string
        mvName: string
        projectName: string
        queryCount: number
        sizeBytes: number
        sizeRows: number
    }[]
    [property: string]: any
}

export interface MvToMergeVO {
    avgQueryCostTimeMs?: number;
    mvName: string;
    queryCount: number;
    sizeBytes: number;
    sizeRows: number;
    [property: string]: any;
}