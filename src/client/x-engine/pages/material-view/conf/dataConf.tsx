// @ts-nocheck
import React from 'react'
import type { MenuProps, TableColumnType, TableProps } from 'antd'
import { Button, Dropdown, message, Space, Typography, Tag, Tooltip } from 'antd'
import { routerMap } from '@XEngineRouter/routerMap'
import { Link } from 'react-router-dom'
import { askBIApiUrls, askBIPageUrls } from 'src/shared/url-map'
import { Api, ApiEngineV1MvActivePostRequest, MaterializedViewHitDetailsVo } from '@api'
// import Columns, { viewTypeMap } from '@ui/table/Columns'
import Columns from '@ui/table/Columns'
import { Broadcast } from '@libs'

import { QuestionCircleOutlined, SwapOutlined } from '@ant-design/icons'
import { IUnit, IUnitObj } from '../../../static.d'
import { Updater } from 'use-immer'
import { formatAsCurrency, splitQueryToPath, resolveSorter, percent, getUnitId, ellipsisText } from '@libs/util'
import moment from 'moment'
import styles from '../material-view.module.scss'
import { LogLink } from '../components/LogLink'
import LoadingText from 'src/client/components/LoadingText'
import { formatFileSize } from 'src/shared/common-utils'
import { customerIsSupportType } from 'src/shared/customer-resolver'
import axios from 'axios'
import { materializeRefreshType } from '@constant/enums'
import { MVItem } from 'src/shared/common-types'

// const computeItems = Object.keys(viewTypeMap).map((key) => {
//     return { value: key, text: viewTypeMap[key as keyof typeof viewTypeMap] }
// })
export const BroadcastIdManage = {
  openMVRecordsModalBId: `@materialList${getUnitId()}`,
  setMVRecordsBId: `@materialList${getUnitId()}`,
  reloadMVListBId: `@materialList${getUnitId()}`,
  path: `@materialList${getUnitId()}`,
}

const items: MenuProps['items'] = [
  { label: '单位(Byte)', key: IUnit.Byte },
  { label: '单位(KB)', key: IUnit.KB },
  { label: '单位(MB)', key: IUnit.MB },
  { label: '单位(GB)', key: IUnit.GB },
]

const sizeFormat = (key: 'uncompressedSize' | 'compressedSize', unit: IUnitObj, num: number) => {
  let t
  switch (unit[key]) {
    case IUnit.KB:
      t = (num / 1024)?.toFixed?.(2)
      break
    case IUnit.MB:
      t = (num / (1024 * 1024))?.toFixed?.(2)
      break
    case IUnit.GB:
      t = (num / (1024 * 1024 * 1024))?.toFixed?.(2)
      break
    default:
      t = num?.toFixed?.(2)
  }
  const text = formatAsCurrency(t)
  return (
    <div>
      {text} <br />
      {unit[key] !== IUnit.Byte && text === '0.00' ? `(${num} ${IUnit.Byte})` : null}
    </div>
  )
}

interface filterFormatType {
  list: any
  filterList: any
  filterField: string
  sortField: string
  order: string
}
export const filterFormat = (props: filterFormatType) => {
  const { list = [], filterList = [], filterField, sortField, order } = props

  let _list = [...list]

  if (filterList.length > 0) {
    _list = list.filter((item: any) => filterList.includes(item[filterField]))
  }
  if (order === 'ascend') {
    _list.sort((a: any, b: any) => {
      const aVal = !Number.isNaN(+a[sortField]) ? +a[sortField] : +new Date(a[sortField])
      const bVal = !Number.isNaN(+b[sortField]) ? +b[sortField] : +new Date(b[sortField])
      return aVal - bVal
    })
  }
  if (order === 'descend') {
    _list.sort((a: any, b: any) => {
      const aVal = !Number.isNaN(+a[sortField]) ? +a[sortField] : +new Date(a[sortField])
      const bVal = !Number.isNaN(+b[sortField]) ? +b[sortField] : +new Date(b[sortField])
      return bVal - aVal
    })
  }
  return _list
}

export const relatedMvColumns = [
  {
    title: '物化视图名称',
    key: 'mvName',
    width: 180,
    dataIndex: 'mvName',
  },
  {
    title: '总命中',
    dataIndex: 'hitCount',
    width: 100,
    sorter: true,
    render: (hitCount, record) => {
      return (
        <>
          <Button type="link" onClick={() => showMVRecord(record)}>
            {hitCount}
          </Button>
        </>
      )
    },
  },
  {
    title: '物化关联事实表',
    dataIndex: 'factTables',
    sorter: true,
    render: (text: string[]) => {
      if (!text || text.length === 0) return '-'
      return (
        <Tooltip placement="leftBottom" title={text.join(' , ')}>
          {text.join(' , ')}
        </Tooltip>
      )
    },
    ellipsis: true,
  },
  {
    title: '物化关联维表',
    dataIndex: 'dimensionTables',
    render: (text: string[]) => {
      if (!text || text.length === 0) return '-'
      return (
        <Tooltip placement="leftBottom" title={text.join(' , ')}>
          {text.join(' , ')}
        </Tooltip>
      )
    },
    ellipsis: true,
  },
  {
    title: '物化关联度量',
    dataIndex: 'measures',
    render: (text: string[]) => {
      if (!text || text.length === 0) return '-'
      return (
        <Tooltip placement="leftBottom" title={text.join(' , ')}>
          {text.join(' , ')}
        </Tooltip>
      )
    },
    ellipsis: true,
  },
  {
    title: '创建时间',
    // width: 200,
    dataIndex: 'createTime',
    sorter: true,
    render: (time) => {
      return moment(time).format('yyyy-MM-DD HH:mm:ss')
    },
  },
  {
    title: '操作',
    key: 'operation',
    fixed: 'right',
    dataIndex: 'operation',
    width: 80,
    render: function (_, record) {
      const path = splitQueryToPath(routerMap.smartx.materialViewDetail.path, record, [
        'catalogName',
        'dbName',
        'mvName',
      ])
      return (
        <Button type="link" className={styles.detailButton}>
          <Link to={path}>详情</Link>
        </Button>
      )
    },
  },
] as TableProps['columns']

export const getMVTableColumns = ({
  unit,
  setUnit,
  changeMVActive,
  handleDeleteMVSuccess,
}: {
  unit: IUnitObj
  setUnit: Updater<IUnitObj>
  changeBlackListStatus: (...args: any[]) => any
  changeMVActive: (arg: ApiEngineV1MvActivePostRequest) => void
  handleDeleteMVSuccess?: () => void
}) =>
  [
    {
      title: '项目',
      dataIndex: 'project',
      ellipsis: true,
      render(project) {
        return project || '-'
      },
    },
    {
      title: '事实表',
      dataIndex: 'model',
      render(_k: unknown, record: MVItem) {
        const factTableTex = (record?.factTables || []).toString()
        return (
          <Tooltip title={factTableTex} placement="topLeft">
            <span>{ellipsisText(factTableTex || '-', 20)}</span>
          </Tooltip>
        )
      },
    },
    {
      title: '物化视图名称',
      key: 'mvName',
      dataIndex: 'mvName',
    },
    // {
    //     title: '物化视图来源',
    //     filters: computeItems,
    //     dataIndex: 'viewType',
    //     filterMultiple: false,
    //     render: (viewType) => {
    //         return Columns({ type: 'viewType' })(viewType)
    //     },
    // },
    // {
    //   title: '关联资产数量',
    //   key: 'relatedMetricsCount',
    //   // width: 150,
    //   dataIndex: 'relatedMetricsCount',
    //   render: (v: number) => v || 0,
    // },
    {
      title: '总命中',
      dataIndex: 'hitCount',
      // width: 150,
      render: (hitCount: number, record: MVItem) => {
        return (
          <>
            <Button type="link" onClick={() => showMVRecord(record)}>
              {hitCount}
            </Button>
          </>
        )
      },
    },
    {
      title: '状态',
      dataIndex: 'available',
      render: (v: boolean) =>
        typeof v === 'boolean' ? <Tag color={v ? 'green' : 'red'}>{v ? '正常' : '不可用'}</Tag> : '-',
      width: 80,
    },
    {
      title: (
        <Dropdown
          menu={{
            items,
            selectable: true,
            onClick(e) {
              setUnit((draft) => {
                draft.compressedSize = e.key as keyof typeof IUnit
              })
            },
            defaultSelectedKeys: [IUnit.Byte],
          }}
        >
          <Space className="gap-0">
            压缩后磁盘占用大小({unit.compressedSize})
            <Typography.Link>
              <SwapOutlined />
            </Typography.Link>
          </Space>
        </Dropdown>
      ),
      ellipsis: true,
      // width: 260,
      dataIndex: 'compressedSize',
      render: (num: number) => {
        return sizeFormat('compressedSize', unit, num)
      },
    },
    {
      title: (
        <Dropdown
          menu={{
            items,
            selectable: true,
            onClick(e) {
              setUnit((draft) => {
                draft.uncompressedSize = e.key as keyof typeof IUnit
              })
            },
            defaultSelectedKeys: [IUnit.Byte],
          }}
        >
          <Space className="gap-0">
            未压缩磁盘占用大小({unit.uncompressedSize})
            <Typography.Link>
              <SwapOutlined />
            </Typography.Link>
          </Space>
        </Dropdown>
      ),
      // width: 200,
      ellipsis: true,
      dataIndex: 'uncompressedSize',
      render: (num: number) => {
        return sizeFormat('uncompressedSize', unit, num)
      },
    },
    {
      title: '压缩比例',
      ellipsis: true,
      // width: 150,
      dataIndex: 'compressionRatio',
      render: (v: number) => {
        const t = percent(v)
        return v > 1 ? (
          <Tooltip title="数据量过小可能导致压缩后体积变大">
            {t}
            <QuestionCircleOutlined className="ml-1" style={{ color: 'rgba(0,0,0,.45)' }} />
          </Tooltip>
        ) : (
          t
        )
      },
    },
    {
      title: '调度模式',
      dataIndex: 'scheduleType',
      render: (v: unknown, record: MVItem) => {
        return record.scheduleType === 'SYSTEM_DEDUCED' ? '系统自动' : v
      },
    },
    // {
    //     title: '创建时间',
    //     // width: 200,
    //     dataIndex: 'createTime',
    //     render: (time) => {
    //         return moment(time).format('yyyy-MM-DD HH:mm:ss')
    //     },
    // },
    // {
    //     title: '更新时间',
    //     // width: 200,
    //     dataIndex: 'updateTime',
    //     render: (time) => {
    //         return moment(time).format('yyyy-MM-DD HH:mm:ss')
    //     },
    // },
    {
      title: '操作',
      key: 'operation',
      fixed: 'right',
      dataIndex: 'operation',
      width: 180,
      render: function (_: unknown, record: MVItem) {
        const path = splitQueryToPath(routerMap.smartx.materialViewDetail.path, record, [
          'catalogName',
          'dbName',
          'mvName',
        ])
        return (
          <>
            <Space>
              <Button type="link" className={styles.detailButton}>
                <Link to={path}>详情</Link>
              </Button>
              {customerIsSupportType('mvOperationButtonHide') ? null : (
                <Button
                  type="link"
                  onClick={() => {
                    changeMVActive({
                      mvName: record.mvName,
                      active: !record.active,
                    })
                  }}
                >
                  {record.active ? '禁用' : '启用'}
                </Button>
              )}
              {/*<Button*/}
              {/*    size={'small'}*/}
              {/*    type='link'*/}
              {/*    onClick={() => {*/}
              {/*        changeBlackListStatus({*/}
              {/*            mvName: record.mvName,*/}
              {/*            blacklist: true,*/}
              {/*        })*/}
              {/*    }}*/}
              {/*>*/}
              {/*    加入黑名单*/}
              {/*</Button>*/}
              <LoadingText
                onSuccess={() => handleDeleteMVSuccess?.()}
                api={() => Api.apiEngineV1MvCascadeDropDelete({ mvName: record.mvName || '' })}
                type="danger"
                popconfirmProps={{
                  title: '确认删除',
                  description: (
                    <div>
                      删除将删除当前物化和所有依赖当前物化的物化视图， <br />
                      并同步删除数据及相应任务，请确认
                    </div>
                  ),
                  okText: '删除',
                }}
                onFail={(err) => message.error(err?.message || '删除物化视图失败')}
              >
                删除
              </LoadingText>
              {/* <Popconfirm
                                title='确认删除'
                                description={
                                    <div>
                                        删除将删除当前物化和所有依赖当前物化的物化视图， <br />
                                        并同步删除数据及相应任务，请确认
                                    </div>
                                }
                                okText='删除'
                                onConfirm={() => {
                                    MVDrop({
                                        mvName: record.mvName,
                                    })
                                }}
                            >
                                <Button size={'small'} type='link' loading={MVDropLoading}>
                                    删除
                                </Button>
                            </Popconfirm> */}
            </Space>
          </>
        )
      },
    },
  ] as TableProps['columns']

function showMVRecord(mvRecord: Record<string, any>) {
  const { catalogName, dbName, mvName } = mvRecord
  const path = splitQueryToPath(routerMap.smartx.materialViewDetail.path, mvRecord, ['catalogName', 'dbName', 'mvName'])

  Api.apiEngineV1MvRecordsGet({
    catalog: catalogName,
    database: dbName,
    mvName,
  }).then((res) => {
    Broadcast.trigger(BroadcastIdManage.openMVRecordsModalBId)
    Broadcast.trigger(BroadcastIdManage.setMVRecordsBId, { mvRecordList: res?.list || [] })
    Broadcast.trigger(BroadcastIdManage.path, path)
  })
}

interface MVDetailDataMapType {
  label: string
  key: string
  render?: React.FC<{ v: any }>
}

// function AppendDataRender({ value, open, onClose }: { value: any[], open: boolean, onClose: () => any }) {
//     const list = Array.isArray(value) ? value : []
//     const total = list.length
//     return <>

//         <Modal width={800} open={open} onCancel={onClose}>
//             {
//                 Array.isArray(list) ? <Table dataSource={{ list, total }} columns={[
//                     { dataIndex: 'appendDataStartTime', title: '补数开始时间' },
//                     { dataIndex: 'appendDataEndTime', title: '补数结束时间' }]} /> : <Empty />
//             }
//         </Modal>
//     </>

// }

/* material detail */
export const createMVDetailDataMap = ({
  showSQL,
}: // showAppendDataDetail,
{
  showSQL: (text: string) => void
  showAppendDataDetail: (appendData: any[]) => void
}): MVDetailDataMapType[] => {
  return [
    { label: '物化视图类型', key: 'viewType', render: ({ v }) => Columns({ type: 'viewType' })(v) },
    {
      label: '物化视图状态',
      key: 'mvState',
      render: ({ v }) => {
        return <>{typeof v === 'boolean' ? <Tag color={v ? 'green' : 'red'}>{v ? '正常' : '不可用'}</Tag> : '-'}</>
      },
    },
    ...(customerIsSupportType('mvDetailDiskHide')
      ? []
      : [
          {
            label: '物化是否禁用',
            key: 'disable',
            render({ v }) {
              return <>{typeof v === 'boolean' ? v ? <span className="text-red-500">是</span> : '否' : v || '-'}</>
            },
          },
        ]),
    { label: '物化关联目录', key: 'catalogName' },
    { label: '物化关联数据库', key: 'databaseName' },
    {
      label: '物化方式',
      key: 'refreshType',
      render({ v }) {
        const valueUpperCase = v?.toUpperCase() as keyof typeof materializeRefreshType
        return materializeRefreshType[valueUpperCase]
      },
    },
    ...(customerIsSupportType('mvDetailDiskHide')
      ? []
      : [
          {
            label: '物化视图关联存储大小',
            key: 'onDisk',
            render({ v }) {
              if (v === '-') {
                return v
              }
              return typeof v === 'string' || typeof v === 'number' ? `${formatFileSize(v)}` : '-'
            },
          },
        ]),
    // {
    //     label: '物化视图补数逻辑',
    //     key: 'appendData',
    //     render: ({ v }) => {
    //         const list = Array.isArray(v) ? v : []
    //         return (
    //             <Typography.Link
    //                 onClick={() => {
    //                     showAppendDataDetail(list)
    //                 }}
    //             >
    //                 补数详情
    //             </Typography.Link>
    //         )
    //     },
    // },
    {
      label: '加工逻辑',
      key: 'sqlQuery',
      render: ({ v: text }) => {
        return (
          <Button
            type="link"
            size={'small'}
            onClick={() => {
              showSQL(text)
            }}
          >
            详情
          </Button>
        )
      },
    },
    { label: '物化描述', key: 'description' },
  ]
}

export const MDTableColumns = (
  onSQLDetail: (record: MaterializedViewHitDetailsVo) => void,
):
  | TableColumnType<MaterializedViewHitDetailsVo>[]
  | Array<{
      dataIndex: keyof MaterializedViewHitDetailsVo
    }> => [
  {
    title: '命中SQL',
    dataIndex: 'hitSqlScript',
    ellipsis: true,
  },
  {
    title: '查询用户',
    dataIndex: 'queryUser',
  },
  {
    title: '命中时间',
    dataIndex: 'hitTime',
    render: Columns({ type: 'time' }),
  },
  {
    title: '命中耗时节点',
    render: (_, record) => {
      return (
        <Space>
          aggregate: {record?.costlyNodesCount?.aggregate || '-'}
          join: {record?.costlyNodesCount?.join || '-'}
        </Space>
      )
    },
  },
  {
    title: 'SQL详情',
    render: (_, record) => {
      return (
        <Button
          type="link"
          onClick={() => {
            onSQLDetail(record)
          }}
        >
          查看
        </Button>
      )
    },
  },
]

export const partitionColumn = [
  {
    title: '分区名称',
    dataIndex: 'partitionName',
    sorter: true,
    sortDirections: ['descend'],
    ellipsis: true,
  },
  {
    title: '分区状态',
    filters: [
      {
        text: '可用',
        value: 'AVAILABLE',
      },
      {
        text: '不可用',
        value: 'UNAVAILABLE',
      },
    ],
    render: Columns({ type: 'partitionStatus' }),
  },
  {
    title: '完成时间',
    dataIndex: 'completeTime',
    sorter: true,
    sortDirections: ['descend'],
    render: Columns({ type: 'time' }),
  },
] as TableColumnType<any>[]
// 物化详情关联指标table的columns
export const MVmetricsColumns = [
  {
    title: '指标名称',
    dataIndex: 'metricsName',
  },
  {
    title: '关联虚拟表',
    dataIndex: 'vtableName',
  },
  {
    title: '业务系统',
    dataIndex: 'businessType',
  },
  {
    title: '查询总数',
    dataIndex: 'queryCount',
    sorter: (a, b) => resolveSorter(a.queryCount, b.queryCount),
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    sorter: (a, b) => resolveSorter(a.createTime, b.createTime),
  },
] as TableColumnType<any>[]

// 物化视图来源表

export const tableColumn = [
  {
    title: '表名称',
    dataIndex: 'table',
    ellipsis: true,
  },
  {
    title: '所属数据目录',
    dataIndex: 'catalog',
  },
  {
    title: '所属数据库',
    dataIndex: 'database',
  },
  {
    title: '操作',
    dataIndex: 'action',
    render: (_: any, record: any) => (
      <a
        href={`${getTablePath(record.catalogType)}?name=${record.table}&catalog=${
          record.catalog
        }&database=${record.database}`}
      >
        详情
      </a>
    ),
  },
]
export const MVMeasureTableColumn = [
  {
    title: '度量名',
    dataIndex: 'name',
    ellipsis: true,
  },
]

export const MVDimensionTableColumn = [
  {
    title: '维度名',
    dataIndex: 'name',
    ellipsis: true,
  },
]

const getTablePath = (type: string) => {
  if (type === 'INTERNAL') {
    return routerMap.dataModel.businessVirtualTableDetail.path
  }
  return askBIPageUrls.manage.externalDatasource.tableDetail
}

export const MVTaskTableColumns = ({
  setReload,
  onClickAppendData,
}: {
  setReload: React.Dispatch<React.SetStateAction<boolean>>
  onClickAppendData?: (record: Record<string, any>) => any
}) =>
  [
    {
      title: '分区',
      dataIndex: 'partition',
      key: 'partition',
      sortDirections: ['descend'],
      sorter: true,
      render(partition, record) {
        return <Typography.Text disabled={!record?.mvExist}>{partition}</Typography.Text>
      },
    },
    {
      title: '当前状态',
      dataIndex: 'state',
      key: 'state',
      filters: [
        {
          text: '成功',
          value: 'SUCCESS',
        },
        {
          text: '失败',
          value: 'FAILURE',
        },
      ],
      width: '120px',
      render: (state: string, record) => {
        if (!record.partition) {
          return <Tag>无数据</Tag>
        }
        return Columns({ type: 'mvjobstatus' })(state, record)
      },
    },
    {
      title: '开始执行时间',
      dataIndex: 'startTime',
      key: 'startTime',
      sorter: true,
      sortDirections: ['descend'],
      render: Columns({ type: 'time' }),
    },
    {
      title: '结束执行时间',
      dataIndex: 'endTime',
      sortDirections: ['descend'],
      key: 'endTime',
      sorter: true,
      render: Columns({ type: 'time' }),
    },
    {
      title: '日志',
      dataIndex: 'logViewUrl',
      render: (_, record) => <LogLink data={record} />,
      width: '80px',
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: '80px',
      render: (_: any, record: Record<string, any>) => {
        if (record.partition) {
          return (
            <LoadingText
              type="link"
              api={() =>
                axios.get(askBIApiUrls.xengine.scheduler.rerunTask, {
                  params: { jobId: record.id },
                })
              }
              onSuccess={(res) => {
                if (res) {
                  message.success(`${record.name}重跑任务开始`)
                  setTimeout(() => setReload((reload: boolean) => !reload), 800)
                } else {
                  message.error(`重跑任务出现错误`)
                }
              }}
              onFail={(err) => message.error(err.message || `重跑任务出现错误`)}
              popconfirmProps={{
                title: '确定重跑任务？',
              }}
            >
              重跑
            </LoadingText>
          )
        }
        return <Typography.Link onClick={() => onClickAppendData?.(record)}>补数</Typography.Link>
      },
    },
  ] as TableProps['columns']
