import { PageHeader } from '@ant-design/pro-layout'
import { type TableColumnsType, message, Typography, Tooltip, Popover } from 'antd'
import { Api } from '@api'
import React, { useState } from 'react'
import { Link } from 'react-router-dom'
import { deleteEmptyValueFromObject, getMVNameFromDpName, splitQueryToPath } from '@libs/util'
import { routerMap } from '@XEngineRouter/routerMap'
import Columns, { mvJobStatusColumnTagMap, isMVJobKillAllowed, ExecutionSecondsTitle } from '@ui/table/Columns'
import { dpStatusMap } from '@constant/enums'
import dayjs from 'dayjs'
import { Search } from '@ui/form'
import LoadingText from '../../../components/LoadingText'
import { LogLink } from './components/LogLink'
import { MoreOutlined } from '@ant-design/icons'

const jobsTableColumns = ({ onKillSuccess }: { onKillSuccess?: () => any }) =>
  [
    {
      title: '项目',
      dataIndex: 'projectName',
      ellipsis: {
        showTitle: true,
      },

      render(projectName) {
        return (
          <Tooltip title={projectName} placement="topLeft">
            {projectName || '-'}
          </Tooltip>
        )
      },
    },
    {
      title: '模型',
      dataIndex: 'modelNames',
      ellipsis: {
        showTitle: true,
      },
      render(modelNames = []) {
        const modelNamesStr = modelNames?.toString()
        return (
          <Tooltip title={modelNamesStr} placement="topLeft">
            {modelNamesStr || '-'}
          </Tooltip>
        )
      },
    },
    {
      title: '物化视图',
      dataIndex: 'mvFullName',
      ellipsis: true,
      render: (mvFullName, record) => {
        const [catalogName, dbName, mvName] = mvFullName?.split('.') || []

        const to = `${routerMap.smartx.materialViewDetail.path}?${new URLSearchParams({
          catalogName,
          dbName,
          mvName,
        }).toString()}`
        return (
          <Tooltip title={mvName} placement="topLeft">
            {record?.mvExist ? (
              <Link
                to={to}
                style={{
                  cursor: 'pointer',
                }}
              >
                {mvName}
              </Link>
            ) : (
              <Typography.Text>{mvName}</Typography.Text>
            )}
          </Tooltip>
        )
      },
    },
    {
      title: '分区',
      dataIndex: 'partition',
      width: '120px',
      sorter: true,
      render(partition, record) {
        return <Typography.Text disabled={!record?.mvExist}>{partition}</Typography.Text>
      },
    },
    { title: '阶段', dataIndex: 'stageName' },
    {
      title: '当前状态',
      dataIndex: 'state',
      render: Columns({ type: 'mvJobStatus' }),
      width: '90px',
    },
    {
      title: '队列',
      dataIndex: 'queueName',
    },
    {
      title: '优先级',
      dataIndex: 'jobPriority',
    },
    {
      title: '类型',
      dataIndex: 'jobType',
    },
    {
      title: '开始执行时间',
      dataIndex: 'startTime',
      render(startTime) {
        return startTime ? dayjs(startTime).format('YYYY-MM-DD HH:mm:ss') : '-'
      },
      sorter: true,
    },
    {
      title: '结束执行时间',
      dataIndex: 'endTime',
      render(endTime) {
        return endTime ? dayjs(endTime).format('YYYY-MM-DD HH:mm:ss') : '-'
      },
      sorter: true,
    },
    {
      title: <ExecutionSecondsTitle />,
      dataIndex: 'executionSeconds',
      width: 120,
      render: Columns({ type: 'duration' }),
      sorter: true,
    },
    {
      title: '操作',
      dataIndex: 'operation',
      width: 60,
      render: (_, record) => {
        return (
          <>
            <Popover
              content={
                <div className="w-[64px] text-center">
                  <div className="text-center">
                    <LoadingText
                      disabled={!isMVJobKillAllowed(record.state)}
                      type="link"
                      api={() => {
                        return Api.apiEngineV1SchedulerStopTaskIdPost({
                          id: record.id,
                        })
                      }}
                      onSuccess={() => {
                        if (typeof onKillSuccess === 'function') {
                          onKillSuccess()
                        }
                        message.success('操作成功')
                      }}
                      onFail={() => {
                        message.error('操作失败')
                      }}
                    >
                      kill
                    </LoadingText>
                  </div>
                  <LogLink data={record} />
                </div>
              }
            >
              <MoreOutlined className="cursor-pointer" />
            </Popover>
          </>
        )
      },
    },
  ] as TableColumnsType

const mbJobsFormItems = [
  {
    tag: 'Select',
    label: '状态',
    name: 'state',
    options: [{ label: '全部', value: 'ALL' }].concat(
      Object.keys(mvJobStatusColumnTagMap).map((k) => ({
        label: mvJobStatusColumnTagMap[k as keyof typeof mvJobStatusColumnTagMap].text,
        value: k,
      })),
    ),
  },
  {
    tag: 'Input',
    label: '搜索条件',
    name: 'searchVal',
    tooltip: '多个条件搜索使用空格分开，支持的搜索条件为物化视图名称、分区、项目、模型名称',
    allowClear: true,
    placeholder: JSON.parse(localStorage.getItem('lastTenantSelection') || '{}').project || '请输入搜索条件',
  },
  {
    tag: 'DatePicker',
    label: '开始执行时间',
    name: 'startDate',
    showTime: true,
  },
  {
    tag: 'DatePicker',
    label: '结束执行时间',
    name: 'endDate',
    showTime: true,
  },
  {
    tag: 'buttons',
    children: [
      {
        tag: 'Button',
        name: 'submit',
        label: '查询',
        type: 'primary',
        htmlType: 'submit',
      },
    ],
  },
]

export default function () {
  const [reloadMVJobs, setReloadMVJobs] = useState(false)
  const project = JSON.parse(localStorage.getItem('lastTenantSelection') || '{}').project
  return (
    <>
      <PageHeader title="任务列表" />
      <Search
        reload={reloadMVJobs}
        key={'mv-jobs-search'}
        items={mbJobsFormItems}
        table={{
          columns: jobsTableColumns({
            onKillSuccess: () => {
              const setTimeId = setTimeout(() => {
                setReloadMVJobs((reload) => !reload)
                clearTimeout(setTimeId)
              }, 1200)
            },
          }),
          rowKey: 'id',
          api: (params: Record<string, any>) => {
            const { state, startDate, endDate, sorter, ...args } = params
            const order = sorter?.order === 'ascend' ? 'ASC' : sorter?.order === 'descend' ? 'DESC' : ''
            const sortField = sorter?.field
            const req = {
              ...args,
              searchVal: args.searchVal || project,
              state: state === 'ALL' ? '' : state,
              startDate: startDate ? dayjs(startDate).format('YYYY-MM-DD HH:mm:ss') : '',
              endDate: endDate ? dayjs(endDate).format('YYYY-MM-DD HH:mm:ss') : '',
            }
            if (sortField && order) {
              Object.assign(req, {
                sortField,
                order,
              })
            }
            return Api.apiEngineV1SchedulerTasksGet(deleteEmptyValueFromObject(req))
          },
        }}
        initialValues={{
          state: 'ALL',
        }}
      />
    </>
  )
}
