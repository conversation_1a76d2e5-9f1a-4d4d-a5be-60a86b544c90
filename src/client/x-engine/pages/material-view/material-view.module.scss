.createMaterialEditor {
  overflow: auto;
  width: calc(100vw - 332px);
  height: 800px;

  &Editor {
    height: 100%;
  }
}

.createMaterialForm {
  width: 520px;
  margin: 0 auto;
}

.MVDetailContainer {
  & .MVCountTitle {
    font-weight: 400;
  }

  & .MVCountLabel {
    color: rgb(0 0 0 / 0.45);
  }
}

.MVDetailRelativeTableWrap :global(.xengine-layout-card) {
  margin: 0 !important;
}

.pageHeaderTitle {
  display: flex;
  align-items: baseline;

  .statusWarning {
    padding-left: 10px;
    color: red;
    font-weight: normal;
    font-size: 13px;
  }
}

.showAdvanceConfig {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 26px;
  color: blue;
  cursor: pointer;
}

.deleteIcon {
  position: relative;
  top: 45px;
  left: 20px;
}

.configText {
  margin-left: 8px;
}

.detailButton {
  padding: 0;
}
