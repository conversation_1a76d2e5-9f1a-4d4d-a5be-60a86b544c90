// @ts-nocheck
import { Tooltip, Typography } from 'antd'
import { LikeBatchType } from '@pages/batch-info/conf/interface'
export const like_batch_columns: any = [
    {
        dataIndex: 'sourceCatalog',
        key: 'sourceCatalog',
        title: '源catalog',
    },
    {
        key: 'sourceDatabase',
        dataIndex: 'sourceDatabase',
        title: '源database',
    },
    {
        key: 'sourceTable',
        dataIndex: 'sourceTable',
        title: '源table',
        ellipsis: {
            showTitle: false,
        },
        render: (text: string) => (
            <Tooltip placement='topLeft' title={text}>
                {text}
            </Tooltip>
        ),
    },
    {
        key: 'targetCatalog',
        dataIndex: 'targetCatalog',
        title: '目标catalog',
    },
    {
        key: 'targetDatabase',
        dataIndex: 'targetDatabase',
        title: '目标database',
    },
    {
        key: 'targetTable',
        dataIndex: 'targetTable',
        title: '目标table',
        ellipsis: {
            showTitle: false,
        },
        render: (text: string) => (
            <Tooltip placement='topLeft' title={text}>
                {text}
            </Tooltip>
        ),
    },
    {
        key: 'failureReason',
        dataIndex: 'failureReason',
        title: '失败原因',
        ellipsis: {
            showTitle: false,
        },
        render: (failureReason: string, record: LikeBatchType) => (
            <Tooltip placement='topLeft' title={failureReason || getErrorMessage(record.statusCode)}>
                <Typography.Text type='danger'>{failureReason || getErrorMessage(record.statusCode)}</Typography.Text>
            </Tooltip>
        ),
    },
]

// 错误码字典
export const getErrorMessage = (code: number | null) => {
    switch (code) {
        case 348162:
            return '提供的表信息错误'
        case 348163:
            return '依赖表不存在'
        case 348164:
            return '表已经存在'
        case 348165:
            return '超出表上传的最大限制数量'
        case 348161:
            return '文件类型错误'
        case 348166:
            return '数据源不存在'
        default:
            return '未知错误'
    }
}
