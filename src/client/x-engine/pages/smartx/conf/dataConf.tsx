// @ts-nocheck
import { Link } from 'react-router-dom'
import { routerMap } from '@XEngineRouter/routerMap'
import { Button, Tag } from 'antd'
import dayjs from 'dayjs'
const detailToPathMap = {
  HIVE: routerMap.smartx.smartQueryHiveDetail.path,
  MYSQL: routerMap.smartx.smartQueryDetail.path,
}

export const allowStartDay = dayjs().subtract(30, 'day')
export const defaultStartDay = dayjs().subtract(7, 'day')

export const SQLTableColumn = [
  {
    title: '文件名称',
    dataIndex: 'fileName',
    key: 'fileName',
  },
  {
    title: '创建人',
    dataIndex: 'creator',
    key: 'creator',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    render: (_, record: Record<string, any>) => {
      const { sqlType, fileName, projectId } = record
      const to = detailToPathMap[sqlType as keyof typeof detailToPathMap] ?? routerMap.smartx.smartQueryDetail.path

      const path = `${to}?fileName=${fileName}&projectId=${projectId}`
      return (
        <>
          <Link to={path}>查看</Link>
        </>
      )
    },
  },
]

export const SQLDetailTablesColumn = (params: { projectId: number; fileName: string }) => {
  const { projectId, fileName } = params
  return [
    {
      title: '数据库名称',
      dataIndex: 'databaseName',
      key: 'databaseName',
    },
    {
      title: '表名称',
      dataIndex: 'tableName',
      key: 'tableName',
    },
    {
      title: '是否有血缘关系',
      dataIndex: 'lineageFlag',
      key: 'lineageFlag',
      render: (lineageFlag: boolean) => {
        return lineageFlag ? '是' : '否'
      },
    },
    {
      title: '关联SQL数量',
      dataIndex: 'relatedSqlNumber',
      key: 'relatedSqlNumber',
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      render: (_, record: Record<string, string>) => {
        const state = {
          fileName,
          projectId,
          ...record,
        }
        const to = `${routerMap.smartx.smartQueryTableDetail.path}`
        return (
          <>
            <Link to={to} state={state}>
              <Button type="link">详情</Button>
            </Link>
          </>
        )
      },
    },
  ]
}

export const SmartMVScanSearch = [
  // {
  //     tag: 'DatePicker',
  //     label: '开始时间：',
  //     tooltip: '从执行历史记录开始时间来做推荐，结束时间自动匹配现在时间',
  //     name: 'startTime',
  //     format: 'YYYY-MM-DD HH:mm',
  //     disabledDate: (current: any) => {
  //         return (
  //             (current && current < allowStartDay.startOf('day')) || current > dayjs().subtract(0, 'day').endOf('day')
  //         )
  //     },
  //     showTime: true,
  // },
  // {
  //     tag: 'InputNumber',
  //     label: '上卷收益',
  //     name: 'minRollupRowsRate',
  //     placeholder: '请输入上卷收益',
  //     tooltip: '上卷收益，越低收益也高，上卷后的行数/不上卷的行数比例，低于该值才做推荐',
  //     colSpan: 8,
  // },
  // {
  //     tag: 'InputNumber',
  //     label: '查询频次：≥',
  //     placeholder: '请输入查询频次',
  //     tooltip: '在N天的历史查询中出现过多少次才进行推荐',
  //     name: 'minFrequency',
  // },
  // {
  //     tag: 'InputNumber',
  //     label: '上卷压缩率：≥',
  //     max: 100,
  //     addonAfter: '%',
  //     placeholder: '请输入上卷压缩率',
  //     name: 'minRollupRowsRate',
  // },
  {
    tag: 'Select',
    label: 'ER 关联事实表',
    placeholder: '请选择ER 关联事实表',
    allowClear: true,
    name: 'entityName',
  },
  {
    tag: 'buttons',
    wrapperCol: { span: 16 },
    children: [
      {
        tag: 'Button',
        name: 'reset',
        label: '重置',
        htmlType: 'button',
      },
      {
        tag: 'Button',
        name: 'submit',
        label: '查询',
        type: 'primary',
        htmlType: 'submit',
      },
    ],
  },
]

export const getSQLQueryResult = (data: any) => {
  return [
    {
      key: 'queryId',
      label: 'queryId',
      span: 3,
    },
    {
      key: 'startTime',
      label: 'startTime',
    },
    {
      key: 'endTime',
      label: 'endTime',
      span: 2,
    },
    {
      key: 'executionTime',
      label: 'executionTime',
    },
    {
      key: 'result',
      label: 'result',
      children: <>{data?.result === 'SUCCESS' ? <Tag color="green">成功</Tag> : <Tag color="red">失败</Tag>}</>,
    },
    {
      key: 'isHitMv',
      label: 'isHitMv',
    },
    {
      key: 'hitMvNames',
      label: 'hitMvNames',
      children: <>{String(data?.hitMvNames)}</>,
      span: 3,
    },
    {
      key: 'relatedTables',
      label: 'relatedTables',
      children: <>{String(data?.relatedTables)}</>,
      span: 3,
    },
    {
      key: 'errMsg',
      label: 'errMsg',
      span: 3,
    },
  ]
}
