type ItemType = { catalogName: string, databaseName: string, tableName: string, [key: string]: any }
type DataType = {
  nodes?: Partial<ItemType>[]
  edges?: {
    source?: Partial<ItemType>
    target?: Partial<ItemType>
  }[]
}

const spellId = (n: Partial<ItemType>) => {
  const { catalogName, databaseName, tableName } = n || {}
  return `${catalogName}.${databaseName}.${tableName}`
}

export function formatVTLineageData(data?: DataType) {
    
    const { edges = [], nodes = [] } = data || {}
    if (!Array.isArray(nodes) || !Array.isArray(edges)) {
        return {
            nodes: [],
            edges: [],
        }
    }
    const ansNodes = nodes.map((n) => {
        const id = spellId(n)
        return {
            ...n,
            id: id,
        }
    })
    const ansEdges = edges.map((e) => {
        const { source, target } = e
        const sourceId = spellId(source as ItemType)
        const targetId = spellId(target as ItemType)
        return {
            source: sourceId,
            target: targetId,
            sourceKey: source?.columnName,
            targetKey: target?.columnName,
        }
    })
    return {
        nodes: ansNodes,
        edges: ansEdges,
    }
}
