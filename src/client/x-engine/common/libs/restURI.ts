/**
 * Rest URL
 */

interface Obj<T> {
    [key: string]: T
}

const restURI: Obj<string | any> = {
    mv: {
        statisticsDownload: '/xengine/api/engine/v1/mv/statistics/download',
    },
    auth: {
        login: '/xengine/login',
        logout: '/xengine/auth/logout',
    },

    devops: {
        home: '/xengine/managerx/setting',
        proxy: '/xengine/managerx/setting/xengine',
    },
}

export default restURI
