import type { CascaderOptionType} from 'src/shared/xengine-types'
import { FormatOptionsWithLanguage, format } from 'sql-formatter'
import type { AnyObject, VirtualTableType } from 'src/shared/xengine-types'
import { MetricModelType } from 'src/shared/metric-types'

/**
 * <AUTHOR>
 * @description util functions
 */

/**
 * 随机ID值
 */
export const getUnitId = (): any => {
    return Math.random().toString(32).slice(2)
}

/**
 * 判断属性值
 * @param {Object} foo 判断对象
 * @param {String} key 对象值
 */
export const hasProperty = (foo: object, key: string): boolean => {
    return Object.prototype.hasOwnProperty.call(foo, key)
}

/**
 * 获取参数
 * @param {string} name - 参数名
 * @param {string} src - 域名，默认使用当前访问域名
 */
export const getParam = (name: string, src?: string): string => {
    const re = new RegExp('(?:^|\\?|#|&)' + name + '=([^&#]*)(?:$|&|#)', 'i')
    const m = re.exec(src || (window.location || {}).href)
    return m ? m[1] : ''
}

/**
 * 获取url的origin
 */
export const getUrlOrigin = (url: string): string => {
    let origin = ''

    if (typeof window.URL === 'function' && url) {
        const urlObj = new URL(url)
        origin = urlObj.origin
    } else {
        const a = document.createElement('a')
        a.href = url
        origin = a.protocol + '//' + a.host
    }

    return origin
}

/**
 * LocalStorage
 * @param name String name of the localStorage
 * @param value String value of the localStorage
 */
export const setItem = (name: string, value: string): void => {
    window.localStorage.setItem(name, value)
}

/**
 * LocalStorage
 * @param name String name of the localStorage
 * @returns string value of the localStorage
 */
export const getItem = (name: string): any => {
    return window.localStorage.getItem(name)
}

/**
 * LocalStorage
 * @param name String name of the localStorage
 * @returns string value of the localStorage
 */
export const removeItem = (name: string): any => {
    return window.localStorage.removeItem(name)
}

/**
 * 设置cookie
 */
export const setCookie = (name: string, value: string, expireTime?: any, path?: any, domain?: string): void => {
    const now = new Date().getTime(),
        expiresTime = new Date(now + expireTime)
    let domainStr = '',
        pathStr = '',
        expiresStr = ''

    if (domain) {
        domainStr = ';domain=' + domain
    }
    if (path) {
        pathStr = ';path=' + path
    }

    if (expireTime) {
        expiresStr = ';expires=' + expiresTime
    }

    document.cookie = name + '=' + encodeURIComponent(value) + expiresStr + pathStr + domainStr
}

/**
 * 获取cookie
 */
export const getCookie = (name: string): string => {
    let c_start: any = '',
        c_end = null

    if (window.document.cookie.length > 0) {
        c_start = window.document.cookie.indexOf(name + '=')
        if (c_start !== -1) {
            c_start = c_start + name.length + 1
            c_end = window.document.cookie.indexOf(';', c_start)
            if (c_end === -1) {
                c_end = window.document.cookie.length
            }
            return decodeURIComponent(window.document.cookie.substring(c_start, c_end))
        }
    }
    return ''
}

/**
 * 对象转换为字符串拼接
 */
export const objToStr = (obj: any): string => {
    let str = ''
    for (const x in obj) {
        str += str === '' ? x + '=' + obj[x] : '&' + x + '=' + obj[x]
    }
    return str
}

export const toString = (str: string): string => {
    let _str = ''
    if (typeof str === 'object') {
        _str = JSON.stringify(str)
    } else if (typeof str === 'undefined') {
        _str = 'undefined'
    } else {
        _str = str
    }

    return _str
}

/**
 * 是否为数组
 */
export const isArray = (obj: object): boolean => {
    if (typeof Array === 'function') {
        return Array.isArray(obj) // {} --> false, [] --> true
    }

    return Object.prototype.toString.call(obj) === '[object Array]'
}

/**
 * 是否为对象
 */
export const isObject = (obj: object): boolean => {
    return Object.prototype.toString.call(obj) === '[object Object]'
}

/**
 * 时间戳转换
 */
export const timestampToTime = (timestamp: number): any => {
    const date = new Date(String(timestamp).length === 10 ? timestamp * 1000 : timestamp)
    const Y = date.getFullYear() + '-'
    const M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-'
    const D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' '
    const h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':'
    const m = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()
    return Y + M + D + h + m
}

/**
 * 对象转数组
 */
export const obj2Arr = (obj: any): any => {
    const arr = []
    if (typeof obj === 'object') {
        for (const x in obj) {
            if (hasProperty(obj, x)) {
                arr.push(obj[x])
            }
        }
    }
    return arr
}

/**
 * 格式化非标准数据
 */
export const parseJSON = (data: any) => {
    if (typeof data !== 'string' || !data) {
        return null
    }
    data = data.replace(/\n| |↵/gi, '')
    return new Function('return ' + data)()
}

/**
 * 深度obj转浅度obj
 */
export const deepObj2shallowObj = (obj: any) => {
    const data: any = {}
    const forEach = (obj: any) => {
        Object.keys(obj).forEach((keys) => {
            if (typeof obj[keys] === 'object') {
                forEach(obj[keys])
            } else if (typeof obj[keys] === 'symbol') {
                return
            } else {
                data[keys] = obj[keys]
            }
        })
    }
    forEach(obj)
    return data
}

export const ellipsisText = (text: string, len = 15): string => {
    if (!text) return ''
    if (text.length > len) return text.substring(0, len) + '...'
    return text
}

export const dealRealTimeGraphData = (mainTable: any, assistantTable: any) => {
    const edge = [mainTable, assistantTable].map((i, index) => {
        return {
            id: i.name,
            shape: 'er-rect',
            label: i.name,
            width: 150,
            height: 24,
            position: {
                x: 24 + index * 230,
                y: 150,
            },
            ports: i.columnList?.map((k: any) => {
                return {
                    id: k.name,
                    group: 'list',
                    attrs: {
                        portNameLabel: {
                            text: k.name,
                        },
                        portTypeLabel: {
                            text: k.primitiveType,
                        },
                    },
                }
            }),
        }
    })
    const line =
        mainTable.columnList
            ?.filter((j: any) => {
                const c = assistantTable.columnList?.find((k: any) => {
                    return j.name === k.name
                })
                return c?.name
            })
            .map((item: any, index: number) => {
                return {
                    id: index,
                    shape: 'edge',
                    source: {
                        cell: mainTable.name,
                        port: item.name,
                    },
                    target: {
                        cell: assistantTable.name,
                        port: item.name,
                    },
                    attrs: {
                        line: {
                            stroke: '#A2B1E3',
                            strokeWidth: 2,
                        },
                    },
                    zIndex: 0,
                }
            }) ?? []
    return [...edge, ...line]
}

// return a promise
export const copyToClipboard = (textToCopy: string) => {
    // navigator clipboard api needs a secure context (https)
    if (navigator.clipboard && window.isSecureContext) {
        // navigator clipboard api method'
        return navigator.clipboard.writeText(textToCopy)
    } else {
        // text area method
        const textArea = document.createElement('textarea')
        textArea.value = textToCopy
        // make the textarea out of viewport
        textArea.style.position = 'fixed'
        textArea.style.left = '-999999px'
        textArea.style.top = '-999999px'
        document.body.appendChild(textArea)
        textArea.focus()
        textArea.select()
        return new Promise((res, rej) => {
            // here the magic happens
            document.execCommand('copy') ? res(0) : rej()
            textArea.remove()
        })
    }
}

// set value by path for tar property
// notice that the function will change tar, so save one more copy of data before use the function
/**
 *
 * @param path
 * @param tar
 * @param operation
 * @param splitChar
 * @returns
 */
export function operationWithGetValueByPath<T extends Record<string, any>>(
    path: string,
    tar: T,
    operation: (parent: any, key: string, val: any) => void,
    splitChar = '/',
) {
    const reg = new RegExp(`[^${splitChar}]+`, 'g')

    const steps = path.match(reg)

    function traverseStep(target: Record<string, any>) {
        if (steps?.length == 0 || !target || typeof target !== 'object') return target
        if (Array.isArray(target)) {
            const len = target.length
            for (let i = 0; i < len; ++i) {
                traverseStep(target[i])
            }
        } else {
            const key = steps?.shift() || ''
            if (steps?.length === 0) {
                operation(target, key, target[key])
            } else {
                traverseStep(target[key])
            }
            steps?.unshift(key)
        }
    }

    traverseStep(tar)

    return tar
}
/**
 * set the intermediate ellipsis for long sentence
 */
export function midElliptical(sentence: string, limit: number, fill = '...') {
    const size = sentence.length
    const ellTar = size - limit
    if (ellTar <= 0 || limit <= 0) return sentence
    let l = Math.floor(size / 2)
    let r = l
    let moveLeft = true
    while (r - l + 1 < ellTar) {
        if (moveLeft) {
            l--
        } else {
            r++
        }
        moveLeft = !moveLeft
    }
    return sentence.slice(0, l) + fill + sentence.slice(r + 1)
}

/**
 * count value width with fontSize & fontFamily
 */
export function measureValueWidth(value: string, fontSize = 16, fontFamily = 'arial') {
    const font = `${fontSize}px ${fontFamily}`
    const canvas = document.createElement('canvas')
    const context = canvas.getContext('2d') as any
    context.font = font
    const { width } = context?.measureText(value) || {}
    // document.removeChild(canvas)
    return width
}
/*
 * get filter entries for antd table header filter
 * */
export const getFiltersEntries = (obj: { [key: string]: string[] }) => {
    const k: Record<string, string> = {}
    Object.keys(obj).forEach((key) => {
        if (obj[key]?.[0]) {
            k[key] = obj[key]?.[0]
        }
    })
    return k
}

/**
 * The size of the browser window
 * @returns
 */
export const getWinInnerSize = () => {
    const innerWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth
    const innerHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight
    return {
        innerWidth,
        innerHeight,
    }
}

// formatAsCurrency('-1019234801211.2142345678') => '-1,019,234,801,211.2142345678'
export const formatAsCurrency = (arg: string | number) => {
    const num = parseFloat(arg?.toString?.()) || 0
    if (!isNaN(num)) {
        return num.toLocaleString('en-US')
    }
    return arg.toString()
}

const validateRules = [
    (val: any) => {
        const badValues: (undefined | null)[] = [undefined, null]
        return badValues.indexOf(val) === -1
    },
    (val: any) => {
        return typeof val !== 'object'
    },
]

export function splitQueryToPath(
    originPath: string,
    queryDataObj: Record<string, unknown>,
    pickKeys: (keyof typeof queryDataObj)[] | undefined,
) {
    if (typeof originPath !== 'string') {
        return ''
    }
    if (!queryDataObj || typeof queryDataObj !== 'object') {
        return originPath
    }
    if (!Array.isArray(pickKeys)) {
        pickKeys = Object.keys(queryDataObj)
    }
    let queryStr = `?`
    pickKeys.forEach((key) => {
        const valid = validateRules.every((validateFunc) => {
            return validateFunc(queryDataObj[key])
        })
        if (valid) {
            queryStr += `${key}=${queryDataObj[key]}&`
        }
    })

    return originPath + queryStr.slice(0, queryStr.length - 1)
}

export function formatERData(data: AnyObject) {
    if (!data) {
        return {
            nodes: [],
            edges: [],
        }
    }

    const { vertices: nodes, edges } = data
    if (!Array.isArray(nodes) || !Array.isArray(edges)) {
        return { nodes: [], edges: [] }
    }
    const nodeJoinKeyMap = {} as AnyObject

    const ansEdges = edges.reduce((pre, e) => {
        const primaryKeys = e.primaryKeys || []
        const foreignKeys = e.foreignKeys || []
        const from = e.from
        const to = e.to

        if (nodeJoinKeyMap[to] === undefined) {
            nodeJoinKeyMap[to] = {
                pks: new Set(),
                fks: new Set(),
            }
        }
        if (nodeJoinKeyMap[from] === undefined) {
            nodeJoinKeyMap[from] = {
                pks: new Set(),
                fks: new Set(),
            }
        }

        const es =
            primaryKeys.map((pkItem: any, index: number) => {
                const toColStrings = (pkItem.name || '').split('.')
                const fromColStrings = (foreignKeys[index].name || '').split('.')

                const fk = fromColStrings[fromColStrings.length - 1]
                const pk = toColStrings[toColStrings.length - 1]
                nodeJoinKeyMap[to].pks.add(pk)
                nodeJoinKeyMap[from].fks.add(fk)

                const source = { id: e.from, column: fk }
                const target = { id: e.to, column: pk }
                return {
                    source,
                    target,
                }
            }) || []
        pre.push(...es)
        return pre
    }, [])

    const ansNodes = nodes.map((n) => {
        // 校验数据
        if (n.id === undefined || n.id === null) {
            return n
        }
        // primaryKey
        const primaryKeys = nodeJoinKeyMap[n.id] ? nodeJoinKeyMap[n.id].pks : new Set()

        // foreignKeys
        const foreignKeys = nodeJoinKeyMap[n.id] ? nodeJoinKeyMap[n.id].fks : new Set()

        const commondColumnsSetArr = [...new Set([...primaryKeys, ...foreignKeys])]

        // 操作数据
        const dimensionsColumns = (n.dimensionsColumns || []).map((col: any) => {
            const colNameStrs = (col.name || '').split('.')
            const colName = colNameStrs[colNameStrs.length - 1]

            const isPk = primaryKeys.has(colName)
            const isFk = foreignKeys.has(colName)
            const portExtraText = isPk ? 'pk' : isFk ? 'fk' : ''
            return { portText: colName, portExtraText }
        })

        const metricsColumns = (n.metricsColumns || []).map((col: any) => {
            const colNameStrs = (col.name || '').split('.')
            const colName = colNameStrs[colNameStrs.length - 1]

            const isPk = primaryKeys.has(colName)
            const isFk = foreignKeys.has(colName)
            const portExtraText = isPk ? 'pk' : isFk ? 'fk' : ''
            return { portText: colName, portExtraText }
        })

        const commonColumns = commondColumnsSetArr
            .filter((col) => {
                const isDimensions = dimensionsColumns.find((dim: any) => dim.portText === col)
                const isMetrics = metricsColumns.find((met: any) => met.portText === col)
                return !isDimensions && !isMetrics
            })
            .map((col) => {
                const isPk = primaryKeys.has(col)
                const isFk = foreignKeys.has(col)
                const portExtraText = isPk ? 'pk' : isFk ? 'fk' : ''
                return {
                    portText: { portText: col || '', portExtraText: portExtraText },
                }
            })

        const _pks = Array.from(primaryKeys)
        const _fks = Array.from(foreignKeys)
        return {
            id: n.id,
            table: n.table,
            type: n.kind,
            dimensionsColumns,
            metricsColumns,
            commonColumns,
            pks: _pks,
            fks: _fks?.filter((k) => !_pks.includes(k)),
        }
    })
    return {
        nodes: ansNodes,
        edges: ansEdges,
    }
}

export function formatERDataToGLineage(data: AnyObject, isHidePFKInfo?: boolean) {
    const { vertices: initNodes, edges = [] } = data || {}
    const nodes = initNodes ? initNodes : data?.nodes
    if (!Array.isArray(nodes) || !Array.isArray(edges)) {
        return { nodes: [], edges: [] }
    }
    const nodeJoinKeyMap = {} as AnyObject

    const ansEdges = edges.reduce((pre, e) => {
        const primaryKeys = e.primaryKeys || []
        const foreignKeys = e.foreignKeys || []
        const from = e.from
        const to = e.to

        if (nodeJoinKeyMap[to] === undefined) {
            nodeJoinKeyMap[to] = {
                pks: new Set(),
                fks: new Set(),
            }
        }
        if (nodeJoinKeyMap[from] === undefined) {
            nodeJoinKeyMap[from] = {
                pks: new Set(),
                fks: new Set(),
            }
        }

        const es =
            primaryKeys.map((pkItem: any, index: number) => {
                const toColStrs = (pkItem.name || '').split('.')
                const fromColStrs = (foreignKeys[index].name || '').split('.')

                const fk = fromColStrs[fromColStrs.length - 1]
                const pk = toColStrs[toColStrs.length - 1]
                nodeJoinKeyMap[to].pks.add(pk)
                nodeJoinKeyMap[from].fks.add(fk)
                return {
                    source: e.from,
                    sourceKey: fk,
                    target: e.to,
                    targetKey: pk,
                }
            }) || []
        pre.push(...es)
        return pre
    }, [])

    const ansNodes = nodes.map((n) => {
        // 校验数据
        if (n.id === undefined || n.id === null) {
            return n
        }
        // primaryKey
        const primaryKeys = nodeJoinKeyMap[n.id] ? nodeJoinKeyMap[n.id].pks : new Set()

        // foreignKeys
        const foreignKeys = nodeJoinKeyMap[n.id] ? nodeJoinKeyMap[n.id].fks : new Set()

        const commondColumnsSetArr = [...new Set([...primaryKeys, ...foreignKeys])]

        // 操作数据
        const dimensionsColumns = (n.dimensionsColumns || []).map((col: any) => {
            const colNameStrs = (col.name || '').split('.')
            const colName = colNameStrs[colNameStrs.length - 1]

            const isPk = primaryKeys.has(colName)
            const isFk = foreignKeys.has(colName)
            const subInfoText = isPk ? 'pk' : isFk ? 'fk' : ''
            return { name: colName, subInfo: isHidePFKInfo ? '' : subInfoText }
        })

        const metricsColumns = (n.metricsColumns || []).map((col: any) => {
            const colNameStrs = (col.name || '').split('.')
            const colName = colNameStrs[colNameStrs.length - 1]

            const isPk = primaryKeys.has(colName)
            const isFk = foreignKeys.has(colName)
            const subInfoText = isPk ? 'pk' : isFk ? 'fk' : ''
            return { name: colName, subInfo: isHidePFKInfo ? '' : subInfoText }
        })

        const commonColumns = commondColumnsSetArr
            .filter((col) => {
                const isDimensions = dimensionsColumns.find((dim: any) => dim.name === col)
                const isMetrics = metricsColumns.find((met: any) => met.name === col)
                return !isDimensions && !isMetrics
            })
            .map((col) => {
                const isPk = primaryKeys.has(col)
                const isFk = foreignKeys.has(col)
                const subInfoText = isPk ? 'pk' : isFk ? 'fk' : ''
                return {
                    name: col,
                    subInfo: isHidePFKInfo ? '' : subInfoText,
                }
            })
        return {
            id: n.id,
            table: n.table,
            type: n.kind,
            dimensionsColumns,
            metricsColumns,
            commonColumns,
        }
    })
    return {
        nodes: ansNodes,
        edges: ansEdges,
    }
}

export function getDagFromTableData(data: any) {
  const isFromJoinDag = !!data?.dataModelDesc?.joinDag?.vertices?.length
  return isFromJoinDag ? data?.dataModelDesc?.joinDag : data?.dataModelDesc?.unionDagDesc
}

export function sortMvColumns(arr: string[]): string[] {
    if (arr && arr.length > 0) {
        const _arr = [...arr]
        _arr.sort((a: string, b: string) => {
            if (a === 'dt') return 1
            if (b === 'dt') return -1
            return a.localeCompare(b)
        })
        return _arr
    } else {
        return []
    }
}

/**过滤一些敏感词
 * @param list 要过滤的数组
 * @param keyword 要过滤的关键词 默认是pingan 不区大小写
 * @param key 如果是对象数组，指定那个 key 去匹配过滤 默认是 label，如果是 string[] 则不需要设置
 */
export function filterSensitive(list?: any, keyword?: string, key?: string) {
    const _keyword = keyword || 'pingan'
    const _key = key || 'label'
    if (!list || list.length === 0) return []
    if (!_keyword) return []
    const item = list[0]
    if (typeof item === 'string') {
        return list.filter((item: string) => !item.toLowerCase().includes(_keyword))
    }
    if (typeof item === 'object' && !(item instanceof Array)) {
        return list.filter((item: any) => !item[_key].toLowerCase().includes(_keyword))
    }
    return list
}

/**
 * 自动的 format byte
 */
const base_unit = 1024
const one_m = 1024 * 1024
const one_g = base_unit * one_m
const one_t = one_g * base_unit
export function formatByteSmart(num: number) {
    if (!num || num === 0) return '0'
    if (num < base_unit) {
        return num + 'B'
    } else if (num < one_m) {
        return (num / base_unit)?.toFixed(2) + 'KB'
    } else if (num < one_g) {
        return (num / one_m)?.toFixed(2) + 'M'
    } else if (num < one_t) {
        return (num / one_g)?.toFixed(2) + 'G'
    } else {
        return (num / one_t)?.toFixed(2) + 'T'
    }
}

/**
 * get XXX from `xxxx${char}XXX`-str
 * @param str
 * @param char
 * @returns string
 */
export function getCharFinalStr(str: string, char?: string) {
    if (!str || typeof str !== 'string') {
        return ''
    }
    const ch = typeof char === 'string' ? char : '.'
    const chLastIndex = str.lastIndexOf(ch)
    if (chLastIndex === -1) {
        return str
    }

    return str.substring(chLastIndex - ch.length + 2)
}

export function deleteEmptyValueFromObject(object: Record<string, any>) {
    if (!isObject(object)) {
        return object
    }
    return Object.keys(object).reduce((ansObj: Record<string, any>, key: string) => {
        const value = object[key]
        const valueType = Object.prototype.toString.call(value).slice(8, -1).toLocaleLowerCase()
        switch (valueType) {
            case 'array': {
                if (value.length > 0) {
                    ansObj[key] = value
                }
                break
            }
            case 'object': {
                if (Object.keys(value).length > 0) {
                    ansObj[key] = value
                }
                break
            }
            default: {
                if (value) {
                    ansObj[key] = value
                }
            }
        }
        return ansObj
    }, {})
}
export function getMVNameFromDpName(dpName: string, replaceStr?: string) {
    if (!dpName || typeof dpName !== 'string') {
        return replaceStr || '-'
    }
    const matches = dpName.match(/^__dip_.+/)
    if (matches && matches.length > 0) {
        return matches[0].replace('__dip_', '')
    }
    return replaceStr || '-'
}
export function isEmptyValue(originValue: any) {
    const valueType = Object.prototype.toString.call(originValue).slice(8, -1).toLocaleLowerCase()
    switch (valueType) {
        case 'array':
            return originValue.length === 0
        case 'object':
            return Object.keys(originValue).length === 0
        case 'undefined':
        case 'null':
            return true
        case 'string':
            return !originValue
        case 'number':
            return Number.isNaN(originValue)
        case 'boolean':
            return false
    }
}

export function resolveSorter(a: any, b: any) {
    if (a === undefined) {
        return -1
    }
    if (b === undefined) {
        return 1
    }
    const aNum = !Number.isNaN(+a) ? +a : +new Date(a)
    const bNum = !Number.isNaN(+b) ? +b : +new Date(b)
    if (!Number.isNaN(aNum) && !Number.isNaN(bNum)) {
        return aNum - bNum
    }
    return String(a).localeCompare(String(b), undefined, { numeric: true, sensitivity: 'base' })
}

export function getUserInfo() {
    const ucookie = getCookie('u_info')
    const uinfo = ucookie.split('|')
    let name = ''
    let avatar = ''
    let email = ''
    let loginStatus = false
    if (uinfo.length > 1) {
        name = uinfo[0]
        avatar = uinfo[1]
        email = uinfo[2] || ''
        loginStatus = true
    } else {
        loginStatus = false
    }
    return { name, avatar, loginStatus, email, source: uinfo[uinfo.length - 1] || 'account' }
}

export const percent = (v: number, tailCount = 0) => {
    return (v * 100).toFixed(tailCount) + '%'
}

export const streamTableCatalogType = ['kafka']

export function assignValueToOrigin(keys: string[], origin: CascaderOptionType[]) {
    if (keys?.length > 0) {
        const key = keys.shift()
        const havingItem = origin.find((i) => i.value === key)
        const target = havingItem || {
            label: key,
            value: key,
            children: [],
        } as CascaderOptionType
        assignValueToOrigin(keys, target?.children || [])
        if (!havingItem) {
            origin.push(target)
        }
    }
    return origin
}
/**
 * 将strSet ['a.b.c']变成Cascader的option选项
 * @param strSet
 * @param splitCh
 * @returns
 */
export function resolveStrSetToCascader(strSet: string[], splitCh = '.') {
    const length = strSet?.length || 0
    const ans: CascaderOptionType[] = []
    for (let i = 0; i < length; ++i) {
        const strItem = strSet[i] || ''
        const keys = strItem.split(splitCh)
        assignValueToOrigin(keys, ans)
    }
    return ans
}
const supportSqlLanguageList = [
    'sql', // - (default) Standard SQL
    'bigquery', // - GCP BigQuery
    'db2', // - IBM DB2
    'db2i', // - IBM DB2i (experimental)
    'hive', // - Apache Hive
    'mariadb', // - MariaDB
    'mysql', // - MySQL
    'n1ql', // - Couchbase N1QL
    'plsql', // - Oracle PL/SQL
    'postgresql', // - PostgreSQL
    'redshift', // - Amazon Redshift
    'singlestoredb', // - SingleStoreDB
    'snowflake', // - Snowflake
    'spark', // - Spark
    'sqlite', // - SQLite
    'transactsql', // or "tsql" - SQL Server Transact-SQL
    'trino', // - Trino (should also work for Presto, which is very similar dialect, though technically different)
] as const

export const formatSql = (
    sql: string,
    option: FormatOptionsWithLanguage = {},
): { res: string; msg: string; code: number } => {
    for (let i = 0; i < supportSqlLanguageList.length; i++) {
        try {
            const res = format(sql, {
                language: supportSqlLanguageList[i],
                ...option,
            })
            return { res, msg: 'format success', code: 0 }
        } catch (error) {
            console.log(error, 'format error')
        }
    }
    return { res: sql, msg: 'format error', code: 1 }
}

export function assignIDToArray(arr: any[]) {
    if (!Array.isArray(arr)) {
        return []
    }
    return arr.map((item, index) => {
        return {
            ...item,
            id: index,
        }
    })
}

/**
 * 维护sessionStorage的sessionKey的对象
 */
export class SingleSessionManager {
  private sessionKey: string
  constructor(prefix: string) {
    this.sessionKey = `${prefix}_${Math.random().toString(36).substring(2, 13)}`
  }
  clear() {
    sessionStorage.removeItem(this.sessionKey)
  }
  setItem(key: string, value: any) {
    if (key) {
      const target: Record<string, any> = JSON.parse(sessionStorage.getItem(this.sessionKey) || 'null') || {}
      target[key] = value
      sessionStorage.setItem(this.sessionKey, JSON.stringify(target))
    }
  }
  getItem(key: string) {
    const target: Record<string, any> = JSON.parse(sessionStorage.getItem(this.sessionKey) || 'null') || {}
    return target[key]
  }
  removeItem(key: string) {
    const target: Record<string, any> = JSON.parse(sessionStorage.getItem(this.sessionKey) || 'null') || {}
    if (target[key] !== undefined) {
      delete target[key]
    }
    sessionStorage.setItem(this.sessionKey, JSON.stringify(target))
  }
}

/**
 * 根据来源表构建metricmodel数据
 */
export function generateMetricmodelByTable(table: VirtualTableType, dimensions?: MetricModelType['dataModelDesc']['dimensions']) {
  if (table.name) { 
    return {
      dataModelDesc: {
        catalog: '',
        modelType: 'METRIC_MODEL',
        dataModelDescType: table.computeType,
        joinDag: {
          vertices: [
            {
              id: table?.id,
              kind: 'FACT',
              table: `${table.catalogName}.${table.databaseName}.${table.name}`,
              dummy: false,
            },
          ],
          edges: [],
        },
        factTable: `${table.catalogName}.${table.databaseName}.${table.name}`,
        dimensions,
      },
      computeType: table.computeType,
      columns: table.columns || [],
    } as Partial<MetricModelType>
  }
}