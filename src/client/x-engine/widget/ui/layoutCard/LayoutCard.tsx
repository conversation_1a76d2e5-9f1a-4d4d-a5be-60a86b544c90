import { Card, theme, CardProps } from 'antd'
import React, { type ReactNode } from 'react'
import './layoutCard.module.scss'
import ghostCS from './ghostCard.module.scss'
import { merge } from 'lodash-es'

interface LayoutCardType {
  children?: ReactNode[] | ReactNode
  style?: Record<string, string | number>
  isBorderHeadType?: boolean
}

const LayoutCard = (props: LayoutCardType & CardProps) => {
  const {
    token: { padding, margin, borderRadius },
  } = theme.useToken()

  const originStyles: Record<string, Record<string, any>> = {
    style: {
      margin: `${margin}px 0`,
      borderRadius,
    },
    styles: {
      body: {
        margin: 0,
        padding: props.title ? `0 ${padding}px ${padding}px ${padding}px` : padding,
      },
      header: {
        border: 0,
        margin: 0,
        padding,
      },
    },
  }
  if (props.isBorderHeadType) {
    originStyles.styles = {
      body: {
        margin: 0,
        padding,
      },
      header: {
        border: 0,
      },
    }
  }

  merge(originStyles, props)

  return (
    <Card {...props} {...originStyles} className={`xengine-layout-card ${props.className}`}>
      <>{props.children}</>
    </Card>
  )
}

export function GhostCard(props: CardProps) {
  return (
    <Card
      className={ghostCS.ghostCard}
      styles={{
        body: {
          backgroundColor: 'transparent',
          padding: 0,
          borderRadius: 0,
        },
      }}
      {...props}
    >
      {props.children}
    </Card>
  )
}

export default LayoutCard
