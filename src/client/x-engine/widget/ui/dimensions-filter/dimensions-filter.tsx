import React, { useEffect, useRef, useState } from 'react'
import { Button, Form, FormProps, Input, Popconfirm, Space } from 'antd'
import './style.scss'
import { CloseOutlined, PlusOutlined } from '@ant-design/icons'
import { FormInstance } from 'antd/es/form'
import { conditionOptions } from './conf'

interface PropsType {
  width?: number
  placeholder?: string
  title?: string
  form?: FormInstance
  formItems?: React.ReactNode
  formProps?: FormProps
  onChange?: (d: any) => void
}

export default function DimensionsFilter(props: PropsType) {
  const [open, setOpen] = useState<boolean>(false)

  const [formInner] = Form.useForm()

  const {
    placeholder = '添加维度',
    width = 200,
    title = '',
    form,
    formItems,
    formProps = { layout: 'vertical' },
    onChange,
  } = props

  const formInstance = form || formInner

  const updateIndex = useRef<number>(-1)

  const [list, setList] = useState<any>([])

  const formatData = (d: string, key?: string) => {
    const str = String(d)
    switch (key) {
      case 'relative': {
        return ''
      }
      case 'condition': {
        const item = conditionOptions.filter((item) => item.value === str)
        return item[0].label
      }
      case 'dimension': {
        return str && str.replaceAll(',', '.')
      }
      default: {
        return str && str.split(',').filter((item) => item)
      }
    }
  }

  const confirmHandle = async (): Promise<any> => {
    const data = await formInstance.validateFields()
    if (data) {
      const currentList = [...list]
      const message = Object.keys(data)
        .map((item: string) => formatData(data[item], item))
        .join(' ')

      if (updateIndex.current > -1) {
        currentList.splice(updateIndex.current, 1, { ...data, message })
      } else {
        currentList.push({ ...data, message })
      }
      setList([...currentList])

      return Promise.resolve(null)
    } else {
      return Promise.reject()
    }
  }

  const updateItem = (d: any, index: number) => {
    if (d) {
      setOpen(true)
      formInstance.setFieldsValue(d)
      updateIndex.current = index
    }
  }

  const deleteItem = (index: number) => {
    const currentList = [...list]
    currentList.splice(index, 1)
    setList([...currentList])
  }

  useEffect(() => {
    if (!open) {
      formInstance.resetFields()
      updateIndex.current = -1
    }
  }, [open])

  useEffect(() => {
    onChange && onChange(list)
  }, [list])

  return (
    <div className="dimensions-list">
      <Popconfirm
        open={open}
        title={title}
        zIndex={1000}
        icon={false}
        destroyTooltipOnHide
        onOpenChange={(e) => !e && setOpen(e)}
        description={() => (
          <div style={{ width }}>
            <Form {...formProps} form={formInstance}>
              {formItems}
            </Form>
          </div>
        )}
        placement="right"
        okText="保存"
        onConfirm={confirmHandle}
      >
        {list.map((item: any, index: number) => (
          <Space.Compact block style={{ marginBottom: 8 }} key={index}>
            <Input readOnly value={item.message} onClick={() => updateItem(item, index)} />
            <Button icon={<CloseOutlined />} onClick={() => deleteItem(index)} />
          </Space.Compact>
        ))}
        <Button
          type="dashed"
          icon={<PlusOutlined />}
          block
          onClick={() => {
            setOpen(true)
          }}
        >
          {placeholder}
        </Button>
      </Popconfirm>
    </div>
  )
}
