/**
 * @class ChartsCard
 */
import React from 'react'
import { Spin } from 'antd'
import { getUnitId } from '@libs'
import { Charts as charts } from './charts'

import './ChartsCard.css'

interface Props {
    data?: string[]
}

const CARD_CONTAINER = 'wgt-charts-card'
const CARD_ITEMS = 'card-item'

/**
 * 渲染charts
 * @param data 数据
 * @param id 容器id
 */
const renderCharts = (data: any): any => {
    const html: any = []

    if (!data || data.length === 0) {
        return (
            <div>
                <Spin />
            </div>
        )
    }

    data.map((v: string[]) => {
        const cardId = `${CARD_ITEMS}-${getUnitId()}`
        html.push(
            <div id={cardId} className={CARD_ITEMS}>
                {charts(v)}
            </div>,
        )
    })
    return html
}

const ChartsCard = (props: Props) => {
    const { data = '' } = props

    return (
        <>
            <section id={CARD_CONTAINER} className={CARD_CONTAINER}>
                {renderCharts(data)}
            </section>
        </>
    )
}

export default ChartsCard
