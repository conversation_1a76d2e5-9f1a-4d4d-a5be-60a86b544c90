// @ts-nocheck
/**
 * @module Search
 * <AUTHOR>
 * @interface
 */
import React, { useEffect } from 'react'
import { Form, Layout, theme, Table, type TableProps, type FormProps} from 'antd'
import formNode from './formNode'
import { Broadcast, getUnitId } from '@libs'
import { useAntdTable, useUpdateEffect } from 'ahooks'
import LayoutCard from '@ui/layoutCard/LayoutCard'
import cs from './form.module.scss'
import { pickBy } from 'lodash'

type SearchProps = {
  bRadius?: any
  items?: any[]
  searchBeforeTransform?: (data: any) => any // 搜索前可以动态修改请求参数
  showSearch?: boolean
  DBSelect?: boolean
  onChange?: (data: any) => void
  onFinish?: (data: any) => void
  onClick?: (e: any, data: any) => void
  title?: string
  extra?: any
  setFieldsValue?: any
  showTableWithCard?: boolean
  table?: {
    columns: object[]
    rowKey: TableProps['rowKey']
    api: any
    autoLoad?: boolean
    loading?: boolean
    initParams?: {
      [key: string]: any
    }
    showHeader?: boolean
    scroll?: object
    onRow?: object
    refreshTrigger?: number
  }
  initialValues?: Record<string, any>
  reload?: boolean
} & FormProps

const Search = (props: SearchProps) => {
  const [form] = Form.useForm()
  const {
    items,
    onChange,
    onFinish,
    onClick,
    table,
    DBSelect,
    showSearch = true,
    reload,
    title,
    extra,
    bRadius = `auto`,
    setFieldsValue,
    showTableWithCard = true,
    ...formProps
  } = props

  const BROADCASTID = `@FORM_NODE_TRIGGER_${getUnitId()}`
  const {
    token: { colorBgContainer, padding, borderRadius },
  } = theme.useToken()
  const {
    tableProps,
    run: loadTable,
    refresh: tableRefresh,
    search,
  } = useAntdTable(
    (arg = {}, formData = {}) => {
      delete arg.extra
      return table?.api(
        pickBy(Object.assign(arg, formData), (v) => {
          if (typeof v === 'number') {
            return true
          }
          return Boolean(v)
        }),
      )
    },
    {
      form,
      manual: true,
      defaultCurrent: 1,
      defaultPageSize: 10,
    },
  )
  const reloadTable = (): void => {
    search.submit()
  }

  Broadcast.listen(`${BROADCASTID}`, (res: { event: any; data: any }) => {
    const { event, data } = res
    if (typeof onClick === 'function') {
      onClick(event, data)
    }

    if (event?.currentTarget?.name === 'reset') {
      form.resetFields()
    }
  })

  const onFormChange = (event: any) => {
    onChange && onChange(event)
  }

  // trigger table refresh with last request arguments
  useUpdateEffect(() => {
    return tableRefresh()
  }, [table?.refreshTrigger])

  /**
   * onSubmit
   * @description If the property contains table, the api request for the table is run, and if onFinish exists, the method is dropped
   */
  const onSubmit = () => {
    if (typeof onFinish === 'function') {
      onFinish(form)
    }

    if (table) {
      reloadTable()
    }
  }

  /**
   * use effect
   */
  useEffect(() => {
    form.setFieldsValue(setFieldsValue)
    const { autoLoad = true } = table
    autoLoad && search.submit()
  }, [reload, table?.autoLoad, setFieldsValue])

  const tableComponent = (
    <Table tableLayout="fixed" columns={table?.columns} rowKey={table?.rowKey} {...tableProps} {...table} />
  )

  return (
    <>
      {showSearch && (
        <Layout
          style={{
            background: colorBgContainer,
            padding,
            borderRadius: bRadius.replace(/auto/gi, borderRadius + 'px'),
            marginBottom: padding,
          }}
        >
          <Form
            form={form}
            className={cs.search}
            onFinish={onSubmit}
            onValuesChange={onFormChange}
            layout={'vertical'}
            labelCol={{ span: 24 }}
            {...formProps}
          >
            {formNode(items, {
              DBSelect,
              broadcastId: BROADCASTID,
            })}
          </Form>
        </Layout>
      )}
      {showTableWithCard ? (
        <LayoutCard title={title} extra={extra}>
          {table && tableComponent}
        </LayoutCard>
      ) : (
        tableComponent
      )}
    </>
  )
}

export default Search
