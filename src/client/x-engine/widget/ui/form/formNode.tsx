// @ts-nocheck
/**
 * @module formNode
 * <AUTHOR>
 */
import { MouseEventHandler } from 'react'
import { DatePicker, Form, Input, InputNumber, Select, Button, Space, Row, Col, Typography, Mentions } from 'antd'
import { Broadcast } from '@libs'
import DBSelect from '@model/DBSelect'
import cs from './form.module.scss'
const { Title } = Typography
const { RangePicker } = DatePicker

interface OptProps {
    DBSelect?: boolean
    colSpan?: number
    broadcastId: string
    required?: any
}

const formNode = (items: any[], opts: OptProps) => {
    const html: JSX.Element[] = []
    const [form] = Form.useForm()
    const { colSpan = 6, broadcastId, required } = opts

    const onClick = (e: MouseEventHandler, data: any) => {
        Broadcast.trigger(`${broadcastId}`, {
            event: e,
            data,
        })
    }

    const renderTag = (_items: any[]) => {
        const tagHtml: JSX.Element[] = []
        _items?.map((value: any, index: number) => {
            const tag = value?.tag
            switch (tag) {
                case 'Input':
                    tagHtml.push(
                        <Col span={colSpan} key={index}>
                            <Form.Item {...value}>
                                <Input {...value} autoComplete='off'/>
                            </Form.Item>
                        </Col>,
                    )
                    break
                case 'InputNumber':
                    tagHtml.push(
                        <Col span={colSpan} key={index}>
                            <Form.Item {...value}>
                                <InputNumber {...value} style={{ width: '100%' }} autoComplete='off' />
                            </Form.Item>
                        </Col>,
                    )
                    break
                case 'Select':
                    tagHtml.push(
                        <Col span={colSpan} key={index}>
                            <Form.Item {...value}>
                                <Select {...value} />
                            </Form.Item>
                        </Col>,
                    )
                    break
                case 'DatePicker':
                    tagHtml.push(
                        <Col span={colSpan} key={index}>
                            <Form.Item {...value}>
                                <DatePicker {...value} style={{ width: '100%' }} />
                            </Form.Item>
                        </Col>,
                    )
                    break
                case 'RangePicker':
                    tagHtml.push(
                        <Col span={colSpan} key={index}>
                            <Form.Item {...value}>
                                <RangePicker {...value} style={{ width: '100%' }} />
                            </Form.Item>
                        </Col>,
                    )
                    break
                case 'DateRangePicker':
                    tagHtml.push(
                        <Col span={colSpan} key={index}>
                            <Form.Item {...value}>
                                <DatePicker.RangePicker {...value} />
                            </Form.Item>
                        </Col>,
                    )
                    break
                case 'Title':
                    tagHtml.push(
                        <Col span={24} offset={0} key={index}>
                            <Title level={value.level}>{value.lebal}</Title>
                        </Col>,
                    )
                    break
                case 'Mentions':
                    tagHtml.push(
                        <Col span={24} offset={0} key={index}>
                            <Form.Item {...value}>
                                <Mentions {...value} style={{ width: '100%' }} />
                            </Form.Item>
                        </Col>,
                    )
                    break
                case 'Group':
                    tagHtml.push(
                        <Col span={colSpan} key={index}>
                            <Form.Item {...value}>
                                <Input.Group compact {...value} style={{ display: 'flex' }}>
                                    {value?.children.map((v: any) => {
                                        return v.tag === 'Select' ? (
                                            <Form.Item {...v} style={{ flex: 1 }} noStyle={true}>
                                                <Select {...v}></Select>
                                            </Form.Item>
                                        ) : (
                                            <Form.Item {...v} style={{ flex: 1 }} noStyle={true}>
                                                <Input {...v} />
                                            </Form.Item>
                                        )
                                    })}
                                </Input.Group>
                            </Form.Item>
                        </Col>,
                    )
                    break
                case 'buttons':
                    html.push(
                        <Col span={12} offset={12} key={index}>
                            <Form.Item {...value} className={cs.searchButton}>
                                <Space {...value.space}>
                                    {value?.children.map((v: { label: any; name: any }, i: number) => {
                                        return (
                                            <Button {...v} key={i} onClick={(e: any) => onClick(e, v)}>
                                                {v.label}
                                            </Button>
                                        )
                                    })}
                                </Space>
                            </Form.Item>
                        </Col>,
                    )
                    break
                default:
                    break
            }
        })
        return tagHtml
    }

    html.unshift(
        <>
            <Row gutter={[16, 0]}>
                {opts?.DBSelect && <DBSelect span={colSpan} setFieldValue={form.setFieldValue} required={required} />}
                {renderTag(items || [])}
            </Row>
        </>,
    )
    return html
}

export default formNode
