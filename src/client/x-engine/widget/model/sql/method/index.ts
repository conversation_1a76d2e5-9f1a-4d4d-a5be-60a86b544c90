// @ts-nocheck
import dayjs from 'dayjs'
import * as monaco from 'monaco-editor'

export const getRandomString = (): string => Math.random().toString(32).slice(2) + new Date().getTime()
export const getTime = (date?: number) => {
  return dayjs(date).format('YYYY/M/D H:m:s')
}

const documentStartPosition = new monaco.Position(1, 1)

const findPreElStartPositionFromPosition = (editor: monaco.editor.IEditor, position, el) => {
  const preRange = editor.getModel().findPreviousMatch(el, position, false, true, null, true)
  const startPosition = preRange && preRange.range.getStartPosition()
  let SQLStartPosition

  if (startPosition && startPosition.isBefore(position)) {
    SQLStartPosition = startPosition
  } else {
    SQLStartPosition = documentStartPosition
  }
  return SQLStartPosition
}
const findAfterElEndPositionFromPosition = (editor: monaco.editor.IEditor, position, el) => {
  const documentEndPosition = new monaco.Position(editor.getModel().getLineCount() + 1, 1)

  const nextRange = editor.getModel().findNextMatch(el, position, false, true, null, true)
  const endPosition = nextRange && nextRange.range.getEndPosition()
  let SQLEndPosition

  if (endPosition && position.isBefore(endPosition)) {
    SQLEndPosition = endPosition
  } else {
    SQLEndPosition = documentEndPosition
  }
  return SQLEndPosition
}

const getRange = (startPosition, endPosition) => {
  return new monaco.Range(startPosition.lineNumber, startPosition.column, endPosition.lineNumber, endPosition.column)
}

export const newGetCursorPositionValue = (editor: monaco.editor.IEditor) => {
  const cursorPosition = editor.getPosition()
  let startPosition = findPreElStartPositionFromPosition(editor, cursorPosition, ';')
  const endPosition = findAfterElEndPositionFromPosition(editor, cursorPosition, ';')
  let time = 100
  let range = getRange(startPosition, endPosition)
  while (
    editor.getModel().getValueInRange(range).replace(';', '').trim() === '' &&
    documentStartPosition.isBefore(startPosition) &&
    time > 0
  ) {
    range = getRange(startPosition, endPosition)
    if (editor.getModel().getValueInRange(range)?.trim() === ';') {
      startPosition = findPreElStartPositionFromPosition(editor, startPosition, ';')
      range = getRange(startPosition, endPosition)
    }
    time--
  }
  return editor.getModel().getValueInRange(range).replace(';', '')
}

export const findItemById = (tree, id) => {
  let res
  const recursiveGetData = (root, id) => {
    if (!root) {
      return
    }
    for (let i = 0; i < root.length; i++) {
      const cur = root[i]
      if (cur.id === id) {
        return (res = cur)
      } else {
        recursiveGetData(cur.children, id)
      }
    }
  }
  recursiveGetData(tree, id)
  return res
}

export const findItemByIdAndDeep = (tree, id, deep = 0) => {
  let res
  const recursiveGetData = (root, id, recursiveDeep) => {
    if (!root) {
      return
    }
    for (let i = 0; i < root.length; i++) {
      const cur = root[i]
      if (cur.id === id && deep === recursiveDeep) {
        return (res = cur)
      } else {
        recursiveGetData(cur.children, id, recursiveDeep + 1)
      }
    }
  }
  recursiveGetData(tree, id, 0)
  return res
}

export const findItemByName = (tree, name) => {
  let res
  const recursiveGetData = (root, name) => {
    if (!root) {
      return
    }
    for (let i = 0; i < root.length; i++) {
      const cur = root[i]
      if (cur.name === name) {
        return (res = cur)
      } else {
        recursiveGetData(cur.children, name)
      }
    }
  }
  recursiveGetData(tree, name)
  return res
}

export const supplementCode = (editor, text: string) => {
  const lineCount = editor.getModel().getLineCount() + 1 // append to next line
  editor.executeEdits('insert-code', [
    {
      range: {
        startLineNumber: lineCount,
        startColumn: 1,
        endLineNumber: lineCount,
        endColumn: 1,
      },
      text,
    },
  ])
}

export const insertAtCursor = (editor, text: string) => {
  const cursorPosition = editor.getPosition()
  editor.executeEdits('insert-code', [
    {
      range: new monaco.Range(
        cursorPosition.lineNumber,
        cursorPosition.column,
        cursorPosition.lineNumber,
        cursorPosition.column,
      ),
      text,
    },
  ])
}
