// @ts-nocheck
import React, { useRef, useEffect, useState } from 'react'
import { debounce } from 'lodash-es'

import * as monaco from 'monaco-editor'
import editorWorker from 'monaco-editor/esm/vs/editor/editor.worker?worker'
import { language } from 'monaco-editor/esm/vs/basic-languages/sql/sql'
import { Button, Col, Dropdown, Form, Row, Select, message, Space, Tooltip } from 'antd'
import { DownOutlined, ClearOutlined, PauseCircleOutlined, QuestionCircleOutlined } from '@ant-design/icons'
import { Api } from '@api'
import { lepg37n78po, p27g9r7bjso } from '@constant/testId'
import UIDivider from '@ui/divider/UIDivider'
import { useRequest } from 'ahooks'
import { formatSql } from '@libs/util'
import { newGetCursorPositionValue } from './method'
import styles from './sql.module.scss'

export enum IMenuItemsKeyEnum {
  runCurrentSQL = 'runCurrentSQL',
  runAllSQL = 'runAllSQL',
  runSelectedSQL = 'runSelectedSQL',
}

export type IMenuItemsKey = keyof typeof IMenuItemsKeyEnum
export type IMenuItems = { label: string; key: IMenuItemsKey }[]
const menuItems: IMenuItems = [
  // @LiuXingYin @LinYuZhi hide runCurrentSQL button.
  // {
  //     label: '运行当前 SQL',
  //     key: 'runCurrentSQL',
  // },
  {
    label: '运行全部 SQL',
    key: 'runAllSQL',
  },
  {
    label: '运行选中的 SQL',
    key: 'runSelectedSQL',
  },
]

const { keywords } = language

// eslint-disable-next-line no-empty-pattern
self.MonacoEnvironment = {
  getWorker() {
    return new editorWorker()
  },
}

const SORT_TEXT = {
  catalog: '0',
  database: '1',
  Table: '2',
  Column: '3',
  Keyword: '4',
}

function renderKeyword(keyword: string) {
  return {
    label: keyword,
    kind: monaco.languages.CompletionItemKind.Keyword,
    detail: '',
    sortText: SORT_TEXT.Keyword,
    insertText: keyword,
  }
}

function renderModule(name: string, detail: string) {
  return {
    label: name,
    kind: monaco.languages.CompletionItemKind.Module,
    detail: detail ?? '<table>',
    sortText: SORT_TEXT.Table,
    insertText: name,
  }
}

function renderTableColumn(table: string, column: { name: string; type: string }) {
  return {
    label: column.name,
    kind: monaco.languages.CompletionItemKind.Field,
    detail: '<column> ' + column.type + ' ' + table,
    sortText: SORT_TEXT.Column,
    insertText: column.name,
  }
}

export interface ISearchContent {
  str: string
  className?: string
}

export interface IEditor {
  className?: string
  editor: monaco.editor.IStandaloneCodeEditor | null
  setEditor: (e: monaco.editor.IStandaloneCodeEditor) => void
  onRunCurrentSQL?: (v: string) => void
  onSQLUpdate?: (v: string) => void // sql update
  onRunAllSQL?: (v: string) => void //  run all sql
  onRunSelectedSQL?: (v: string) => void //  run selected sql
  showFormatBtn?: boolean // show format button
  showEngineSelect?: boolean // show engine select
  showRunBtn?: boolean // show run button
  readOnly?: boolean
  autoFocus?: boolean
  height?: number
  placeholder?: React.ReactNode
  loading?: boolean
  searchContentArr?: ISearchContent[]
  queryEngine?: string
  onQueryEngineChange?: (a: string) => void
}

// 替换原来的扁平队列结构为层级结构
// Map<catalogName, {type: string, databases: Map<databaseName, {tables: Map<tableName, {columns: Array<{name, type}>}>}>}>
const catalogData = new Map()
// 保留原始队列用于兼容
const catalogQueue: string[] = [] // 'name'
const databaseQueue: string[] = [] // 'name'
const tableQueue: string[] = [] // 'name'
const columnQueue: { name: string; columnType: string; tableName: string }[] = [] // {name: 'name', columnType: 'kkkk', tableName: 'tableName' }

function taskDistribute(catalog?: string, database?: string, type?: string) {
  if (database?.length) {
    return getTable(catalog, database, type)
  } else if (catalog?.length) {
    return getDatabase(catalog, type)
  } else {
    return getCatalog()
  }
}

const init = taskDistribute.bind(null)

const requestQueue = [init]

function getCatalog() {
  return Api.apiEngineV1CatalogListGet({
    current: 1,
    pageSize: -1,
  })
    .then((res) => {
      res.forEach(({ name: catalog, type }) => {
        requestQueue.push(taskDistribute.bind(null, catalog, undefined, type))
        catalogQueue.push(catalog)
        // 添加到层级结构
        catalogData.set(catalog, { type, databases: new Map() })
      })
      return res
    })
    .catch(() => {
      return Api.apiEngineV1CatalogListGet({
        current: 1,
        pageSize: -1,
      }).then((res) => {
        res.forEach(({ name: catalog, type }) => {
          requestQueue.push(taskDistribute.bind(null, catalog, undefined, type))
          catalogQueue.push(catalog)
          // 添加到层级结构
          catalogData.set(catalog, { type, databases: new Map() })
        })
        return res
      })
    })
}

function getDatabase(catalog: string, type: string) {
  return Api.apiEngineV1DatabaseListGet({
    catalog,
    current: 1,
    pageSize: -1,
  }).then((res) => {
    res.forEach(({ name: database }) => {
      requestQueue.push(taskDistribute.bind(null, catalog, database, type))
      databaseQueue.push(database)

      // 添加到层级结构
      const catalogEntry = catalogData.get(catalog)
      if (catalogEntry) {
        catalogEntry.databases.set(database, { tables: new Map() })
      }
    })
    return res
  })
}

function getTable(catalog: string, database: string, type: string) {
  // 对于外部数据源的处理，这里只能拿到表的名称
  if (type !== 'INTERNAL') {
    return Api.apiEngineV1DatasourceTableListGet({
      catalog,
      database,
      current: 1,
      pageSize: -1,
    })
      .then((res) => {
        res.list.forEach((item: string) => {
          const tableName = item.split('.').pop()
          tableQueue.push(tableName)

          // 添加到层级结构
          const catalogEntry = catalogData.get(catalog)
          if (catalogEntry) {
            const databaseEntry = catalogEntry.databases.get(database)
            if (databaseEntry) {
              databaseEntry.tables.set(tableName, { columns: [] })
            }
          }
        })
        return res.list
      })
      .catch((error) => {
        console.warn(`Failed to get tables for catalog ${catalog}, database ${database}:`, error)
        return { list: [] }
      })
  }

  return Api.apiEngineV1VtableListGet({
    catalog,
    database,
    current: 1,
    pageSize: -1,
  })
    .then((res) => {
      res.list.forEach(({ columns, name: tableName }) => {
        tableQueue.push(tableName)

        // 添加到层级结构
        const catalogEntry = catalogData.get(catalog)
        if (catalogEntry) {
          const databaseEntry = catalogEntry.databases.get(database)
          if (databaseEntry) {
            const tableColumns = []

            columns.forEach(({ columnType, name }) => {
              columnQueue.push({
                columnType,
                name,
                tableName,
              })

              // 添加到层级结构
              tableColumns.push({ name, type: columnType })
            })

            databaseEntry.tables.set(tableName, { columns: tableColumns })
          }
        }
      })
      return res.list
    })
    .catch((error) => {
      console.warn(`Failed to get tables for catalog ${catalog}, database ${database}:`, error)
      return { list: [] }
    })
}

//  max need smaller than max or equal to max
function requestWithMaxConcurrent(queue: Promise<any>[], max: number) {
  const result: any[] = []
  let ranCount = 0

  function recursive(queue: Promise<any>[], index: number) {
    Promise.resolve(queue[index]()).then((res) => {
      result[index] = res
      if (ranCount >= queue.length) {
        return
      }
      recursive(queue, ranCount++)
    })
  }

  for (let i = 0; i < max && i < queue.length; i++) {
    recursive(queue, i)
    ranCount++
  }
  return result
}

requestWithMaxConcurrent(requestQueue, 1)

monaco.languages.registerCompletionItemProvider('sql', {
  triggerCharacters: [' ', '.'],
  provideCompletionItems(model, position) {
    const { lineNumber, column } = position
    const textBeforePointer = model.getValueInRange({
      startLineNumber: lineNumber,
      startColumn: 0,
      endLineNumber: lineNumber,
      endColumn: column,
    })
    const tokens = textBeforePointer.trim().split(/\s+/)
    const lastToken = tokens[tokens.length - 1].toLowerCase()

    // 处理分层自动完成
    if (lastToken.includes('.')) {
      const parts = lastToken.split('.')

      // 情况: catalog.database.
      if (parts.length === 3 && parts[2] === '') {
        const catalogName = parts[0]
        const databaseName = parts[1]

        const catalogEntry = catalogData.get(catalogName)
        if (catalogEntry) {
          const databaseEntry = catalogEntry.databases.get(databaseName)
          if (databaseEntry) {
            // 返回此catalog.database下的表
            const tableSuggestions = Array.from(databaseEntry.tables.keys()).map((name) =>
              renderModule(name, '<Table>'),
            )
            return { suggestions: tableSuggestions }
          }
        }
        return { suggestions: [] }
      }

      // 情况: catalog.
      if (parts.length === 2 && parts[1] === '') {
        const catalogName = parts[0]

        const catalogEntry = catalogData.get(catalogName)
        if (catalogEntry) {
          // 返回此catalog下的数据库
          const databaseSuggestions = Array.from(catalogEntry.databases.keys()).map((name) =>
            renderModule(name, '<Database>'),
          )
          return { suggestions: databaseSuggestions }
        }
        return { suggestions: [] }
      }

      // 情况: catalog.database.table.
      if (parts.length === 4 && parts[3] === '') {
        const catalogName = parts[0]
        const databaseName = parts[1]
        const tableName = parts[2]

        const catalogEntry = catalogData.get(catalogName)
        if (catalogEntry) {
          const databaseEntry = catalogEntry.databases.get(databaseName)
          if (databaseEntry) {
            const tableEntry = databaseEntry.tables.get(tableName)
            if (tableEntry) {
              // 返回此catalog.database.table下的列
              const columnSuggestions = tableEntry.columns.map((column) =>
                renderTableColumn(tableName, { name: column.name, type: column.type }),
              )
              return { suggestions: columnSuggestions }
            }
          }
        }
        return { suggestions: [] }
      }
    }

    // 默认的非层级补全情况
    const catalogSuggestions = Array.from(catalogData.keys()).map((name) => renderModule(name, '<Catalog>'))

    const databaseSuggestions = databaseQueue.map((name) => renderModule(name, '<Database>'))
    const tableSuggestions = tableQueue.map((name) => renderModule(name, '<Table>'))
    const columnSuggestions = columnQueue.map((i) =>
      renderTableColumn(i.tableName, {
        name: i.name,
        type: i.columnType,
      }),
    )

    if (lastToken === 'from') {
      return {
        suggestions: [...catalogSuggestions, ...databaseSuggestions, ...tableSuggestions],
      }
    } else if (lastToken === 'select') {
      return {
        suggestions: [...columnSuggestions],
      }
    }
    return {
      suggestions: [
        ...catalogSuggestions,
        ...databaseSuggestions,
        ...tableSuggestions,
        ...keywords.map(renderKeyword),
        ...columnSuggestions,
      ],
    }
  },
})

export function Editor({
  className,
  editor,
  setEditor,
  onRunCurrentSQL,
  onRunAllSQL,
  onRunSelectedSQL,
  onSQLUpdate,
  showFormatBtn,
  showRunBtn,
  showEngineSelect,
  readOnly = false,
  height,
  placeholder,
  autoFocus,
  loading = false,
  searchContentArr,
  queryEngine,
  onQueryEngineChange,
}: IEditor) {
  const changeEvent = useRef()

  const [editorNoContent, setEditorNoContent] = useState(true)
  const [selectedSQLContent, setSelectedSQLContent] = useState('')
  useEffect(() => {
    changeEvent.current?.dispose?.()
    changeEvent.current = editor?.onDidChangeModelContent(function () {
      const sqlContent = editor.getValue()
      setEditorNoContent(sqlContent?.trim()?.length === 0)
      onSQLUpdate?.(sqlContent)
    })
    setEditorNoContent(editor?.getValue().trim()?.length === 0)
  }, [editor, onSQLUpdate])

  const monacoEl = useRef(null)

  const [selectedMenuKey, setSelectedMenuKey] = useState<IMenuItemsKey>(IMenuItemsKeyEnum.runAllSQL)

  const preSelectedMenuKey = useRef<IMenuItemsKey>(IMenuItemsKeyEnum.runAllSQL)
  const time = useRef<number>(0)

  const currentCancelRef = useRef<() => void>()

  const [cancelLoading, setCancelLoading] = useState(false)

  useEffect(() => {
    let temp
    if (monacoEl.current && !editor && time.current === 0) {
      time.current++
      temp = monaco.editor?.create?.(monacoEl.current!, {
        value: '',
        language: 'sql',
        minimap: {
          enabled: false,
        },
        readOnly: readOnly,
        renderLineHighlight: 'none',
        automaticLayout: true,
        scrollBeyondLastLine: false,
        scrollbar: {
          verticalScrollbarSize: 8,
          horizontalScrollbarSize: 8,
          alwaysConsumeMouseWheel: false,
        },
      })

      setEditor(temp)
    }
    const k = temp || editor
    k?.onDidBlurEditorWidget(() => {
      showPlaceholder(k.getValue())
    })

    k?.onDidFocusEditorWidget(() => {
      hidePlaceholder()
    })
    const dealSelectedSQL = () => {
      const str = k.getModel().getValueInRange(k.getSelection())
      if (str.length) {
        if (selectedMenuKey !== IMenuItemsKeyEnum.runSelectedSQL) {
          // save  preSelectedMenuKey for mouseup reset btn
          preSelectedMenuKey.current = selectedMenuKey
        }
        setSelectedMenuKey(IMenuItemsKeyEnum.runSelectedSQL)
      } else {
        setSelectedMenuKey(preSelectedMenuKey.current)
      }
      setSelectedSQLContent(str)
    }

    k?.onKeyUp(dealSelectedSQL)
    k?.onMouseUp(dealSelectedSQL)
  }, [monacoEl, setEditor, editor, setSelectedSQLContent, selectedMenuKey, preSelectedMenuKey, readOnly])

  // highlight string in editor
  useEffect(() => {
    if (editor && searchContentArr?.length) {
      searchContentArr.forEach(({ str, className }) => {
        const res = editor.getModel().findMatches(str, true, false, true, null, true)
        res.forEach(({ range }) => {
          editor.createDecorationsCollection([
            {
              range,
              options: { inlineClassName: className || styles.defaultHighlight },
            },
          ])
        })
      })
    }
  }, [editor, searchContentArr])

  // deal autofocus and placeholder
  useEffect(() => {
    if (editor) {
      if (autoFocus === true) {
        editor.focus()
      } else if (placeholder) {
        showPlaceholder(editor.getValue())
      }
    }
  }, [placeholder, editor, autoFocus])

  useEffect(() => {
    return () => {
      editor?.dispose()
    }
  }, [editor])
  // autoLayout
  useEffect(() => {
    const layout = () => {
      if (editor) {
        editor.layout()
      }
    }

    const debounedLayout = debounce(layout, 200)

    window.addEventListener('resize', debounedLayout)

    return () => {
      window.removeEventListener('resize', debounedLayout)
    }
  }, [editor])

  // define a document formatting provider
  // then you contextmenu will add an "Format Document" action
  monaco.languages.registerDocumentFormattingEditProvider('sql', {
    provideDocumentFormattingEdits(model) {
      const { res: formatted, code } = formatSql(model.getValue())
      if (code === 0) {
        return [
          {
            range: model.getFullModelRange(),
            text: formatted,
          },
        ]
      } else {
        message.error('暂不支持格式化当前方言SQL')
      }
    },
  })

  const placeholderRef = useRef<HTMLDivElement>()

  function showPlaceholder(value: string) {
    if (value === '') {
      placeholderRef.current.style.display = 'initial'
    }
  }

  function hidePlaceholder() {
    placeholderRef.current.style.display = 'none'
  }
  const { data: engineList } = useRequest(Api.apiEngineV1EditorListQueryEnginesGet)
  return (
    <div className={className}>
      <Row
        className={`${styles.editorOperateCon} ${!showRunBtn && !showFormatBtn && styles.notShow}`}
        justify={'space-between'}
      >
        <Col flex={1}>
          {showRunBtn && (
            <Space>
              <Tooltip title="点击暂停当前运行的SQL（支持暂停的SQL类型：DQL、DML）">
                <PauseCircleOutlined
                  spin={cancelLoading}
                  className="cursor-pointer align-middle text-xl"
                  onClick={async () => {
                    const cancel = currentCancelRef.current
                    setCancelLoading(true)
                    if (loading && cancel) {
                      await Promise.resolve(cancel()).catch(() => setCancelLoading(false))
                    }
                    setCancelLoading(false)
                  }}
                />
              </Tooltip>
              <Dropdown.Button
                disabled={editorNoContent}
                icon={<DownOutlined />}
                loading={loading}
                type={'primary'}
                size={'middle'}
                className={lepg37n78po}
                menu={{
                  selectable: true,
                  items: menuItems,
                  selectedKeys: [selectedMenuKey],
                  defaultSelectedKeys: [IMenuItemsKeyEnum.runAllSQL],
                  onClick(e) {
                    setSelectedMenuKey(e.key)
                  },
                }}
                onClick={() => {
                  switch (selectedMenuKey) {
                    case IMenuItemsKeyEnum.runCurrentSQL:
                      currentCancelRef.current = onRunCurrentSQL?.(newGetCursorPositionValue(editor))
                      break
                    case IMenuItemsKeyEnum.runAllSQL:
                      currentCancelRef.current = onRunAllSQL?.(editor.getValue())
                      break
                    case IMenuItemsKeyEnum.runSelectedSQL:
                      currentCancelRef.current = onRunSelectedSQL?.(selectedSQLContent)
                      break
                  }
                }}
              >
                {
                  menuItems.find((item) => {
                    return item.key === selectedMenuKey
                  }).label
                }
              </Dropdown.Button>
            </Space>
          )}
        </Col>
        <Col>
          {showEngineSelect && (
            <Form.Item
              label={
                <>
                  查询代理
                  <Tooltip title="此处可以设置sql执行的具体查询代理引擎，默认推荐使用【X-Engine联邦查询】代理引擎，X-Engine会将您的SQL自动转译下发到不同的数据库进行联邦查询；但因为不同数据库可能有特殊语法，如果X-Engine联邦查询失败，建议通过数据库自带的查询引擎进行代理查询。">
                    <QuestionCircleOutlined className="ml-1" />
                  </Tooltip>
                </>
              }
              className="m-0 mr-1 flex"
            >
              <Select
                className="min-w-[160px]"
                showSearch
                onChange={(val) => {
                  onQueryEngineChange(val)
                }}
                value={queryEngine}
                defaultValue={'XEngine'}
                placeholder="请选择查询引擎"
                options={engineList?.map((item: { catalogName: string; catalogType: string }) => ({
                  label: item.catalogName === 'XEngine' ? 'X-Engine联邦查询' : item.catalogName,
                  value: item.catalogName,
                }))}
              />
            </Form.Item>
          )}
        </Col>
        <Col className={styles.formatBtnCon}>
          {showFormatBtn && (
            <Button
              className={`${p27g9r7bjso} flex items-center`}
              type={'text'}
              onClick={() => {
                editor.trigger('editor', 'editor.action.formatDocument', {})
              }}
            >
              <ClearOutlined /> 格式化
            </Button>
          )}
        </Col>
      </Row>
      <UIDivider type={'horizontal'} />
      <div className={`${styles.editor} editorContainer`} style={{ height }} ref={monacoEl}>
        <div ref={placeholderRef} className={styles.editorPlaceholder}>
          {placeholder}
        </div>
      </div>
    </div>
  )
}
