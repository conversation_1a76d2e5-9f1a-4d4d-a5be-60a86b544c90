// @ts-nocheck
import React, { useEffect, useState, useRef } from 'react'
import { Col, message, Row, notification } from 'antd'
import * as monaco from 'monaco-editor'
import { useSize, useRequest, useUpdateEffect } from 'ahooks'
import { Api } from '@api'
import { Editor } from './Editor'
import { getUnitId } from '@libs/util'
import { QueryResult } from './QueryResult'
import { set } from 'lodash-es'
import styles from './sql.module.scss'
import namespace from '@libs/namespace'
import { useAtom } from 'jotai/index'
import { activeTabKeyInSearchResultTab } from '@atoms/xEngineAtoms'
import { SQL_QUERY_ROW_KEY } from '@constant'
export const TabContent = ({
  setWindowInfoList,
  sqlContent,
  queryEngine,
  windowId,
  title,
  itemKey,
  toggleClosable,
}: {
  setWindowInfoList: any[]
  sqlContent: string
  queryEngine: string
  windowId: undefined | number
  title: string
  itemKey: string
  toggleClosable: (a: string, b: boolean) => void
}) => {
  const [editor, setEditor] = useState<monaco.editor.IStandaloneCodeEditor | null>(null)
  const currentQueryIds = useRef<string[]>([])

  // add tab
  const { run: createEditorWindow } = useRequest(Api.apiEngineV1EditorPost, {
    manual: true,
    onSuccess(data) {
      setWindowInfoList((draft) => {
        const needChangeItem = draft.find((item) => {
          return item.key === itemKey
        })
        needChangeItem.windowId = data
      })
    },
    onError() {
      message.error('创建窗口出错')
    },
  })
  useEffect(() => {
    if (sqlContent === undefined) {
      createEditorWindow({
        queryEngine,
        windowId,
        windowName: title,
        sqlContent: '',
      })
    }
  }, [sqlContent])
  useEffect(() => {
    if (editor) {
      setWindowInfoList((draft) => {
        const needChangeItem = draft.find((item) => {
          return item.key === itemKey
        })
        needChangeItem.editor = editor
      })

      if (sqlContent) {
        editor?.setValue(sqlContent)
      }
    }
  }, [editor, sqlContent])
  const [previousUpdateInfo, setPreviousUpdateInfo] = useState({})
  const { run: updateInfo } = useRequest(
    (arg) => {
      // save data for change query engine
      setPreviousUpdateInfo(arg)
      return Api.apiEngineV1EditorPut(arg)
    },
    {
      manual: true,
      debounceWait: 500,
    },
  )
  useUpdateEffect(() => {
    updateInfo({
      queryEngine,
      windowId,
      windowName: title,
      sqlContent: editor.getValue(),
    })
  }, [title, windowId])

  const {
    run: runSQLQuery,
    data: SQLQueryResult,
    loading: runSQLQueryLoading,
    mutate: mutateSQLQueryResult,
  } = useRequest(
    (arg) =>
      Api.apiEngineV1SqlQueryPost({
        ...(arg || {}),
        asyncQuery: true,
      }).then((res = []) => {
        return Promise.all(
          res.map((i) => {
            const queryType = i?.queryType
            // 只有DQL、DML语句并且status为2成功态才支持异步查询
            if ((queryType === 'DQL' || queryType === 'DML') && i?.status?.code === 2) {
              currentQueryIds.current.push(i?.queryId)
              return Api.apiEngineV1SqlGetAsyncQueryResultGet({
                queryId: i?.queryId,
              })
                .then((result) => ({
                  ...(result || {}),
                  queryEngine: arg.queryEngine,
                  data: (result?.data || []).map((j) => ({ ...(j || {}), [SQL_QUERY_ROW_KEY]: getUnitId() })),
                }))
                .finally(() => {
                  currentQueryIds.current = currentQueryIds.current.filter((id) => id !== i?.queryId)
                })
            }
            return {
              ...(i || {}),
              queryEngine: arg.queryEngine,
              data: (i?.data || []).map((j) => ({ ...(j || {}), [SQL_QUERY_ROW_KEY]: getUnitId() })),
            }
          }),
        )
      }),
    {
      manual: true,
      onSuccess(data) {
        const isHitMV = data.some((i) => {
          return i?.isHitMV
        })

        if (isHitMV) {
          notification.success({
            placement: 'topRight',
            message: '已命中物化视图,更快返回结果,更低计算资源消耗',
            duration: 2.5,
          })
        }
      },
      onError(err) {
        console.log(err, 'error')
        mutateSQLQueryResult([])
      },
    },
  )

  useEffect(() => {
    toggleClosable(itemKey, !runSQLQueryLoading)
  }, [runSQLQueryLoading])

  useEffect(() => {
    try {
      set(namespace.autoTest, `${[location.pathname.replace('/xengine/engine/', '')]}.activeEditor`, editor)
    } catch (e) {
      console.log('autotest::Editor error', e)
    }
  }, [editor])

  const size = useSize(document.querySelector('body'))
  const [, setActiveTabKeyInSearchResultTab] = useAtom(activeTabKeyInSearchResultTab)

  const commonRun = () => {
    setActiveTabKeyInSearchResultTab((pre) => ({ ...(pre || {}), [windowId]: 'dataPreview' }))
  }

  const killSQL = () => {
    const queryIds = currentQueryIds.current
    return Promise.allSettled(
      queryIds.map((queryId) =>
        Api.apiEngineV1SqlKillQueryPost({
          queryId,
        }).then((killQueryId) => {
          currentQueryIds.current = currentQueryIds.current.filter((id) => id !== killQueryId)
        }),
      ),
    )
  }
  return (
    <div className={styles.tabContentCon}>
      <Row wrap={false}>
        <Col flex={'auto'}>
          <Editor
            height={size?.height / 3.2 || 300}
            autoFocus={true}
            onSQLUpdate={(val) => {
              updateInfo({
                queryEngine,
                windowId,
                windowName: title,
                sqlContent: val,
              })
            }}
            queryEngine={queryEngine}
            showEngineSelect={true}
            onQueryEngineChange={(val) => {
              previousUpdateInfo
              updateInfo({ ...previousUpdateInfo, windowId, windowName: title, queryEngine: val })
              setWindowInfoList((draft) => {
                const needChangeItem = draft.find((item) => {
                  return item.key === itemKey
                })
                needChangeItem.queryEngine = val
              })
            }}
            showFormatBtn={true}
            showRunBtn={true}
            loading={runSQLQueryLoading}
            onRunSelectedSQL={(val) => {
              commonRun()
              runSQLQuery({
                queryEngine,
                windowId,
                sqlContent: val,
              })
              return killSQL
            }}
            onRunCurrentSQL={(val) => {
              commonRun()
              runSQLQuery({
                queryEngine,
                windowId,
                sqlContent: val,
              })
              return killSQL
            }}
            onRunAllSQL={(val) => {
              commonRun()
              runSQLQuery({
                queryEngine,
                windowId,
                sqlContent: val,
              })
              return killSQL
            }}
            editor={editor}
            setEditor={setEditor}
          />
        </Col>
      </Row>

      <QueryResult windowId={windowId} loading={runSQLQueryLoading} SQLQueryResult={SQLQueryResult} />
    </div>
  )
}
