// @ts-nocheck
import React, { useEffect, useState } from 'react'
import { Button, Modal, List, message } from 'antd'
import { Broadcast, copyToClipboard } from '@libs'
import { Link, useSearchParams } from 'react-router-dom'

type BroadcastIdManageType = {
    openMVRecordsModalBId: string
    setMVRecordsBId: string
    reloadMVListBId: string
}

export default function MVRecordsModal({ broadcastIdManage }: { broadcastIdManage: BroadcastIdManageType }) {
    const [dataList, setDataList] = useState([])
    const [isOpen, setIsOpen] = useState(false)
    const [path, setPath] = useState('')
    
    // 解析当前路径中的查询参数
    const createDetailPath = () => {
        const [basePath, existingQuery] = path.split('?')
        const searchParams = new URLSearchParams(existingQuery || '')
        if (!searchParams.has('activeTab')) {
            searchParams.set('activeTab', 'list')
        }
        return `${basePath}?${searchParams.toString()}`
    }

    useEffect(() => {
        Broadcast.listen(broadcastIdManage.openMVRecordsModalBId, () => {
            setIsOpen(true)
        })
        Broadcast.listen(broadcastIdManage.setMVRecordsBId, ({ mvRecordList }: { mvRecordList: string[] }) => {
            setDataList(mvRecordList)
        })
        Broadcast.listen(broadcastIdManage.path, (path: string) => {
            setPath(path)
        })
    }, [])
    return (
        <>
            <Modal
                width={900}
                open={isOpen}
                destroyOnClose
                footer={<div className='flex justify-start'>
                    <Link to={createDetailPath()}>跳转详情</Link>
                </div>}
                onCancel={() => {
                    setIsOpen(false)
                }}
            >
                <List
                    header={<div className='font-medium text-base'>去重后的SQL列表：</div>}
                    dataSource={dataList}
                    pagination={{}}
                    renderItem={(mvSql) => (
                        <List.Item>
                            <div className='w-[100%] flex justify-between items-center'>
                                <span className='break-all'>{mvSql}</span>
                                <Button
                                    type='link'
                                    onClick={() => {
                                        copyToClipboard(mvSql)
                                        message.success('复制成功')
                                    }}
                                >
                                    复制
                                </Button>
                            </div>
                        </List.Item>
                    )}
                ></List>
            </Modal>
        </>
    )
}
