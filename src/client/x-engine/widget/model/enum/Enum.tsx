// @ts-nocheck
import { Api } from '@api'
import { setItem, getItem } from '@libs/util'
import { useRequest } from 'ahooks'

const LStorage = '@wgt/enum'
const Enum = () => {
    const { runAsync: getEnumItems } = useRequest(Api.apiEngineV1MetricsEnumItemsGet, {
        manual: true,
    })
    const localData = (getItem(LStorage) && JSON.parse(getItem(LStorage))) || {}
    const { enumData, windowId } = localData

    const enums: any = {
        windowId: window.dipeak.windowId,
        enumData: enumData,
    }

    const getEnums = (callback: (data: any) => void, badback?: (data: any) => void): void => {
        if (window.dipeak.windowId === windowId) {
            callback(enumData)
        } else {
            getEnumItems()
                .then((data) => {
                    enums['enumData'] = data
                    setItem(LStorage, JSON.stringify(enums))
                    callback(data)
                })
                .catch((err) => {
                    typeof badback === 'function' && badback(err)
                })
        }
    }

    return { getEnums }
}

export default Enum
