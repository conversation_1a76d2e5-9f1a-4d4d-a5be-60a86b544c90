// @ts-nocheck
import { Api } from '@api'
import { useRequest } from 'ahooks'
import React from 'react'
import { useSearchParams } from 'react-router-dom'
import LayoutCard from '@ui/layoutCard/LayoutCard'
import Lineage from '@model/Lineage'

const MetricsDetailRelative: React.FC = () => {
  const [searchParams] = useSearchParams()
  const metricsId = searchParams.get('metricsId')
  const metricsType = searchParams.get('metricsType')

  const { data: metricsLineageData } = useRequest(() => {
    return !Number.isNaN(parseInt(metricsId)) && metricsType
      ? Api.apiEngineV1MetricsLineageGet({
          metricsId: parseInt(metricsId),
          metricsType,
        })
      : Promise.resolve({})
  })

  return (
    <>
      <LayoutCard title="血缘关系">
        {metricsLineageData && (
          <div
            style={{
              height: '400px',
            }}
          >
            <Lineage
              data={metricsLineageData}
              typeKey="metricsType"
              legend={true}
              layout="dagre"
              layoutConf={{
                type: 'dagre',
                rankdir: 'LR',
                controlPoints: true,
                nodesepFunc: (node: Record<string, any>) => {
                  return node.height
                },
                ranksepFunc: (node: Record<string, any>) => {
                  return node.width * 0.5
                },
              }}
              popoverTipParams={{ name: 'name', columnExpression: 'detail' }}
            />
          </div>
        )}
      </LayoutCard>
    </>
  )
}

export default MetricsDetailRelative
