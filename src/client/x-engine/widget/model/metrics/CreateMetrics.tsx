// @ts-nocheck
import React, { useState, useEffect } from 'react'
import { Api } from '@api'
import { Froms, setSelectOpts } from '@ui/form'
import { useRequest } from 'ahooks'
import { clone } from 'lodash-es'
import { Button, Drawer, Dropdown, Typography, message } from 'antd'
import type { MenuProps } from 'antd'
import { PlusOutlined } from '@ant-design/icons'
import { dropdownItems, formItems, compositeFormItems, derivedFormItems, context } from './conf/itemConf'
import { DataVO, CreateMetrice } from './interface'
import Enum from '@model/enum/Enum'
import MetricsPrompt from './MetricsPrompt'

const { Title } = Typography

const CreateMetrics: React.FC = (props: CreateMetrice) => {
  const { onSuccess } = props

  const [open, setOpen] = useState(false)
  const [metricsType, setMetricsType] = useState('')
  const [metricsLabel, setMetricsLabel] = useState('')
  const [metricsList, setMetricsList] = useState<any[]>([])
  const [formItem, setFormItem] = useState<any[]>([])
  const [metricsPrompt, setMetricsPrompt] = useState([])
  const [initValues, setInitValues] = useState({})
  const [resetFields, setResetFields] = useState(false)

  const { getEnums } = Enum()

  const { runAsync: getMetricsList } = useRequest(Api.apiEngineV1MetricsListPost, {
    manual: true,
  })
  const { runAsync: getVTalbeList } = useRequest(Api.apiEngineV1VtableListGet, {
    manual: true,
  })
  const { runAsync: getVTalbeDetail } = useRequest(Api.apiEngineV1VtableDetailGet, {
    manual: true,
  })
  const { runAsync: getMetricsDetail } = useRequest(Api.apiEngineV1MetricsGet, {
    manual: true,
  })
  const { runAsync: postMetrics } = useRequest(Api.apiEngineV1MetricsPost, {
    manual: true,
  })

  const { runAsync: postAtomicMetrics } = useRequest(Api.apiEngineV1MetricsGetRecommendAtomicMetricsPost, {
    manual: true,
  })

  const handleMenuClick: MenuProps['onClick'] = (e: { key: string }) => {
    setMetricsType(e?.key)
    setMetricsLabel(context[e.key].label)

    switch (e?.key) {
      case 'atomic':
        setFormItem(formItems)
        break
      case 'derived':
        setFormItem(derivedFormItems)
        break
      case 'composite':
        setFormItem(compositeFormItems)
        break
      default:
        break
    }
    setOpen(true)
    setResetFields(false)
  }

  const onClose = () => {
    setOpen(false)
    setResetFields(true)
  }

  /**
   * 指标推荐
   */
  const onPrompt = (data: { name: any; dimensions: any; function: any; column: any }) => {
    const { name, dimensions, function: fn, column } = data
    setInitValues({
      name,
      dimensions,
      function: fn,
      column,
    })
  }

    /**
     * @param vtable
     * @param name
     */
    const renderSelectOptions = (dataList: [], name: string, opts?: { label?: string; value?: any }) => {
        setSelectOpts(
            {
                dataList,
                dealName: name,
                formItems: formItem,
                options: opts,
            },
            (newformItems) => {
                setFormItem(newformItems)
            },
        )
    }

  const onValuesChange = (data: object, form: any) => {
    const key: string = Object.keys(data).join('')
    const params = form?.getFieldValue()
    const dataVO = formatParams(form)
    const { metricsMetaVO } = dataVO
    const { vtableMeta } = metricsMetaVO
    const vTParams = clone(params)

        switch (key) {
            case 'catalog':
                setInitValues({
                    database: '',
                    virtualTableName: '',
                    dimensions: [],
                    column: '',
                    columnName: '',
                    quoteAtomicMetricsId: [],
                })
                setMetricsPrompt([])
                break
            case 'database':
                vTParams.metricsVtable = true
                getVTalbeList(vTParams).then((data: any) => {
                    renderSelectOptions(data?.list, 'virtualTableName', { value: 'name', label: 'name' })
                })

                setInitValues({
                    virtualTableName: '',
                    dimensions: [],
                    column: '',
                    columnName: '',
                    quoteAtomicMetricsId: [],
                })
                setMetricsPrompt([])
                break
            case 'virtualTableName':
                vTParams.name = vTParams.virtualTableName
                getVTalbeDetail(vTParams).then((data: any) => {
                    renderSelectOptions(data?.columns, 'dimensions', { value: 'name', label: 'name' })
                    renderSelectOptions(data?.columns, 'column', { value: 'name', label: 'name' })
                    renderSelectOptions(data?.columns, 'columnName', { value: 'name', label: 'name' })
                })

        setInitValues({
          dimensions: [],
          column: '',
          columnName: '',
          functionExpression: '',
          quoteAtomicMetricsId: [],
        })

        if (metricsType === 'atomic') {
          const { catalog: catalogName, database: databaseName, virtualTableName } = vTParams
          postAtomicMetrics({ catalogName, databaseName, virtualTableName })
            .then((data: any) => {
              setMetricsPrompt(data)
            })
            .catch(() => {
              setMetricsPrompt([])
            })
        }

        if (metricsType === 'composite' || metricsType === 'derived') {
          getMetricsList({ vtableMeta }).then((data: any) => {
            setMetricsList(data?.list)

                        setSelectOpts(
                            {
                                dataList: data?.list.filter(
                                    (d: { metricsType: string }) => d.metricsType !== 'composite',
                                ),
                                dealName: 'functionExpression',
                                formItems: formItem,
                                options: { value: 'name', label: 'name' },
                            },
                            (newformItems) => {
                                setFormItem(newformItems)
                            },
                        )

                        setSelectOpts(
                            {
                                dataList: data?.list.filter(
                                    (d: { metricsType: string }) => d.metricsType !== 'composite',
                                ),
                                dealName: 'quoteAtomicMetricsId',
                                formItems: formItem,
                                options: { value: 'id', label: 'name' },
                            },
                            (newformItems) => {
                                setFormItem(newformItems)
                            },
                        )
                    })
                }
                break
            case 'quoteAtomicMetricsId':
                vTParams.metricsId = vTParams.quoteAtomicMetricsId.join(',')
                vTParams.metricsType = 'atomic'
                delete vTParams.quoteAtomicMetricsId

        if (vTParams.metricsId) {
          getMetricsDetail(vTParams).then((data: any) => {
            renderSelectOptions(data.dimensions, 'dimensions')
          })
        }
        break
      default:
        break
    }
  }

  const menuProps = {
    items: dropdownItems,
    onClick: handleMenuClick,
  }

  /**
   * get metrics objs
   * @param metricsArr
   * @param metricsList
   * @returns
   */
  const getMetrics = (metricsArr: any[], metricsList: any) => {
    const metrics: any[] = []
    metricsList.map((v: { name: string }) => {
      metricsArr.map((value: string) => {
        v.name === value && metrics.push(v)
      })
    })
    return metrics
  }

  /**
   * Format funtion expression data
   * @param data String
   * @returns string[]
   */
  const formatFNExpression = (fnExpression: string) => {
    const metricsArr =
      fnExpression
        .replace(/(\+|-|\*|\/|\(|\))/gi, ' ')
        .split(' ')
        .filter((d) => d) || []
    const metricsSet = [...new Set(metricsArr)]
    return metricsSet
  }

  /**
   * Submit
   * @param form Object params
   */

  const fromSubmit = (form?: any) => {
    const dataParams = formatParams(form)

    const { metricsMetaVO } = dataParams
    const { metricsType } = metricsMetaVO

    if (metricsType === 'composite') {
      const { functionExpression } = dataParams
      const metricsArr = formatFNExpression(functionExpression)
      const metrics = getMetrics(metricsArr, metricsList)
      dataParams.quoteAtomicMetricsId = metrics
        .map((v) => {
          if (v.metricsType === 'atomic') return v.id
        })
        .filter((d) => d)
      dataParams.quoteDerivedMetricsId = metrics
        .map((v) => {
          if (v.metricsType === 'derived') {
            return v.id
          }
        })
        .filter((d) => d)
    }

    message.loading({
      content: '正在创建指标，请稍后',
      key: 'postMetricsLoading',
    })
    postMetrics(dataParams).then(() => {
      message.success({
        content: '指标创建成功',
        key: 'postMetricsLoading',
      })
      onSuccess()
      setOpen(false)
      setResetFields(true)
    })
  }

  /**
   * Format params
   * @returns Object
   */
  const formatParams = (form?: any) => {
    const params = form?.getFieldValue()
    const { quoteAtomicMetricsId } = params

    if (quoteAtomicMetricsId?.length > 1) {
      params?.quoteAtomicMetricsId.shift()
    }

    const dataVO: DataVO = {
      metricsMetaVO: {
        vtableMeta: {
          catalogName: params?.catalog,
          databaseName: params?.database,
          virtualTableName: params?.virtualTableName,
        },
        name: params?.name,
        meaning: params?.meaning,
        metricsType,
        dimensions: params?.dimensions,
        columnExpression: params?.columnExpression,
        businessType: params?.businessType,
      },
      filters: [
        {
          columnName: params?.columnName,
          condition: params?.condition,
          value: params?.value,
        },
      ],
      quoteAtomicMetricsId: params?.quoteAtomicMetricsId,
      quoteDerivedMetricsId: params?.quoteDerivedMetricsId,
      ...params,
    }

    return dataVO
  }

    /**
     * useEffect
     */
    useEffect(() => {
        getEnums((data: any) => {
            renderSelectOptions(data?.businessType, 'businessType', { value: 'value', label: 'value' })
            renderSelectOptions(data?.metricsFunction, 'function', { value: 'value', label: 'desc' })
            renderSelectOptions(data?.conditionType, 'condition', { value: 'value', label: 'desc' })
        })

        // 派生指标 --> 获取原子指标
        metricsType === 'derived' &&
            getMetricsList({
                metricsType: 'atomic',
                current: 1,
                pageSize: 10,
            }).then((data: any) => {
                renderSelectOptions(data?.list, 'quoteAtomicMetricsId', {
                    label: 'name',
                    value: 'id',
                })
            })
    }, [metricsType])

  return (
    <>
      <Dropdown menu={menuProps}>
        <Button type="primary" icon={<PlusOutlined />}>
          创建指标
        </Button>
      </Dropdown>
      <Drawer title={metricsLabel} placement="right" onClose={onClose} open={open} width="1040">
        <Title level={5}>
          {metricsType === 'atomic' ? '选择虚拟表' : metricsType === 'derived' ? '选择原子指标' : '选择数据源'}
        </Title>
        <Froms
          resetFields={resetFields}
          initialValues={initValues}
          key={'metrics-create'}
          items={formItem}
          DBSelect={true}
          colSpan={8}
          onFinish={fromSubmit}
          onValuesChange={onValuesChange}
        />
        <br style={{ clear: 'both', margin: '12px 0' }} />
        <MetricsPrompt metricsType={metricsType} items={metricsPrompt} onClick={onPrompt} />
      </Drawer>
    </>
  )
}

export default CreateMetrics
