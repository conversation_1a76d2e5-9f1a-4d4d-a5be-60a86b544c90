/**
 * This file contains the MetricsPrompt component which displays recommended metrics for a given metrics type.
 * It takes in an array of items and renders them as cards with information such as business type, dimensions, and meaning.
 * The component also allows for a callback function to be passed in to handle when a user clicks on a "Use Recommendation" button.
 */
import React from 'react'
import { Card, Divider, Layout, Typography } from 'antd'
import cs from './metrics.module.scss'
import { Strings } from '@constant'
const { Title } = Typography

interface Prompt {
    metricsType: string
    items: any[]
    onClick?: (event: any) => void
}

const MetricsPrompt = (props: Prompt) => {
    const { items, metricsType, onClick } = props
    const showStatus = items.length > 0 && metricsType === 'atomic' ? true : false

    const onPromptClick = (event: any) => {
        const { index } = event.target.dataset
        if (typeof onClick === 'function') {
            onClick(items[index])
        }
    }

    const renderCards = (items: any[]) => {
        const html: any[] = []
        items.map((item, index: number) => {
            html.push(
                <Card
                    key={index}
                    title={item.name}
                    extra={
                        <a data-index={index} onClick={onPromptClick}>
                            使用推荐
                        </a>
                    }
                    className={cs.metricsPromptItem}
                >
                    <p>
                        {Strings.text.dimensions}：{item.dimensions.join('、')}
                    </p>
                    <p>
                        {Strings.text.metricsFuncton}：{item.function}
                    </p>
                    <p>字段：{item.column}</p>
                </Card>,
            )
        })
        return html
    }

    return (
        <>
            {showStatus === true ? (
                <>
                    <Divider plain orientation='left'>
                        <Title level={5}>指标推荐</Title>
                    </Divider>
                    <Layout className={cs.metricsPrompt}>{renderCards(items)}</Layout>
                </>
            ) : (
                <></>
            )}
        </>
    )
}

export default MetricsPrompt
