// @ts-nocheck
import React, { useEffect, useState, useRef } from 'react'
import { Descriptions, Button, Modal, Form, Select, message, Space } from 'antd'
import { Editor } from '@model/sql'
import { editor } from 'monaco-editor'
import { useRequest } from 'ahooks'
import { Api } from '@api'
import cs from './metrics.module.scss'
import type { FormInstance } from 'antd/es/form'
import { useNavigate, useSearchParams } from 'react-router-dom'
import LayoutCard from '@ui/layoutCard/LayoutCard'
import { routerMap } from '@XEngineRouter/routerMap'
// import Columns from '@ui/table/Columns'

interface Item {
  key: string
  label: string
  render: () => JSX.Element
  allowEditor?: boolean
  editRender?: (props: any) => JSX.Element
  params?: any
}

interface SQLDescItemsPropsType {
  sql: string
}

interface DescTitlePropsType {
  name: string
  meaning: string
}

enum metricsTypeEnum {
  derived = 'derived',
  composite = 'composite',
  atomic = 'atomic',
}

interface DescExtraPropsType {
  metricsId?: string
  metricsType: keyof typeof metricsTypeEnum
  synMetricsPost: () => void
  setIsEditor: React.Dispatch<React.SetStateAction<boolean>>
}

function SQLDescItems(props: SQLDescItemsPropsType) {
  const { sql } = props
  const [editor, setEditor] = useState<editor.IStandaloneCodeEditor | null>(null)
  const [open, setOpen] = useState(false)

  useEffect(() => {
    if (editor) {
      editor.setValue(sql)
    }
  }, [editor])

  return (
    <>
      <a
        onClick={() => {
          setOpen(true)
        }}
      >
        SQL
      </a>
      <Modal
        open={open}
        width="80%"
        centered={true}
        onCancel={() => {
          setOpen(false)
        }}
        footer={null}
      >
        <Editor editor={editor} setEditor={setEditor} className={cs.editorStyle} readOnly={true} />
      </Modal>
    </>
  )
}

function DescItemDimensionsEdit(props: any) {
  const { vtableMeta, dimensions } = props

  const { catalogName: catalog, databaseName: database, virtualTableName: name } = vtableMeta

  const [options, setOptions] = useState(dimensions.map((item: string) => ({ value: item, disabled: true })))

  useRequest(Api.apiEngineV1VtableDetailGet, {
    defaultParams: [
      {
        catalog,
        database,
        name,
      },
    ],
    onSuccess: (data) => {
      const { columns } = data
      const resColumns = columns.filter((col) => dimensions.indexOf(col.name) === -1)
      setOptions((pre: Record<string, string>[]) => {
        return [
          ...pre,
          ...resColumns.map((item) => ({
            value: item.name,
          })),
        ]
      })
    },
  })

  return (
    <>
      <Form.Item
        name="dimensions"
        style={{
          marginBottom: '0',
        }}
      >
        <Select mode="multiple" showArrow style={{ width: '100%' }} options={options} />
      </Form.Item>
    </>
  )
}

const descItems: (data: any) => Item[] = (data) => {
  if (!data) return []
  return [
    {
      key: 'creator',
      label: '创建人',
      render: () => {
        return <>{data['creator']}</>
      },
    },
    {
      key: 'createTime',
      label: '创建时间',
      render: function () {
        return <>{data['createTime']}</>
      },
    },
    {
      key: 'label',
      label: '标签',
      render: () => {
        return <>{data['label']}</>
      },
    },
    {
      key: 'virtualTableName',
      label: '虚拟表',
      render: () => {
        if (!data['vtableMeta']) return <></>
        return (
          <>{`${data['vtableMeta']['catalogName']}.${data['vtableMeta']['databaseName']}.${data['vtableMeta']['virtualTableName']}`}</>
        )
      },
    },
    {
      key: 'dimensions',
      label: '关联维度',
      render: () => {
        const ch = ','
        return <>{data['dimensions'].join(ch)}</>
      },
      allowEditor: true,
      editRender: DescItemDimensionsEdit,
      params: ['vtableMeta', 'dimensions'],
    },
    {
      key: 'displaySql',
      label: '指标定义',
      render: () => {
        return (
          <>
            <SQLDescItems sql={data['displaySql']}></SQLDescItems>
          </>
        )
      },
    },
    // {
    //     key: 'metricsType',
    //     label: '指标分类',
    //     render: () => {
    //         return <>{Columns({ type: 'metricstype' })(data['metricsType'])}</>
    //     },
    // },
    {
      key: 'businessType',
      label: '业务系统',
      render: () => {
        return <>{data['businessType']}</>
      },
    },
    {
      key: 'hot',
      label: '热度',
      render: () => {
        return <>{data['hot']}</>
      },
    },
  ]
}

function DescTitle(props: DescTitlePropsType) {
  const { name, meaning } = props

  return (
    <>
      <div>
        <span className={cs.metricsDescTit}>{name}&nbsp;&nbsp;</span>
        <span className={cs.metricsDescSubTit}>{meaning}</span>
      </div>
    </>
  )
}

function DescExtra(props: DescExtraPropsType) {
  const navigate = useNavigate()
  const { metricsId } = props
  return (
    <Space>
      {/*{props.metricsType === 'atomic' && (*/}
      {/*    <Button*/}
      {/*        size='small'*/}
      {/*        type='primary'*/}
      {/*        onClick={() => {*/}
      {/*            props.synMetricsPost()*/}
      {/*        }}*/}
      {/*    >*/}
      {/*        指标分析*/}
      {/*    </Button>*/}
      {/*)}*/}
      <Button
        size="small"
        type="primary"
        onClick={() => {
          navigate(routerMap.metricsManager.metricsCreate.path + '?id=' + metricsId)
        }}
      >
        编辑
      </Button>
    </Space>
  )
}

const MetricsDetailDesc: React.FC = () => {
  const [searchParams] = useSearchParams()
  const metricsId = searchParams.get('metricsId')
  const metricsType = searchParams.get('metricsType')

  const [isEditor, setIsEditor] = useState(false)
  const [descForm] = Form.useForm()
  const formRef = useRef<FormInstance>(null)

  const { data: metricsData, mutate: mutateMetricsData } = useRequest(
    ({ metricsId, metricsType }) => {
      if (Number.isNaN(parseInt(metricsId)) || !metricsType) return Promise.reject('metricsId || metricsType not get')
      return Api.apiEngineV1MetricsGet({ metricsId: parseInt(metricsId), metricsType })
    },
    {
      defaultParams: [{ metricsId, metricsType }],
    },
  )

  const { run: putMetricsRun } = useRequest(
    (formValues: any) => {
      return Api.apiEngineV1MetricsPut({
        metricsId: parseInt(metricsId),
        metricsType: metricsType,
        name: metricsData.name,
        dimensions: formValues['dimensions'],
      }).then((isOK) => {
        if (isOK) {
          return Api.apiEngineV1MetricsGet({ metricsId: parseInt(metricsId), metricsType })
        }
        return Promise.reject('metrics edit error')
      })
    },
    {
      manual: true,
      onSuccess: (metrics) => {
        message.success('编辑成功！')
        mutateMetricsData(metrics)
        setIsEditor(false)
      },
      onError: (err) => {
        message.error('编辑失败，请重试')
        console.log(err)
      },
    },
  )

  const { run: getSupersetPort } = useRequest(Api.apiEngineV1SupersetPortGet, {
    manual: true,
    onSuccess(data) {
      message.success('分析成功')
      window.open(data, '_blank')
    },
  })

  const { run: synMetricsPost } = useRequest(Api.apiEngineV1SupersetSynMetricsPost, {
    manual: true,
    onSuccess() {
      getSupersetPort()
    },
  })

  return (
    <>
      <LayoutCard>
        {metricsData && (
          <Form form={descForm} onFinish={putMetricsRun} initialValues={metricsData} ref={formRef}>
            <Descriptions
              title={<DescTitle name={metricsData.name} meaning={metricsData.meaning} />}
              extra={
                !isEditor && (
                  <DescExtra
                    metricsType={metricsType as keyof typeof metricsTypeEnum}
                    synMetricsPost={() =>
                      synMetricsPost({
                        metricsId: Number(metricsId),
                        metricsType,
                      })
                    }
                    metricsId={metricsId}
                    setIsEditor={setIsEditor}
                  />
                )
              }
              contentStyle={{
                alignItems: 'center',
              }}
              bordered
            >
              {descItems(metricsData).map((item, index) => {
                return (
                  <Descriptions.Item label={item.label} key={index}>
                    {isEditor && item.allowEditor ? (
                      <item.editRender
                        {...item?.params.reduce((pre: Record<string, any>, cur: string) => {
                          pre[cur] = metricsData[cur as keyof typeof metricsData]
                          return pre
                        }, {})}
                      />
                    ) : (
                      item.render()
                    )}
                  </Descriptions.Item>
                )
              })}
            </Descriptions>

            {isEditor && (
              <Form.Item
                style={{
                  marginBottom: '0',
                }}
              >
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'flex-end',
                    padding: '20px 0 0 0',
                  }}
                >
                  <Space>
                    <Button
                      onClick={() => {
                        setIsEditor(false)
                      }}
                    >
                      取消编辑
                    </Button>
                    <Button type="primary" htmlType="submit">
                      完成编辑
                    </Button>
                  </Space>
                </div>
              </Form.Item>
            )}
          </Form>
        )}
      </LayoutCard>
    </>
  )
}

export default MetricsDetailDesc
