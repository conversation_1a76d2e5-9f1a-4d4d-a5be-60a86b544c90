import React, { useCallback, useState, memo, useEffect } from 'react'
import { DeleteOutlined, DownOutlined, UpOutlined } from '@ant-design/icons'
import { Popconfirm, Tooltip, Typography } from 'antd'
import { LINE_HEIGHT, NODE_HEADER_HEIGHT, NODE_WIDTH, COLUMN_LIMIT } from './conf/constant'
import { useAtom, useSetAtom } from 'jotai'
import { hasFactAtom, dataModelDescType<PERSON>tom, erFlowEdgesAtom } from '@atoms/er'
import clsx from 'clsx'
import { atom } from 'jotai'
import { getPortsData, generateEdge, generatePorts } from './conf/utils'
import type { ErFlowNodeDataType, ErFlowEdgeDataType } from '@shared/xengine-types'

const tableCounterAtom = atom(1)

type PortChangeAction = 'add' | 'remove'

const placeholderPortID = `place-holder-id-${Math.random().toString(36).slice(2)}`

const generatedPlaceholderPorts = generatePorts(placeholderPortID, 'group1', {
  y: LINE_HEIGHT * COLUMN_LIMIT + NODE_HEADER_HEIGHT + LINE_HEIGHT / 2,
  magnet: true,
})

const keyName = (pk: string[], fk: string[]) => {
  const words = []
  if (pk.length > 0) {
    words.push('pk')
  }
  if (fk.length > 0) {
    words.push('fk')
  }
  return words.join('/')
}

const NodeColumn = memo(({ columnData }: { columnData: ErFlowNodeDataType['columns'][number] }) => {
  return (
    <div
      className={`columns-item align-center flex border-b-[1px] border-[#e8e8e8] p-[6px] pl-[8px]`}
      style={{ height: LINE_HEIGHT }}
    >
      <div className={`columns-item-title max-w-[150px]`}>
        <Typography.Text
          style={{ fontSize: 16 }}
          color="#333"
          ellipsis={{
            tooltip: columnData.name,
          }}
        >
          {columnData.name}
        </Typography.Text>
      </div>
      <div style={{ fontSize: 12, marginLeft: 5 }} className="flex w-[74px] flex-1 items-center justify-end">
        <Typography.Text
          type="secondary"
          italic
          ellipsis={{
            tooltip: columnData.columnType,
          }}
        >
          {columnData.columnType}
        </Typography.Text>

        {/* {(columnData.pk?.length || columnData.fk?.length || null) && (
          <div className="ml-1 box-border max-w-[30px] flex-none rounded-sm bg-[#f7f0fe] text-center text-[#4d20a4]">
            pk/fk
            {keyName(columnData.pk || [], columnData.fk || [])}
          </div>
        )} */}
        {
          <div className="ml-1 box-border max-w-[32px] flex-none rounded-sm bg-[#f7f0fe] px-[2px] text-center text-[#4d20a4]">
            {keyName(columnData.pk || [], columnData.fk || [])}
          </div>
        }
      </div>
    </div>
  )
})
const Node = memo(
  ({
    data,
    onDelete,
    onPortChange,
    tableIndex,
  }: {
    data: ErFlowNodeDataType
    onDelete?: (data: any) => void
    onPortChange: (action: PortChangeAction) => void
    tableIndex: number
  }) => {
    const max = data?.columns.length

    const isToggle = max - COLUMN_LIMIT > 0

    const [limit, setLimit] = useState<number>(COLUMN_LIMIT)

    const isFold = max > limit

    const toggleHandle = () => {
      const _limit = isFold ? max : COLUMN_LIMIT
      const action = isFold ? 'add' : 'remove'
      setLimit(_limit)
      onPortChange(action)
    }

    return (
      <div className={`react-er-node rounded-[4px] border-[1px] border-[#e8e8e8]`} style={{ width: NODE_WIDTH }}>
        <div className="node-header border-b-[1px] border-[#e8e8e8]" style={{ height: NODE_HEADER_HEIGHT }}>
          <div className="flex items-center justify-between">
            <div className="flex flex-1">
              {isToggle && (
                <div
                  onClick={toggleHandle}
                  className="mr-1 w-[16px] cursor-pointer"
                  title={`${isFold ? '展开' : '收起'}`}
                >
                  {isFold ? <DownOutlined /> : <UpOutlined />}
                </div>
              )}
              <Tooltip title={data.name}>
                <Typography.Text ellipsis className="block w-[198px] flex-1">
                  {data.name}
                </Typography.Text>
              </Tooltip>
            </div>
            <Popconfirm
              title={'确认删除'}
              description={data.isFact ? '删除主表后，画布中其他的副表也会被一并清除' : ''}
              okText="确定"
              cancelText="取消"
              onConfirm={() => onDelete?.(data)}
            >
              <DeleteOutlined title="删除" />
            </Popconfirm>
          </div>
          <div className="flex items-center">
            <div
              className={clsx(
                'w-fix mt-[6px] rounded-[2px] px-[4px] py-[2px] text-[12px]',
                data.isFact ? 'bg-[#F3E6F6]' : 'bg-[#EBF9F6]',
              )}
            >
              {data.isFact ? '主表' : '副表'}
            </div>
            <Typography.Text
              className="ml-2 mt-1 flex-1"
              ellipsis={{ tooltip: `${data.catalogName}.${data.databaseName}` }}
            >
              {data.catalogName}.{data.databaseName}
            </Typography.Text>
            <span className="mt-[6px] text-[12px]">{data.isFact ? 't1' : `t${tableIndex}`}</span>
          </div>
        </div>
        <div className="columns-list">
          {data.columns.slice(0, limit).map((item) => (
            <NodeColumn columnData={item} key={item.name} />
          ))}
          {/* <div className={clsx('absolute', isToggle && max > limit && 'none')}>
          {data.columns.slice(limit).map((item) => (
            <NodeColumn columnData={item} key={item.name} />
          ))}
        </div> */}
        </div>

        {isToggle && (
          <div
            onClick={toggleHandle}
            className={`h-[${LINE_HEIGHT}px] flex items-center justify-center gap-1 border-t border-[#e8e8e8] py-[6px]`}
          >
            {isFold ? (
              <>
                <DownOutlined /> 展开
              </>
            ) : (
              <>
                <UpOutlined /> 收起
              </>
            )}
          </div>
        )}
      </div>
    )
  },
)

const ReactERNode = ({ node, graph }: { node?: any; graph?: any }) => {
  const data = node.prop('data')
  const [counter, setCounter] = useAtom(tableCounterAtom)
  const [nodeIndex, setNodeIndex] = useState<number | null>(null)

  const setHasFact = useSetAtom(hasFactAtom)
  const [dataModelDescType, setDataModelDescType] = useAtom(dataModelDescTypeAtom)
  const [erFlowEdges, setErFlowEdges] = useAtom(erFlowEdgesAtom)

  useEffect(() => {
    if (!data.isFact && nodeIndex === null) {
      // 新的副表被创建时，增加计数器
      setCounter((prev) => prev + 1)
      setNodeIndex(counter + 1)
      node.setData({
        ...data,
        tableIndex: counter + 1,
      })
    }
  }, [data, nodeIndex])

  // 添加port的小圆点
  const onPortChange = (action: 'remove' | 'add') => {
    // console.time('onPortChange-------')
    if (dataModelDescType !== 'STREAM_BATCH') {
      const generatedPlaceholderPortIds = generatedPlaceholderPorts.map((i) => i.id)
      const collapsiblePortsData = getPortsData(data, 'group-collapsible', COLUMN_LIMIT) as { id: string }[]
      const collapsiblePortsSet = new Set(collapsiblePortsData.map((i) => i.id))
      const existedEdges = new Set()
      const collapsiblePortEdgesData = erFlowEdges.filter(
        (edge) => collapsiblePortsSet.has(edge.source.port) || collapsiblePortsSet.has(edge.target.port),
      ) as ErFlowEdgeDataType[]

      const addEdgesData = [] as ErFlowEdgeDataType[]
      const removeEdgesData = [] as ErFlowEdgeDataType[]
      collapsiblePortEdgesData.forEach((edge) => {
        const source = edge.source
        const target = edge.target

        const replaceSourcePort = action === 'add' ? source.port : generatedPlaceholderPorts[1].id
        const replaceTargetPort = action === 'add' ? target.port : generatedPlaceholderPorts[0].id

        const otherSourcePort = source.collapse ? generatedPlaceholderPorts[1].id : source.port
        const otherTargetPort = target.collapse ? generatedPlaceholderPorts[0].id : target.port
        const addEdgeData = {
          source: {
            cell: source.cell,
            port: collapsiblePortsSet.has(source.port) ? replaceSourcePort : otherSourcePort,
          },
          target: {
            cell: target.cell,
            port: collapsiblePortsSet.has(target.port) ? replaceTargetPort : otherTargetPort,
          },
          joinType: edge.joinType,
          sourceData: edge.sourceData,
          targetData: edge.targetData,
        }
        const id = `${addEdgeData.source.cell}~${addEdgeData.source.port}~${addEdgeData.target.cell}~${addEdgeData.target.port}`
        if (!existedEdges.has(id)) {
          addEdgesData.push(addEdgeData)
          existedEdges.add(id)
        }
        if (action === 'remove') {
          removeEdgesData.push({
            ...addEdgeData,
            source: {
              cell: source.cell,
              port: collapsiblePortsSet.has(source.port) ? source.port : otherSourcePort,
            },
            target: {
              cell: target.cell,
              port: collapsiblePortsSet.has(target.port) ? target.port : otherTargetPort,
            },
          })
        }
      })
      if (action === 'add') {
        const element = document.querySelector(`[data-cell-id="${data.id}"]`)
        element?.classList.add('x6-port-show')
        if (addEdgesData.length > 0) {
          graph.addEdges(addEdgesData.map(generateEdge), { virtual: true })
        }
        node.removePorts(generatedPlaceholderPortIds, { virtual: true })
        setErFlowEdges((edges) =>
          edges.map((edge) => {
            if (!collapsiblePortsSet.has(edge.source.port) && !collapsiblePortsSet.has(edge.target.port)) {
              return edge
            }
            return {
              ...edge,
              source: {
                ...edge.source,
                collapse: collapsiblePortsSet.has(edge.source.port) ? false : edge.source.collapse,
              },
              target: {
                ...edge.target,
                collapse: collapsiblePortsSet.has(edge.target.port) ? false : edge.target.collapse,
              },
            }
          }),
        )
      }

      if (action === 'remove') {
        const element = document.querySelector(`[data-cell-id="${data.id}"]`)
        element?.classList.remove('x6-port-show')
        if (collapsiblePortEdgesData.length > 0) {
          graph.removeCells(
            removeEdgesData.map((edge) => generateEdge(edge).id),
            { virtual: true },
          )
          node.addPorts(generatedPlaceholderPorts)
          setTimeout(() => {
            graph.addEdges(
              addEdgesData.map((edge) => generateEdge({ ...edge, virtual: true })),
              { virtual: true },
            )
          }, 120)
        }
        setErFlowEdges((edges) =>
          edges.map((edge) => {
            if (!collapsiblePortsSet.has(edge.source.port) && !collapsiblePortsSet.has(edge.target.port)) {
              return edge
            }
            return {
              ...edge,
              source: {
                ...edge.source,
                collapse: collapsiblePortsSet.has(edge.source.port) ? true : edge.source.collapse,
              },
              target: {
                ...edge.target,
                collapse: collapsiblePortsSet.has(edge.target.port) ? true : edge.target.collapse,
              },
            }
          }),
        )
      }
    }
    // console.timeEnd('onPortChange-------')
  }

  const deleteHandle = useCallback(
    (info?: any) => {
      if (info) {
        if (info.isFact) {
          graph.clearCells()
          setDataModelDescType('')
          setHasFact(false)
          setErFlowEdges([])
        } else {
          graph.removeNode(info.id, { type: 'remove-node', id: info.id })
          setErFlowEdges((edges) =>
            edges.filter((edge) => edge.source.cell !== info.id && edge.target.cell !== info.id),
          )
        }
      }
    },
    [graph],
  )
  if (!data) return null
  return (
    <Node
      data={data}
      onDelete={deleteHandle}
      onPortChange={onPortChange}
      tableIndex={data.isFact ? 1 : nodeIndex || 2}
    />
  )
}

export default ReactERNode
