import { useGraphEvent, useGraphInstance, useGraphStore } from '@antv/xflow'
import React, { useEffect, useState } from 'react'
import { register } from '@antv/x6-react-shape'
import ReactERNode from './ReactERNode'
import { getPortsData, isSchemaEqual, generateEdge } from '@model/er-flow/conf/utils'
import { useAtom } from 'jotai'
import { hasFactAtom, dataModelDescTypeAtom, erFlowEdgesAtom, etlPaintInfoAtom } from '@atoms/er'
import TableConfigModal from '@model/er-flow/TableConfigModel'
import { Button, message, Modal, Space } from 'antd'
import { customAlphabet } from 'nanoid'
import { useAtomValue } from 'jotai'
import { etlPaintActionMap } from 'src/client/pages/AskBI/DIDataManage/ETLPaint/conf'
import { COLUMN_LIMIT } from './conf/constant'
import type { ErFlowEdgeDataType, ErFlowNodeDataType } from '@shared/xengine-types'
const numericalID = customAlphabet('1234567890', 10)

interface PropsType {
  onChange?: (d: any) => void
}

register({
  shape: 'er-node',
  component: ReactERNode,
  effect: ['data'],
})

// todo：批量dom操作优化，暂时移除这个操作
const changePortsVisible = (visible: boolean) => {
  const ports = document.body.querySelectorAll('.x6-port-body') as NodeListOf<SVGElement>
  for (let i = 0, len = ports.length; i < len; i = i + 1) {
    ports[i].style.visibility = visible ? 'visible' : 'hidden'
  }
}

export const InitNode = (props: PropsType): any => {
  const [hasFact, setHasFact] = useAtom(hasFactAtom)
  const [dataModelDescType, setDataModelDescType] = useAtom(dataModelDescTypeAtom)
  const [tableCfgModelOpen, setTableCfgModelOpen] = useState<boolean>(false)
  const [tableInfo, setTableInfo] = useState<any>()
  const { onChange } = props
  const etlPaintInfo = useAtomValue(etlPaintInfoAtom)

  const initData = useGraphStore((state) => state.initData)
  const removeNodes = useGraphStore((state) => state.removeNodes)
  const addNodes = useGraphStore((state) => state.addNodes)
  const addEdges = useGraphStore((state) => state.addEdges)
  const removeEdges = useGraphStore((state) => state.removeEdges)
  const nodes = useGraphStore((state) => state.nodes)
  const [erFlowEdges, setErFlowEdges] = useAtom(erFlowEdgesAtom)
  const graph = useGraphInstance()
  // todo 自定义react label（目前是使用原生的label，自定义节点暂未解决位置的问题）
  // useEffect(() => {
  //     if (graph) {
  //         graph.options.onEdgeLabelRendered = (args) => {
  //             const { selectors } = args
  //             const content = selectors.foContent as HTMLDivElement
  //             if (content) {
  //                 ReactDOM.createRoot(content).render(<p style={{width: '60px', height: '30px'}}>left join</p>)
  //             }
  //         }
  //     }
  // }, [graph]);

  // 监听节点添加
  useGraphEvent('node:added', async ({ node }) => {
    const data = node.getData()
    const position = node.getPosition()
    if (node.id === 'placeholder') {
      removeNodes([node.id])
      // 为什么是数组 存 edge id。解决添加/删除边的时候 判断这个columns 是否还是其他 columns 的pk/fk
      data.columns.forEach((item: any) => {
        item.pk = []
        item.fk = []
      })
      const info = {
        data,
        x: position.x,
        y: position.y,
      }
      // 验证这张表是否可以被添加到画布中
      const valid = await validateNodes(info)
      if (!valid) return
      // 如果添加的是副表，则需要指定时间列,相对父表,join方式
      info.data.id = numericalID()
      if (hasFact) {
        setTableCfgModelOpen(true)
        setTableInfo(info)
      } else {
        createNewReactErNode(info, dataModelDescType)
      }
    }
  })

  // 监听连线
  useGraphEvent('edge:connected', ({ edge }) => {
    const sourceData = edge?.getSourceNode()?.data as ErFlowNodeDataType
    const targetData = edge?.getTargetNode()?.data as ErFlowNodeDataType
    const sourcePortId = edge?.getSourcePortId() || ''
    const targetPortId = edge?.getTargetPortId() || ''
    const sourceCellId = edge?.getSourceCellId() || ''
    const targetCellId = edge?.getTargetCellId() || ''
    const targetPortInfo = targetPortId.split('~')

    const edgeData = {
      id: `${sourceCellId}+${sourcePortId}+${targetCellId}+${targetPortId}`,
      source: {
        cell: sourceCellId,
        port: sourcePortId,
        collapse: false,
      },
      sourceData,
      target: {
        cell: targetCellId,
        port: targetPortId,
        collapse: false,
      },
      targetData,
      joinType: targetPortInfo[6],
    }
    /** 拖拽过程中会生成一条无实际业务含义的线, 需要手动删除 */
    if (!edge.getData()) {
      // todo：不必要的线，不知道怎么产生的
      removeEdges([edge.id])
      createRelationHandle(edgeData)
    }
  })

  // 监听边的增加
  useGraphEvent('edge:added', ({ edge, options }) => {
    // 排除那条无意义的虚线
    if (!options.virtual && edge.getData()) {
      const sourceNode = edge?.getSourceNode()
      const targetNode = edge?.getTargetNode()
      const sourceData = sourceNode?.getData() as ErFlowNodeDataType
      const targetData = targetNode?.getData() as ErFlowNodeDataType

      // 更新两个节点的状态(增加pk/fk 字段)
      const sourceColumnName = edge?.getSourcePortId()?.split('~')[4]
      const targetColumnName = edge?.getTargetPortId()?.split('~')[4]
      sourceNode?.updateData({
        columns: sourceData.columns.map((col) => {
          if (col.name === sourceColumnName && !col.fk?.includes(edge.id)) {
            return {
              ...col,
              fk: [...(col.fk || []), edge.id],
            }
          }
          return col
        }),
      })
      targetNode?.updateData({
        columns: targetData.columns.map((col) => {
          if (col.name === targetColumnName && !col.pk?.includes(edge.id)) {
            return {
              ...col,
              pk: [...(col.pk || []), edge.id],
            }
          }
          return col
        }),
      })
    }
  })

  // 监听边被删除
  useGraphEvent('edge:removed', ({ edge, options }) => {
    // 排除那条无意义的虚线
    const edgeData = edge?.getData()

    if (!options.virtual && edgeData && (!edgeData.virtual || options.type === 'remove-node')) {
      const sourceNode = graph?.getCellById(edgeData.sourceData.id as string)
      const targetNode = graph?.getCellById(edgeData.targetData.id as string)
      const sourceData = sourceNode?.getData() as ErFlowNodeDataType
      const targetData = targetNode?.getData() as ErFlowNodeDataType

      const sourceColumnNameEdgeIdMap = {} as Record<string, string>
      const targetColumnNameEdgeIdMap = {} as Record<string, string>
      const resolveEdges = [] as string[]

      if (options.type === 'remove-node' && options.id) {
        resolveEdges.push(...erFlowEdges.filter((edge) => edge.id?.includes(options.id)).map((edge) => edge.id || ''))
      } else {
        resolveEdges.push(edge.id)
      }

      resolveEdges.forEach((edgeId) => {
        const [, sourcePortId, , targetPortId] = edgeId.split('+') || []
        const sourceColumnName = sourcePortId?.split('~')[4]
        const targetColumnName = targetPortId?.split('~')[4]
        sourceColumnNameEdgeIdMap[sourceColumnName] = edgeId
        targetColumnNameEdgeIdMap[targetColumnName] = edgeId
      })

      // 更新两个节点的状态(删除pk/fk 字段)
      if (sourceNode) {
        sourceNode.updateData({
          columns: sourceData.columns.map((col) => {
            const edgeId = sourceColumnNameEdgeIdMap[col.name]
            if (edgeId && col.fk?.includes(edgeId)) {
              return {
                ...col,
                fk: col.fk.filter((i) => i !== edgeId),
              }
            }
            return col
          }),
        })
      }
      if (targetNode) {
        targetNode.updateData({
          columns: targetData.columns.map((col) => {
            const edgeId = targetColumnNameEdgeIdMap[col.name]
            if (edgeId && col.pk?.includes(edgeId)) {
              return {
                ...col,
                pk: col.pk.filter((i) => i !== edgeId),
              }
            }
            return col
          }),
        })
        options.type !== 'remove-node' && setErFlowEdges((edges) => edges.filter((e) => e.id !== edge.id))
      }
    }
  })

  // 动态添加删除按钮
  useGraphEvent('edge:mouseenter', ({ edge }) => {
    edge.addTools({
      name: 'button-remove',
      args: { distance: 0.5 },
    })
  })
  useGraphEvent('edge:mouseleave', ({ edge }) => {
    if (edge.hasTool('button-remove')) {
      edge.removeTool('button-remove')
    }
  })

  // // 监听节点鼠标移入移出
  // useGraphEvent(
  //   'node:mouseenter',
  //   debounce(() => changePortsVisible(true), 400),
  // )
  // useGraphEvent(
  //   'node:mouseleave',
  //   debounce(() => changePortsVisible(false), 400),
  // )

  // 创建节点与节点的关系
  const createRelationHandle = (info: ErFlowEdgeDataType) => {
    addEdges([generateEdge(info)])
    setErFlowEdges((pre) => [...pre, info])
  }

  // 创建er节点
  const createNewReactErNode = (data: any, type?: string) => {
    const _dataModelDescType = type || dataModelDescType
    const info = data.data
    info.isFact = !hasFact
    // 流批一体不给设置链接桩
    const portsArray =
      _dataModelDescType === 'STREAM_BATCH'
        ? []
        : getPortsData(info, 'group1', 0, COLUMN_LIMIT).concat(getPortsData(info, 'group-collapsible', COLUMN_LIMIT))
    addNodes([
      {
        id: info.id,
        shape: 'er-node',
        data: info,
        x: data.x,
        y: data.y,
        ports: {
          groups: {
            group1: {
              attrs: {
                circle: {
                  magnet: true,
                  stroke: '#31d0c6',
                  fill: '#fff',
                },
              },
              // 文档：https://x6.antv.vision/zh/docs/api/registry/port-layout#absolute
              position: {
                name: 'absolute',
              },
            },
            'group-collapsible': {
              attrs: {
                circle: {
                  magnet: true,
                  stroke: '#31d0c6',
                  fill: '#fff',
                },
              },
              position: {
                name: 'absolute',
              },
            },
          },
          items: portsArray,
        },
      },
    ])

    if (!hasFact) {
      setHasFact(true)
    }
  }

  // 校验表
  const validateNodes = (info: any) => {
    return new Promise((resolve) => {
      //@todo 这一块在backend 无法解决 流表 computeType 的时候，先简单处理保证测试
      const computeType = info.data.tableEngine === 'Kafka' ? 'STREAM' : info?.data?.computeType || 'BATCH'
      // nodes 为0 代表 画布上面目前一张表都没有
      if (nodes.length === 0) {
        // 主表是批表，则默认是构建基于批表的er
        if (computeType === 'BATCH') {
          setDataModelDescType(computeType)
          resolve(computeType)
        } else {
          Modal.confirm({
            title: '检测到您使用流表作为主表，请选择您期望构建的模型',
            okButtonProps: {
              hidden: true,
            },
            cancelText: '关闭',
            onCancel: () => resolve(false),
            content: (
              <Space direction="vertical" size="large" style={{ width: '100%', margin: '20px 0' }}>
                <Button
                  type="primary"
                  block
                  onClick={() => {
                    setDataModelDescType('STREAM')
                    resolve('STREAM')
                    Modal.destroyAll()
                  }}
                >
                  ER模型
                </Button>
                <Button
                  type="primary"
                  block
                  onClick={() => {
                    setDataModelDescType('STREAM_BATCH')
                    resolve('STREAM_BATCH')
                    Modal.destroyAll()
                  }}
                >
                  流批一体表
                </Button>
              </Space>
            ),
          })
        }
      } else {
        // 主表为批表时，副表不能是流表
        if (dataModelDescType === 'BATCH' && computeType === 'STREAM') {
          Modal.warning({
            title: '提示',
            content: '当前不支持批式表作为主表，流式表作为副表的场景，请重新选择',
          })
          resolve(false)
        }
        // 如果构建 流批一体表，则只能有且只有一个副表，副表只能是批表
        if (dataModelDescType === 'STREAM_BATCH') {
          if (nodes.length === 1 && (computeType === 'STREAM' || computeType === 'STREAM_BATCH')) {
            Modal.warning({
              title: '提示',
              content: '当前为流批一体表，在主表为流表的情况下，副表不能为流表，请重新选择',
            })
            resolve(false)
          }
          if (nodes.length > 1) {
            Modal.warning({
              title: '提示',
              content: '当前为流批一体表，副表有且只有一张，不可多选副表',
            })
            resolve(false)
          }
          // 添加批表后开始检查数据一致性
          if (nodes.length === 1 && computeType === 'BATCH') {
            void message.loading({
              type: 'loading',
              content: '正在检验两张表schema是否一致',
              duration: 0,
            })

            const isPassed = isSchemaEqual(info?.data?.columns, nodes[0].data.columns)
            message.destroy()
            if (!isPassed) {
              Modal.warning({
                title: '提示',
                content: '当前为流批一体表，两张表schema不一致，请重新选择',
              })
              resolve(false)
            }
            resolve(dataModelDescType)
          }
        }
        resolve(dataModelDescType)
      }
    })
  }

  // 初始化画布内容，可用于回显er模型关系。创建则默认为空数组
  useEffect(() => {
    if (etlPaintInfo.createNodeStepShowOpen && etlPaintInfo.nodeInfo?.tableDetail) {
      const tableNode = etlPaintInfo.nodeInfo?.tableNode

      const table = etlPaintInfo.nodeInfo?.tableDetail
      const info = { ...(table || {}), id: numericalID(), isFact: true }
      info.columns = (info?.columns || []).map((item: any) => ({
        ...(item || {}),
        pk: [],
        fk: [],
      }))
      const currentDataModelDescType =
        (tableNode?.computeType || 'BATCH') !== 'BATCH'
          ? etlPaintInfo.type === etlPaintActionMap.BUSINESS_TABLE_STREAM_BATCH
            ? 'STREAM_BATCH'
            : 'STREAM'
          : 'BATCH'
      // 流批一体不给设置链接桩
      const portsArray =
        currentDataModelDescType === 'STREAM_BATCH'
          ? []
          : getPortsData(info, 'group1', 0, COLUMN_LIMIT).concat(getPortsData(info, 'group-collapsible', COLUMN_LIMIT))

      initData({
        nodes: [
          {
            id: info.id,
            shape: 'er-node',
            data: info,
            x: 20,
            y: 20,
            ports: {
              groups: {
                group1: {
                  attrs: {
                    circle: {
                      magnet: true,
                      stroke: '#31d0c6',
                      fill: '#fff',
                    },
                  },
                  // 文档：https://x6.antv.vision/zh/docs/api/registry/port-layout#absolute
                  position: {
                    name: 'absolute',
                  },
                },
                'group-collapsible': {
                  attrs: {
                    circle: {
                      magnet: true,
                      stroke: '#31d0c6',
                      fill: '#fff',
                    },
                  },
                  position: {
                    name: 'absolute',
                  },
                },
              },
              items: portsArray,
            },
          },
        ],
        edges: [],
      })
      setDataModelDescType(currentDataModelDescType)
      setHasFact(true)
      setErFlowEdges([])
    } else {
      initData({
        nodes: [],
        edges: [],
      })
      setHasFact(false)
      setErFlowEdges([])
    }
  }, [initData, etlPaintInfo])

  // 当nodes 和edges 发生变化的时候，及时callback
  useEffect(() => {
    onChange?.({
      nodes,
      edges: erFlowEdges,
      dataModelDescType,
    })
  }, [nodes, erFlowEdges, dataModelDescType, onChange])

  // 程序退出时，重置
  useEffect(() => {
    return () => {
      setHasFact(false)
      setDataModelDescType('')
    }
  }, [])

  return (
    <TableConfigModal
      tableType={dataModelDescType}
      open={tableCfgModelOpen}
      onClose={() => setTableCfgModelOpen(false)}
      data={tableInfo}
      onConfirm={createNewReactErNode}
    />
  )
}
