import React, { useContext, useEffect, useState } from 'react'
import { Button, Form, FormInstance, InputNumber, Modal, Select } from 'antd'
import type { SelectProps } from 'antd/lib'
import './TimeLimitModal.scss'
import clsx from 'clsx'
import { PlusOutlined, MinusOutlined } from '@ant-design/icons'
import { TimeLimitTimeOffsetTimeOptions } from 'src/shared/constants'
import { VTableColumnType } from 'src/client/pages/MetricStore/components/MetricModelTable/conf'
import { IVirtualTable } from 'src/shared/common-types'
import { get } from 'lodash'

const TimeLimitTimeOffsetOptions: { value: TimeLimitTimeOffsetProps['status']; label: string }[] = [
  { value: 'NONE', label: '-' },
  { value: 'PLUS', label: '加' },
  { value: 'MINUS', label: '减' },
]

const LogicCompareOptions = [
  { value: 'GT', label: '大于' },
  { value: 'LT', label: '小于' },
  { value: 'GE', label: '大于等于' },
  { value: 'LE', label: '小于等于' },
  { value: 'EQ', label: '等于' },
]

const TimeLimitContext = React.createContext<TimeLimitContextValue | null>(null)

const initialValuesExceptColumn = {
  [`time-limit1-input1-offset-select`]: TimeLimitTimeOffsetOptions[0].value,
  [`time-limit1-input2-offset-select`]: TimeLimitTimeOffsetOptions[0].value,
  [`time-limit2-input1-offset-select`]: TimeLimitTimeOffsetOptions[0].value,
  [`time-limit2-input2-offset-select`]: TimeLimitTimeOffsetOptions[0].value,

  [`time-limit1-input1-time-num`]: 0,
  [`time-limit1-input2-time-num`]: 0,
  [`time-limit2-input1-time-num`]: 0,
  [`time-limit2-input2-time-num`]: 0,

  [`time-limit1-input1-time-select`]: TimeLimitTimeOffsetTimeOptions[2].value,
  [`time-limit1-input2-time-select`]: TimeLimitTimeOffsetTimeOptions[2].value,
  [`time-limit2-input1-time-select`]: TimeLimitTimeOffsetTimeOptions[2].value,
  [`time-limit2-input2-time-select`]: TimeLimitTimeOffsetTimeOptions[2].value,

  [`time-limit1-logic-compare`]: LogicCompareOptions[0].value,
  [`time-limit2-logic-compare`]: LogicCompareOptions[0].value,

  'need-time-limit': false,
  'need-add': false,
}

interface TimeLimitTimeOffsetProps {
  status?: 'PLUS' | 'MINUS' | 'NONE'
  unit?: 'h' | 'm' | 's'
  value?: number
  onChange?: (val: number) => void
  prefix?: string
}

function TimeLimitTimeOffset(props: TimeLimitTimeOffsetProps) {
  const { prefix = '', status: _status } = props
  const { namePrefix } = useContext(TimeLimitContext)!
  let status = Form.useWatch(`${namePrefix}-${prefix}-offset-select`, Form.useFormInstance())
  if (_status) status = _status
  return (
    <>
      <Form.Item className="mb-0 ml-[-1px] w-[60px] shrink-0" name={`${namePrefix}-${prefix}-offset-select`}>
        <Select className="time-limit-modal-offset-select" options={TimeLimitTimeOffsetOptions} />
      </Form.Item>
      {status !== TimeLimitTimeOffsetOptions[0].value && (
        <>
          <Form.Item
            className="mb-0 ml-[-1px] w-[80px] shrink-0"
            name={`${namePrefix}-${prefix}-time-num`}
            rules={[{ required: true, message: '请输入' }]}
          >
            <InputNumber
              stringMode
              step="1"
              precision={1}
              className={clsx('time-limit-modal-time-num w-full rounded-[0px]')}
            />
          </Form.Item>
          <Form.Item className="mb-0 ml-[-1px] w-[80px] shrink-0" name={`${namePrefix}-${prefix}-time-select`}>
            <Select className="time-limit-modal-time-select" options={TimeLimitTimeOffsetTimeOptions} />
          </Form.Item>
        </>
      )}
    </>
  )
}

interface TimeLimitInputProps {
  prefix?: string
  selectProps?: Partial<SelectProps>
  timeOffsetProps?: Partial<TimeLimitTimeOffsetProps>
}

function TimeLimitInput(props: TimeLimitInputProps) {
  const { prefix } = props
  const { namePrefix } = useContext(TimeLimitContext)!
  return (
    <div className="time-limit-modal-time-input flex w-full">
      <Form.Item
        className="mb-0 ml-[-1px] w-full"
        name={`${namePrefix}-${prefix}-time-limit-select`}
        rules={[{ required: true, message: '请选择' }]}
      >
        <Select
          onChange={(val) => {
            console.log('val', val)
          }}
          {...props.selectProps}
          className={clsx('time-limit-modal-time-limit-select max-w-[400px]', props.selectProps?.className)}
        />
      </Form.Item>
      <TimeLimitTimeOffset prefix={props.prefix} {...props.timeOffsetProps} />
    </div>
  )
}

function LogicCompare() {
  const { namePrefix } = useContext(TimeLimitContext)!
  return (
    <Form.Item className="mb-0 w-[112px]" name={`${namePrefix}-logic-compare`}>
      <Select options={LogicCompareOptions} />
    </Form.Item>
  )
}

interface TimeLimitContextValue {
  namePrefix: string
}

interface TimeLimitModalProps {
  form: FormInstance<any>
  originData: any
  visible: boolean
  setVisible: (v: boolean) => void
}

// 用来生成value 可用于初始值获取 也可以用于 column 中获取
const getValueString = (node: { data: IVirtualTable }, getColumnName: (node: { data: IVirtualTable }) => string) => {
  const columnName = getColumnName(node)
  return columnName
    ? `${node?.data.catalogName}.${node?.data.databaseName}.${node?.data.name}.${columnName}~${node?.data.id}` // node?.data.timeColumn
    : undefined
}
// 获取时间列的options 类型只能为时间类型
const getOptions = (node: { data: IVirtualTable }, index: number) => {
  return node?.data.columns
    .filter((col: VTableColumnType) =>
      ['DATETIME', 'DATE', 'TIME', 'TIMESTAMP', 'TIMESTAMP_WITH_LOCAL_TIME_ZONE'].includes(col.columnType),
    )
    .map((col: VTableColumnType) => ({
      label: index === 0 ? `主表时间字段 ${col.name}` : `副表时间字段 ${col.name}`,
      value: getValueString(node, () => col.name),
    }))
}
export function TimeLimitModal(props: TimeLimitModalProps) {
  const { visible, setVisible, originData, form } = props
  const nodes = originData?.nodes ?? []
  const majorTableSelectOptions = getOptions(nodes[0], 0)
  const minorTableSelectOptions = getOptions(nodes[1], 1)
  const selectOptions = [
    {
      placeholder:
        nodes?.length < 1
          ? '请先拖入主表'
          : majorTableSelectOptions.length
            ? '主表无时间列请选择'
            : '该表中没有时间类型的字段，请重新选择虚拟表',
      value: getValueString(nodes[0], () => get(nodes[0], 'data.timeColumn')),
      options: majorTableSelectOptions,
    },
    {
      value: getValueString(nodes[1], () => get(nodes[0], 'data.timeColumn')),
      placeholder:
        nodes?.length <= 1
          ? '请先拖入副表'
          : minorTableSelectOptions.length
            ? '副表无时间列请选择'
            : '该表中没有时间类型的字段，请重新选择虚拟表',
      options: getOptions(nodes[1], 1),
    },
  ]
  // defaultTimeColumnValue maybe  undefined
  const defaultTimeColumnValue = {
    [`time-limit1-input1-time-limit-select`]: selectOptions[0].value,
    [`time-limit1-input2-time-limit-select`]: selectOptions[1].value,
    [`time-limit2-input1-time-limit-select`]: selectOptions[0].value,
    [`time-limit2-input2-time-limit-select`]: selectOptions[1].value,
  }

  // 做图表拉动的时候会触发重置 所以 图表拉动之前 如果有值就保存值并且记得恢复现场
  useEffect(() => {
    form.setFieldsValue({ ...defaultTimeColumnValue, ...form.getFieldsValue() })
  }, [originData, form])
  const timeLimit = (
    <>
      <TimeLimitInput
        prefix="input1"
        selectProps={{
          placeholder: selectOptions[0].placeholder,
          options: selectOptions[0].options,
        }}
      />
      <LogicCompare />
      <TimeLimitInput
        prefix="input2"
        selectProps={{
          placeholder: selectOptions[1].placeholder,
          options: selectOptions[1].options,
        }}
      />
    </>
  )
  const [currentFormValues, setCurrentFormValues] = useState({})
  useEffect(() => {
    if (visible) {
      setCurrentFormValues(form.getFieldsValue())
    }
  }, [visible])

  const needAdd = Form.useWatch('need-add', form)
  return (
    <Modal
      className="time-limit-modal"
      title="时间约束条件设置"
      open={visible}
      onOk={() => {
        // 数据校验 因为后面要有 value 切分的问题 所以 填充的时候就进行校验
        form
          .validateFields()
          .then(() => {
            setVisible(false)
            form.setFieldValue('need-time-limit', true)
          })
          .catch((err) => {
            console.log(err, '验证失败')
          })
      }}
      onCancel={() => {
        setVisible(false)
        form.setFieldsValue(currentFormValues)
      }}
      footer={(originNode) => {
        return (
          <div className="flex justify-between">
            <div>
              <Button
                onClick={() => {
                  form.setFieldsValue({ ...initialValuesExceptColumn, ...defaultTimeColumnValue })
                }}
              >
                清空
              </Button>
            </div>
            <div className="flex gap-[4px]">{originNode}</div>
          </div>
        )
      }}
    >
      <Form
        form={form}
        initialValues={initialValuesExceptColumn}
        onValuesChange={(_) => {
          form.setFieldValue('need-time-limit', true)
        }}
      >
        <div className="flex flex-col gap-4">
          <TimeLimitContext.Provider value={{ namePrefix: 'time-limit1' }}>{timeLimit}</TimeLimitContext.Provider>
          {needAdd && (
            <>
              <div>AND</div>
              <TimeLimitContext.Provider value={{ namePrefix: 'time-limit2' }}>{timeLimit}</TimeLimitContext.Provider>
            </>
          )}
          <a
            className="w-fit"
            type="link"
            onClick={() => {
              form.setFieldValue('need-add', !needAdd)
            }}
          >
            {needAdd ? (
              <>
                <MinusOutlined />
                删除AND条件
              </>
            ) : (
              <>
                <PlusOutlined />
                配置AND条件
              </>
            )}
          </a>
          <Form.Item name="need-add" className="hidden"></Form.Item>
          <Form.Item name="need-time-limit" className="hidden"></Form.Item>
        </div>
      </Form>
    </Modal>
  )
}
