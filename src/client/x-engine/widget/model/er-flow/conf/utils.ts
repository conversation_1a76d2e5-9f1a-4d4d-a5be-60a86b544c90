import { LINE_HEIGHT, NODE_HEADER_HEIGHT, NODE_WIDTH, RADIUS, JOIN_ENUM } from './constant'
import type { ErFlowEdgeDataType, VirtualTableType } from '@shared/xengine-types'

export const getPortsData = (info: any, groupName: string, start: number, end?: number) => {
  const length = info.columns?.length || 0
  const final = end ? end : length
  const halfLineHeight = LINE_HEIGHT / 2 
  const result = info.columns?.slice(start, final).map((item: any, index: number) => {
        const id = [
            info.catalogName,
            info.databaseName,
            info.id,
            info.isFact,
            item.name,
            info.parent || '0',
            info.joinType || '0',
        ].join('~')
        return generatePorts(id, groupName, {
            y: LINE_HEIGHT * (start + index) + NODE_HEADER_HEIGHT + halfLineHeight,
            magnet: true,
        })
    }) || []
    return result.flat()
  
}

export const generatePorts = (id: string, groupName: string, attrs: Record<string, any>) => {
    return [generateCirclePort(id, groupName, attrs, 0, 'left'), generateCirclePort(id, groupName, attrs, NODE_WIDTH, 'right')]
}

// function generateReactPort(id: string, attrs: Record<string, any>, x: number, position?: string, color?: string) {
//     return {
//         id: id + '~' + position,
//         markup: [
//             {
//                 tagName: 'rect',
//                 selector: 'portBody',
//             },
//         ],
//         args: {
//             position: position || 'top',
//         },
//         highlighting: false,
//         selected: false,
//         attrs: {
//             portBody: {
//                 width: PORT_WIDTH, //需要跟自定义节点每一行设置的一致
//                 height: LINE_HEIGHT, //需要跟自定义节点每一行设置的一致
//                 fill: color || 'rgba(255, 255, 255, 0)',
//                 magnet: true,
//                 transform: 'matrix(1,0,0,1,0,0)',
//                 x,
//                 ...attrs,
//             },
//         },
//         // zIndex: 0, //隐藏在节点层级之后
//     }
// }

function generateCirclePort(id: string, groupName: string, attrs: Record<string, any>, x: number, position?: string, color?: string) {
    return {
        id: id + '~' + position,
        group: groupName,
        args: {
            x,
            y: attrs.y,
        },
        attrs: {
            circle: {
                ...attrs,
                r: RADIUS,
                strokeWidth: 2,
                stroke: color,
            },
        },
        // zIndex: 0
    }
}

// 判断两张表的schema是否一致
export const isSchemaEqual = (schema1: VirtualTableType['columns'], schema2: VirtualTableType['columns']) => {
    if (schema1.length !== schema2.length) {
        return false
    }
  const sourceSchema = [...schema1 || []].sort((a, b) => a.name.localeCompare(b.name))
  const targetSchema = [...schema2 || []].sort((a, b) => a.name.localeCompare(b.name))
    for (let i = 0; i < sourceSchema.length; i++) {
        const schema1ColumnType = getFormatColumnType(sourceSchema[i].columnType)
        const schema2ColumnType = getFormatColumnType(targetSchema[i].columnType)
        if (sourceSchema[i].name !== targetSchema[i].name || schema1ColumnType !== schema2ColumnType) {
            return false
        }
    }
    return true
}

// 遇到 TIMESTAMP 的类型 暂时 当成 VARCHAR 处理
const getFormatColumnType = (columnType: string) => {
    if (columnType === 'TIMESTAMP') return 'VARCHAR'
    return columnType
}

// 生成添加画布的edge数据
export const generateEdge = (info: ErFlowEdgeDataType) => { 
  // 这个id具有语义化，注意修改
  return {
    id: `${info.source.cell}+${info.source.port}+${info.target.cell}+${info.target.port}`,
    target: info.target,
    source: info.source,
    selected: false,
    animated: false,
    zIndex: 1,
    data: {
      sourceData: info.sourceData,
      targetData: info.targetData,
      joinType: info.joinType,
      virtual: info.virtual === true
    },
    attrs: {
      line: {
        stroke: '#C2C8D5',
        strokeWidth: 1,
        targetMarker: {
          name: 'classic',
        },
      },
    },
    label: JOIN_ENUM[info.joinType as keyof typeof JOIN_ENUM],
      // label: { // todo 自定义react label
        //     draggable: false,
        //     movable: false,
        //     position: {
        //         distance: 0.5,
        //         args: {
        //             keepGradient: true,
        //             ensureLegibility: true,
        //         },
        //     },
        //     markup: Markup.getForeignObjectMarkup(),
        //     attrs: {
        //         fo: {
        //             width: 60,
        //             height: 30,
        //             x: 30,
        //             y: -15,
        //         },
        //     },
        // }
  }
}