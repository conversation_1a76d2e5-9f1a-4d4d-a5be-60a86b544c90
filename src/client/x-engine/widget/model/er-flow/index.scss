.xflow-container {
  width: 100%;
  height: 100%;
  padding-left: 300px;
  background: #fff;
  position: relative;
  .xflow-absolute {
    position: absolute;
    z-index: 1;
  }
  .xflow-dnd {
    left: 0;
    top: 0;
    bottom: 0;
    width: 300px;
    padding-bottom: 50px;
    overflow-x: hidden;
    overflow-y: scroll;
    background: #fff;
  }
  .xflow-mask {
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    background: #f8f8f8;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 30px;
    padding-left: 300px;
    color: #ccc;
  }
  .xflow-control {
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
  }
  .xflow-map {
    bottom: 0;
    right: 0;
  }
  .react-er-node {
    overflow: hidden;
    background: #ffffff;
    .node-header {
      padding: 10px;
      color: #171717;
    }
    .columns-list:not(:has(~ button)) .columns-item:last-child {
      border-bottom: none;
    }
    .columns-item {
      .columns-item-title {
        flex: 1;
        display: flex;
        align-items: center;
      }
    }
    .bg-dimension-true {
      background: #f4be8a;
    }
    .bg-metrics-true {
      background: #d9dff2;
    }
  }
  .x6-port-body {
    // visibility: hidden;
  }
  .x6-port-group-collapsible {
    visibility: hidden;
  }
  .x6-port-show > .x6-port-group-collapsible{
    visibility: visible;
  }
  .x6-edge-label rect {
    fill: none;
  }
  .x6-graph {
    border-radius: 6px;
    overflow: hidden;
  }
  .xflow-operation {
    $height: 52px;
    left: 300px;
    height: $height;
    top: -$height;
  }
}
.x6-node-selected rect {
  stroke: transparent;
}
.x6-highlight-stroke {
  display: none;
}
