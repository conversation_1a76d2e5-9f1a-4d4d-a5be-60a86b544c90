import { Popover, theme, Tree, Typography, type TreeDataNode, Input, Skeleton } from 'antd'
import React, { useState, useMemo, useEffect, useRef } from 'react'
import { Api } from '@api'
import { HolderOutlined } from '@ant-design/icons'
import { useDnd } from '@antv/xflow'
import { getUnitId, streamTableCatalogType } from '@libs/util'
import TextHighlight from 'src/client/components/TextHighlight'
import { debounce, omit } from 'lodash-es'
import { tableTypeName } from 'src/chrome/AskBI/src/constants'
import { useRequest } from 'ahooks'
import { useAtomValue } from 'jotai'
import { etlPaintInfoAtom } from '@atoms/er'
import request from 'src/shared/xengine-axios'
import { askBIApiUrls } from 'src/shared/url-map'
import type { VirtualTableType, CommonListType } from 'src/shared/xengine-types'

type TreeNodeType = TreeDataNode & {
  name: string
  _type: string
  isMatch: boolean
  children: TreeNodeType[] | null
}

// 生成treeData
async function generateTreeData(
  type: string,
  createNodeStepShowOpen: boolean,
  dataSceneId: string,
  parent?: { name: string; type: string; [key: string]: any },
): Promise<TreeNodeType[]> {
  let dataList = [] as any[]
  let isLeaf = false
  switch (type) {
    case 'catalog': {
      const catalogList = await Api.apiEngineV1CatalogListGet({ current: 1, pageSize: -1 }).then(
        (res) =>
          // 目前不支持流表，只支持INTERNAL类型
          res?.filter((item: any) => item.type === 'INTERNAL'),
        () => [],
      )
      for (let i = 0; i < catalogList?.length; ++i) {
        const catalogItem = catalogList[i]
        const children = await generateTreeData('database', createNodeStepShowOpen, dataSceneId, catalogItem)
        dataList.push({
          ...catalogItem,
          catalogType: catalogItem.type,
          children,
        })
      }
      break
    }
    case 'database': {
      if (parent) {
        const databaseList = await Api.apiEngineV1DatabaseListGet({
          current: 1,
          pageSize: -1,
          catalog: parent?.name,
        }).catch(() => [])
        for (let i = 0; i < databaseList?.length; ++i) {
          const databaseItem = databaseList[i]
          const children = await generateTreeData('table', createNodeStepShowOpen, dataSceneId, {
            ...databaseItem,
            type: parent.type,
            catalogType: parent.type,
            catalogName: parent.name,
          })
          dataList.push({
            ...databaseItem,
            children,
          })
        }
      }
      break
    }
    case 'table':
      {
        if (parent) {
          const isStreamTableCatalogType = streamTableCatalogType.includes(parent.type?.toLocaleLowerCase())
          const tableList = isStreamTableCatalogType
            ? await Api.apiEngineV1PtableListGet({
                current: '1',
                pageSize: '-1',
                catalog: parent.catalogName,
                database: parent.name,
              }).catch(() => ({ list: [], total: 0 }))
            : await request
                .get<unknown, CommonListType<VirtualTableType>>(askBIApiUrls.xengine.VTable.search, {
                  params: {
                    current: 1,
                    pageSize: -1,
                    catalog: parent.catalogName,
                    database: parent.name,
                    dataSceneId,
                  },
                })
                .catch(() => ({ list: [], total: 0 }))
          dataList = tableList.list
        }
        isLeaf = true
      }
      break
    default: {
      break
    }
  }
  return dataList.map((item) => ({
    title: item.name,
    key: `${type}_${item.id}_${getUnitId()}`,
    isLeaf,
    _type: type,
    ...item,
  }))
}

const TreeNode = () => {
  const { token } = theme.useToken()
  const { startDrag } = useDnd()
  const containerRef = useRef<HTMLDivElement>(null)
  const [searchValue, setSearchValue] = useState('')
  const [treeExpandKeys, setTreeExpandKeys] = useState<React.Key[]>([])
  const [treeHeight, setTreeHeight] = useState(500)
  const etlPaintInfo = useAtomValue(etlPaintInfoAtom)

  const handleMouseDown = (e: React.MouseEvent<Element, MouseEvent>, item: any) => {
    startDrag(
      {
        id: 'placeholder',
        shape: 'rect',
        width: 150,
        height: 32,
        attrs: {
          body: {
            stroke: '#D9DADD',
            strokeWidth: 1,
          },
        },
        data: item,
        label: item.name,
      },
      e,
    )
  }

  // 包装title，高亮显示
  function wrapTreeTitle({
    type,
    item,
    searchHightLight,
  }: {
    type: string
    item: { name: string; computeType: keyof typeof tableTypeName }
    searchHightLight?: string
  }) {
    switch (type) {
      case 'catalog':
      case 'database': {
        return <TextHighlight text={item.name} highlight={searchHightLight} />
      }
      case 'table': {
        return (
          <div
            style={{ width: '100%', display: 'flex', alignItems: 'center', overflow: 'hidden' }}
            onMouseDown={(e) => handleMouseDown(e, item)}
          >
            <HolderOutlined color={token.colorPrimary} />
            <div style={{ flex: 1, overflow: 'hidden', width: 0, marginLeft: 8 }}>
              <Popover content={item.name}>
                <Typography.Text ellipsis>
                  {tableTypeName[item.computeType]}
                  <TextHighlight text={item.name} highlight={searchHightLight} />
                </Typography.Text>
              </Popover>
            </div>
          </div>
        )
      }
      default: {
        return item.name
      }
    }
  }

  const { loading, data: treeData = [] } = useRequest(() =>
    generateTreeData('catalog', etlPaintInfo.createNodeStepShowOpen, etlPaintInfo.dataSceneId),
  )

  const showTreeData = useMemo(() => {
    const expandKeys: React.Key[] = []
    const searchValueLowerCase = (searchValue || '').toLocaleLowerCase()
    const generateTreeDataLoop = (data: TreeNodeType[]): TreeNodeType[] => {
      return data.map((tree) => {
        const children = tree.children ? generateTreeDataLoop(tree.children) : []
        const isMatch = tree.name.includes(searchValueLowerCase) || children.some((child) => child.isMatch)
        if (isMatch) {
          expandKeys.push(tree.key)
        }
        return {
          ...tree,
          isMatch,
          children: children as TreeNodeType[],
          title: tree.name,
        }
      })
    }
    if (searchValue) {
      const tree = generateTreeDataLoop(treeData)
      setTreeExpandKeys(expandKeys)
      return tree
    }
    return treeData
  }, [treeData, searchValue])

  useEffect(() => {
    if (containerRef.current) {
      const containerHeight = containerRef.current.getBoundingClientRect().height || 500
      setTreeHeight(containerHeight - 40)
    }
  }, [])

  return (
    <div className="mr-2 h-full" ref={containerRef}>
      {loading ? (
        <Skeleton active />
      ) : (
        <>
          <Input.Search
            onChange={debounce((e: React.ChangeEvent<HTMLInputElement>) => {
              setSearchValue(e.target.value)
            }, 400)}
            allowClear
            className="mb-2"
            placeholder="请输入搜索"
          />
          <Tree
            titleRender={(nodeData: TreeNodeType) => {
              const itemData = omit(nodeData as TreeNodeType & { computeType: keyof typeof tableTypeName }, [
                'key',
                'isLeaf',
                'title',
                'children',
              ])
              return wrapTreeTitle({
                type: nodeData._type,
                item: itemData,
                searchHightLight: searchValue,
              })
            }}
            showLine
            blockNode
            treeData={showTreeData}
            onExpand={setTreeExpandKeys}
            expandedKeys={treeExpandKeys}
            height={treeHeight}
          />
        </>
      )}
    </div>
  )
}

export default TreeNode
