import { Modal, Form, Select } from 'antd'
import { commonJoin } from './conf/constant'
import { cloneDeep } from 'lodash-es'
import { useGraphStore } from '@antv/xflow'
import React, { useEffect, useMemo } from 'react'
import axios from 'axios'
import { askBIApiUrls } from 'src/shared/url-map'
import { useRequest } from 'ahooks'

function StreamBatchTableUnion() {
  const { data: unionTypes, loading } = useRequest(() =>
    axios.get(askBIApiUrls.xengineVTable.unionType).then((res) => res.data.data),
  )
  return (
    <Form.Item
      name="unionType"
      label="更新模式"
      rules={[
        {
          required: true,
        },
      ]}
    >
      <Select
        options={unionTypes?.map((v: string) => ({ label: v, value: v }))}
        loading={loading}
        placeholder="请选择"
      />
    </Form.Item>
  )
}

interface Props {
  tableType: string
  open: boolean
  data?: any
  onClose?: () => void
  onConfirm?: (d: any) => void
}

export default function TableConfigModal(props: Props) {
  const { tableType, open, data, onClose, onConfirm } = props

  const [form] = Form.useForm()

  const nodes = useGraphStore((state) => state.nodes)

  const onCloseHandle = () => {
    form.resetFields()
    onClose && onClose()
  }

  const okHandle = async () => {
    const formValues = await form.validateFields()
    const d = cloneDeep(data)
    onCloseHandle()
    switch (tableType) {
      case 'STREAM':
      case 'BATCH': {
        if (formValues.timeColumn) {
          d.data.timeColumn = formValues.timeColumn
        }
        d.data.parent = formValues.parent
        d.data.joinType = formValues.joinType
        break
      }
      case 'STREAM_BATCH': {
        // unionType是放在unionDagDesc下一级添加，这里为了传递数据的统一性，放在node里面传递给外层，最后在请求创建的时候进行处理
        d.data.unionType = formValues.unionType
      }
    }
    onConfirm && onConfirm(d)
  }

  useEffect(() => {
    if (open) {
      // 初始化数据
      if (tableType !== 'STREAM_BATCH' && nodes.length === 1) {
        form.setFieldValue('parent', nodes[0].data.id)
      }
    }
  }, [open, tableType])

  const title = useMemo(() => (tableType === 'STREAM_BATCH' ? '构建表关系' : '请设置副表相关配置'), [tableType])
  const renderFormItems = useMemo(() => {
    if (tableType === 'STREAM_BATCH') {
      return <StreamBatchTableUnion />
    }
    return (
      <>
        <Form.Item name="parent" label="请选择当前副表父节点" required rules={[{ required: true }]}>
          <Select
            placeholder="请选择"
            options={nodes.map((item: any) => {
              return {
                label: item?.data.name,
                value: item?.id,
              }
            })}
          />
        </Form.Item>
        <Form.Item name="joinType" label="与父节点的join方式" required rules={[{ required: true }]}>
          <Select placeholder="请选择" options={commonJoin} />
        </Form.Item>
        <Form.Item name="timeColumn">
          <Select
            placeholder="请选择副表时间列（选填）"
            options={data?.data?.columns.map((item: any) => ({ label: item.name, value: item.name }))}
          />
        </Form.Item>
      </>
    )
  }, [tableType, nodes, data])

  if (!data) return null
  return (
    <Modal open={open} title={title} onOk={okHandle} onCancel={onCloseHandle} destroyOnClose>
      <Form form={form} layout={'vertical'}>
        {renderFormItems}
      </Form>
    </Modal>
  )
}
