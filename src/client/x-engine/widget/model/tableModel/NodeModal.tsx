// @ts-nocheck
import React, { useEffect } from 'react'
import { Col, Row, Form, Select, Button, Modal, Space } from 'antd'
import { MinusCircleOutlined, PlusCircleOutlined, PlusOutlined } from '@ant-design/icons'
import { getCharFinalStr } from '@libs/util'
import { type NodeType } from '../../../pages/data-model/create-business-virtual-table/conf'

const joinTypeList = [{ label: '左外连接', value: 'LEFT' }]

// 选择子节点组件
function SelectChildNode({ value, onChange, onClickPlusIcon, onClickMinusIcon, fields, options }) {
    const triggerChange = (changedValue) => {
        onChange?.(changedValue)
    }
    return (
        <>
            <Row gutter={[20, 0]}>
                <Col span='22'>
                    <Select
                        showSearch
                        value={value}
                        placeholder='请选择添加的子节点'
                        options={options}
                        onChange={triggerChange}
                    />
                </Col>
                <Col
                    span={2}
                    style={{
                        marginTop: 5,
                    }}
                >
                    <Space
                        direction='horizontal'
                        style={{
                            justifyContent: 'space-between',
                        }}
                    >
                        <PlusCircleOutlined
                            onClick={() => {
                                if (typeof onClickPlusIcon === 'function') {
                                    onClickPlusIcon()
                                }
                            }}
                        />
                        {fields.length > 0 ? (
                            <MinusCircleOutlined
                                onClick={() => {
                                    if (typeof onClickMinusIcon === 'function') {
                                        onClickMinusIcon()
                                    }
                                }}
                            />
                        ) : null}
                    </Space>
                </Col>
            </Row>
        </>
    )
}

// 获取排除之外的columnsOptions
function getColumnsOptions(vTable: string, excludeColumns: string[], data: VTableListItemType[]) {
    excludeColumns = Array.isArray(excludeColumns) ? excludeColumns : []
    if (vTable === null || vTable === '' || vTable === undefined) {
        return []
    }
    if (!data || !Array.isArray(data)) {
        return []
    }
    // 同一个database下
    const columns = data.find((d) => d.name === vTable)?.columns || []
    if (!columns) {
        return []
    }

    const options = columns.filter((col) => !excludeColumns.includes(col.name))
    return options.map((o) => ({ value: o.name, label: o.name }))
}

function getSelectChildNodeOptions(data: VTableListItemType[], excludeNodes: string[]) {
    if (!data || !Array.isArray(data)) {
        return []
    }
    const filterVT = data.filter((vt) => vt.virtualTableType && vt.virtualTableType === 'AS')
    const options = filterVT.filter((d) => !excludeNodes.includes(d.name))
    return options.map((o) => ({ value: o.name, label: o.name }))
}

type VTableListItemType = {
    name: string
    database: string
    columns: { name: string }[]
    [key: string]: any
}

function NodeModal({
    vTableList,
    handleFormFinsh,
    modalOpen,
    setModalOpen,
    initFormValues,
    parentNodes,
}: {
    vTableList: VTableListItemType[]
    handleFormFinsh?: (values: NodeType) => any
    modalOpen: boolean
    setModalOpen: any
    parentNodes: string[]
    initFormValues: NodeType & {
        id: string
        table: string
        database: string
    }
}) {
    const { table: dbTable, primaryKeys = [], foreignKeys = [], nodes = [] } = initFormValues || {}
    const selectedVTName = getCharFinalStr(dbTable)
    parentNodes = parentNodes.map((n) => getCharFinalStr(n.id, '.'))
    useEffect(() => {
        if (nodeForm) {
            // const filterForeignKeys = foreignKeys.filter((k) => k !== 'dt')
            // const filterNodes = nodes.map((n) => {
            //     const { joinKeys = [] } = n
            //     const filterJoinKeys = joinKeys.filter((e) => e.sourceKey !== 'dt' && e.targetKey !== 'dt')
            //     return {
            //         ...n,
            //         joinKeys: filterJoinKeys,
            //     }
            // })
            nodeForm.setFieldsValue({
                primaryKeys: primaryKeys,
                foreignKeys: foreignKeys,
                nodes: nodes,
            })
        }
    }, [initFormValues])

    const [nodeForm] = Form.useForm<NodeType>()
    function handleFormValuesChange(
        changedValue: {
            [key: keyof NodeType]: NodeType[keyof NodeType]
        },
        allValues: NodeType,
    ) {
        const changedKey = Object.keys(changedValue)[0]
        switch (changedKey) {
            case 'foreignKeys': {
                const { nodes = [], foreignKeys = [] } = allValues || {}
                const filterNodes = nodes.map((n) => {
                    const originJoinKeys = n.joinKeys || []
                    const joinKeys = originJoinKeys.map((e) => (foreignKeys.includes(e.sourceKey) ? e : undefined))
                    return {
                        ...n,
                        joinKeys,
                    }
                })
                nodeForm.setFieldsValue({
                    nodes: filterNodes,
                })

                break
            }
            case 'nodes': {
                const changeNodes: NodeType['nodes'] = changedValue[changedKey as keyof typeof changedValue] || []
                const innerChangeIndex = changeNodes.findIndex((item) => item)
                const innerChange = changeNodes[innerChangeIndex] || ({} as NodeType['nodes'][0])
                const innerChangeKey = innerChange && Object.keys(innerChange)[0]
                switch (innerChangeKey) {
                    case 'childNode': {
                        const changeChildNodeVal = innerChange[innerChangeKey]
                        const targetInitNodesData = (allValues.nodes || []).map((n) => {
                            const childNode = n.childNode
                            if (childNode === changeChildNodeVal) {
                                const joinKeys = (n.joinKeys || []).map(({ sourceKey }) => ({
                                    sourceKey,
                                    targetKey: undefined,
                                }))
                                return {
                                    ...n,
                                    joinKeys: joinKeys,
                                }
                            }
                            return n
                        })
                        nodeForm.setFieldsValue({
                            nodes: targetInitNodesData,
                        })
                        break
                    }
                }
                break
            }
        }
    }

    return (
        <>
            <Modal
                open={modalOpen}
                title='构建模型'
                width='940px'
                destroyOnClose
                onCancel={() => {
                    setModalOpen(false)
                    nodeForm.resetFields()
                }}
                maskClosable={false}
                onOk={async () => {
                    const values = await nodeForm.validateFields()
                    if (typeof handleFormFinsh === 'function') {
                        handleFormFinsh(values)
                    }
                    nodeForm.resetFields()
                    setModalOpen(false)
                }}
            >
                <Form
                    labelAlign='right'
                    labelCol={{ span: 3 }}
                    wrapperCol={{ span: 21 }}
                    preserve={false}
                    form={nodeForm}
                    onValuesChange={handleFormValuesChange}
                >
                    <Form.Item name='primaryKeys' label='主节点主键'>
                        <Select
                            showSearch
                            disabled
                            mode='multiple'
                            options={getColumnsOptions(
                                selectedVTName,
                                nodeForm.getFieldValue('foreignKeys') || [],
                                vTableList,
                            )}
                        />
                    </Form.Item>

                    <Form.Item
                        noStyle
                        shouldUpdate={(preValues, curValues) => {
                            return preValues.foreignKeys !== curValues.foreignKeys
                        }}
                    >
                        {() => (
                            <Form.Item name='foreignKeys' label='主节点外键'>
                                <Select
                                    showSearch
                                    mode='multiple'
                                    placeholder='请选择主节点外键'
                                    options={getColumnsOptions(
                                        selectedVTName,
                                        nodeForm.getFieldValue('primaryKeys') || [],
                                        vTableList,
                                    )}
                                />
                            </Form.Item>
                        )}
                    </Form.Item>

                    <Form.List name='nodes'>
                        {(fields, { add, remove }) => {
                            return (
                                <>
                                    {fields.map(({ key, name: topName }) => {
                                        return (
                                            <React.Fragment key={key}>
                                                <Form.Item noStyle shouldUpdate>
                                                    {() => (
                                                        <Form.Item
                                                            name={[topName, 'childNode']}
                                                            label='添加子节点'
                                                            rules={[
                                                                {
                                                                    required: true,
                                                                    message: '请选择',
                                                                },
                                                            ]}
                                                        >
                                                            <SelectChildNode
                                                                options={(() => {
                                                                    const nodes = nodeForm.getFieldValue('nodes') || []
                                                                    const nodeItemChildNodes = nodes
                                                                        .filter(
                                                                            (e: Record<string, any>) =>
                                                                                e && e.childNode,
                                                                        )
                                                                        .map((e: { childNode: string }) => e.childNode)
                                                                    const excludeNodes = [
                                                                        ...(parentNodes || []),
                                                                    ].concat(nodeItemChildNodes)
                                                                    return getSelectChildNodeOptions(
                                                                        vTableList,
                                                                        excludeNodes,
                                                                    )
                                                                })()}
                                                                fields={fields}
                                                                onClickMinusIcon={() => {
                                                                    remove(topName)
                                                                }}
                                                                onClickPlusIcon={() => {
                                                                    add()
                                                                }}
                                                            />
                                                        </Form.Item>
                                                    )}
                                                </Form.Item>

                                                <Form.Item
                                                    label='关联方式'
                                                    name={[topName, 'joinType']}
                                                    initialValue='LEFT'
                                                >
                                                    <Select
                                                        showSearch
                                                        placeholder={'请选择关联方式'}
                                                        options={joinTypeList.map((e) => ({
                                                            label: e.label,
                                                            value: e.value,
                                                        }))}
                                                    />
                                                </Form.Item>

                                                <Form.Item label='关联 key'>
                                                    <Form.Item shouldUpdate noStyle>
                                                        {() => (
                                                            <Row gutter={[20, 0]}>
                                                                <Col span={10}>{selectedVTName || '关联 key1'}</Col>
                                                                <Col span={10}>
                                                                    {(() => {
                                                                        const nodes = nodeForm.getFieldValue('nodes')
                                                                        return nodes && nodes[topName]?.childNode
                                                                            ? `${nodes[topName]?.childNode}(需选择该表关联主键)`
                                                                            : '关联 Key2 (选择为该表关联主键)'
                                                                    })()}
                                                                </Col>
                                                            </Row>
                                                        )}
                                                    </Form.Item>

                                                    {/* <Row
                                                        gutter={[20, 0]}
                                                        style={{
                                                            marginBottom: '20px',
                                                        }}
                                                    >
                                                        <Col span={10}>
                                                            <Select
                                                                showSearch
                                                                disabled
                                                                style={{ width: '100%' }}
                                                                defaultValue='dt'
                                                            />
                                                        </Col>
                                                        <Col span={10}>
                                                            <Select
                                                                showSearch
                                                                disabled
                                                                style={{ width: '100%' }}
                                                                defaultValue='dt'
                                                            />
                                                        </Col>
                                                    </Row> */}
                                                    <Form.List name={[topName, 'joinKeys']} initialValue={[{}]}>
                                                        {(fields, { add, remove }) => (
                                                            <>
                                                                {fields.map(({ key, name, ...restField }) => (
                                                                    <Row key={key} gutter={[20, 0]}>
                                                                        <Col span={10}>
                                                                            <Form.Item noStyle shouldUpdate>
                                                                                {() => (
                                                                                    <Form.Item
                                                                                        {...restField}
                                                                                        name={[name, 'sourceKey']}
                                                                                        rules={[
                                                                                            {
                                                                                                required: true,
                                                                                                message: '请输入字段名',
                                                                                            },
                                                                                        ]}
                                                                                    >
                                                                                        <Select
                                                                                            showSearch
                                                                                            placeholder='选择列'
                                                                                            options={(() => {
                                                                                                return (
                                                                                                    [
                                                                                                        ...new Set([
                                                                                                            ...(nodeForm.getFieldValue(
                                                                                                                'primaryKeys',
                                                                                                            ) || []),
                                                                                                            ...(nodeForm.getFieldValue(
                                                                                                                'foreignKeys',
                                                                                                            ) || []),
                                                                                                        ]),
                                                                                                    ]?.map((col) => ({
                                                                                                        value: col,
                                                                                                        label: col,
                                                                                                    })) || []
                                                                                                )
                                                                                            })()}
                                                                                        />
                                                                                    </Form.Item>
                                                                                )}
                                                                            </Form.Item>
                                                                        </Col>
                                                                        <Col span={10}>
                                                                            <Form.Item noStyle shouldUpdate>
                                                                                {() => (
                                                                                    <Form.Item
                                                                                        {...restField}
                                                                                        name={[name, 'targetKey']}
                                                                                        rules={[
                                                                                            {
                                                                                                required: true,
                                                                                                whitespace: true,
                                                                                                message: '请选择',
                                                                                            },
                                                                                        ]}
                                                                                    >
                                                                                        <Select
                                                                                            showSearch
                                                                                            placeholder='选择列'
                                                                                            options={(() => {
                                                                                                const node =
                                                                                                    nodeForm.getFieldValue(
                                                                                                        'nodes',
                                                                                                    )[topName]
                                                                                                const childNode =
                                                                                                    node.childNode
                                                                                                const excludeColumns =
                                                                                                    (
                                                                                                        node.joinKeys ||
                                                                                                        []
                                                                                                    )
                                                                                                        .filter(
                                                                                                            (
                                                                                                                e: Record<
                                                                                                                    string,
                                                                                                                    string
                                                                                                                >,
                                                                                                            ) => e,
                                                                                                        )
                                                                                                        .map(
                                                                                                            (
                                                                                                                e: Record<
                                                                                                                    string,
                                                                                                                    string
                                                                                                                >,
                                                                                                            ) =>
                                                                                                                e.targetKey,
                                                                                                        ) || []
                                                                                                return getColumnsOptions(
                                                                                                    childNode,
                                                                                                    excludeColumns,
                                                                                                    vTableList,
                                                                                                )
                                                                                            })()}
                                                                                        />
                                                                                    </Form.Item>
                                                                                )}
                                                                            </Form.Item>
                                                                        </Col>
                                                                        <Col
                                                                            span={4}
                                                                            style={{
                                                                                marginTop: 5,
                                                                            }}
                                                                        >
                                                                            <Space
                                                                                direction='horizontal'
                                                                                style={{
                                                                                    justifyContent: 'space-between',
                                                                                }}
                                                                            >
                                                                                <PlusCircleOutlined
                                                                                    onClick={() => add()}
                                                                                />
                                                                                {fields.length > 1 ? (
                                                                                    <MinusCircleOutlined
                                                                                        onClick={() => remove(name)}
                                                                                    />
                                                                                ) : null}
                                                                            </Space>
                                                                        </Col>
                                                                    </Row>
                                                                ))}
                                                            </>
                                                        )}
                                                    </Form.List>
                                                </Form.Item>
                                            </React.Fragment>
                                        )
                                    })}

                                    <Button
                                        type='dashed'
                                        onClick={() => {
                                            add()
                                        }}
                                        style={{ width: '90%', margin: '0 auto', display: 'block' }}
                                        icon={<PlusOutlined />}
                                    >
                                        添加子节点
                                    </Button>
                                </>
                            )
                        }}
                    </Form.List>
                </Form>
            </Modal>
        </>
    )
}
export default NodeModal
