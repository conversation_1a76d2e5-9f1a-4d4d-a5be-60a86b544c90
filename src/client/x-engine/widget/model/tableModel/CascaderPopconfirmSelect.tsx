// @ts-nocheck
import React, { useEffect, useState } from 'react'
import { Button, Input, Popconfirm, Cascader, Divider, Tooltip } from 'antd'
import { PlusOutlined, SearchOutlined, FormOutlined, CloseOutlined } from '@ant-design/icons'
import type { GetProp, CascaderProps } from 'antd'
import './cascaderPopconfirmSelect.scss'
interface Option {
    value: string
    label: string
    children?: Option[]
    disabled?: boolean
}

interface CascaderPopconfirmSelectValueType {
    select: string[]
    description?: string
}
type DefaultOptionType = GetProp<CascaderProps, 'options'>[number]

function hasValue(val: string, tar) {
    if (!val) {
        return true
    }
    if (!tar) {
        return false
    }
    const label = tar.label
    return label.includes(val) || (Array.isArray(tar.children) && tar.children.some((child) => hasValue(val, child)))
}

// 传递参数options
export default function CascaderPopconfirmSelect({
    value,
    onChange,
    btnText,
    options,
    addDescIcon,
    closeIcon,
}: {
    value?: CascaderPopconfirmSelectValueType[]
    onChange?: (value: any) => any
    btnText?: string
    options: Option[]
    addDescIcon?: boolean
    closeIcon?: boolean
}) {
    const [selectValue, setSelectValue] = useState<CascaderPopconfirmSelectValueType[]>(
        Array.isArray(value) ? value : [],
    )
    const [cascaderValue, setCascaderValue] = useState([])
    const [cascaderOptions, setCascaderOptions] = useState(options || [])

    useEffect(() => {
        if (Array.isArray(value)) {
            setSelectValue(value)
            setCascaderValue(value.map((i) => i.select))
        }
    }, [value])
    useEffect(() => {
        setCascaderOptions(options)
    }, [options])

    const onCascaderSelectChange = (value: string[][]) => {
        setCascaderValue(value)
    }

    const onPopconfirmComfirm = () => {
        const remainSelectValue = selectValue.filter((selectItem) =>
            cascaderValue.find((i) => i.join('.') === selectItem.select.join('.')),
        )
        const newAddValue = cascaderValue.filter(
            (i) => !remainSelectValue.find((selectItem) => selectItem.select.join('.') === i.join('.')),
        )
        const setValue = (newAddValue || []).map((v) => ({ select: v }))
        const newValue = [...remainSelectValue, ...setValue]
        setSelectValue(newValue)
        onChange(newValue)
    }

    // todo: 搜索
    const filter = (inputValue: string, path: DefaultOptionType[]) => {
        return path.some((option) => (option.label as string).toLowerCase().indexOf(inputValue.toLowerCase()) > -1)
    }

    function deleteSelectItem(selectItem: CascaderPopconfirmSelectValueType, selectItemIdx: number) {
        if (!selectItem || !selectItem.select) {
            return
        }
        const afterDeleteValues = [...selectValue.slice(0, selectItemIdx), ...selectValue.slice(selectItemIdx + 1)]
        const afterDeleteCascaderValue = cascaderValue.filter(
            (item) => !(item && item[0] === selectItem.select[0] && item[1] === selectItem.select[1]),
        )
        setCascaderValue(afterDeleteCascaderValue)
        setSelectValue(afterDeleteValues)
        onChange(afterDeleteValues)
    }

    return (
        <div>
            {(selectValue || []).map((selectItem, selectItemIdx) => (
                <React.Fragment key={selectItemIdx}>
                    <div
                        style={{
                            marginBottom: 8,
                            padding: `6px 12px`,
                            background: '#f9f0ff',
                            border: '1px solid rgba(57, 16, 133, .5)',
                            boxSizing: 'border-box',
                            borderRadius: '8px',
                            fontSize: '14px',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'space-between',
                        }}
                    >
                        <div
                            style={{
                                wordWrap: 'break-word',
                                flex: 'auto',
                                overflow: 'hidden',
                                whiteSpace: 'nowrap',
                                textOverflow: 'ellipsis',
                            }}
                            title={(selectItem.select || []).join('.')}
                        >
                            {(selectItem.select || []).join('.')}
                        </div>
                        {addDescIcon === true && (
                            <div
                                style={{
                                    flex: '0 0 18px',
                                }}
                            >
                                <Tooltip title='点击添加说明'>
                                    <PlusOutlined
                                        style={{
                                            cursor: 'pointer',
                                            margin: '0 0 0 4px',
                                        }}
                                        onClick={() => {
                                            if (selectItem.description === undefined) {
                                                const preSelectValue = selectValue.slice(0, selectItemIdx)
                                                const afterSelectValue = selectValue.slice(selectItemIdx + 1)
                                                const curSelectValueItem = { ...(selectItem || {}), description: '' }
                                                const newSelectValue = [
                                                    ...preSelectValue,
                                                    curSelectValueItem,
                                                    ...afterSelectValue,
                                                ]
                                                setSelectValue(newSelectValue)
                                            }
                                        }}
                                    />
                                </Tooltip>
                            </div>
                        )}
                        {closeIcon !== false && (
                            <div className='flex-[0_0_18px] cursor-pointer ml-4'>
                                <CloseOutlined
                                    onClick={() => {
                                        deleteSelectItem(selectItem, selectItemIdx)
                                    }}
                                />
                            </div>
                        )}
                    </div>
                    {selectItem.description !== undefined && (
                        <Input
                            prefix={
                                <Tooltip title='请输入说明'>
                                    <FormOutlined />
                                </Tooltip>
                            }
                            placeholder='请输入说明'
                            variant='borderless'
                            style={{
                                borderRadius: '0',
                                borderBottom: '1px solid black',
                                margin: '0 0 8px 0',
                            }}
                            onChange={(e) => {
                                const desc = e?.currentTarget?.value
                                const preSelectValue = selectValue.slice(0, selectItemIdx)
                                const afterSelectValue = selectValue.slice(selectItemIdx + 1)
                                const curSelectValueItem = { ...(selectItem || {}), description: desc }
                                const newSelectValue = [...preSelectValue, curSelectValueItem, ...afterSelectValue]
                                setSelectValue(newSelectValue)
                                onChange(newSelectValue)
                            }}
                        />
                    )}
                </React.Fragment>
            ))}

            <Popconfirm
                title={btnText}
                icon={false}
                placement='right'
                okText='保存'
                onConfirm={onPopconfirmComfirm}
                description={() => (
                    <div style={{ overflow: 'auto', minWidth: '280px' }}>
                        <Cascader
                            value={cascaderValue}
                            style={{
                                width: '100%',
                            }}
                            showCheckedStrategy={Cascader.SHOW_CHILD}
                            multiple
                            options={cascaderOptions}
                            onChange={onCascaderSelectChange}
                            showSearch={{ filter }}
                        >
                            <Input
                                placeholder='点击进行选择'
                                prefix={<SearchOutlined />}
                                allowClear
                                onChange={(e) => {
                                    const searchValue = e?.currentTarget?.value
                                    const filterOptions =
                                        options.filter((item) => {
                                            return hasValue(searchValue, item)
                                        }) || []
                                    setCascaderOptions(filterOptions)
                                }}
                            />
                        </Cascader>
                        <Divider
                            style={{
                                margin: '10px 0 0px 0',
                            }}
                        />

                        <div className='popconfirm-select-dropdown'>
                            {(cascaderValue || [])
                                .filter((v) => v && Array.isArray(v))
                                .map((v, index) => (
                                    <div className='popconfirm-select-item popconfirm-select-item-option' key={index}>
                                        <div className='popconfirm-select-item-option-content'>
                                            {(v || []).join('.')}
                                        </div>
                                    </div>
                                ))}
                        </div>
                    </div>
                )}
            >
                <Button type='dashed' icon={<PlusOutlined />} block>
                    {btnText}
                </Button>
            </Popconfirm>
        </div>
    )
}
