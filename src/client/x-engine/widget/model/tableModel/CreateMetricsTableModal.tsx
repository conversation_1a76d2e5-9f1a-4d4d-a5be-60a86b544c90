// @ts-nocheck
import React, { useState } from 'react'
import { Api } from '@api'
import { Form, Select, Input, Modal } from 'antd'
import { useRequest } from 'ahooks'
import { ArrowLeftOutlined, LoadingOutlined, CheckCircleTwoTone, CloseCircleOutlined } from '@ant-design/icons'

export interface MetricsTableBaseInfoType {
    catalog: string
    database: string
    semanticModelName: string
}

export default function CreateMetricsTableModal({
    open,
    onSuccessClose,
}: {
    open: boolean
    onSuccessClose?: (data: MetricsTableBaseInfoType) => void
}) {
    const [baseForm] = Form.useForm<MetricsTableBaseInfoType>()
    const [projectState, setProjectState] = useState<'processing' | 'success' | 'error' | 'unselect'>('unselect')
    const { run: createProject, loading: createProjectLoading } = useRequest(Api.apiEngineV1DbtCreateProjectPost, {
        manual: true,
        onSuccess() {
            setProjectState('success')
        },
        onError() {
            setProjectState('error')
        },
    })

    const { data: databaseData, loading: getDatabaseLoading } = useRequest(
        () => {
            return Api.apiEngineV1DatabaseListGet({
                current: 1,
                pageSize: 9999999,
                catalog: baseForm.getFieldValue('catalog'),
            })
        },
        {
            onSuccess(data) {
                if (data?.length > 0) {
                    const projectName = data[0].name
                    baseForm.setFieldValue('database', projectName)
                    createProject({
                        projectName: projectName,
                        schema: projectName,
                    })
                }
            },
        },
    )

    function getProjectStateIcon() {
        if (createProjectLoading) {
            return <LoadingOutlined />
        }
        switch (projectState) {
            case 'success':
                return <CheckCircleTwoTone twoToneColor='#52c41a' />
            case 'error':
                return <CloseCircleOutlined className='text-red-500' />
        }
        return <></>
    }

    function handleBaseFormValueChange(changedValues: {
        [key in keyof MetricsTableBaseInfoType]: MetricsTableBaseInfoType[keyof MetricsTableBaseInfoType]
    }) {
        const changedValueKey = Object.keys(changedValues)[0]
        if (!changedValueKey) {
            return
        }
        switch (changedValueKey) {
            case 'database': {
                const database = changedValues[changedValueKey]
                if (database) {
                    createProject({
                        projectName: database,
                        schema: database,
                    })
                }
                break
            }
        }
    }
    return (
        <Modal
            title={
                <div
                    style={{
                        display: 'flex',
                        alignItems: 'center',
                    }}
                >
                    {' '}
                    <span onClick={() => window.history.back()}>
                        <ArrowLeftOutlined />
                    </span>
                    &nbsp; <span>创建指标虚拟表</span>
                </div>
            }
            open={open}
            onOk={async () => {
                await baseForm.validateFields()
                if (typeof onSuccessClose === 'function') {
                    onSuccessClose(baseForm.getFieldsValue())
                }
            }}
            onCancel={async () => {
                await baseForm.validateFields()
                if (typeof onSuccessClose === 'function') {
                    onSuccessClose(baseForm.getFieldsValue())
                }
            }}
        >
            <Form
                form={baseForm}
                layout='vertical'
                initialValues={{
                    catalog: 'dipeak',
                }}
                onValuesChange={handleBaseFormValueChange}
            >
                <Form.Item label='指标虚拟表目录' name='catalog' rules={[{ required: true }]}>
                    <Select
                        options={[
                            {
                                label: 'dipeak',
                                value: 'dipeak',
                            },
                        ]}
                        placeholder='请选择指标虚拟表目录'
                    />
                </Form.Item>
                {/* todo：请求虚拟表库 */}
                <Form.Item
                    label={<>指标虚拟表库 &nbsp;{getProjectStateIcon()}</>}
                    name='database'
                    rules={[{ required: true }]}
                >
                    <Select
                        loading={getDatabaseLoading}
                        options={(databaseData || []).map((i) => ({
                            label: i.name,
                            value: i.name,
                        }))}
                        placeholder='请选择指标虚拟表库'
                    />
                </Form.Item>

                <Form.Item
                    name='semanticModelName'
                    label='指标虚拟表名称'
                    validateFirst
                    validateDebounce={1000}
                    rules={[
                        {
                            required: true,
                        },
                        {
                            validator() {
                                return new Promise((resolve, reject) => {
                                    switch (projectState) {
                                        case 'processing': {
                                            reject('指标虚拟表库正在检验是否可用')
                                            break
                                        }
                                        case 'error': {
                                            reject('此指标虚拟表库不可用')
                                            break
                                        }
                                        case 'success': {
                                            resolve('success')
                                            break
                                        }
                                    }
                                })
                            },
                        },
                        {
                            validator: (_, value) => {
                                const projectName = baseForm.getFieldValue('database')
                                if (!projectName) {
                                    return Promise.reject('请先选择指标虚拟表库')
                                }
                                const valid = new Promise((resolve, reject) => {
                                    return Api.apiEngineV1DbtIsSemanticModelExistGet({
                                        projectName: projectName,
                                        name: value,
                                    }).then((data) => {
                                        if (data) {
                                            reject('指标虚拟表名称冲突，请更换指标虚拟表名称')
                                        } else {
                                            resolve('success')
                                        }
                                    })
                                })
                                return valid
                            },
                        },
                    ]}
                >
                    <Input placeholder='请输入指标虚拟表的名称' />
                </Form.Item>
            </Form>
        </Modal>
    )
}
