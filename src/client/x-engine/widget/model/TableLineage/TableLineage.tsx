import React, { useEffect, useRef } from 'react'
import G6, { Graph } from '@antv/g6'
import './shape-conf'
import { askBIPageUrls } from 'src/shared/url-map'
import { routerMap as XEngineRouterMap } from '@XEngineRouter/routerMap'
import { createRoot } from 'react-dom/client'
import { flushSync } from 'react-dom'
import './table-lineage.scss'
import debounce from 'lodash-es/debounce'

type PropsType = {
  data: TableLineageOriginDataType
  renderAddMenuComponent?: (data: any) => React.ReactNode
  handleMenuClick?: (nodeInfo: any, operationType: string) => any
}

const MIN_WIDTH = 800
const MIN_HEIGHT = 400

export type TableLineageOriginDataType = {
  nodes: {
    id: string
    title: string
    nodeType: string
    extra?: string
    tableType?: string
    type: string
    relationType?: string
    size?: [number, number]
    computeType?: string
    meta?: any
    [key: string]: any
  }[]
  edges: {
    source: string
    target: string
    style?: { endArrow?: boolean }
  }[]
}
const tableTypeNavPathMap = {
  likeVirtualTable: XEngineRouterMap.dataModel.virtualTableDetail.path,
  asVirtualTable: XEngineRouterMap.dataModel.businessVirtualTableDetail.path,
  externalTable: askBIPageUrls.manage.externalDatasource.tableDetail,
  dataModel: askBIPageUrls.manage.metricModel.list,
}

export default function TableLineage({ data, handleMenuClick, renderAddMenuComponent }: PropsType) {
  const containerRef = useRef<HTMLDivElement>(null)
  const graphRef = useRef<Graph>()
  const currentAddNodeInfo = useRef({})

  useEffect(() => {
    const container = containerRef.current
    let graphInstance: Graph
    if (container) {
      // 文字省略提示的tooltip
      const textInfoTooltip = new G6.Tooltip({
        offsetX: -4,
        offsetY: 0,
        shouldBegin(e) {
          const shape = e?.shape
          if (shape) {
            const type = shape.get('type')
            const name = shape.get('name')
            const isElliptical = shape.attr('isElliptical')
            if (name === 'relation') {
              return true
            }
            if (type === 'text' && isElliptical) {
              return true
            }
            // if (name === 'table-type-icon') {
            //   return true
            // }
          }
          return false
        },
        getContent(e) {
          const shape = e?.shape
          if (!shape) {
            return ''
          }
          let text = ''
          const name = shape.get('name')
          switch (name) {
            case 'title-text':
            case 'extra-text': {
              text = shape.get('meta')?.text ?? ''
              break
            }
            case 'table-type-icon': {
              text = shape.get('meta')?.tableTypeLabel
              break
            }
            case 'relation': {
              text = shape.get('meta')?.title
            }
          }
          const outDiv = document.createElement('div')
          outDiv.style.maxWidth = '320px'
          outDiv.style.maxHeight = '480px'
          outDiv.style.padding = '10px'
          outDiv.style.fontSize = '14px'
          outDiv.style.overflow = 'auto'
          outDiv.oncontextmenu = (contextmenuEvent) => {
            contextmenuEvent.preventDefault()
            graph.emit('contextmenu', e)
          }
          outDiv.innerHTML = text
          return outDiv
        },
        itemTypes: ['node'],
      })
      // 添加菜单的tooltip
      const addMenuTooltip = new G6.Tooltip({
        trigger: 'click',
        style: {
          background: '#fff',
          padding: '0', // 设置 padding 为 0
          border: '1px solid red',
          borderRadius: '4px',
          boxShadow: '2px 2px 4px rgba(0, 0, 0, 0.1)',
        },
        shouldBegin(e) {
          const name = e?.shape?.get('name')
          if (name === 'add-circle' || name === 'add-circle-text') {
            return true
          }
          return false
        },
        shouldEnd() {
          return true
        },
        getContent() {
          const outDiv = document.createElement('div')
          const root = createRoot(outDiv)
          outDiv.className = 'add-node-tooltip'
          flushSync(() => {
            root.render(<>{renderAddMenuComponent?.(currentAddNodeInfo.current || {})}</>)
          })
          return outDiv
        },
      })
      // 删除菜单
      const deleteMenu = new G6.Menu({
        getContent(e) {
          // if (e?.item) {
          //   graphRef.current?.setItemState(e.item, 'active', true)
          // }
          const model = e?.item?.getModel() || ({} as any)
          return `
            <ul class="table-lineage-menu">
              <li data-title="delete-canvas">在画布中删除</li>
              <li data-title="delete-instance">删除实体数据</li>
              <li data-title="detail">查看详情</li>
              ${model.tableType === 'likeVirtualTable' ? '<li data-title="modify">编辑</li>' : ''}
            </ul>`
        },
        shouldBegin(e) {
          const model = e?.item?.getModel() || ({} as any)
          return model.nodeType === 'TABLE' || model.nodeType === 'COMMON_SCENE'
        },
        handleMenuClick: (target, item) => {
          const title = target?.getAttribute('data-title')
          const nodeData = (item?.getModel() || {}) as any
          if (title === 'delete-canvas') {
            handleMenuClick?.(nodeData.meta, 'DELETE_CANVAS')
          }
          if (title === 'delete-instance') {
            handleMenuClick?.(nodeData.meta, 'DELETE_CANVAS_AND_REAL')
          }
          if (title === 'detail') {
            switch (nodeData.nodeType) {
              case 'TABLE': {
                const navPath = tableTypeNavPathMap[nodeData.tableType as keyof typeof tableTypeNavPathMap]
                const qualifiedTable = nodeData.meta?.tableNode?.qualifiedTable
                if (navPath && qualifiedTable?.catalog && qualifiedTable?.database && qualifiedTable.table) {
                  window.open(
                    `${navPath}?catalog=${qualifiedTable.catalog}&database=${qualifiedTable.database}&name=${qualifiedTable.table}`,
                  )
                }
                break
              }
              case 'COMMON_SCENE': {
                const commonScene = nodeData.meta?.commonSceneNode
                window.open(
                  `${askBIPageUrls.scenarios.detail}?projectId=${commonScene.projectId}&scenarioId=${commonScene.sceneId}`,
                )
                break
              }
              default:
                break
            }
          }
          if (title === 'modify') {
            handleMenuClick?.(nodeData.meta, 'MODIFY')
          }
          // graph?.setItemState(item, 'active', false)
        },
      })
      const plugins = [textInfoTooltip, addMenuTooltip, deleteMenu]
      const width = container.scrollWidth
      const height = container.scrollHeight || 500
      const graph = new G6.Graph({
        container,
        width,
        height,
        defaultNode: {
          type: 'table-rect',
          anchorPoints: [
            [0, 0.5],
            [1, 0.5],
          ],
        },
        defaultEdge: {
          // type: 'cubic-horizontal',
          type: 'smooth-edge',
        },
        modes: {
          default: ['drag-node', 'drag-canvas', 'zoom-canvas'],
        },
        plugins,
        layout: {
          type: 'self-layout',
        },
      })
      graphInstance = graph

      graph.data(data)
      graph.render()
      // 开始渲染位置
      graph.translate(0, 40)
      graphRef.current = graph
      graph.on('canvas:dragstart', () => {
        const canvasElement = graph.get('canvas').get('el')
        canvasElement.style.cursor = 'grabbing'
      })

      // canvas:dragend
      graph.on('canvas:dragend', () => {
        const canvasElement = graph.get('canvas').get('el')
        canvasElement.style.cursor = 'grab'
      })

      graph.on('node:mouseenter', (e) => {
        const { item } = e
        if (!item) return
        const model = (e.item?.getModel() || {}) as any
        if (
          model.tableType === 'likeVirtualTable' ||
          model.tableType === 'asVirtualTable' ||
          model.tableType === 'dataModel'
        ) {
          ;(item.getContainer() || [])
            .findAll((shape) => {
              const name = shape.get('name')
              return name === 'add-circle' || name === 'add-circle-text'
            })
            .forEach((shape) => {
              shape.show()
            })
          currentAddNodeInfo.current = model.meta
        }
      })

      graph.on('node:mouseleave', (e) => {
        const { item } = e
        const addNodeTooltipContainer = document.querySelector('.add-node-tooltip')?.parentNode as HTMLElement
        const addMenuShow = addNodeTooltipContainer && addNodeTooltipContainer?.style?.display !== 'none'
        if (!item || addMenuShow) return
        const { x, y } = e
        const model = (e.item?.getModel() || {}) as any
        const { size } = model
        if (
          model.tableType === 'likeVirtualTable' ||
          model.tableType === 'asVirtualTable' ||
          model.tableType === 'dataModel'
        ) {
          const nodeRect = {
            x: model.x,
            y: model.y,
            width: size[0] + 16,
            height: size[1],
          }

          if (
            x >= nodeRect.x &&
            x <= nodeRect.x + nodeRect.width &&
            y >= nodeRect.y &&
            y <= nodeRect.y + nodeRect.height
          ) {
            // 如果鼠标仍然在节点范围内，不移除圆形加号
            return
          }
          ;(item.getContainer() || {})
            .findAll((shape) => {
              const name = shape.get('name')
              return name === 'add-circle' || name === 'add-circle-text'
            })
            .forEach((shape) => {
              shape.hide()
            })
          currentAddNodeInfo.current = {}
        }
      })

      graph.on('node:click', (e) => {
        const name = e.shape.get('name')
        if (name === 'mv') {
          const qualifiedMv = (e.item?.getModel()?.meta as any)?.tableNode?.qualifiedMv
          const searchParams = new URLSearchParams({
            catalogName: qualifiedMv.catalog,
            dbName: qualifiedMv.database,
            mvName: qualifiedMv.view,
          }).toString()
          window.open(`${XEngineRouterMap.smartx.materialViewDetail.path}?${searchParams}`)
        }
      })
    }
    // 窗口自适应
    const resizeDebounceFn = debounce(() => {
      if (window) {
        if (!graphInstance || graphInstance.get('destroyed')) return
        if (!container || !container.clientWidth || !container.clientHeight) return
        const resizeWidth = container.clientWidth
        const resizeHeight = container.clientHeight
        const w = resizeWidth < MIN_WIDTH ? MIN_WIDTH : resizeWidth
        const h = resizeHeight < MIN_HEIGHT ? MIN_HEIGHT : resizeHeight
        graphInstance.changeSize(w, h)
      }
    }, 1000)

    window.addEventListener('resize', resizeDebounceFn)
    return () => {
      window.removeEventListener('resize', resizeDebounceFn)
      if (graphRef.current) {
        graphRef.current.destroy()
      }
    }
  }, [])
  return (
    <div className="table-lineage h-full w-full">
      <div ref={containerRef} className="h-full w-full bg-[#F8F8F8] dark:bg-gray-800" />
    </div>
  )
}
