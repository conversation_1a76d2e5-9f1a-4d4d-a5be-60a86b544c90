import G6 from '@antv/g6'
import tableLineageIcon from './tableLineageIcon.png'
import relationFilterIcon from './filterIcon.png'
import relationJoinIcon from './joinIcon.png'
import metricModelIcon from './metricModelIcon.png'
import { customerIsSupportType } from 'src/shared/customer-resolver'

const isNumber = (x: number) => typeof x === 'number'
const isAllowGetLocation = (model: { size: [number, number]; x: number; y: number }) => {
  return isNumber(model.size[0]) && isNumber(model.size[1]) && isNumber(model.x) && isNumber(model.y)
}

const getPoint = (start: number, end: number, long: number) => {
  if (start <= end) {
    return start + long
  }
  return start
}

const renderIcon = (nodeType: string, tableType: string) => {
  if (nodeType === 'COMMON_SCENE' || tableType === 'dataModel') {
    return metricModelIcon
  }
  return tableLineageIcon
}
const fittingString = (str: string, maxWidth: number, fontSize: number) => {
  const ellipsis = '...'
  const ellipsisLength = G6.Util.getTextSize(ellipsis, fontSize)[0]
  let currentWidth = 0
  let res = str
  const pattern = new RegExp('[\u4E00-\u9FA5]+') // distinguish the Chinese characters and letters
  str?.split('')?.forEach((letter, i) => {
    if (currentWidth > maxWidth - ellipsisLength) return
    if (pattern.test(letter)) {
      // Chinese characters
      currentWidth += fontSize
    } else {
      // get the width of single letter according to the fontSize
      currentWidth += G6.Util.getLetterWidth(letter, fontSize)
    }
    if (currentWidth > maxWidth - ellipsisLength) {
      res = `${str.substring(0, i + 1)}${ellipsis}`
    }
  })
  return res
}

export const tableTypeMap = {
  likeVirtualTable: '贴源虚拟表',
  asVirtualTable: '业务虚拟表',
  externalTable: '外部数据表',
  dataModel: '数据模型',
  physicalTable: '物理表',
}

G6.registerNode('table-rect', {
  draw(cfg, group) {
    const { title, tableType, extra, size, meta: originMeta } = cfg
    const fittingTitle = fittingString(title as string, 194, 14)
    const fittingExtra = fittingString(extra as string, 172, 12)
    const isTitleElliptical = fittingTitle !== title
    const isExtraElliptical = fittingExtra !== extra

    const meta = cfg
    const width = (size as any)?.[0] || 226
    const height = (size as any)?.[1] || 69

    const keyShape = group.addShape('rect', {
      attrs: {
        x: 0,
        y: 0,
        width,
        height,
        stroke: '#eee',
        fill: '#fff',
        radius: [6, 6, 6, 6],
        cursor: 'move',
      },
      draggable: true,
      name: 'table-lineage-wrap',
      meta,
    })
    group.addShape('text', {
      attrs: {
        fontSize: 14,
        x: 16,
        y: 32,
        text: fittingTitle,
        fill: '#171717',
        isElliptical: isTitleElliptical,
        cursor: isTitleElliptical ? 'pointer' : '',
      },
      name: 'title-text',
      meta: {
        ...meta,
        text: title,
      },
    })
    group.addShape('text', {
      attrs: {
        fontSize: 12,
        x: 38,
        y: 52,
        text: fittingExtra,
        fill: '#575757',
        isElliptical: isExtraElliptical,
        cursor: isExtraElliptical ? 'pointer' : '',
      },
      meta: {
        ...meta,
        text: extra,
      },
      name: 'extra-text',
    })
    group.addShape('image', {
      attrs: {
        x: 16,
        y: 38,
        img: renderIcon((originMeta as any)?.nodeType || '', tableType as string),
        cursor: 'pointer',
        width: 16,
        height: 16,
      },
      meta: {
        ...meta,
        tableTypeLabel: tableTypeMap[tableType as keyof typeof tableTypeMap] ?? tableType,
      },
      name: 'table-type-icon',
    })
    if (
      tableType === 'likeVirtualTable' ||
      tableType === 'asVirtualTable' ||
      (!customerIsSupportType('etlSceneHide') && tableType === 'dataModel')
    ) {
      const addCircle = group.addShape('circle', {
        attrs: {
          x: 244,
          y: 35,
          r: 16,
          labelText: '+',
          iconFontFamily: 'iconfont',
          iconText: '\ue602',
          fill: '#fff',
          stroke: '#eee',
          lineWidth: 1.4,
          cursor: 'pointer',
        },
        name: 'add-circle',
      })
      const addCircleText = group.addShape('text', {
        attrs: {
          x: 244, // 圆心 x 坐标
          y: 33, // 圆心 y 坐标
          text: '+',
          fontSize: 22, // 字体大小
          fill: '#575757', // 字体颜色
          textAlign: 'center', // 文本居中
          textBaseline: 'middle', // 文本垂直居中
          cursor: 'pointer',
        },
        name: 'add-circle-text',
      })
      addCircle.hide()
      addCircleText.hide()
    }

    // 显示flink任务
    // if ((cfg?.meta as any)?.tableNode?.published) {
    //   group.addShape('rect', {
    //     attrs: {
    //       width: width - 40,
    //       height: 1,
    //       x: 20,
    //       y: 60,
    //       fill: '#EDEDED',
    //     },
    //   })
    //   group.addShape('text', {
    //     attrs: {
    //       fontSize: 12,
    //       x: 18,
    //       y: 80,
    //       text: 'flink任务',
    //       fill: '#7750FE',
    //       // cursor: 'pointer',
    //     },
    //     name: '已上线',
    //   })
    // }

    // 显示mv信息
    if ((cfg?.meta as any)?.tableNode?.qualifiedMv) {
      group.addShape('rect', {
        attrs: {
          width: width - 40,
          height: 1,
          x: 20,
          y: 60,
          fill: '#EDEDED',
        },
      })
      group.addShape('text', {
        attrs: {
          fontSize: 12,
          x: 18,
          y: 80,
          text: '物化视图',
          fill: '#7750FE',
          cursor: 'pointer',
        },
        name: 'mv',
      })
    }

    return keyShape
  },
  setState(name, value, item) {
    const group = item?.getContainer() || []
    const shape = group.find((e) => e.get('name') === 'table-lineage-wrap')
    switch (name) {
      case 'active': {
        if (value) {
          shape?.attr({
            stroke: '#7750FE',
          })
        } else {
          shape?.attr({
            stroke: '#eee',
          })
        }
        break
      }
      default:
        break
    }
  },
})

G6.registerNode('relation', {
  options: {
    size: [36, 36],
  },
  draw(cfg, group) {
    const { title } = cfg
    // todo： 待验证
    const icon = cfg.relationType === 'JOIN' ? relationJoinIcon : relationFilterIcon
    const keyShape = group.addShape('image', {
      attrs: {
        img: icon,
        cursor: 'pointer',
        width: 36,
        height: 36,
      },
      meta: {
        title,
      },
      name: 'relation',
    })
    return keyShape
  },
})

G6.registerLayout('self-layout', {
  // 默认参数
  getDefaultCfg() {
    return {}
  },
  // 执行布局
  execute() {
    const { nodes, edges } = this
    const data = { nodes, edges }
    // const dagre = new G6.Layout['antv-dagre']()
    const dagre = new G6.Layout['dagre']({
      rankdir: 'LR',
      align: 'DR',
      controlPoints: true,
      begin: [100, 200],
      nodesepFunc: () => 20,
      ranksepFunc: () => 60,
      preventOverlap: true,
    })
    dagre.layout(data)
    // 用x、y计算得到rank、order
    // 先利用带x、y的数据组成一个二维数组
    // 然后根据rank、order调整x、y
    const matrix = buildMatrix(data.nodes, 'LR', 50)
    adjustSpace(matrix, 'LR', 30, 100, 60)
    // rank一调整一行都要调
  },
})

// 会改变原始数据
function adjustSpace<T extends { x: number; y: number; [key: string]: any }>(
  data: T[][],
  direction: string,
  error: number,
  // todo：这个可能是函数，那每个rank里面都不一样，现在item传下面data[i][j]
  rankSpace: number,
  orderSpace: number,
) {
  if (!data.length) {
    return data
  }
  if (direction === 'LR') {
    let preEnd = Math.min(...data[0].map((point) => point.x)) - rankSpace
    for (let i = 0; i < data.length; ++i) {
      const layer = data[i]
      const minX = Math.min(...layer.map((point) => point.x))
      const rankBlank = Math.ceil(minX - preEnd - rankSpace)
      if (rankBlank > error || rankBlank < 0) {
        layer.forEach((d) => {
          d.x = d.x - rankBlank
        })
      }
      preEnd = Math.max(...layer.map((point) => point.x + (point?.size?.[0] || 0)))
      let lastLayerItemEnd = layer[0].y - orderSpace
      for (let j = 0; j < layer.length; ++j) {
        const item = layer[j]
        const orderBlank = Math.ceil(item.y - lastLayerItemEnd - orderSpace)
        if (orderBlank < 0 || orderBlank > error) {
          item.y = item.y - orderBlank
        }
        lastLayerItemEnd = item.y + (item?.size?.[1] || 0)
      }
    }
  }
  return data
}

// 行就是按照direction排列的rank，然后每一行里面就是order排列
function buildMatrix<T extends { x: number; y: number; [key: string]: any }>(
  data: T[],
  direction: string,
  error: number,
) {
  const matrix: T[][] = []
  const copyData = data.slice()
  if (direction === 'LR') {
    copyData.sort((a, b) => a.x - b.x)
  }
  for (let i = 0; i < copyData.length; ++i) {
    const { x, y } = copyData[i]
    // 从左到右分rank
    if (direction === 'LR') {
      const rankIndex = binarySearch<T[]>(matrix, (item) => {
        const itemX = Math.min(...item.map((point) => point.x))
        if (x >= itemX && x <= itemX + error) {
          return 0
        } else if (x < itemX) {
          return 1
        } else {
          return -1
        }
      })
      if (!matrix[rankIndex]) {
        matrix[rankIndex] = []
      }
      const orderIndex = binarySearch<T>(matrix[rankIndex], (item) => {
        const itemY = item.y
        if (y === itemY) {
          return 0
        } else if (y < itemY) {
          return 1
        } else {
          return -1
        }
      })
      matrix[rankIndex].splice(orderIndex, 0, copyData[i])
    }
  }
  return matrix
}

// left左边都是满足条件的
function binarySearch<T = any>(arr: T[], targetFn: (item: T) => number) {
  if (!arr.length) return 0 // 如果数组为空，直接返回 -1

  let left = 0 // 左边界
  let right = arr.length - 1 // 右边界

  while (left <= right) {
    const mid = Math.floor((left + right) / 2) // 计算中间索引
    const compareValue = targetFn(arr[mid])
    if (compareValue === 0) {
      return mid
    } else if (compareValue < 0) {
      // 如果中间值小于目标值，调整左边界
      left = mid + 1
    } else {
      // 如果中间值大于目标值，调整右边界
      right = mid - 1
    }
  }
  // 如果循环结束仍未找到目标值，返回 -1
  return left
}
G6.registerEdge(
  'smooth-edge',

  {
    draw(cfg, group) {
      const { sourceNode, targetNode } = cfg as any
      // const parallelLineLong = 6
      const sourceModel = sourceNode?.getModel()
      const targetModel = targetNode?.getModel()

      const sourceBBox = sourceNode?.getBBox()
      const targetBBox = targetNode?.getBBox()

      const start = isAllowGetLocation(sourceModel)
        ? {
            x: getPoint(sourceModel.x, targetModel.x, sourceModel.size[0]),
            y: sourceModel.y + Math.ceil(sourceModel.size[1] / 2),
          }
        : {
            x: getPoint(sourceBBox.minX, targetBBox.minX, sourceBBox.maxX - sourceBBox.minX),
            y: sourceBBox.centerY,
          }

      const end = isAllowGetLocation(targetModel)
        ? {
            x: getPoint(targetModel.x, sourceModel.x, targetModel.size[0]),
            y: targetModel.y + Math.ceil(targetModel.size[1] / 2),
          }
        : {
            x: getPoint(targetBBox.minX, sourceBBox.minX, targetBBox.maxX - targetBBox.minX),
            y: targetBBox.centerY,
          }
      // 计算边的路径
      // const shape = group.addShape('path', {
      //   attrs: {
      //     path: [
      //       ['M', start?.x, start?.y],
      //       ['L', start?.x + parallelLineLong, start?.y],
      //       [
      //         'C',
      //         end.x / 3 + (2 / 3) * end.x,
      //         start.y,
      //         end.x / 3 + (2 / 3) * (start.x + parallelLineLong),
      //         end.y,
      //         end.x - parallelLineLong,
      //         end.y,
      //       ],
      //       ['L', end.x, end.y],
      //     ],
      //     endArrow: !!cfg.style?.endArrow,
      //     stroke: '#D3D3D3',
      //     lineWidth: 1,
      //   },
      // })
      // #7F94A8 #6C849A #A3B1BF #B1BECB #C2C9D1 #E0E5E9 #D1D9E1 #BFC8D2 #8291A0 #95A3B1 #AAB7C4

      const hgap = Math.abs(end.x - start.x)

      const path = [
        ['M', start.x, start.y],
        ['C', start.x + hgap / 4, start.y, end.x - hgap / 2, end.y, end.x, end.y],
      ]

      const shape = group.addShape('path', {
        attrs: {
          stroke: '#C2C9D1',
          path,
          endArrow: !!cfg.style?.endArrow,
          lineWidth: 1,
        },
        // must be assigned in G6 3.3 and later versions. it can be any string you want, but should be unique in a custom item type
        name: 'smooth-path-shape',
      })

      return shape
    },
    update: undefined,
  },
  'cubic',
)
