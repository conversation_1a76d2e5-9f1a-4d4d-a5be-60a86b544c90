import React, { useState } from 'react'
import { Modal, Upload, message, Result, Button, Row, Col, Statistic, Space, Typography } from 'antd'
import { type UploadFile } from 'antd'
import { InboxOutlined, EyeOutlined } from '@ant-design/icons'
import axios from 'axios'
import { useRequest } from 'ahooks'
import { useNavigate } from 'react-router-dom'
import { routerMap } from '@XEngineRouter/routerMap'
import { askBIApiUrls } from 'src/shared/url-map'
// import { TOKEN_RANGER } from 'src/shared/constants'
// import { stateStore } from 'src/client/utils'
// import { currentLoginUserAtom } from 'src/client/pages/AskBI/askBIAtoms'

const { Dragger } = Upload

function validateFileExt(file: File, exts?: string[]): boolean {
  if (!file) return false
  const { name: fileName } = file
  const fileExt = fileName.match(/\.[^.]+$/)?.pop()
  if (!fileExt || !exts?.includes(fileExt)) {
    return false
  }
  return true
}

function viewFile(file: File | UploadFile) {
  const f = file instanceof File ? file : file?.originFileObj
  if (!(f instanceof File)) return
  const fileUrl = URL.createObjectURL(f)
  window.open(fileUrl)
}

const sampleList = {
  likeVT: [
    {
      fileName: '示例.csv',
      fileUrl: `${askBIApiUrls.model.CSVUploadTemplate}?type=BATCH_LIKE_MODE_VIRTUAL_TABLE`,
    },
  ],
}

async function downloadFile(fileUrl: string, filename: string) {
  const response = await axios.get(fileUrl, {
    // headers: { TOKEN_RANGER: `${localStorage.getItem(TOKEN_RANGER)}` },
    responseType: 'blob',
  })
    // const response = await fetch(fileUrl, {
    //     headers: { Authorization: `Bearer ${stateStore.get(currentLoginUserAtom)?.token}` },
    // })
    // const blob = await response.blob()
  const link = document.createElement('a')
  link.href = URL.createObjectURL(response.data)
  link.download = filename
  link.click()
  URL.revokeObjectURL(link.href)
  void message.success('下载成功')
}

function BatchUploadModal(props: {
  open?: boolean
  title?: string
  sampleType?: keyof typeof sampleList
  uploadUrl: string
  onClose?: () => unknown
  onConfirm?: () => unknown
}) {
  const { open = true, title = '批量上传', uploadUrl, onClose, onConfirm, sampleType } = props
  const [uploadFile, setUploadFile] = useState<Blob | null>(null)
  const [result, setResult] = useState<any>(null)

  const navigate = useNavigate()

  const { loading: uploadBtnLoading, run: uploadRun } = useRequest(
    () => {
      const formData = new FormData()
      uploadFile && formData.append('file', uploadFile as Blob)
      return axios
        .post(uploadUrl, formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
            // Authorization: `Bearer ${stateStore.get(currentLoginUserAtom)?.token}`,
          },
        })
        .then((res) => {
          if (onConfirm && typeof onConfirm === 'function') {
            onConfirm()
          }
          setResult(res.data?.data)
        })
        .catch((err) => {
          if (
            err.response.status === 400 &&
            err.response.data === 'Found invisible chars in line 1, please modify and upload again.'
          ) {
            message.error('文件中发现不明字符，可以尝试修改保存文件，重新上传')
          } else {
            message.error('上传失败')
          }
        })
    },
    {
      manual: true,
    },
  )

  const closeHandle = () => {
    setUploadFile(null)
    setResult(null)
    if (onClose && typeof onClose === 'function') {
      onClose()
    }
  }

  const okHandle = () => {
    if (!uploadFile) {
      void message.error('请先上传文件')
      return
    }
    uploadRun()
  }

  const samples = sampleList[sampleType as keyof typeof sampleList]

  return (
    <>
      <Modal
        open={open}
        title={title}
        onCancel={() => closeHandle()}
        width="600px"
        destroyOnClose={true}
        footer={
          result ? (
            <div />
          ) : (
            <Space>
              <Button onClick={closeHandle}>取消</Button>
              <Button type="primary" loading={uploadBtnLoading} onClick={okHandle}>
                确定
              </Button>
            </Space>
          )
        }
      >
        <br />
        {result ? (
          <Result
            status="success"
            title="处理完成"
            subTitle={
              <Row gutter={16} style={{ marginTop: 16 }}>
                <Col span={8}>
                  <Statistic title="创建总数" value={result?.total || 0} />
                </Col>
                <Col span={8}>
                  <Statistic title="成功个数" value={result?.successNumber || 0} />
                </Col>
                <Col span={8}>
                  <Statistic title="失败个数" value={result?.failedNumber || 0} valueStyle={{ color: '#cf1322' }} />
                </Col>
              </Row>
            }
            extra={
              result && result.failedNumber > 0
                ? [
                    <Button
                      type="primary"
                      onClick={() => navigate(routerMap.batchInfo.path, { state: { result, type: 'like' } })}
                    >
                      查看失败原因
                    </Button>,
                  ]
                : [
                    <Button type="primary" onClick={closeHandle}>
                      确定
                    </Button>,
                  ]
            }
          />
        ) : (
          <Dragger
            listType="text"
            maxCount={1}
            accept=".csv"
            showUploadList={{
              showDownloadIcon: true,
              downloadIcon: <EyeOutlined />,
            }}
            onDownload={(file) => {
              viewFile(file)
            }}
            beforeUpload={(file) => {
              const valid = [
                () => {
                  const v = validateFileExt(file, ['.csv'])
                  if (!v) {
                    message.error('上传文件后缀名不符')
                  }
                  return v
                },
              ].every((bool) => bool())
              if (!valid) {
                return Upload.LIST_IGNORE
              }
            }}
            onChange={(uploadInfo) => {
              const { file } = uploadInfo
              return {
                file: Object.assign(file, {
                  status: 'done',
                  response: '{"status": "success"}',
                }),
              }
            }}
            customRequest={(args) => {
              const { file } = args
              setUploadFile(file as Blob)
            }}
          >
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">点击或将文件拖拽到此区域进行上传</p>
            <p className="ant-upload-hint">支持扩展名为：*.csv文件</p>
            <p>
              <Typography.Text mark>tips：单次上传数量限制在1000条以内</Typography.Text>
            </p>
            {samples && samples.length > 0 && (
              <p className="ant-upload-hint">
                示例文件下载：
                {samples.map((sample, index) => (
                  <a
                    onClick={(e) => {
                      e.preventDefault()
                      e.stopPropagation()
                      downloadFile(sample.fileUrl, sample.fileName)
                    }}
                  >
                    {`${sample.fileName}${index === samples.length - 1 ? '' : '、'}`}
                  </a>
                ))}
              </p>
            )}
          </Dragger>
        )}
        <br />
      </Modal>
    </>
  )
}

export default BatchUploadModal
