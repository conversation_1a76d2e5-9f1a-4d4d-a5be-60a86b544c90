// @ts-nocheck
import type { Dayjs } from 'dayjs'
import { Select, TimePicker, InputNumber } from 'antd'

const timeFormatMap = {
  minute: 'ss',
  hour: 'mm:ss',
  day: 'HH:mm:ss',
  week: 'HH:mm:ss',
  month: 'HH:mm:ss',
}
const timeUnitList = [
  { label: '分钟', value: 'minute' },
  { label: '小时', value: 'hour' },
  { label: '天', value: 'day' },
  { label: '周', value: 'week' },
  { label: '月', value: 'month' },
] as const
const weekList = [
  { label: '周一', value: 'Mon' },
  { label: '周二', value: 'Tues' },
  { label: '周三', value: 'Wed' },
  { label: '周四', value: 'Thur' },
  { label: '周五', value: 'Fri' },
  { label: '周六', value: 'Sat' },
  { label: '周日', value: 'Sun' },
]
const monthList = Array.from({ length: 31 }, (_, index) => ({ label: index + 1, value: index + 1 }))

export interface RawCorn {
  frequency: number // 频率
  timeUnit: (typeof timeUnitList)[number]['value'] // "minute" | "hour" | "day" | "week" | "month"
  time: {
    hh?: string // 小时
    mm?: string // 分钟
    ss?: string //秒
  }
  weekStr?: string // 如果时间频率是周 那么这天是星期几
  monthNum?: number //// 如果时间频率是月 那么这天是几号
}

export const CornComp: React.FC<{
  value: RawCorn
  onChange: (data: RawCorn) => void
}> = ({ value: rawCorn, onChange: setRawCorn }) => {
  const onTimeChange = (_time: Dayjs, timeString: string) => {
    setRawCorn({
      ...rawCorn,
      time: convertTimeStr(timeString),
    })
  }
  const onHzChange = (value: number) => {
    setRawCorn({
      ...rawCorn,
      frequency: value,
    })
  }
  const onTimeUnitChange = (value: 'minute' | 'hour' | 'day' | 'week' | 'month') => {
    const updatedRawCorn = {
      ...rawCorn,
      timeUnit: value,
    }
    // The conversion function is based on the timeUnit, so it is OK to have both weekStr and monthNum
    if (value === 'week') {
      updatedRawCorn.weekStr = 'Mon'
    } else if (value === 'month') {
      updatedRawCorn.monthNum = 1
    }
    setRawCorn(updatedRawCorn)
  }
  return (
    <div style={{ width: '100%', display: 'flex' }}>
      <span className="cursor-default rounded-lg bg-[#f5f5f5] px-1 py-0 leading-8 text-[#c3c5c6]">每</span>
      <InputNumber min={0} onChange={onHzChange} />
      <Select
        style={{ width: '90px' }}
        defaultValue={'day'}
        onChange={onTimeUnitChange}
        options={timeUnitList.map((e) => ({
          label: e.label,
          value: e.value,
        }))}
      />
      {rawCorn.timeUnit === 'week' && (
        <Select
          style={{ width: '90px' }}
          defaultValue={'Mon'}
          onChange={(value: string) => {
            setRawCorn({
              ...rawCorn,
              weekStr: value,
            })
          }}
          options={weekList.map((e) => ({
            label: e.label,
            value: e.value,
          }))}
        />
      )}
      {rawCorn.timeUnit === 'month' && (
        <>
          <Select
            style={{ width: '68px' }}
            defaultValue={1}
            onChange={(value: number) => {
              setRawCorn({
                ...rawCorn,
                monthNum: value,
              })
            }}
            options={monthList.map((e) => ({
              label: e.label,
              value: e.value,
            }))}
          />
          <span className="cursor-default rounded-lg bg-[#f5f5f5] px-1 py-0 leading-8 text-[#c3c5c6]">号</span>
        </>
      )}
      <TimePicker
        style={{ width: rawCorn.timeUnit === 'week' || rawCorn.timeUnit === 'month' ? '120px' : '210px' }}
        onChange={onTimeChange}
        format={timeFormatMap[rawCorn.timeUnit]}
      />
    </div>
  )
}

function convertTimeStr(time: string) {
  if (time === '') return {}
  const timeList = time.split(':')
  const timeObj = {
    hh: '00',
    mm: '00',
    ss: '00',
  }
  if (timeList.length === 3) {
    timeObj.hh = timeList[0]
    timeObj.mm = timeList[1]
    timeObj.ss = timeList[2]
  } else if (timeList.length === 2) {
    timeObj.mm = timeList[0]
    timeObj.ss = timeList[1]
  } else if (timeList.length === 1) {
    timeObj.ss = timeList[0]
  }
  return timeObj
}

function getTrimmedNumber(num: string) {
  let numStr = num.toString()
  if (numStr.length === 2 && numStr[0] === '0') {
    numStr = numStr.slice(1) // 去掉十位上的 0
  }
  return Number(numStr)
}

export function toCorn(rawCorn: RawCorn): string {
  const { frequency, timeUnit, time, weekStr, monthNum } = rawCorn
  if ((timeUnit === 'day' || timeUnit === 'week' || timeUnit === 'month') && (!time.mm || !time.ss || !time.hh)) {
    return ''
  }
  if (timeUnit === 'hour' && (!time.mm || !time.ss)) return ''
  if (timeUnit === 'minute' && !time.ss) return ''
  let cronExp: string
  const frequencyExp = frequency === 0 || null ? `*` : `*/${frequency}`
  const weekFrequency = frequency === 0 || null ? `${weekStr}` : `${weekStr}/${frequency}`
  const ss = getTrimmedNumber(time.ss)
  const mm = getTrimmedNumber(time.mm)
  const hh = getTrimmedNumber(time.hh)
  switch (timeUnit) {
    case 'minute':
      cronExp = `${ss} ${frequencyExp} * * * ?`
      break
    case 'hour':
      cronExp = `${ss} ${mm} ${frequencyExp} * * ?`
      break
    case 'day':
      cronExp = `${ss} ${mm} ${hh} ${frequencyExp} * ?`
      break
    case 'week':
      cronExp = `${ss} ${mm} ${hh} ? * ${weekFrequency}`
      break
    case 'month':
      cronExp = `${ss} ${mm} ${hh} ${monthNum} ${frequencyExp} ?`
      break
    default:
      throw new Error('Invalid time unit')
  }
  return cronExp
}
