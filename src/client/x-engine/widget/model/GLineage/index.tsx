import React, { useEffect, useRef, useState } from 'react'
import G6, { Graph } from '@antv/g6'
import { initConf, formatData } from './initConf'
import { Tag, Slider, Empty, message } from 'antd'
import './lineage.scss'
import cs from './relative.module.scss'
import {
  ZoomInOutlined,
  ZoomOutOutlined,
  AlignCenterOutlined,
  ColumnWidthOutlined,
  OneToOneOutlined,
} from '@ant-design/icons'
import type { GLineagePropsType } from './type'
import { resolveItemsNameOccupyWidthWithData } from './ShapesConf/shapeInit'
import { throttle } from 'lodash-es'
import { copyToClipboard } from '@libs/util'

const ZOOM_STEP = 0.1
const MIN_ZOOM = 0.1

const toolBarList = [
  {
    type: 'zoomIn',
    icon: ZoomInOutlined,
    title: '放大',
    onClick: (graph: Graph) => {
      if (graph) {
        const width = graph.getWidth()
        const height = graph.getHeight()
        const centerWidth = Math.ceil(width / 2)
        const centerHeight = Math.ceil(height / 2)
        const zoom = graph.getZoom()
        const zoomIn = zoom + zoom * ZOOM_STEP
        graph.zoomTo(zoomIn, { x: centerWidth, y: centerHeight })
      }
    },
  },
  {
    type: 'zoomOut',
    icon: ZoomOutOutlined,
    title: '缩小',
    onClick: (graph: Graph) => {
      if (graph) {
        const width = graph.getWidth()
        const height = graph.getHeight()
        const centerWidth = Math.ceil(width / 2)
        const centerHeight = Math.ceil(height / 2)
        const zoom = graph.getZoom()
        const zoomOut = zoom <= zoom * ZOOM_STEP + MIN_ZOOM ? zoom : zoom - zoom * ZOOM_STEP
        graph.zoomTo(zoomOut, { x: centerWidth, y: centerHeight })
      }
    },
  },
  {
    type: 'alignCenter',
    icon: AlignCenterOutlined,
    title: '居中',
    onClick: (graph: Graph) => {
      if (graph) {
        graph.fitCenter()
      }
    },
  },
  {
    type: 'selfAdaption',
    icon: ColumnWidthOutlined,
    title: '自适应',
    onClick: (graph: Graph) => {
      if (graph) {
        graph.fitView()
      }
    },
  },
  {
    type: 'initStatus',
    icon: OneToOneOutlined,
    title: '初始状态',
    onClick: (graph: Graph) => {
      if (graph) {
        graph.moveTo(0, 30)
        graph.zoomTo(1)
      }
    },
  },
]

const minimapGenerate = (miniMapEl: HTMLDivElement) =>
  new G6.Minimap({
    container: miniMapEl,
    size: [150, 100],
    type: 'delegate',
  })

const GLineage: React.FC<GLineagePropsType> = (props) => {
  const graphRef = useRef<Graph | null>(null)
  const miniMapRef = useRef<any>(null)
  const containerRef = useRef<any>(null)
  const [legendInfo, setLegendInfo] = useState<any[]>([])
  const [zoom, setZoom] = useState(50)
  const {
    typeKey = 'type',
    data: initData,
    rightTopIconType,
    handleClickRightTopIcon,
    afterInitGraph,
    graphSettings,
    nodeShapeCfg: initNodeShapeCfg,
    menuProps,
    emptyDescriptionText,
  } = props
  const isEmptyData = !initData?.nodes?.length
  const nodeShapeCfg = initNodeShapeCfg
  useEffect(() => {
    if (isEmptyData) {
      return
    }
    const container = containerRef.current
    const miniMapEl = miniMapRef.current
    if (container) {
      const tooltip = new G6.Tooltip({
        offsetX: 10,
        offsetY: 20,
        shouldBegin(e) {
          const shape = e?.shape
          if (shape) {
            const name = shape.get('name')
            const text = shape.attr('text')
            if (text && name) {
              return true
            }
          }
          return false
        },
        getContent(e) {
          const shape = e?.shape
          if (!shape) {
            return ''
          }
          const name = shape.get('name')
          const text = shape.attr('text')
          if (!text || !name) {
            return ''
          }
          const info = shape.attr('full')
          const nameReg = /^title-text$/g
          const itemReg = /^item-/g
          const liListCtx = []
          if (nameReg.test(name)) {
            const { text } = info
            if (text && typeof text === 'string') {
              liListCtx.push(text)
            }
          }
          if (itemReg.test(name)) {
            const { name: itemName, subInfo } = info
            const itemSubInfo = Array.isArray(subInfo) ? subInfo : []
            const liCtxArr = [itemName, ...itemSubInfo]
              .filter((i) => i.text && typeof i.text === 'string')
              .map((i) => i.text)
            liListCtx.push(...liCtxArr)
          }
          if (liListCtx.length <= 0) {
            return ''
          }
          const outDiv = document.createElement('div') as any
          outDiv.style = {
            display: 'inline-block',
          }
          let innerCtx = `<ul style="padding:0 0 0 8px;margin:0;">`

          liListCtx.forEach((ctx) => {
            innerCtx += `<li style="padding:0;margin:0;">${ctx}</li>`
          })
          innerCtx += '</ul>'
          outDiv.innerHTML = innerCtx
          return outDiv
        },
        itemTypes: ['node'],
      })
      container.innerHTML = ''
      miniMapEl.innerHTML = ''
      const minimap = minimapGenerate(miniMapEl)
      const width = container.scrollWidth
      const height = (container.scrollHeight || 450) - 10
      const { data, legendInfo } = formatData({
        data: initData,
        typeKey: typeKey,
        nodeShapeCfg,
      })
      const resolvedItemsShapeInfo = resolveItemsNameOccupyWidthWithData(data?.nodes as any, nodeShapeCfg as any)
      const resolvedNodeShapeCfg = Object.assign(nodeShapeCfg || {}, resolvedItemsShapeInfo)
      // 统一在这里init配置
      initConf(graphRef, {
        rightTopIconType,
        handleClickRightTopIcon,
        nodeShapeCfg: resolvedNodeShapeCfg,
      })
      const plugins = [minimap, tooltip]
      if (menuProps?.content) {
        const contextMenu = new G6.Menu({
          getContent() {
            return menuProps.content || ''
          },
          handleMenuClick: (target, item) => {
            if (typeof menuProps.handleClickMenu === 'function') {
              menuProps.handleClickMenu(target, item.getModel())
            }
          },
          offsetX: 16 + 10,
          offsetY: 0,
          itemTypes: ['node'],
        })
        plugins.push(contextMenu as any)
      }
      if (legendInfo) {
        const legends = Object.keys(legendInfo).map((k) => {
          const { legendText, fill } = legendInfo[k as keyof typeof legendInfo]
          return {
            label: legendText,
            color: fill,
          }
        })
        setLegendInfo(legends)
      }
      const graph = new G6.Graph({
        container: container,
        width,
        height,
        modes: {
          default: ['dice-er-scroll', 'drag-node', 'drag-canvas'],
        },
        layout: {
          type: 'dagre',
          begin: [30, 30],
          rankdir: 'LR', // 可选，默认为图的中心
          align: 'UL', // 可选
          nodesepFunc: (d: any) => {
            const { items = [] } = d
            const i = Array.isArray(items) ? items.length : 0
            if (i > 10) {
              return i * 8 + 50
            }
            return 40
          },
          ranksep: 40, // 可选
          controlPoints: true, // 可选
        },
        plugins: plugins,
        animate: true,
        fitView: Boolean(graphSettings?.isFitView),
        fitViewPadding: [30, 20, 20, 20],
      })
      if (typeof afterInitGraph === 'function') {
        afterInitGraph(graph)
      }
      graph.on('canvas:dragstart', () => {
        const canvasElement = graph.get('canvas').get('el')
        canvasElement.style.cursor = 'grabbing'
      })

      // canvas:dragend
      graph.on('canvas:dragend', () => {
        const canvasElement = graph.get('canvas').get('el')
        canvasElement.style.cursor = 'grab'
      })

      // 添加双击复制功能
      graph.on(
        'node:dblclick',
        throttle((e) => {
          const fullText = e.shape?.attr('full')?.text
          const allowCopy = e.shape?.attr('allowCopy')
          if (allowCopy && fullText) {
            copyToClipboard(fullText).then(() => message.success(`已复制${fullText}`))
          }
        }, 1000),
      )
      graph.data(data)
      graph.render()
      graph.moveTo(10, 40)
      graphRef.current = graph
    }
    return () => {
      if (graphRef.current) {
        graphRef.current.destroy()
      }
    }
  }, [typeKey, afterInitGraph, rightTopIconType, handleClickRightTopIcon, nodeShapeCfg, isEmptyData])
  useEffect(() => {
    if (graphRef.current && !isEmptyData) {
      const { data, legendInfo } = formatData({
        data: initData,
        typeKey: typeKey,
      })
      if (legendInfo) {
        const legends = Object.keys(legendInfo).map((k) => {
          const { legendText, fill } = legendInfo[k as keyof typeof legendInfo]
          return {
            label: legendText,
            color: fill,
          }
        })
        setLegendInfo(legends)
      }
      graphRef.current.changeData(data)
    }
  }, [initData, typeKey, isEmptyData])
  if (isEmptyData) {
    return <Empty description={emptyDescriptionText || '暂无数据'} image={Empty.PRESENTED_IMAGE_SIMPLE} />
  }
  return (
    <>
      <div
        style={{
          minHeight: '500px',
          height: '100%',
          width: '100%',
          position: 'relative',
          display: 'flex',
          flexDirection: 'column',
          boxSizing: 'border-box',
        }}
      >
        <div ref={miniMapRef} />
        <div
          ref={containerRef}
          style={{
            cursor: 'grab',
            flex: 1,
            width: '100%',
          }}
        />
        <div
          style={{
            top: '0px',
            right: '0',
            position: 'absolute',
            boxShadow: '0, 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 4px 28px 8px rgba(0, 0, 0, 0.05)',
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <Slider
            value={zoom}
            onChange={(zoom: number) => {
              const graph = graphRef.current
              setZoom(zoom)
              if (graph) {
                const zoomTo = ((zoom / 100) * 2).toFixed(2)
                const width = graph.getWidth()
                const height = graph.getHeight()
                const centerWidth = Math.ceil(width / 2)
                const centerHeight = Math.ceil(height / 2)
                graph.zoomTo(+zoomTo, {
                  x: centerWidth,
                  y: centerHeight,
                })
              }
            }}
            style={{
              width: '150px',
              display: 'inline-block',
            }}
          />
          {toolBarList.map((toolItem, index) => {
            return (
              <toolItem.icon
                title={toolItem.title}
                key={index}
                style={{
                  cursor: 'pointer',
                  margin: '0 0 0 6px',
                  fontSize: '20px',
                }}
                onClick={() => {
                  const graph = graphRef.current
                  if (graph) {
                    toolItem.onClick(graph)
                    switch (toolItem.type) {
                      case 'zoomIn':
                      case 'zoomOut':
                      case 'selfAdaption': {
                        const zoom = graph.getZoom()
                        const zoomTo = zoom * 50
                        setZoom(Math.round(zoomTo))
                        break
                      }
                      case 'initStatus': {
                        setZoom(50)
                        break
                      }
                    }
                  }
                }}
              />
            )
          })}
        </div>

        <div className={cs.legendContent}>
          <span
            style={{
              flexShrink: 0,
            }}
          >
            {legendInfo?.length > 0 ? '图例说明：' : ''}
          </span>

          <div className="legendCtx">
            {legendInfo.map((legendItem, index) => {
              return (
                <Tag
                  color={legendItem.color}
                  style={{
                    color: '#fff',
                    flexShrink: 0,
                  }}
                  key={index}
                >
                  {legendItem.label}
                </Tag>
              )
            })}
          </div>
        </div>
      </div>
    </>
  )
}

export default GLineage
