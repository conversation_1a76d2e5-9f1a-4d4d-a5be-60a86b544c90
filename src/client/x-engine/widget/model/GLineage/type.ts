import type { Graph } from "@antv/g6"
export interface GLineagePropsType {
    typeKey: string
    data: LineageBaseData
    rightTopIconType?: 'edit'
    handleClickRightTopIcon?: (...args: any[]) => any
    afterInitGraph?: (graph: Graph) => any
    nodeShapeCfg?: NodeShapeCfgType
    graphSettings?: {
        isFitView?: boolean
    }
    menuProps?: {
        content?: string
        handleClickMenu?: (target: HTMLElement, info: Record<string, any>) => void
    }
    emptyDescriptionText?: string
}

export interface NodeShapeCfgType {
    itemsNameWidth?: number
    itemsSubInfoWidth?: number
    width?: number
}

export interface LineageBaseData {
    nodes: {
        id: string
      [key: string]: any
    }[]
    edges: {
        source: string,
        target: string,
        sourceKey?: string
        targetKey?: string
    }[]
}
// format函数出来的结构，最后元素定义是根据此结构进行数据填充
export interface TitleAndMulItemsShapeType {
    id: string
    title: {
        text: string,
        isEllipsis?: boolean,
        styleCfg?: Record<string, string | number>
    }
    items: {
        key: string,
        name: {
            text: string,
            isEllipsis?: boolean,
            styleCfg?: Record<string, string | number>
        }
        subInfo?: {
            text: string
            isEllipsis?: boolean,
            styleCfg?: Record<string, string | number>
        }[]
    }[]
}