// @ts-nocheck
const joinDiagram = {
	nodes: [
		{
			vid: '0',
			table: 'DEFAULT.KYLIN_SALES',
			Kind: 'FACT',
			alias: '',
			dimensions_columns: [
				{
					name: 'dimensions_columns__1',
					alias: '',
					tableKey: 'pk'
				},
				{
					name: 'dimensions_columns__2',
					alias: '',
				},
			],
			metrics_columns: [
				{
					name: 'metrics_columns__1',
					alias: '',
				},
				{
					name: 'metrics_columns__2',
					alias: '',
				},
			],
			common_columns: [
				{
					name: 'metrics_columns__111',
					alias: '',
				},
				{
					name: 'metrics_columns__2111ssadlaksdaksjdlaskdjklsaj;ldkajsd;asjd;ask',
					alias: '',
				},
			]
		},
		{
			vid: '1',
			table: 'DEFAULT.KYLIN_CAL_DT',
			Kind: 'DIM',
			alias: '',
			dimensions_columns: [
				{
					name: 'dimensions_columns__3',
					alias: '',
				},
				{
					name: 'dimensions_columns__4',
					alias: '',
				},
				{
					name: 'dimensions_columns__5',
					alias: '',
				},
				{
					name: 'dimensions_columns__6',
					alias: '',
				},
			],
			metrics_columns: [
				{
					name: 'metrics_columns__3',
					alias: '',
				},
				{
					name: 'metrics_columns__4',
					alias: '',
				},
				{
					name: 'metrics_columns__5',
					alias: '',
				},
				{
					name: 'metrics_columns__6',
					alias: '',
				},
				{
					name: 'metrics_columns__7',
					alias: '',
				},
				{
					name: 'metrics_columns__8',
					alias: '',
				},
				{
					name: 'metrics_columns__9',
					alias: '',
				},
				{
					name: 'metrics_columns__10',
					alias: '',
				},
			],
		},
		{
			vid: '2',
			table: 'DEFAULT.KYLIN_CAL_DT_2',
			Kind: 'DIM',
			alias: '',
			dimensions_columns: [
				{
					name: 'dimensions_columns__7',
					alias: '',
				},
				{
					name: 'dimensions_columns__8',
					alias: '',
				},
			],
			metrics_columns: [
				{
					name: 'metrics_columns__11',
					alias: '',
				},
				{
					name: 'metrics_columns__12',
					alias: '',
				},
			],
		},
	],
	edges: [
		{
			eid: '0',
			from: '0',
			to: '1',
			join_type: 'left',
			primary_key: ['KYLIN_CAL_DT.DT'],
			foreign_key: ['KYLIN_SALES.DT'],
		},
		{
			eid: '1',
			from: '1',
			to: '2',
			join_type: 'left',
			primary_key: ['KYLIN_SALES.DT_A'],
			foreign_key: ['KYLIN_CAL_DT.CAL_DT'],
		},
		// {
		//     "eid": 2,
		//     "from": 0,
		//     "to": 3,
		//     "join_type": "left",
		//     "primary_key": [
		//         "KYLIN_CAL_DT_3.CAL_DT",
		//         "KYLIN_CAL_DT_3.DT"
		//     ],
		//     "foreign_key": [
		//         "KYLIN_SALES.PART_DT",
		//         "KYLIN_SALES.DT"
		//     ]
		// }
	],
}

const d = {
	"vertices": [
		{
			"id": "dipeak.vtpcds.vt_dim_catalog_page",
			"table": "vtpcds.vt_dim_catalog_page",
			"kind": "FACT",
			"dimensionsColumns": [
				{
					"name": "cp_start_date"
				}
			],
			"metricsColumns": [
				{
					"name": "cp_end_date"
				}
			],
			"primaryKeys": [],
			"foreignKeys": [
				"cp_catalog_page_id",
				"cp_department"
			]
		},
		{
			"id": "dipeak.vtpcds.vt_dim_income_band",
			"table": "vtpcds.vt_dim_income_band",
			"kind": "DIM",
			"dimensionsColumns": [],
			"metricsColumns": [],
			"primaryKeys": [
				"ib_income_band_sk"
			],
			"foreignKeys": []
		}
	],
	"edges": [
		{
			"id": "era0857jotao",
			"from": "dipeak.vtpcds.vt_dim_catalog_page",
			"to": "dipeak.vtpcds.vt_dim_income_band",
			"joinType": "LEFT",
			"primaryKeys": [
				{
					"name": "vtpcds.vt_dim_income_band.ib_income_band_sk"
				}
			],
			"foreignKeys": [
				{
					"name": "vtpcds.vt_dim_catalog_page.cp_catalog_page_id"
				}
			]
		}
	]
}

function formatData(data) {
	const { nodes, edges } = data
	if (!Array.isArray(nodes) || !Array.isArray(edges)) {
		return data
	}
	const nodeJoinKeyMap = {}

	const ansEdges = edges.reduce((pre, e) => {
		const primaryKeys = e.primary_key
		const foreignKeys = e.foreign_key
		const from = e.from
		const to = e.to
		if (nodeJoinKeyMap[to] === undefined) {
			nodeJoinKeyMap[to] = new Set()
		}
		if (nodeJoinKeyMap[from] === undefined) {
			nodeJoinKeyMap[from] = new Set()
		}

		const es =
			primaryKeys.map((fromCol, index) => {
				const source = { id: e.from, column: fromCol }
				const target = { id: e.to, column: foreignKeys[index] }
				nodeJoinKeyMap[from].add(fromCol)
				nodeJoinKeyMap[to].add(foreignKeys[index])
				return {
					source,
					target,
				}
			}) || []
		pre.push(...es)
		return pre
	}, [])

	const ansNodes = nodes.map((n) => {
		// 校验数据
		if (n.vid === undefined || n.vid === null) {
			return n
		}
		// 操作数据
		const dimensionsColumns = (n.dimensions_columns || []).map((col) => {
			return ({
				name: col.name,
				subInfo: [{
					text: 'pk',
					isEllipsis: false
				}, {
					text: 'Hellow'
				}]
			})
		})
		const metricsColumns = (n.metrics_columns || []).map((col) => {
			return col.name
		})

		const commonColumns = (n.common_columns || []).map((col) => {
			return col.name
		})
		const joinKeyColumns = nodeJoinKeyMap[n.vid] ? [...nodeJoinKeyMap[n.vid]] : []
		return {
			id: n.vid,
			table: n.table,
			type: n.Kind,
			dimensionsColumns,
			metricsColumns,
			joinKeyColumns,
			commonColumns
		}
	})
	return {
		nodes: ansNodes,
		edges: ansEdges,
	}
}

function f1(d) {
	const { nodes, edges } = d
	const es = edges.map(e => {
		const { source, target } = e
		return ({
			source: source.id,
			sourceKey: source.column,
			target: target.id,
			targetKey: target.column
		})
	})
	return {
		nodes,
		edges: es
	}
}
export const initData = f1(formatData(joinDiagram))