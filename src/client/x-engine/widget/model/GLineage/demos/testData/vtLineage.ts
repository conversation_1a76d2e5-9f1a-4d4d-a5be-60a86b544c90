// @ts-nocheck
const data = {
    "nodes": [
        {
            "catalogName": "catalog1",
            "databaseName": "catalogaaabbbcccdddeeefff",
            "tableName": "vt_hive_test_2802",
            "type": "likeVirtualTable",
            "columnNames": [
                "id",
                "content",
                "price",
                "day"
            ],
            "metrics": false
        },
        {
            "catalogName": "hive_ea5d0caebb9f4d22b02e98ad56199fb8",
            "databaseName": "test_hive1",
            "tableName": "hive_test_2802",
            "type": "externalTable",
            "columnNames": [
                { name: "id", subInfo: ['aaaa', 'bbb', 'cccc', 'dddd', 'ddd'] },
                "content",
                "price",
                "day"
            ],
            "metrics": false
        }
    ],
    "edges": [
        {
            "source": {
                "catalogName": "catalog1",
                "databaseName": "catalogaaabbbcccdddeeefff",
                "tableName": "vt_hive_test_2802",
                "columnName": "id"
            },
            "target": {
                "catalogName": "hive_ea5d0caebb9f4d22b02e98ad56199fb8",
                "databaseName": "test_hive1",
                "tableName": "hive_test_2802",
                "columnName": "id"
            }
        },
        {
            "source": {
                "catalogName": "catalog1",
                "databaseName": "catalogaaabbbcccdddeeefff",
                "tableName": "vt_hive_test_2802",
                "columnName": "content"
            },
            "target": {
                "catalogName": "hive_ea5d0caebb9f4d22b02e98ad56199fb8",
                "databaseName": "test_hive1",
                "tableName": "hive_test_2802",
                "columnName": "content"
            }
        },
        {
            "source": {
                "catalogName": "catalog1",
                "databaseName": "catalogaaabbbcccdddeeefff",
                "tableName": "vt_hive_test_2802",
                "columnName": "price"
            },
            "target": {
                "catalogName": "hive_ea5d0caebb9f4d22b02e98ad56199fb8",
                "databaseName": "test_hive1",
                "tableName": "hive_test_2802",
                "columnName": "price"
            }
        },
        {
            "source": {
                "catalogName": "catalog1",
                "databaseName": "catalogaaabbbcccdddeeefff",
                "tableName": "vt_hive_test_2802",
                "columnName": "day"
            },
            "target": {
                "catalogName": "hive_ea5d0caebb9f4d22b02e98ad56199fb8",
                "databaseName": "test_hive1",
                "tableName": "hive_test_2802",
                "columnName": "day"
            }
        }
    ]
}


const spellId = (tar: Record<string, any>) => {
    const { metrics, catalogName, databaseName, tableName } = tar
    const metricsChar = 'MET_'
    const name = `${catalogName}.${databaseName}.${tableName}`
    return metrics === true ? `${metricsChar}${name}` : `${name}`
}
function formatFn(data) {
    const { edges = [], nodes = [] } = data
    if (!Array.isArray(nodes) || !Array.isArray(edges)) {
        return {
            nodes: [],
            edges: []
        }
    }
    const ansNodes = nodes.map((n) => {
        const id = spellId(n)
        return {
            ...n,
            id: id,
        }
    })
    const ansEdges = edges.map((e) => {
        const { source, target } = e
        const sourceId = spellId(source)
        const targetId = spellId(target)
        return {
            source: sourceId,
            target: targetId,
            sourceKey: source.columnName,
            targetKey: target.columnName
        }
    })
    return {
        nodes: ansNodes,
        edges: ansEdges
    }
}
export const vtLineageData = formatFn(data)