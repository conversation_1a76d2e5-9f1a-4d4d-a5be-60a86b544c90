// @ts-nocheck
import axios from "axios"

const data = {
    nodes: [
        {
            databaseName: 'hds',
            tableName: 'hds_opdo_t89_emp_basic_info_ds',
            type: 'externalTable',
            columnNames: [
                'data_dt',
                'statt_dt',
                'emp_id',
                'emp_nm',
                'hr_org_id',
                'hr_org_nm',
                'emp_doc_num',
                'tel_num1',
                'tel_num2',
                'elec_mail',
                'emp_gender',
                'birth_dt',
                'emp_stat_cd',
                'dept_main_ky',
                'dept_nm',
                'tellr_id',
                'emp_core_org_id',
                'fst_enter_mbank_tm',
                'dimss_dt',
                'post_lvl_key',
                'post_lvl_cd',
                'post_lvl_nm',
                'belg_range',
            ],
        },
        {
            databaseName: 'ods',
            tableName: 'opdo_t89_emp_basic_info_ds',
            type: 'externalTable',
            columnNames: [
                'statt_dt',
                'emp_id',
                'emp_nm',
                'hr_org_id',
                'hr_org_nm',
                'emp_doc_num',
                'tel_num1',
                'tel_num2',
                'elec_mail',
                'emp_gender',
                'birth_dt',
                'emp_stat_cd',
                'dept_main_ky',
                'dept_nm',
                'tellr_id',
                'emp_core_org_id',
                'fst_enter_mbank_tm',
                'dimss_dt',
                'post_lvl_key',
                'post_lvl_cd',
                'post_lvl_nm',
                'belg_range',
            ],
        },
        {
            databaseName: 'famsdb',
            tableName: 'fams_psum',
            type: 'externalTable',
            columnNames: [
                'emp_id',
                'emp_nm',
                'hr_org_id',
                'dept_main_ky',
                'dept_nm',
                'emp_stat_cd',
                'statt_dt',
                'update_time',
            ],
        },
        {
            databaseName: 'famsdb',
            tableName: 'fams_hr_psum',
            type: 'externalTable',
            columnNames: [
                'emp_id',
                'emp_nm',
                'hr_org_id',
                'dept_main_ky',
                'dept_nm',
                'emp_stat_cd',
                'statt_dt',
                'update_time',
            ],
        },
    ],
    edges: [
        {
            source: { databaseName: 'ods', tableName: 'opdo_t89_emp_basic_info_ds', columnName: 'statt_dt' },
            target: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'statt_dt' },
        },
        {
            source: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'statt_dt' },
            target: { databaseName: 'famsdb', tableName: 'fams_psum', columnName: 'statt_dt' },
        },
        {
            source: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'statt_dt' },
            target: { databaseName: 'famsdb', tableName: 'fams_hr_psum', columnName: 'statt_dt' },
        },
        {
            source: { databaseName: 'ods', tableName: 'opdo_t89_emp_basic_info_ds', columnName: 'emp_id' },
            target: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'emp_id' },
        },
        {
            source: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'emp_id' },
            target: { databaseName: 'famsdb', tableName: 'fams_psum', columnName: 'emp_id' },
        },
        {
            source: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'emp_id' },
            target: { databaseName: 'famsdb', tableName: 'fams_hr_psum', columnName: 'emp_id' },
        },
        {
            source: { databaseName: 'ods', tableName: 'opdo_t89_emp_basic_info_ds', columnName: 'emp_nm' },
            target: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'emp_nm' },
        },
        {
            source: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'emp_nm' },
            target: { databaseName: 'famsdb', tableName: 'fams_psum', columnName: 'emp_nm' },
        },
        {
            source: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'emp_nm' },
            target: { databaseName: 'famsdb', tableName: 'fams_hr_psum', columnName: 'emp_nm' },
        },
        {
            source: { databaseName: 'ods', tableName: 'opdo_t89_emp_basic_info_ds', columnName: 'hr_org_id' },
            target: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'hr_org_id' },
        },
        {
            source: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'hr_org_id' },
            target: { databaseName: 'famsdb', tableName: 'fams_psum', columnName: 'hr_org_id' },
        },
        {
            source: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'hr_org_id' },
            target: { databaseName: 'famsdb', tableName: 'fams_hr_psum', columnName: 'hr_org_id' },
        },
        {
            source: { databaseName: 'ods', tableName: 'opdo_t89_emp_basic_info_ds', columnName: 'hr_org_nm' },
            target: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'hr_org_nm' },
        },
        {
            source: { databaseName: 'ods', tableName: 'opdo_t89_emp_basic_info_ds', columnName: 'emp_doc_num' },
            target: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'emp_doc_num' },
        },
        {
            source: { databaseName: 'ods', tableName: 'opdo_t89_emp_basic_info_ds', columnName: 'tel_num1' },
            target: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'tel_num1' },
        },
        {
            source: { databaseName: 'ods', tableName: 'opdo_t89_emp_basic_info_ds', columnName: 'tel_num2' },
            target: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'tel_num2' },
        },
        {
            source: { databaseName: 'ods', tableName: 'opdo_t89_emp_basic_info_ds', columnName: 'elec_mail' },
            target: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'elec_mail' },
        },
        {
            source: { databaseName: 'ods', tableName: 'opdo_t89_emp_basic_info_ds', columnName: 'emp_gender' },
            target: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'emp_gender' },
        },
        {
            source: { databaseName: 'ods', tableName: 'opdo_t89_emp_basic_info_ds', columnName: 'birth_dt' },
            target: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'birth_dt' },
        },
        {
            source: { databaseName: 'ods', tableName: 'opdo_t89_emp_basic_info_ds', columnName: 'emp_stat_cd' },
            target: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'emp_stat_cd' },
        },
        {
            source: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'emp_stat_cd' },
            target: { databaseName: 'famsdb', tableName: 'fams_psum', columnName: 'emp_stat_cd' },
        },
        {
            source: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'emp_stat_cd' },
            target: { databaseName: 'famsdb', tableName: 'fams_hr_psum', columnName: 'emp_stat_cd' },
        },
        {
            source: { databaseName: 'ods', tableName: 'opdo_t89_emp_basic_info_ds', columnName: 'dept_main_ky' },
            target: {
                databaseName: 'hds',
                tableName: 'hds_opdo_t89_emp_basic_info_ds',
                columnName: 'dept_main_ky',
            },
        },
        {
            source: {
                databaseName: 'hds',
                tableName: 'hds_opdo_t89_emp_basic_info_ds',
                columnName: 'dept_main_ky',
            },
            target: { databaseName: 'famsdb', tableName: 'fams_psum', columnName: 'dept_main_ky' },
        },
        {
            source: {
                databaseName: 'hds',
                tableName: 'hds_opdo_t89_emp_basic_info_ds',
                columnName: 'dept_main_ky',
            },
            target: { databaseName: 'famsdb', tableName: 'fams_hr_psum', columnName: 'dept_main_ky' },
        },
        {
            source: { databaseName: 'ods', tableName: 'opdo_t89_emp_basic_info_ds', columnName: 'dept_nm' },
            target: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'dept_nm' },
        },
        {
            source: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'dept_nm' },
            target: { databaseName: 'famsdb', tableName: 'fams_psum', columnName: 'dept_nm' },
        },
        {
            source: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'dept_nm' },
            target: { databaseName: 'famsdb', tableName: 'fams_hr_psum', columnName: 'dept_nm' },
        },
        {
            source: { databaseName: 'ods', tableName: 'opdo_t89_emp_basic_info_ds', columnName: 'tellr_id' },
            target: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'tellr_id' },
        },
        {
            source: { databaseName: 'ods', tableName: 'opdo_t89_emp_basic_info_ds', columnName: 'emp_core_org_id' },
            target: {
                databaseName: 'hds',
                tableName: 'hds_opdo_t89_emp_basic_info_ds',
                columnName: 'emp_core_org_id',
            },
        },
        {
            source: {
                databaseName: 'ods',
                tableName: 'opdo_t89_emp_basic_info_ds',
                columnName: 'fst_enter_mbank_tm',
            },
            target: {
                databaseName: 'hds',
                tableName: 'hds_opdo_t89_emp_basic_info_ds',
                columnName: 'fst_enter_mbank_tm',
            },
        },
        {
            source: { databaseName: 'ods', tableName: 'opdo_t89_emp_basic_info_ds', columnName: 'dimss_dt' },
            target: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'dimss_dt' },
        },
        {
            source: { databaseName: 'ods', tableName: 'opdo_t89_emp_basic_info_ds', columnName: 'post_lvl_key' },
            target: {
                databaseName: 'hds',
                tableName: 'hds_opdo_t89_emp_basic_info_ds',
                columnName: 'post_lvl_key',
            },
        },
        {
            source: { databaseName: 'ods', tableName: 'opdo_t89_emp_basic_info_ds', columnName: 'post_lvl_cd' },
            target: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'post_lvl_cd' },
        },
        {
            source: { databaseName: 'ods', tableName: 'opdo_t89_emp_basic_info_ds', columnName: 'post_lvl_nm' },
            target: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'post_lvl_nm' },
        },
        {
            source: { databaseName: 'ods', tableName: 'opdo_t89_emp_basic_info_ds', columnName: 'belg_range' },
            target: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'belg_range' },
        },
    ],
}

export function formatFn(data) {
    
    const { edges = [], nodes = [] } = data 
    if (!Array.isArray(nodes) || !Array.isArray(edges)) {
        return {
            nodes: [],
            edges: []
        }
    }
    const ansNodes = nodes.map((n) => {
        const id = `${n.databaseName}.${n.tableName}`
        return {
            ...n,
            id: id
        }
    })
    const ansEdges = edges.map((e) => {
        const { source, target } = e
        const sourceId = `${source.databaseName}.${source.tableName}`
        const targetId = `${target.databaseName}.${target.tableName}`
        return {
            source: sourceId,
            target: targetId,
            sourceKey: source.columnName,
            targetKey: target.columnName
        }
    })
    return {
        nodes: ansNodes,
        edges: ansEdges
    }
}
export const askmetaLineageData = formatFn(data)