import React from 'react'
import GLineage from '@model/GLineage'
const d = {
  nodes: [
    {
      id: 1879,
      mvName: 'amv_c14bd760e4fff11610e12cec7f0f4cc2',
      relationWithSourceMv: 'SOURCE',
      usability: true,
    },
    {
      id: 1877,
      mvName: 'amv_b32c956fd37d6afa03b286171e7fde92',
      relationWithSourceMv: 'UPSTREAM',
      usability: true,
    },
  ],
  edges: [
    {
      source: {
        id: 1879,
        mvName: 'amv_c14bd760e4fff11610e12cec7f0f4cc2',
        relationWithSourceMv: 'SOURCE',
        usability: true,
      },
      target: {
        id: 1877,
        mvName: 'amv_b32c956fd37d6afa03b286171e7fde92',
        relationWithSourceMv: 'UPSTREAM',
        usability: true,
      },
    },
  ],
}

function formatLineageData(data: any) {
  const { edges = [], nodes = [] } = data || {}
  const initNodes = nodes.map((n: any) => {
    return {
      // id: `${n.id}`,
      id: n.id,
      type: 'materializedView',
      name: n.mvName,
      // columnExpression: ''
    }
  })
  const initEdges = edges.map((e: any) => {
    return {
      // source: `${e.source.id}`,
      source: e.source.id,
      // target: `${e.target.id}`,
      target: e.target.id,
    }
  })
  const ans = {
    nodes: initNodes,
    edges: initEdges,
  }

  console.log('and__________', ans)
  return ans
}
export default function Page() {
  return (
    <div
      style={{
        height: '500px',
        // background: 'pink'
      }}
    >
      <GLineage data={formatLineageData(d)} typeKey="type" />
    </div>
  )
}
