// @ts-nocheck
import { Node } from '@antv/x6'
import cs from './relative.module.scss'
import { Divider } from 'antd'
import { CloseOutlined } from '@ant-design/icons'
import { set, cloneDeep } from 'lodash-es'
import { operationWithGetValueByPath, midElliptical, measureValueWidth } from '@dipeak/libs'
import { type NodeRegisterMapType } from './dataConf'
import { type RelativePropsType, type RelativeDataType, EmptyObject } from './interface'
import { handleAddCollapseNode, handleAddHighlightNode } from './tools'

const FONT_SIZE = 16
const paddingTop = 16

const radiusX = 8
const radiusY = 8

const bodyRadiusX = 6
const bodyRadiusY = 6
const portRadiusX = 6
const portRadiusY = 6

export const ConstantInfo = {
	BODY_STROKE_WIDTH: 1,
	TITLE_FONTSIZE: 18,
	TITLE_LINEHEIGHT: 28,
	TEXT_FONTSIZE: 16,
	FONT_SIDES_PADDING: 8,

	PADDING_SIDES: 6,
	PADDING_TOP: 16,
	PADDING_BOTTOM: 2,

	MAXWIDTH: 280,
	PORT_LINEHEIGHT: 24,
	PORT_FONT_HEIGHT: 30,

	COLLAPSE_SVG_Height: 20,

	// port text 缩进
}

// shape interface
interface SingleRectShapeSettings {
	name?: Record<string, string | number>
	nameText?: Record<string, string | number>
}
export interface DoubleRectShapeSettings {
	name?: Record<string, string | number>
	subName?: Record<string, string | number>
	nameText?: Record<string, string | number>
	subNameText?: Record<string, string | number>
}

interface SingleRectAndMulPortsShapeSettings {
	bodyRect?: Record<string, string | number>
	titleText?: Record<string, string | number>
	titleRect?: Record<string, string | number>
	titleMask?: Record<string, string | number>
	portBody?: Record<string, string | number>
	portText?: Record<string, string | number>
	portRect?: Record<string, string | number>
	portExtraText?: Record<string, string | number>
}

export type ShapeAttrSettingType =
	| SingleRectShapeSettings
	| DoubleRectShapeSettings
	| SingleRectAndMulPortsShapeSettings

// shape definitions  used in shape definitions to create shape
export function doubleRectShape(shapeSettings: DoubleRectShapeSettings) {
	const { name, subName, nameText, subNameText } = shapeSettings || {}
	return {
		inherit: 'rect',
		markup: [
			{
				tagName: 'rect',
				selector: 'name',
			},
			{
				tagName: 'text',
				selector: 'nameText',
			},
			{
				tagName: 'rect',
				selector: 'subName',
			},
			{
				tagName: 'text',
				selector: 'subNameText',
			},
		],
		attrs: {
			name: {
				refWidth: 1,
				strokeWidth: 1,
				rx: radiusX,
				ry: radiusY,
				...name,
			},
			nameText: {
				ref: 'name',
				refX: 0.5,
				refY: paddingTop,
				textAnchor: 'middle',
				fontSize: FONT_SIZE,
				fill: '#fff',
				// fontWeight: 'bold',
				...nameText,
			},
			subName: {
				fill: '#fff',
				rx: 4,
				ry: 4,
				strokeWidth: 0,
				...subName,
			},
			subNameText: {
				fill: '#232323',
				fontSize: FONT_SIZE,
				ref: 'subName',
				refX: 0.5,
				refY: 0.5,
				textAnchor: 'middle',
				...subNameText,
			},
		},
	}
}
export function singleRectShape(shapeSettings: SingleRectShapeSettings) {
	const { name, nameText } = shapeSettings || {}
	return {
		inherit: 'rect',
		markup: [
			{
				tagName: 'rect',
				selector: 'body',
			},
			{
				tagName: 'rect',
				selector: 'name',
			},
			{
				tagName: 'text',
				selector: 'nameText',
			},
		],
		attrs: {
			body: {
				strokeWidth: 0,
			},
			name: {
				refWidth: 1,
				strokeWidth: 1,
				refHeight: 1,
				...name,
			},
			nameText: {
				fill: '#232323',
				ref: 'name',
				refX: 0.5,
				refY: 0.5,
				textAnchor: 'middle',
				fontSize: FONT_SIZE,
				...nameText,
			},
		},
	}
}

export function singleRectAndMulPortsShape(shapeSettings: SingleRectAndMulPortsShapeSettings) {
	const { bodyRect, titleText, portBody, portText, titleMask, titleRect, portExtraText } = shapeSettings || {}
	return {
		inherit: 'rect',
		markup: [
			{
				tagName: 'rect',
				selector: 'bodyRect',
			},

			{
				tagName: 'rect',
				selector: 'titleRect',
			},
			{
				tagName: 'rect',
				selector: 'titleMask',
			},
			{
				tagName: 'text',
				selector: 'titleText',
			},
		],
		attrs: {
			bodyRect: {
				strokeWidth: ConstantInfo.BODY_STROKE_WIDTH,
				rx: bodyRadiusX,
				ry: bodyRadiusY,
				...bodyRect,
			},
			titleRect: {
				rx: bodyRadiusX,
				ry: bodyRadiusY,
				ref: 'bodyRect',
				strokeWidth: 0,
				...titleRect,
			},
			titleMask: {
				strokeWidth: 0,
				ref: 'titleRect',
				refWidth: 1,
				refDy: -ConstantInfo.TITLE_LINEHEIGHT / 2,
				...titleMask,
			},
			titleText: {
				fill: '#fff',
				fontSize: ConstantInfo.TITLE_FONTSIZE,
				ref: 'titleRect',
				yAlign: 'middle',
				xAlign: 'middle',
				refX: 0.5,
				refY: 0.5,
				...titleText,
			},
		},
		ports: {
			groups: {
				list: {
					markup: [
						{
							tagName: 'rect',
							selector: 'portBody',
						},
						{
							tagName: 'rect',
							selector: 'portRect',
						},
						{
							tagName: 'text',
							selector: 'portText',
						},
						{
							tagName: 'text',
							selector: 'portExtraText',
						}
					],
					attrs: {
						portBody: {
							height: ConstantInfo.PORT_LINEHEIGHT,
							magnet: true,
							fill: '#fff',
							strokeWidth: 0,
							rx: portRadiusX,
							ry: portRadiusY,
							...portBody,
						},
						portText: {
							yAlign: 'middle',
							ref: 'portBody',
							refX: 8,
							refY: 0.5,
							fontSize: ConstantInfo.TEXT_FONTSIZE,
							...portText,
						},
						portExtraText: {
							yAlign: 'middle',
							xAlign: 'right',
							ref: 'portBody',
							refX: '100%',
							refX2: -8,
							refY: 0.5,
							fontSize: ConstantInfo.TEXT_FONTSIZE,
							...portExtraText,
						},
					},
					position: 'erPortPosition',
				},
			},
		},
	}
}

export const PopoverTipComponent = ({ node }: { node: Node }) => {
	const data = node.getData()
	if (!data) return <></>
	const { detail, parent } = data
	if (!detail || detail.length === 0) return <></>
	return (
		<>
			<div className={cs.reltivePopover}>
				<div className={cs.relativePopoverArrow}></div>
				<CloseOutlined
					style={{
						position: 'absolute',
						right: 10,
						top: 10,
					}}
					onClick={() => {
						node.remove()
						parent.setAttrs({
							name: { isOpen: false },
						})
					}}
				/>
				<div className={cs.relativePopoverContent}>
					{detail.map((item: [string, string], index: number) => {
						const [title, info] = item
						return (
							<div key={index}>
								<Divider plain>{title}</Divider>
								<p>{info}</p>
							</div>
						)
					})}
				</div>
			</div>
		</>
	)
}

type ApiEnumType =
	| 'atomic'
	| 'composite'
	| 'derived'
	| 'metricsTable'
	| 'virtualTable'
	| 'materializedView'
	| 'physicalTable'
	| 'outerTable'
	| 'frontselfSourceMV'
	| 'mv'
	| '外部数据表'
	| 'FACT'
	| 'DIM'
interface ApiEnumShapeMapItemType {
	include: { label: string; type: ApiEnumType }[]
	shape: NodeRegisterMapType
	legendColor: string
}
// shape data format
// 与后端返回的type枚举息息相关
const ApiEnumTypeShapeMap: ApiEnumShapeMapItemType[] = [
	{
		include: [{ label: '原子指标', type: 'atomic' }],
		shape: 'aGreenSingleRectAndMulPortsShape',
		legendColor: '#3b8771',
	},
	{
		include: [{ label: '复合指标', type: 'composite' }],
		shape: 'aPinkSingleRectAndMulPortsShape',
		legendColor: '#d63864',
	},
	{
		include: [{ label: '派生指标', type: 'derived' }],
		shape: 'aOrangeSingleRectAndMulPortsShape',
		legendColor: '#ec9235',
	},
	{
		include: [
			{ label: '业务虚拟表', type: 'metricsTable' },
			{ label: '物化视图', type: 'materializedView' },
			{ label: '物理表', type: 'physicalTable' },
		],
		shape: 'aBlueSingleRectAndMulPortsShape',
		legendColor: '#4359f5',
	},
	{
		include: [
			{ label: '贴源虚拟表', type: 'virtualTable' },
			{
				label: '物理表本表',
				type: 'frontselfSourceMV',
			},
		],
		shape: 'aPurpleSingleRectAndMulPortsShape',
		legendColor: '#9c1ef6',
	},
	{
		include: [
			{ label: '外部数据表', type: 'outerTable' },
			{
				label: '外部数据表',
				type: '外部数据表',
			},
		],
		shape: 'aBrownSingleRectAndMulPortsShape',
		legendColor: '#74574a',
	},
	{
		include: [{ label: '维度表', type: 'DIM' }],
		shape: 'aBlueGreySingleRectAndMulPortsShape',
		legendColor: '#78909c',
	},
	{
		include: [{ label: '事实表', type: 'FACT' }],
		shape: 'aGreySingleRectAndMulPortsShape',
		legendColor: '#424242',
	},
]
type ShapeNameType = 'doubleRectShape' | 'singleRectShape' | 'singleRectAndMulPortsShape'

type ShapeType =
	| {
		name: string
		subName: string
	}
	| { name: string }
	| { titleText: string; portsText: string | string[] }

const ShapeNameMapApiKey: { [key in ShapeNameType]: ShapeType[] } = {
	doubleRectShape: [
		{
			name: 'name',
			subName: 'columnExpression',
		},
	],
	singleRectShape: [
		{
			name: 'name',
		},
	],
	singleRectAndMulPortsShape: [
		{
			titleText: 'databaseName.tableName',
			portsText: 'columnNames',
		},
		{
			titleText: 'name',
			portsText: 'columnExpression',
		},
		{
			titleText: 'name',
			portsText: null,
		},
		{
			titleText: 'mvName',
			portsText: null,
		},
		{
			titleText: 'table',
			portsText: ['joinKeyColumns', 'metricsColumns', 'dimensionsColumns', 'commonColumns', 'primaryKeys', 'foreignKeys'],
		},
	],
}
// 找寻对应值
function getShapeApiKey(item: Record<string, any>, shapeName: keyof typeof ShapeNameMapApiKey) {
	const shapeApiKeyStructures = ShapeNameMapApiKey[shapeName]
	const SplitCharReg = new RegExp(/[^/.]+/g)

	const shapeApiKeyMap = shapeApiKeyStructures.find((structure) => {
		const apiKeys = Object.values(structure)
		return apiKeys.every((apiKey) => {
			const apiKeyType = Object.prototype.toString.call(apiKey).slice(8, -1).toLocaleLowerCase()
			switch (apiKeyType) {
				case 'null':
					return true
				case 'string': {
					const keys = apiKey.match(SplitCharReg)
					return keys && keys.every((k) => Object.hasOwnProperty.call(item, k))
				}
				case 'array': {
					return apiKey.every((k) => Object.hasOwnProperty.call(item, k))
				}
				default:
					return false
			}
		})
	})
	console.log('getShapeApiKey_________', item, shapeApiKeyMap)
	if (!shapeApiKeyMap) {
		console.warn('没有在shapeNameMapApiKey中找到相应的结构，请去shapeNameMapApiKey中定义')
		return {}
	}

	const shapeApiKeyInfo = Object.entries(shapeApiKeyMap).reduce((pre, [structureKey, apiKeyItem]) => {
		// 无脑这样写真的好吗
		const apiKeyType = Object.prototype.toString.call(apiKeyItem).slice(8, -1).toLocaleLowerCase()
		switch (apiKeyType) {
			case 'string': {
				pre[structureKey as keyof ShapeType] =
					item[apiKeyItem] ??
					apiKeyItem.replace(SplitCharReg, (key) => {
						return item[key]
					})
				break
			}
			case 'array': {
				pre[structureKey as keyof ShapeType] = apiKeyItem.reduce((preItem, apiKey) => {
					if (Object.hasOwnProperty.call(item, apiKey)) {
						let value = item[apiKey]
						if (!Array.isArray(value) && !(value instanceof Set)) {
							value = [value]
						}
						preItem.push(...value)
					}
					return preItem
				}, [])
			}
		}
		return pre
	}, {} as ShapeType)
	return shapeApiKeyInfo
}

interface LimitSettingProps {
	limit?: number
	unit?: number
	maxWidth: number
	fontSize?: number
	fontFamily?: string
}

function innerResolveWidth(txt: string, setting: { maxWidth: number; fontSize: number; fontFamily: string }) {
	if (!txt && !setting) {
		return {
			width: 0,
			text: '',
			needOpen: false,
		}
	}
	const { fontSize, fontFamily, maxWidth } = setting
	const fontWidth = measureValueWidth(txt, fontSize, fontFamily)
	if (fontWidth > maxWidth) {
		const size = txt.length
		const limit = size - Math.ceil((fontWidth - maxWidth) / (fontWidth / size))
		return {
			width: maxWidth,
			text: midElliptical(txt, limit),
			needOpen: true,
		}
	} else {
		return {
			width: fontWidth,
			text: txt,
			needOpen: false,
		}
	}
}
// 量宽度，设置宽度。最长可展示的内容
const getWidthAndTextUnderLimit = (
	texts: Record<string, string | string[]>,
	setting?: Record<keyof typeof texts, LimitSettingProps>,
): {
	maxWidth: number
	map: Record<
		string,
		{ width: number; text: string; needOpen: boolean } | { width: number; text: string; needOpen: boolean }[]
	>
} => {
	if (!texts) return { maxWidth: 0, map: {} }

	let ansMaxWidth = 0

	const ansMap = Object.entries(texts).reduce(
		(
			map: Record<
				string,
				| { width: number; text: string; needOpen: boolean }
				| { width: number; text: string; needOpen: boolean }[]
			>,
			[key, text],
		) => {
			const { maxWidth = 200, fontFamily = 'arial', fontSize = 16 } = setting[key]
			if (Array.isArray(text)) {
				map[key] = text.map((t) => {
					const itemAns = innerResolveWidth(t, { maxWidth, fontFamily, fontSize })
					if (itemAns.width > ansMaxWidth) {
						ansMaxWidth = itemAns.width
					}
					return itemAns
				})
			} else {
				const itemAns = innerResolveWidth(text, { maxWidth, fontFamily, fontSize })
				map[key] = itemAns
				if (itemAns.width > ansMaxWidth) {
					ansMaxWidth = itemAns.width
				}
			}
			return map
		},
		{},
	)

	return {
		maxWidth: ansMaxWidth,
		map: ansMap,
	}
}

// format SingleRectShape 的形状shape
function formatSingleRectShape(
	item: { name: string },
	parent: Record<string, any>,
	attrsSetting: SingleRectShapeSettings,
) {
	if (!item) return
	const { name } = item
	const NameHeight = 42
	const NameFontSize = 18
	const FontPadding = 20
	const MaxWidth = 200

	const { maxWidth: w, map } = getWidthAndTextUnderLimit(
		{
			name,
		},
		{
			name: {
				maxWidth: MaxWidth,
				fontSize: 18,
			},
		},
	) as { maxWidth: number; map: Record<string, { width: number; text: string; needOpen: boolean }> }

	parent.width = w + FontPadding * 2

	parent.height = NameHeight

	if (!parent.attrs) {
		parent.attrs = {}
	}

	const needOpen = Object.values(map).some(({ needOpen }) => needOpen)
	set(parent, 'attrs.name', {
		height: NameHeight,
		needOpen,
		cursor: needOpen ? 'pointer' : '',
		...attrsSetting?.name,
	})
	set(parent, 'attrs.nameText', {
		text: map.name.text,
		fontSize: NameFontSize,
		cursor: needOpen ? 'pointer' : '',
		...attrsSetting?.nameText,
	})
}

// format SingleRectAndMulPortsShape 的形状shape
function formatSingleRectAndMulPortsShape(
	item: { titleText: string; portsText: string[] | string },
	parent: Record<string, any>,
	attrsSetting: SingleRectAndMulPortsShapeSettings,
	portItemProps?: RelativePropsType['portItemProps']
) {
	if (typeof item.portsText === 'string') {
		item.portsText = [item.portsText]
	}
	if (!item) return

	const { titleText, portsText = [] } = item
	function getPortText(portItem) {
		if (typeof portItem === 'string') {
			return portItem
		}
		if (portItem && typeof portItem === 'object' && typeof portItem.portText === 'string') {
			return portItem.portText
		}
		if (portItem && portItem.portText && typeof portItem.portText === 'object' && typeof portItem.portText.portText === 'string') {
			return portItem.portText.portText
		}
	}
	const { maxWidth: textMeasureMaxW } = getWidthAndTextUnderLimit(
		{
			titleText: titleText,
			portsText: portsText.map(p => getPortText(p))
		},
		{
			titleText: { maxWidth: 99999, fontSize: ConstantInfo.TITLE_FONTSIZE + 2 },
			portsText: { maxWidth: 99999, fontSize: ConstantInfo.TEXT_FONTSIZE + 2 },
		},
	) as { maxWidth: number; map: Record<string, { width: number; text: string; needOpen: boolean }[]> }

	const maxW = [attrsSetting?.bodyRect?.width || 0, attrsSetting?.titleRect?.width || 0, textMeasureMaxW].sort((a, b) => {
		return (+b - +a)
	})[0]
	const w = Math.ceil(+maxW) + ConstantInfo.FONT_SIDES_PADDING

	const W = w + ConstantInfo.PADDING_SIDES * 2
	const H =
		ConstantInfo.TITLE_LINEHEIGHT +
		ConstantInfo.PORT_LINEHEIGHT * portsText.length +
		ConstantInfo.BODY_STROKE_WIDTH * 2

	parent.width = W + ConstantInfo.BODY_STROKE_WIDTH * 2
	parent.height = H
	parent.size = {
		width: parent.width,
		height: parent.height,
	}

	if (!parent.attrs) {
		parent.attrs = {}
	}

	// 没有ports的情况
	if (portsText?.length !== 0) {
		set(parent, 'attrs.titleMask', {
			height: ConstantInfo.TITLE_LINEHEIGHT / 2,
		})
	}

	// const needOpen = Object.values(map).some(({ needOpen }) => needOpen)

	set(parent, 'attrs.bodyRect', {
		height: H,
		// needOpen,
		// cursor: needOpen ? 'pointer' : '',
		...attrsSetting?.bodyRect,
		width: W + ConstantInfo.BODY_STROKE_WIDTH * 2,
	})
	set(parent, 'attrs.titleRect', {
		height:
			portsText?.length !== 0
				? ConstantInfo.TITLE_LINEHEIGHT
				: ConstantInfo.TITLE_LINEHEIGHT + ConstantInfo.BODY_STROKE_WIDTH * 2,
		...attrsSetting?.titleRect,
		width: W + ConstantInfo.BODY_STROKE_WIDTH * 2,
	})

	set(parent, 'attrs.titleText', {
		text: titleText,
		// cursor: needOpen ? 'pointer' : '',
		...attrsSetting?.titleText,
	})

	// 对port的值做限制
	const portExitSet = new Set()
	const ports = portsText.filter((port) => {
		const portText = getPortText(port)
		if (portText && !portExitSet.has(portText)) {
			portExitSet.add(portText)
			return true
		}
		return false
	}).map((port) => {
		const portText = getPortText(port)
		const portExtraText = port?.portText && typeof port?.portText === 'object' && port.portText.portExtraText || ''
		const portAttrs = port && typeof port === 'object' ? port.attrs || {} : {}

		return {
			id: portText,
			group: 'list',
			attrs: {
				portBody: Object.assign(
					{
						width: W,
					},
					portAttrs.portBody,
				),
				portText: Object.assign(
					{
						text: portText,
					},
					isHasAllProperties(parent, ['name', 'columnExpression']) ? { xAlign: 'middle', refX: 0.5 } : {},
				),
				portExtraText: Object.assign(
					{
						text: portExtraText
					},
					portAttrs.portExtraText
				)
			},
		}
	})
	const { sort } = portItemProps || {}
	if (sort === 'asc' || sort === 'desc') {
		ports.sort((optionA, optionB) => {
			if (sort === 'asc') {
				return (optionA?.attrs.portText.text ?? '').toLowerCase().localeCompare((optionB?.attrs.portText.text ?? '').toLowerCase())
			}
			if (sort === 'desc') {
				return (optionB?.attrs.portText.text ?? '').toLowerCase().localeCompare((optionA?.attrs.portText.text ?? '').toLowerCase())
			}
		})
	}
	set(parent, 'ports', ports)
}
// 根据不同形状进行format后端传回来的数据
function dataFormatByShape<T extends { shape?: NodeRegisterMapType }>(
	shape: NodeRegisterMapType,
	parent: T,
	attrsSetting: ShapeAttrSettingType,
	portItemProps?: RelativePropsType['portItemProps']
) {
	parent.shape = shape ?? 'defaultRect'
	let shapeInfo = {}
	switch (shape) {
		case 'aPinkSingleRectAndMulPortsShape':
		case 'aGreenSingleRectAndMulPortsShape':
		case 'aOrangeSingleRectAndMulPortsShape':
		case 'aBrownSingleRectAndMulPortsShape':
		case 'aBlueSingleRectAndMulPortsShape':
		case 'aPurpleSingleRectAndMulPortsShape':
		case 'aBlueGreySingleRectAndMulPortsShape':
		case 'aGreySingleRectAndMulPortsShape':
			shapeInfo = getShapeApiKey(parent, 'singleRectAndMulPortsShape')
			formatSingleRectAndMulPortsShape(
				shapeInfo as { titleText: string; portsText: string[] },
				parent,
				attrsSetting,
				portItemProps
			)
			break
		default:
			shapeInfo = getShapeApiKey(parent, 'singleRectShape')

			formatSingleRectShape(shapeInfo as { name: string }, parent, attrsSetting)
	}
	return shapeInfo
}
// todo：提取变量
const PortSetting = [
	{
		include: [{ label: '关联键', colKind: 'joinKeyColumns' }],
		legendColor: '#9cbebd',
	},
	{
		include: [{ label: '度量', colKind: 'metricsColumns' }],
		legendColor: '#c5cae9',

	},
	{
		include: [{ label: '维度', colKind: 'dimensionsColumns' }],
		legendColor: '#ec9235',
	},
]

// 根据type格式化数据
function dataFormatByType(type: ApiEnumType, parent: Record<string, any>) {
	const PortLegendInfo = {}
	switch (type) {
		case 'DIM':
		case 'FACT': {
			['joinKeyColumns', 'metricsColumns', 'dimensionsColumns', 'commonColumns', 'primaryKeys', 'foreignKeys'].forEach((colKey) => {
				if (!Array.isArray(parent[colKey])) {
					parent[colKey] = []
				}
				let portLabel = ''
				const PortSettingItem = PortSetting.find((psItem) => {
					const includeItem = psItem.include.find((item) => {
						return item.colKind === colKey
					})
					if (includeItem) {
						portLabel = includeItem.label
					}
					return !!includeItem
				})
				if (!PortSettingItem) {
					return
				}
				if (portLabel !== '' && portLabel !== 'CUSTOM') {
					PortLegendInfo[PortSettingItem.legendColor] = portLabel
				}

				parent[colKey] = parent[colKey].map((col) => {
					if (portLabel === 'CUSTOM') {
						PortLegendInfo[PortSettingItem.legendColor] = col
					}
					return {
						portText: col,
						type: colKey,
						attrs: {
							portBody: {
								fill: PortSettingItem.legendColor,
							},
						},
					}
				})
			})
			break
		}
	}
	return {
		portLegendInfo: PortLegendInfo,
	}
}

// 根据条件处理没传type的数据
function handleMissType(tar: Record<string, any>): ApiEnumType {
	// 指标血缘，table表的类型设置
	if (Object.hasOwnProperty.call(tar, 'table') && tar.table === true) {
		tar.metricsType = 'metricsTable'
		return 'metricsTable'
	}
}

// 主要函数
export function RelativeDataFormat(
	props: Pick<
		RelativePropsType,
		'data' | 'typeKey' | 'showArrow' | 'attrsSetting' | 'defaultType' | 'isUseCollapseTool' | 'isUseHightlightTool' | 'portItemProps'
	>,
): {
	data: RelativeDataType | EmptyObject
	legendInfo: { label: string; color: string }[]
} {
	const { data, typeKey, attrsSetting, showArrow = true, defaultType, isUseCollapseTool, isUseHightlightTool, portItemProps } = props

	if (!data) return { data: {}, legendInfo: [] }

	const ans = cloneDeep(data)
	const legendInfo: Record<string, string> = {}
	let keepSameApiType: null | ApiEnumType = null

	operationWithGetValueByPath('nodes/height', ans, (parent) => {
		let type = parent[typeKey] || defaultType
		keepSameApiType = type
		if (!type || typeof typeof type !== 'string') {
			type = handleMissType(parent)
			if (!type || typeof typeof type !== 'string') {
				return
			}
		}
		let label = ''
		const chooseApiEnumTypeShapeMapItem = ApiEnumTypeShapeMap.find((enumItem) => {
			const { include } = enumItem
			const includeItem = include.find((item) => item.type === type)
			label = includeItem?.label
			return !!includeItem
		})
		const shape = chooseApiEnumTypeShapeMapItem?.shape
		if (!shape) {
			console.warn('没有在ApiEnumTypeShapeMap上进行定义！')
		} else {
			legendInfo[chooseApiEnumTypeShapeMapItem.legendColor] = label
		}
		// 根据type格式化数据，与业务相关
		const dataFormatByTypeInfo = dataFormatByType(type, parent)
		Object.assign(legendInfo, dataFormatByTypeInfo.portLegendInfo)
		// 根据形状shape格式化数据
		const shapeKeyMapApiKeyInfo = dataFormatByShape<typeof parent>(shape, parent, attrsSetting, portItemProps)

		if (isUseCollapseTool) {
			handleAddCollapseNode(shape, parent)
		}

		if (isUseHightlightTool) {
			handleAddHighlightNode(parent)
		}

		Object.entries(shapeKeyMapApiKeyInfo).forEach(([shapeKey, apiInfo]) => {
			parent[shapeKey] = apiInfo
		})
		parent.id = spellNodeIdByType(type, parent)
	})

	// operationWithGetValueByPath 路径前一级路径含有即可
	operationWithGetValueByPath('edges/source', ans, (parent) => {
		const { source, target } = parent
		parent.id = spellEdgeId(source, target)
		parent.shape = showArrow ? 'defaultEdge' : 'noarrowDeaultEdge'
	})

	operationWithGetValueByPath('edges/source/name', ans, (parent) => {
		const t = handleMissType(parent) ?? keepSameApiType
		resolveIdByShape(t, parent)
	})

	operationWithGetValueByPath('edges/target/name', ans, (parent) => {
		const t = handleMissType(parent) ?? keepSameApiType
		resolveIdByShape(t, parent)
	})

	return {
		data: ans as RelativeDataType,
		legendInfo: Object.entries(legendInfo).map(([color, label]) => ({ color, label })),
	}
}

// 处理edge中关于node的id
function resolveIdByShape(type: ApiEnumType, parent: Record<string, any>) {
	switch (type) {
		case 'outerTable':
		case '外部数据表': {
			parent.port = parent.columnName
			break
		}
		case 'DIM':
		case 'FACT': {
			parent.port = parent.column
		}
	}
	parent.cell = spellNodeIdByType(type, parent)
}

function isHasAllProperties(target: Record<string, unknown>, properties: string[]) {
	if (!target || typeof target !== 'object') return false
	return properties.every((propStr: string) => {
		return Object.hasOwnProperty.call(target, propStr)
	})
}

// 用于确定节点id，找寻边所对应的节点id也需要
export function spellNodeIdByType(type: ApiEnumType, infoObj: Record<string, string | boolean>) {
	const METRiCS_TABLE_SIGN = 'metricsTableSign'
	switch (type) {
		case 'atomic':
		case 'composite':
		case 'derived':
		case 'virtualTable':
		case 'materializedView':
		case 'physicalTable':
			if (isHasAllProperties(infoObj, ['mvName'])) {
				return infoObj.mvName
			}
			if (isHasAllProperties(infoObj, ['name'])) {
				return infoObj.name
			}
			return JSON.stringify(infoObj)
		case 'metricsTable':
			if (isHasAllProperties(infoObj, ['name', 'table'])) {
				return `${infoObj.name}${METRiCS_TABLE_SIGN}`
			}
			return JSON.stringify(infoObj)
		case 'outerTable':
		case '外部数据表':
			if (isHasAllProperties(infoObj, ['databaseName', 'tableName'])) {
				return `${infoObj.databaseName}.${infoObj.tableName}`
			}
			return JSON.stringify(infoObj)
		case 'mv':
		case 'frontselfSourceMV':
			if (isHasAllProperties(infoObj, ['mvName'])) {
				return `${infoObj.mvName}`
			}
			return JSON.stringify(infoObj)
		case 'FACT':
		case 'DIM':
			// 判断条件
			return infoObj.id
		default:
			return infoObj.name
	}
}

export function spellEdgeId(source: Record<string, string | boolean>, target: Record<string, string | boolean>) {
	const edgeChar = 'e'
	return edgeChar + Object.values(source).toString() + Object.values(target).toString()
}
