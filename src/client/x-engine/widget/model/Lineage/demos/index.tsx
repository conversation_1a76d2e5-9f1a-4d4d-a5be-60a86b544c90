// @ts-nocheck
import React from 'react'
// 记得把 Lineage 在 src/index.ts 暴露出来
import Lineage from '@model/Lineage'

export default function Page() {
  const data = {
    nodes: [
      {
        databaseName: 'hds',
        tableName: 'hds_opdo_t89_emp_basic_info_ds',
        type: '外部数据表',
        columnNames: [
          'data_dt',
          'statt_dt',
          'emp_id',
          'emp_nm',
          'hr_org_id',
          'hr_org_nm',
          'emp_doc_num',
          'tel_num1',
          'tel_num2',
          'elec_mail',
          'emp_gender',
          'birth_dt',
          'emp_stat_cd',
          'dept_main_ky',
          'dept_nm',
          'tellr_id',
          'emp_core_org_id',
          'fst_enter_mbank_tm',
          'dimss_dt',
          'post_lvl_key',
          'post_lvl_cd',
          'post_lvl_nm',
          'belg_range',
        ],
      },
      {
        databaseName: 'ods',
        tableName: 'opdo_t89_emp_basic_info_ds',
        type: '外部数据表',
        columnNames: [
          'statt_dt',
          'emp_id',
          'emp_nm',
          'hr_org_id',
          'hr_org_nm',
          'emp_doc_num',
          'tel_num1',
          'tel_num2',
          'elec_mail',
          'emp_gender',
          'birth_dt',
          'emp_stat_cd',
          'dept_main_ky',
          'dept_nm',
          'tellr_id',
          'emp_core_org_id',
          'fst_enter_mbank_tm',
          'dimss_dt',
          'post_lvl_key',
          'post_lvl_cd',
          'post_lvl_nm',
          'belg_range',
        ],
      },
      {
        databaseName: 'famsdb',
        tableName: 'fams_psum',
        type: '外部数据表',
        columnNames: [
          'emp_id',
          'emp_nm',
          'hr_org_id',
          'dept_main_ky',
          'dept_nm',
          'emp_stat_cd',
          'statt_dt',
          'update_time',
        ],
      },
      {
        databaseName: 'famsdb',
        tableName: 'fams_hr_psum',
        type: '外部数据表',
        columnNames: [
          'emp_id',
          'emp_nm',
          'hr_org_id',
          'dept_main_ky',
          'dept_nm',
          'emp_stat_cd',
          'statt_dt',
          'update_time',
        ],
      },
    ],
    edges: [
      {
        source: { databaseName: 'ods', tableName: 'opdo_t89_emp_basic_info_ds', columnName: 'statt_dt' },
        target: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'statt_dt' },
      },
      {
        source: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'statt_dt' },
        target: { databaseName: 'famsdb', tableName: 'fams_psum', columnName: 'statt_dt' },
      },
      {
        source: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'statt_dt' },
        target: { databaseName: 'famsdb', tableName: 'fams_hr_psum', columnName: 'statt_dt' },
      },
      {
        source: { databaseName: 'ods', tableName: 'opdo_t89_emp_basic_info_ds', columnName: 'emp_id' },
        target: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'emp_id' },
      },
      {
        source: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'emp_id' },
        target: { databaseName: 'famsdb', tableName: 'fams_psum', columnName: 'emp_id' },
      },
      {
        source: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'emp_id' },
        target: { databaseName: 'famsdb', tableName: 'fams_hr_psum', columnName: 'emp_id' },
      },
      {
        source: { databaseName: 'ods', tableName: 'opdo_t89_emp_basic_info_ds', columnName: 'emp_nm' },
        target: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'emp_nm' },
      },
      {
        source: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'emp_nm' },
        target: { databaseName: 'famsdb', tableName: 'fams_psum', columnName: 'emp_nm' },
      },
      {
        source: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'emp_nm' },
        target: { databaseName: 'famsdb', tableName: 'fams_hr_psum', columnName: 'emp_nm' },
      },
      {
        source: { databaseName: 'ods', tableName: 'opdo_t89_emp_basic_info_ds', columnName: 'hr_org_id' },
        target: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'hr_org_id' },
      },
      {
        source: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'hr_org_id' },
        target: { databaseName: 'famsdb', tableName: 'fams_psum', columnName: 'hr_org_id' },
      },
      {
        source: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'hr_org_id' },
        target: { databaseName: 'famsdb', tableName: 'fams_hr_psum', columnName: 'hr_org_id' },
      },
      {
        source: { databaseName: 'ods', tableName: 'opdo_t89_emp_basic_info_ds', columnName: 'hr_org_nm' },
        target: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'hr_org_nm' },
      },
      {
        source: { databaseName: 'ods', tableName: 'opdo_t89_emp_basic_info_ds', columnName: 'emp_doc_num' },
        target: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'emp_doc_num' },
      },
      {
        source: { databaseName: 'ods', tableName: 'opdo_t89_emp_basic_info_ds', columnName: 'tel_num1' },
        target: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'tel_num1' },
      },
      {
        source: { databaseName: 'ods', tableName: 'opdo_t89_emp_basic_info_ds', columnName: 'tel_num2' },
        target: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'tel_num2' },
      },
      {
        source: { databaseName: 'ods', tableName: 'opdo_t89_emp_basic_info_ds', columnName: 'elec_mail' },
        target: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'elec_mail' },
      },
      {
        source: { databaseName: 'ods', tableName: 'opdo_t89_emp_basic_info_ds', columnName: 'emp_gender' },
        target: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'emp_gender' },
      },
      {
        source: { databaseName: 'ods', tableName: 'opdo_t89_emp_basic_info_ds', columnName: 'birth_dt' },
        target: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'birth_dt' },
      },
      {
        source: { databaseName: 'ods', tableName: 'opdo_t89_emp_basic_info_ds', columnName: 'emp_stat_cd' },
        target: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'emp_stat_cd' },
      },
      {
        source: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'emp_stat_cd' },
        target: { databaseName: 'famsdb', tableName: 'fams_psum', columnName: 'emp_stat_cd' },
      },
      {
        source: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'emp_stat_cd' },
        target: { databaseName: 'famsdb', tableName: 'fams_hr_psum', columnName: 'emp_stat_cd' },
      },
      {
        source: { databaseName: 'ods', tableName: 'opdo_t89_emp_basic_info_ds', columnName: 'dept_main_ky' },
        target: {
          databaseName: 'hds',
          tableName: 'hds_opdo_t89_emp_basic_info_ds',
          columnName: 'dept_main_ky',
        },
      },
      {
        source: {
          databaseName: 'hds',
          tableName: 'hds_opdo_t89_emp_basic_info_ds',
          columnName: 'dept_main_ky',
        },
        target: { databaseName: 'famsdb', tableName: 'fams_psum', columnName: 'dept_main_ky' },
      },
      {
        source: {
          databaseName: 'hds',
          tableName: 'hds_opdo_t89_emp_basic_info_ds',
          columnName: 'dept_main_ky',
        },
        target: { databaseName: 'famsdb', tableName: 'fams_hr_psum', columnName: 'dept_main_ky' },
      },
      {
        source: { databaseName: 'ods', tableName: 'opdo_t89_emp_basic_info_ds', columnName: 'dept_nm' },
        target: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'dept_nm' },
      },
      {
        source: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'dept_nm' },
        target: { databaseName: 'famsdb', tableName: 'fams_psum', columnName: 'dept_nm' },
      },
      {
        source: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'dept_nm' },
        target: { databaseName: 'famsdb', tableName: 'fams_hr_psum', columnName: 'dept_nm' },
      },
      {
        source: { databaseName: 'ods', tableName: 'opdo_t89_emp_basic_info_ds', columnName: 'tellr_id' },
        target: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'tellr_id' },
      },
      {
        source: { databaseName: 'ods', tableName: 'opdo_t89_emp_basic_info_ds', columnName: 'emp_core_org_id' },
        target: {
          databaseName: 'hds',
          tableName: 'hds_opdo_t89_emp_basic_info_ds',
          columnName: 'emp_core_org_id',
        },
      },
      {
        source: {
          databaseName: 'ods',
          tableName: 'opdo_t89_emp_basic_info_ds',
          columnName: 'fst_enter_mbank_tm',
        },
        target: {
          databaseName: 'hds',
          tableName: 'hds_opdo_t89_emp_basic_info_ds',
          columnName: 'fst_enter_mbank_tm',
        },
      },
      {
        source: { databaseName: 'ods', tableName: 'opdo_t89_emp_basic_info_ds', columnName: 'dimss_dt' },
        target: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'dimss_dt' },
      },
      {
        source: { databaseName: 'ods', tableName: 'opdo_t89_emp_basic_info_ds', columnName: 'post_lvl_key' },
        target: {
          databaseName: 'hds',
          tableName: 'hds_opdo_t89_emp_basic_info_ds',
          columnName: 'post_lvl_key',
        },
      },
      {
        source: { databaseName: 'ods', tableName: 'opdo_t89_emp_basic_info_ds', columnName: 'post_lvl_cd' },
        target: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'post_lvl_cd' },
      },
      {
        source: { databaseName: 'ods', tableName: 'opdo_t89_emp_basic_info_ds', columnName: 'post_lvl_nm' },
        target: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'post_lvl_nm' },
      },
      {
        source: { databaseName: 'ods', tableName: 'opdo_t89_emp_basic_info_ds', columnName: 'belg_range' },
        target: { databaseName: 'hds', tableName: 'hds_opdo_t89_emp_basic_info_ds', columnName: 'belg_range' },
      },
    ],
  }
  return (
    <div
      style={{
        width: '100%',
        height: '520px',
      }}
    >
      <Lineage
        miniMapSize={{ height: 100, width: 120 }}
        data={data}
        typeKey="type"
        layout="dagre"
        layoutConf={{
          type: 'dagre',
          rankdir: 'LR',
          align: 'UL',
          begin: [0, 0],
          nodesepFunc: (node: Record<string, any>) => {
            return node.height * 0.6
          },
          ranksepFunc: (node: Record<string, any>) => {
            return node.width * 0.4
          },
        }}
        isUseHightlightTool={true}
        isUseCollapseTool={true}
        showArrow={true}
        legend={true}
      />
    </div>
  )
}
