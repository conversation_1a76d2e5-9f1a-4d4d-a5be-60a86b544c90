import clsx from 'clsx'
import { useAtomValue, useSet<PERSON>tom } from 'jotai'
import React, { ReactNode } from 'react'
import { conversationIdAtom } from 'src/client/pages/AskBI/askBIAtoms'
import { DeepSeekData } from 'src/client/components/deepseek/deepseek-data'
import { chatsAtom, DeepSeekChat } from '../chats'
import { DeepSeekIcon } from './DeepSeekIcon'

export function DeepSeekActiveButton({
  containerProps = {},
  textProps = {},
  chatId,
  icon,
}: {
  icon?: ReactNode
  chatId: string
  containerProps?: React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement>
  textProps?: React.DetailedHTMLProps<React.HTMLAttributes<HTMLSpanElement>, HTMLSpanElement>
  updateDeepSeekData?: (data: DeepSeekData) => DeepSeekData
}) {
  const conversationId = useAtomValue(conversationIdAtom)
  const setChats = useSet<PERSON>tom(chats<PERSON>tom)
  // 临时隐藏，后期再考虑是否开启
  // if (!isBaoWu(Object.values(metricConfigRecord).map((v) => v.metricTableName))) return null

  return (
    <div
      {...containerProps}
      className={clsx('flex cursor-pointer items-center', containerProps.className)}
      onClick={() => {
        setChats((chats) => {
          const idx = chats.findIndex((v) => v.id === chatId)
          if (idx === -1) return chats
          const newChat = new DeepSeekChat().set('ansChatId', chatId).set('conversationId', conversationId)
          const newChats = chats.slice()
          const isExist = idx + 1 < chats.length && DeepSeekChat.assert(chats[idx + 1])
          if (isExist) {
            const oldChat = chats[idx + 1] as DeepSeekChat
            newChat.set('id', oldChat.id).set('status', 'recreated')
          }
          newChats.splice(idx + 1, isExist ? 1 : 0, newChat)
          return newChats
        })
      }}
    >
      {icon === null ? null : icon === undefined ? <DeepSeekIcon className="h-[22px] w-[22px]" /> : icon}
      <span {...textProps} className={clsx('text-primary', textProps.className)} />
    </div>
  )
}
