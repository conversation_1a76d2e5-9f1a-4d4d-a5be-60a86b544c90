/**
 * @description 指标和维度的展示小组件，以后还会有 measure、group by 等的展示小组件
 */
import React from 'react'
import { XMarkIcon } from '@heroicons/react/24/outline'
import clsx from 'clsx'
import { SvgIcon, dimensionCategoryIcon, metricLineIcon } from '@components/SvgIcon'
import { Dimension, DimensionTypeNames, Metric, MetricTypeNames } from 'src/shared/metric-types'
import { IS_H5 } from 'src/shared/constants'
import TextHighlight from './TextHighlight'

interface MetricViewProps {
  label: string
  highlight?: string
  className?: string
  showClose?: boolean
  onClose?: (event: React.MouseEvent<HTMLDivElement, MouseEvent>) => void
}

const tagClassName = 'flex flex-row gap-1 rounded-lg px-1.5 py-1 text-xs'

export function MetricView({ label, highlight, className, showClose, onClose }: MetricViewProps) {
  return (
    <div className={clsx(tagClassName, 'bg-[#E2DFF7]', className)}>
      <SvgIcon icon={metricLineIcon} className="h-4 w-4 text-black" />
      <div className="text-gray-700">
        <TextHighlight text={label} highlight={highlight} />
      </div>
      {showClose && (
        <div onClick={onClose}>
          <XMarkIcon className="h-4 w-4 cursor-pointer text-gray-500 hover:text-black" />
        </div>
      )}
    </div>
  )
}

export function MetricPopupContent({ metric, highlight }: { metric: Metric; highlight?: string }) {
  return (
    <div className="flex flex-col gap-1 md:max-w-xl">
      <div className="text-xs text-gray-500 dark:text-gray-300">
        标识：
        <TextHighlight text={metric.name} highlight={highlight} />
      </div>
      <div className="text-xs text-gray-500 dark:text-gray-300">类型：{MetricTypeNames[metric.type]}</div>
      {metric.synonyms.length > 0 && (
        <div className="text-xs text-gray-500 dark:text-gray-300">
          同义词：
          {metric.synonyms.map((str, i) => {
            return (
              <code key={i} className="rounded-lg bg-gray-100 px-1 py-1">
                <TextHighlight text={str} highlight={highlight} />
              </code>
            )
          })}
        </div>
      )}
      {metric.type === 'list' ? (
        // 列表指标展示每个列表
        <div className="text-xs text-gray-500 dark:text-gray-300">
          {metric.typeParams.metrics.map((n) => n.name).join(', ')}
        </div>
      ) : (
        <div className="text-xs text-gray-500 dark:text-gray-300">
          计算方式：<code className="rounded-lg bg-gray-100 px-1 py-1 dark:bg-gray-800">{metric.displayExpr}</code>
        </div>
      )}
      {'filter' in metric && (
        <div className="text-xs text-gray-500 dark:text-gray-300">
          过滤条件：<code className="rounded-lg bg-gray-100 px-1 py-1 dark:bg-gray-800">{metric.filter}</code>
        </div>
      )}
      <div className="text-xs text-gray-500 dark:text-gray-300">描述：{metric.description}</div>
    </div>
  )
}

interface DimensionViewProps {
  dimension?: Dimension
  className?: string
  showClose?: boolean
  onClose?: (event: React.MouseEvent<HTMLDivElement, MouseEvent>) => void
}
export function DimensionView({ dimension, className, showClose, onClose }: DimensionViewProps) {
  return (
    <div className={clsx(tagClassName, 'bg-[#D0F2EA]', className)}>
      <SvgIcon icon={dimensionCategoryIcon} className="h-4 w-4 text-black" />
      <div className="text-gray-700">{dimension?.label ?? 'Unknown'}</div>
      {showClose && (
        <div onClick={onClose}>
          <XMarkIcon className="h-4 w-4 cursor-pointer text-gray-500 hover:text-black" />
        </div>
      )}
    </div>
  )
}

export function DimensionPopupContent({ dimension }: { dimension: Dimension }) {
  // const renderDimensionValues = (dimension: Dimension) => {
  //   const replaceReg = new RegExp(codeValueSplitChar, 'g')
  //   if (dimension.type === 'categorical') {
  //     if (dimension.values?.length === 0) {
  //       return null
  //     }
  //     return (
  //       <div className="flex max-h-48 flex-wrap gap-1 overflow-y-auto text-xs text-gray-500 dark:text-gray-300">
  //         码值：
  //         {dimension.values?.map((v, i) => (
  //           <span className="rounded-lg bg-gray-100 px-2 py-1 dark:bg-gray-800" key={i}>
  //             {v?.toString().replace(replaceReg, ' / ')}
  //           </span>
  //         ))}
  //       </div>
  //     )
  //   }
  // }

  return (
    <div className="flex max-w-xl flex-col gap-1">
      {!IS_H5 && (
        <>
          <div className="text-xs text-gray-500 dark:text-gray-300">标识：{dimension.name}</div>
          <div className="text-xs text-gray-500 dark:text-gray-300">类型：{DimensionTypeNames[dimension.type]}</div>
          <div className="text-xs text-gray-500 dark:text-gray-300">字段：{dimension.expr}</div>
          <div className="text-xs text-gray-500 dark:text-gray-300">描述：{dimension.description}</div>
        </>
      )}
      {/* {renderDimensionValues(dimension)} */}
    </div>
  )
}
