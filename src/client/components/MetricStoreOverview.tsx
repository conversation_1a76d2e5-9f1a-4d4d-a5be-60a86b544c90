/**
 * @description 展示指标中心的维度、度量、指标列表信息，用于第一个聊天内容 hello-text 下面
 */
import React, { useEffect, useRef, useState } from 'react'
import { Divider, Popover } from 'antd'
import clsx from 'clsx'
import Search from 'antd/es/input/Search'
import { useAtomValue } from 'jotai/react'
import { Dimension, Metric, MetricConfigResponse } from 'src/shared/metric-types'
import { IS_H5 } from 'src/shared/constants'
import { AllMetricsAndDimensionsAtom, metricConfigAtom } from '../pages/AskBI/askBIAtoms'
import { DimensionPopupContent, DimensionView, MetricPopupContent, MetricView } from './MetricTinyViews'

interface Props {
  metricConfig: MetricConfigResponse | null
}

/** 展开或者收起按钮 */
const ToggleButton = ({ isFolded, onClick }: { isFolded: boolean; onClick: () => void }) => (
  <div className="cursor-pointer select-none text-link" aria-expanded={isFolded} onClick={onClick}>
    {isFolded ? '展开' : '收起'}
  </div>
)

const ExpandableList = ({
  title,
  children,
  maxHeight = 96,
  searchPlaceholder,
  onSearch,
  enableSearch = false,
}: {
  title: string
  children: React.ReactNode | JSX.Element
  maxHeight?: number
  searchPlaceholder?: string
  onSearch?: (value: string) => void
  enableSearch?: boolean
}) => {
  const listRef = useRef<HTMLDivElement>(null)
  const [showExpandButton, setShowExpandButton] = useState(false)
  const [isFolded, setIsFolded] = useState(true)

  useEffect(() => {
    const checkHeight = () => {
      const current = listRef.current
      if (current && children) {
        setShowExpandButton(current.scrollHeight > maxHeight)
      }
    }
    checkHeight()
    window.addEventListener('resize', checkHeight)
    return () => {
      window.removeEventListener('resize', checkHeight)
    }
  }, [children, maxHeight])

  return (
    <div className="ExpandableList flex flex-col gap-2">
      <div className="flex flex-row items-center">
        <h3 className="font-bold">{title}</h3>
        {showExpandButton && (
          <>
            <Divider type="vertical" />
            <ToggleButton isFolded={isFolded} onClick={() => setIsFolded(!isFolded)} />
          </>
        )}
        {enableSearch && !IS_H5 && (
          <>
            <Divider type="vertical" />
            <Search
              placeholder={searchPlaceholder}
              allowClear
              onSearch={onSearch}
              style={{ width: 150 }}
              size="small"
            />
          </>
        )}
      </div>
      <div
        className={clsx('flex flex-row flex-wrap gap-1', {
          'max-h-20 overflow-hidden': isFolded,
        })}
        ref={listRef}
      >
        {children}
      </div>
    </div>
  )
}

export default function MetricStoreOverview() {
  const metricConfig = useAtomValue(metricConfigAtom)
  if (!metricConfig) {
    return null
  }

  return (
    <div className="flex flex-col gap-4">
      <DimensionsOverview metricConfig={metricConfig} />
      <MetricsOverview metricConfig={metricConfig} />
    </div>
  )
}

function MetricsOverview({ metricConfig }: Props) {
  const [searchValue, setSearchValue] = useState('')

  const allMetricsAndDimensions = useAtomValue(AllMetricsAndDimensionsAtom)
  if (!metricConfig) {
    return null
  }

  const handleMetricSearch = (value: string) => {
    setSearchValue(value)
  }

  const filteredMetrics = allMetricsAndDimensions.allMetrics.filter(
    (metric) =>
      metric.label.toLowerCase().includes(searchValue.toLowerCase()) ||
      metric.synonyms?.some((synonym) => synonym.toLowerCase().includes(searchValue.toLowerCase())) ||
      metric.name.toLowerCase().includes(searchValue.toLowerCase()),
  )

  return (
    <ExpandableList
      title={`可用的指标列表 ${allMetricsAndDimensions.allMetrics?.length || 0}`}
      enableSearch
      searchPlaceholder="搜索指标"
      onSearch={handleMetricSearch}
    >
      {filteredMetrics.map((metric: Metric) => (
        <Popover
          key={metric.name}
          title={metric.label}
          content={<MetricPopupContent metric={metric} highlight={searchValue} />}
        >
          <div>
            <MetricView label={metric.label} highlight={searchValue} />
          </div>
        </Popover>
      ))}
    </ExpandableList>
  )
}

function DimensionsOverview({ metricConfig }: Props) {
  const allMetricsAndDimensions = useAtomValue(AllMetricsAndDimensionsAtom)
  if (!metricConfig) {
    return null
  }

  const timeDimensions = allMetricsAndDimensions.allDimensions.filter(
    (dim) => dim.type === 'time' || dim.type === 'virtual-time',
  )
  const categoricalTimeDimensions = allMetricsAndDimensions.allDimensions.filter((dim) => dim.type === 'categorical')

  return (
    <>
      {timeDimensions.length > 0 && (
        <ExpandableList title={`可用的时间维度 ${timeDimensions.length}`}>
          {timeDimensions.map((dim: Dimension) => (
            <Popover
              key={`${dim.name}-${dim.label}`}
              title={dim.label}
              placement="bottom"
              content={<DimensionPopupContent dimension={dim} />}
            >
              <div>
                <DimensionView dimension={dim} />
              </div>
            </Popover>
          ))}
        </ExpandableList>
      )}
      <ExpandableList title={`可用的分类维度 ${categoricalTimeDimensions.length}`}>
        {categoricalTimeDimensions.map((dim: Dimension) => (
          <Popover
            key={`${dim.name}-${dim.label}`}
            title={dim.label}
            content={<DimensionPopupContent dimension={dim} />}
          >
            <div>
              <DimensionView dimension={dim} />
            </div>
          </Popover>
        ))}
      </ExpandableList>
    </>
  )
}

export { DimensionsOverview, MetricsOverview }
