import React from 'react'
import { ArrowLeftOutlined } from '@ant-design/icons'
type PageHeaderPropsType = {
  title: React.ReactNode
  onBack?: () => void
  className?: string
  extra?: React.ReactNode
}

export default function PageHeader({ title, onBack, className = '', extra }: PageHeaderPropsType) {
  return (
    <div className={`${className} flex items-center justify-between`}>
      <div className="page-header-title flex flex-1 items-center text-[22px] font-bold text-gray-900 dark:text-slate-100">
        {onBack && <ArrowLeftOutlined onClick={onBack} className="mr-4" />}
        {title}
      </div>
      <div className="page-header-extra-wrap">{extra}</div>
    </div>
  )
}
