/**
 * 管理页面的 Page 组件
 */
import React from 'react'
import clsx from 'clsx'
import PageHeader from 'src/client/components/PageHeader'

interface Props {
  className?: string
  title: React.ReactNode
  children: React.ReactNode
  onBack?: () => void
  extra?: React.ReactNode
}

export default function AdminPage(props: Props) {
  return (
    <div className={clsx('admin-page flex w-full flex-col gap-4', props.className)}>
      <h1 className="text-[22px] font-bold text-[#101828] dark:text-gray-200">
        {<PageHeader title={props.title} extra={props.extra} onBack={props.onBack} />}
      </h1>
      {props.children}
    </div>
  )
}
