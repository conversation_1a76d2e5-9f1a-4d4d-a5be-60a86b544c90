import React, { useRef, useCallback, useEffect, forwardRef, useImperativeHandle, useState } from 'react'
import { useAtom, useAtomValue, useSet<PERSON>tom } from 'jotai/react'
import clsx from 'clsx'
import axios from 'axios'
import { useDebounceFn } from 'ahooks'
import { JsonContentItem, STRUCTURED_MESSAGE_DATA_TYPE } from 'src/shared/common-types'
import {
  agentStructuredMessageAtom,
  askHistoryAtom,
  currentDatasetAtom,
  isProjectChosenAtom,
  isShowMatchedParamDrawerAtom,
  latestSelectedMessageAtom,
  messageInputEditorRefAtom,
  searchMetricConfigAtom,
  updateMessageAndHtmlAtom,
} from 'src/client/pages/AskBI/askBIAtoms'
import './MessageInputEditable.css'
import { IS_H5 } from 'src/shared/constants'
import { askBIApiUrls } from 'src/shared/url-map'

type Props = React.AllHTMLAttributes<HTMLElement> & {
  isMiniMode: boolean
  maxLength?: number
  placeholder?: string
  onFocus?: (event: React.FocusEvent<HTMLElement>) => void
  onBlur?: (event: React.FocusEvent<HTMLElement>) => void
  onKeyDown: (e: React.KeyboardEvent<Element>) => void
}

interface ElementNode extends ChildNode {
  data?: string
  innerText?: string
  attributes?: { name: string; value: string }[]
  dataset?: { [key: string]: string }
}

const keyBlackList = ['tagName', 'content', 'type', 'children', 'class']
const genAttributes = (node: JsonContentItem) => {
  let res = ''
  for (const key in node) {
    if (keyBlackList.includes(key)) {
      continue
    } else {
      res += `${key}='${node[key]}' `
    }
  }
  return res
}

const invisibleChar = '\ufeff'

export const convertToHtml = (json: JsonContentItem[] | string) => {
  if (json) {
    if (typeof json === 'string') {
      return json
    }
    let result = ''
    json.forEach((item) => {
      let res = ''
      if (item.type === '#text') {
        result += item['data-content']
        return
      }
      if (item['data-type'] !== STRUCTURED_MESSAGE_DATA_TYPE.TEXT) {
        const borderClass =
          item['data-type'] === STRUCTURED_MESSAGE_DATA_TYPE.METRIC
            ? 'decoration-metricColor'
            : 'decoration-dimensionColor'
        // 末尾添加不可见字符 \ufeff, 确保h5中光标正常显示
        res = `<span class='${clsx('inline underline px-1 underline-offset-4 decoration leading-6', borderClass)}' ${genAttributes(item) || ''} contenteditable='false'>${item['data-content']}</span>${invisibleChar}`
      } else {
        res = `<span ${genAttributes(item) || ''}>${item['data-content']}</span>`
      }
      result += res
    })
    return result ? result : ''
  }
  return json
}

function convertToJson(node: ElementNode) {
  // 默认data-type是text
  const obj: JsonContentItem = { 'data-type': STRUCTURED_MESSAGE_DATA_TYPE.TEXT }
  obj.type = node.nodeName
  if (!node.data && !node.innerText) {
    return
  }
  if (node.data && node.data === invisibleChar) {
    // 碰到特定字符时不转化为json, 保证删除字符时操作正常
    return
  }
  if (node.nodeType === 3) {
    if (node.nodeName === '#text') {
      obj['data-content'] = node.data
    }
    return obj
  }
  if (node.attributes) {
    // 添加属性
    for (let i = 0; i < node.attributes.length; i++) {
      const attr = node.attributes[i]
      obj[attr.name] = attr.value
    }
  }
  if (node.dataset) {
    // 添加数据
    for (const key in node.dataset) {
      const value = node.dataset[key]
      obj['data-' + key] = value
    }
  }
  obj['data-content'] = node.innerText

  if (obj['data-type'] === STRUCTURED_MESSAGE_DATA_TYPE.METRIC || STRUCTURED_MESSAGE_DATA_TYPE.DIMENSION) {
    return obj
  }

  if (!obj['data-type']) {
    obj['data-type'] = STRUCTURED_MESSAGE_DATA_TYPE.TEXT
  }
  // 添加子元素
  obj.children = []
  for (let i = 0; i < node.childNodes.length; i++) {
    const child = node.childNodes[i]
    if (child.nodeType === Node.ELEMENT_NODE) {
      const item = convertToJson(child)
      if (item) {
        obj.children.push(item)
      }
    }
  }

  return obj
}

const getJson = (element: HTMLDivElement | null) => {
  if (element) {
    const htmlString = element.innerHTML
    const parser = new DOMParser()
    const doc = parser.parseFromString(htmlString, 'text/html')

    // filter后jsonArr不会有undefined
    const jsonArr = Array.from(doc.body?.childNodes).map?.(convertToJson).filter(Boolean) as JsonContentItem[]
    return jsonArr
  }
  return []
}

export default forwardRef(function MessageInputEditable(
  { maxLength = 0, onFocus, onBlur, onKeyDown, isMiniMode, placeholder = '开始向AI提问...', ...props }: Props,
  ref,
) {
  const [contents, setContents] = useAtom(agentStructuredMessageAtom)
  const updateMessageAndHtml = useSetAtom(updateMessageAndHtmlAtom)
  const [isShowMatchedParamDrawer, setIsShowMatchedParamDrawer] = useAtom(isShowMatchedParamDrawerAtom)
  const setMessageInputEditorRefAtom = useSetAtom(messageInputEditorRefAtom)
  const handleSearchMetricConfig = useSetAtom(searchMetricConfigAtom)
  const setLatestSelectedMessage = useSetAtom(latestSelectedMessageAtom)
  const currentDataset = useAtomValue(currentDatasetAtom)
  const [isH5Focused, setIsH5Focused] = useState(false)
  const setAskHistory = useSetAtom(askHistoryAtom)
  const isProjectChosen = useAtomValue(isProjectChosenAtom)

  const editableRef = useRef<HTMLDivElement>(null)

  useImperativeHandle(ref, () => ({
    focus: () => {
      editableRef.current?.focus()
    },
    blur: () => {
      editableRef.current?.blur()
    },
  }))

  // 输入时更新json
  const updateJson = () => {
    const newContents = getJson(editableRef.current)
    setContents(newContents)
  }

  // 输入后搜索指标/维度
  const { run: handleSearch } = useDebounceFn(
    (contents) => {
      if (typeof contents === 'string') {
        handleSearchMetricConfig(contents)
        return
      }
      if (Array.isArray(contents)) {
        if (contents.length === 0) {
          handleSearchMetricConfig('')
        } else {
          const lastContent = contents[contents.length - 1]
          if (lastContent['data-type'] === STRUCTURED_MESSAGE_DATA_TYPE.TEXT) {
            const searchValue = lastContent?.['data-content'].trim() || ''
            handleSearchMetricConfig(searchValue)
          } else {
            handleSearchMetricConfig('')
          }
        }
      }
    },
    {
      wait: 100,
    },
  )

  useEffect(() => {
    handleSearch(contents)
  }, [contents, handleSearch])

  useEffect(() => {
    const pasteHandler = (event: ClipboardEvent) => {
      // 阻止默认的粘贴行为
      event.preventDefault()

      // 获取粘贴的文本内容
      const text = event?.clipboardData?.getData('text/plain')
      // 插入文本内容
      document.execCommand('insertText', false, text)
    }

    const editableElement = editableRef.current
    if (editableElement) {
      editableElement.addEventListener('paste', pasteHandler)
    }

    return () => {
      if (editableElement) {
        editableElement.removeEventListener('paste', pasteHandler)
      }
    }
  }, [editableRef])

  const handleInput = () => {
    // 获取最新输入的内容
    const textContent = editableRef.current?.textContent
    if (textContent && textContent.length > maxLength) {
      editableRef.current.textContent = textContent.slice(0, maxLength)
      updateJson()
      updateCursorPosition()
      return
    }

    // 只在输入为空时才将isInputting置为false, 而不是在structuredMessageAtom里处理, 因为点击热门指标时仍需显示热门指标标签
    // setIsInputting(bool)

    // 手动输入内容后就把选择过得数据去掉
    setLatestSelectedMessage({})
    updateJson()
  }

  const updateCursorPosition = useCallback(() => {
    if (editableRef.current) {
      const range = document.createRange()
      range.selectNodeContents(editableRef?.current)
      range.collapse(false)
      const selection: Selection = window.getSelection() || ({} as Selection)
      selection.removeAllRanges()
      selection.addRange(range)
    }
  }, [editableRef])

  const resetHtml = useCallback(
    (data: JsonContentItem[] | string) => {
      const html = convertToHtml(data)
      if (editableRef.current) {
        editableRef.current.innerHTML = html
      }
    },
    [editableRef],
  )

  useEffect(() => {
    if (!IS_H5) {
      return
    }
    // 只在h5生效
    if (!isH5Focused && contents && contents.length > 0) {
      resetHtml(contents)
      if (isShowMatchedParamDrawer) {
        // 只在刚打开弹窗时, 使光标移到最后面
        updateCursorPosition()
      }
    }
    if (isShowMatchedParamDrawer) {
      setIsH5Focused(true)
    } else {
      setIsH5Focused(false)
    }
  }, [isShowMatchedParamDrawer, contents, isH5Focused, resetHtml, updateCursorPosition])

  const handleFocus = (e: React.FocusEvent<HTMLElement>) => {
    onFocus?.(e)
    if (IS_H5) {
      // 目前只在h5里展示
      setIsShowMatchedParamDrawer(true)
    }
    updateCursorPosition()
  }

  // 点开移动端Drawer的时候重新获取askHistoryAtom
  useEffect(() => {
    if (isShowMatchedParamDrawer) {
      axios
        .get(askBIApiUrls.convers.askHistory, {
          params: {
            sceneId: currentDataset?.sceneId || '',
            isProjectChosen,
            projectId: currentDataset?.projectId || '',
          },
        })
        .then((res) => {
          setAskHistory(res.data.data)
        })
        .catch((error) => {
          setAskHistory([])
          console.error('Get Ask History with error:', error)
        })
    }
  }, [currentDataset, isProjectChosen, isShowMatchedParamDrawer, setAskHistory])

  const handleBlur = (e: React.FocusEvent<HTMLElement>) => {
    onBlur?.(e)
    resetHtml(contents)
  }

  useEffect(() => {
    setMessageInputEditorRefAtom({
      updateCursorPosition,
      setHtml: resetHtml,
    })

    // 清除引用
    return () => {
      setMessageInputEditorRefAtom({ setHtml: () => {} })
    }
  }, [editableRef, setMessageInputEditorRefAtom, resetHtml, updateCursorPosition])

  const handleDelete = (e: React.KeyboardEvent<Element>) => {
    const text = editableRef.current?.innerText
    const isAtEnd = checkCursorPosition(text)
    // 光标在最后时,再去判断 要删除的字符是不是预期的不可见字符
    if (isAtEnd && text) {
      const lastChar = text.slice(-1)
      if (lastChar === invisibleChar) {
        // 碰到\ufeff字符时,直接删除最后一个json对象(能确定为指标/维度区块) ,防止删除字符后, 不可编辑元素光标显示异常,
        // 暂时不处理中间的指标删除情况, 比较麻烦
        e.preventDefault()
        const newContents = contents.slice(0, contents.length - 1)
        updateMessageAndHtml(newContents)
      }
    }
  }
  function checkCursorPosition(text?: string) {
    let caretOffset = 0
    let isAtEnd = true
    if (!text) {
      return isAtEnd
    }
    const target = editableRef.current as HTMLInputElement
    const range = window.getSelection()?.getRangeAt(0)
    if (range) {
      // 克隆一个选中区域
      const preCaretRange = range.cloneRange()
      // 设置选中区域的节点内容为当前节点
      preCaretRange.selectNodeContents(target)
      // 重置选中区域的结束位置
      preCaretRange.setEnd(range.endContainer, range.endOffset)
      caretOffset = preCaretRange.toString().length
      isAtEnd = text.length === caretOffset
    }
    return isAtEnd
  }

  const handleKeyDown = (e: React.KeyboardEvent<Element>) => {
    if (e.key === 'Backspace') {
      handleDelete(e)
    }
    onKeyDown?.(e)
  }

  const handleClick = () => {
    setTimeout(() => {
      editableRef.current?.focus()
    }, 0)
  }

  return (
    <div className={clsx('message-input-editable max-w-full py-1')}>
      <div
        ref={editableRef}
        data-placeholder={placeholder}
        suppressContentEditableWarning
        onInput={handleInput}
        className={clsx('placeholder relative min-h-[16px] break-all px-1.5 py-1.5 leading-[16px] outline-none')}
        contentEditable
        tabIndex={0}
        data-tap-disabled="false"
        onFocus={handleFocus}
        onBlur={handleBlur}
        onKeyDown={handleKeyDown}
        onClick={handleClick}
        {...props}
      />
    </div>
  )
})
