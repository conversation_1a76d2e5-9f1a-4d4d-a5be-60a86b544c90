/* eslint-disable @typescript-eslint/naming-convention */
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react'
import { DecompositionTreeGraph, Shape, ShapeCfg, CardNodeCfg, NodeCfg } from '@ant-design/graphs'
import { useResetState } from 'ahooks'
import { App, Modal, Space } from 'antd'
import { produce } from 'immer'
import html2canvas from 'html2canvas'
import clsx from 'clsx'
import { RedoOutlined, ZoomInOutlined, ZoomOutOutlined } from '@ant-design/icons'
import { Square3Stack3DIcon } from '@heroicons/react/24/solid'
import { timeFunctionToString } from 'src/shared/time-utils'
import {
  AttrAnalysisDimension,
  AttrAnalysisItem,
  AttrAnalysisResult,
  BlobWithRatio,
  ThemeType,
} from 'src/shared/common-types'
import { IS_H5 } from 'src/shared/constants'
import { AttrTimeDiffParams } from 'src/shared/metric-types'
import { formatNumberWithChineseUnit } from 'src/shared/common-utils'
import { generateBlobForCopy } from '../utils/generateBlobForCopy'
import { SvgIcon, arrowToggleRightIcon } from '../components/SvgIcon'
import TextTruncate from '../components/TextTruncate'

enum ScaleRange {
  max = 500,
  min = 60,
}
const scaleStep = 20

const AttrAnalysisChart = (
  props: { data: AttrAnalysisResult; theme: ThemeType },
  ref: React.Ref<{
    downloadPNG: () => void
    copyPNG: () => void
    getBlob: () => Promise<BlobWithRatio | string | undefined>
  }>,
) => {
  // 解构出归因分析的数据
  const {
    data: {
      tree: attrAnalysisData,
      base_compare: baseCompare,
      dimension: attrAnalysisDimension,
      attr_params: attrParams,
      mockChart: isMockChart,
    },
    theme,
  } = props
  let currentId = 0

  const [showChartModal, setShowChartModal] = useState(false)

  const generateNextId = () => {
    currentId += 1
    return currentId
  }

  function transformData(data: AttrAnalysisItem, levelCounts: Record<number, number>, level: number) {
    const id = generateNextId().toString()

    if (!levelCounts[level]) {
      levelCounts[level] = 1
    } else {
      levelCounts[level]++
    }

    const transformedData = {
      id,
      level,
      value: {
        title: data.label,
        node_type: data.node_type,
        items: data.attribution_analysis_result,
      },
      children: [] as any,
    }

    if (data.children_names && data.children_names.length > 0) {
      transformedData.children = data.children_names.map((child) => transformData(child, levelCounts, level + 1))
    }

    return transformedData
  }

  const levelCounts: Record<number, number> = {} // 用于存储每个层级的节点数量，计算canvas高度
  const transformedData = transformData(processTree(attrAnalysisData), levelCounts, 0)

  const attrAnalysisChartRef = useRef<HTMLDivElement>(null)
  const { message } = App.useApp()

  useImperativeHandle(ref, () => ({
    downloadPNG: () => {
      if (attrAnalysisChartRef.current == null) {
        console.error('当前界面不存在此图表的dom元素!')
        return
      }
      html2canvas(attrAnalysisChartRef.current).then((canvas: HTMLCanvasElement) => {
        const pngUrl = canvas.toDataURL('image/png')
        const link = document.createElement('a')
        link.href = pngUrl
        link.download = 'AttrAnalysis.png'
        link.click()
      })
    },
    copyPNG: async () => {
      if (attrAnalysisChartRef.current == null) {
        console.error('当前界面不存在此图表的dom元素!')
        message.error('图表复制失败!')
        return
      }

      const pngItem = new ClipboardItem({
        'image/png': generateBlobForCopy(attrAnalysisChartRef.current).then((blob) => blob),
      })

      navigator.clipboard
        .write([pngItem])
        .then(() => {
          message.success('图表复制成功')
        })
        .catch((error) => {
          message.error('图表复制失败!')
          console.error(error)
        })
    },
    getBlob: async () => {
      if (attrAnalysisChartRef.current == null) {
        console.error('当前界面不存在此图表的dom元素!')
        message.error('导出报告失败')
        return
      }
      return await generateBlobForCopy(attrAnalysisChartRef.current)
    },
  }))

  const graphRef = useRef<any>(null) // 用于保存图表实例
  const [oldScale, setOldScale] = useState(100)
  const [scale, setScale, resetScale] = useResetState(100)

  useEffect(() => {
    if (graphRef.current) {
      // 获取图表实例
      const graph = graphRef.current
      // 获取图表容器的宽度和高度
      const width = graph.getWidth()
      const height = graph.getHeight()
      // 计算中心点坐标
      const centerX = width / 2
      const centerY = height / 2

      const zoomFactor = scale / oldScale // 计算本次缩放因子
      setOldScale(scale)
      graph.zoom(zoomFactor, { x: centerX, y: centerY })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [scale, setScale])

  const config = {
    behaviors: ['drag-canvas'],
    data: transformedData,
    autoFit: true,
    onReady: (graph: any) => {
      graphRef.current = graph
    },
    minimapCfg: { show: true, type: 'delegate' as const },
    markerCfg: (cfg: CardNodeCfg) => {
      const { children }: any = cfg
      return {
        show: children?.length,
      }
    },
    nodeCfg: {
      size: [280, 36],
      autoWidth: true,
      items: {
        style: (_cfg: NodeCfg, _group: any, type: 'value' | 'text') => {
          const styles = {
            text: { fill: '#aaa', fontSize: 28 },
            value: { fill: '#000', fontSize: 28 },
          }
          return styles[type]
        },
        layout: 'follow',
        sort: true,
      },
      title: {
        containerStyle: (cfg: NodeCfg) => {
          const nodeType = (cfg.value as any).node_type
          if (nodeType === 'non_summable') {
            return { fill: '#52c41a' }
          }
          return {}
        },
        style: {
          fontSize: 32,
        },
      },
      label: {
        style: {
          fontSize: 28,
        },
      },
    },
    edgeCfg: {
      type: 'cubic-horizontal',
      label: {
        style: {
          fill: theme === 'dark' ? 'white' : 'black',
        },
      },
      style: (_edge: Shape | ShapeCfg) => {
        return {}
      },
      edgeStateStyles: {
        hover: {
          stroke: 'green',
          strokeOpacity: 1,
        },
      },
    },
    layout: {
      direction: 'LR' as const,
      getHGap: () => {
        return 240
      },
      getVGap: () => {
        return 60
      },
    },
  }
  const miniConfig = {
    ...config,
    minimapCfg: { show: false },
    behaviors: [],
  }

  const height = calculateMaxValueHeight(levelCounts)

  const chartComponent = () => {
    return (
      <>
        <div
          className={clsx('w-full', {
            'min-w-[440px]': isMockChart,
            'min-w-[1000px]': !isMockChart,
          })}
          style={{ height }}
          ref={attrAnalysisChartRef}
        >
          <DecompositionTreeGraph className={theme === 'dark' ? 'bg-slate-700' : 'bg-white'} {...config} />
        </div>
        <div className="analysis-chart-bottom flex justify-between">
          <AnalysisLegend />
          <Space direction="horizontal" size="middle">
            <div className="">{`${scale}%`}</div>
            <ZoomInOutlined
              className={scale >= ScaleRange.max ? 'opacity-40' : ''}
              onClick={() => {
                if (scale < ScaleRange.max) {
                  setScale((pre) => {
                    return pre + scaleStep
                  })
                }
              }}
            />
            <ZoomOutOutlined
              className={scale <= ScaleRange.min ? 'opacity-40' : ''}
              onClick={() => {
                if (scale > ScaleRange.min) {
                  setScale((pre) => {
                    return pre - scaleStep
                  })
                }
              }}
            />
            <RedoOutlined
              onClick={() => {
                resetScale()
              }}
            />
          </Space>
        </div>
      </>
    )
  }

  if (isMockChart) {
    return <>{chartComponent()}</>
  }

  if (!attrAnalysisData) {
    return <div>当前没有归因数据</div>
  }

  return (
    <>
      <div className="flex w-fit flex-col">
        <AttrAnalysisReport
          baseAnalysisResult={attrAnalysisData.attribution_analysis_result}
          baseCompare={baseCompare}
          attrParams={attrParams}
        >
          {!IS_H5 && (
            <div
              className="h-24 w-full cursor-pointer"
              onClick={() => {
                console.info('show modal!')
                setShowChartModal(true)
              }}
            >
              <DecompositionTreeGraph className={theme === 'dark' ? 'bg-slate-700' : 'bg-white'} {...miniConfig} />
            </div>
          )}
        </AttrAnalysisReport>
        <AttrAnalysisDimensionComponent dimensionArray={attrAnalysisDimension} />
        <Modal
          width={1048}
          title="归因分析图表-基于指标树"
          open={showChartModal}
          onCancel={() => {
            setShowChartModal(false)
          }}
          footer={null}
        >
          {chartComponent()}
        </Modal>
      </div>
    </>
  )
}

function AnalysisLegend() {
  return (
    <div className="flex items-center">
      <div className="mr-1 h-3 w-3 rounded-sm bg-[#3696FE]" />
      <p className="mr-2">可加和</p>
      <div className="mr-1 h-3 w-3 rounded-sm bg-[#52c41a]" />
      <p>不可加和</p>
    </div>
  )
}

/** 根据每个层级的节点数量来计算canvas高度 最大不超过610px */
function calculateMaxValueHeight(data: Record<number, number>): number {
  const maxValue = Math.max(...Object.values(data))
  if (maxValue < 7) {
    return 420
  } else {
    return Math.min(810, 360 + (maxValue - 7) * 36)
  }
}

/** 对归因分析的数据进行先置处理，涉及到一些计算、round等 */
function processTree(root: AttrAnalysisItem) {
  return produce(root, (draft) => {
    processNode(draft)
  })
}

/** 对数据进行处理 保留两位小数 */
const formatNumber = (value: number | string | undefined, flag: 'value' | 'percentage') => {
  if (typeof value === 'number') {
    if (flag === 'percentage') {
      return (value * 100).toFixed(2)
    }
    return formatNumberWithChineseUnit({ originNum: value, decimals: 2 })
  } else if (typeof value === 'string') {
    return value.slice(0, 5)
  }
  return NaN
}

function processNode(node: AttrAnalysisItem) {
  const formattedValue = {
    rate: formatNumber(node.attribution_analysis_result.rate_change, 'percentage'),
    contribution: formatNumber(node.attribution_analysis_result.contribution, 'percentage'),
  }
  const absNumber = formatNumberWithChineseUnit({
    originNum: Math.abs(node.attribution_analysis_result.values[1]),
    decimals: 2,
  })

  const attributionAnalysisResult: any = [
    { value: absNumber },
    { text: '波动', value: `${formattedValue.rate}${formattedValue.rate ? '%' : ''}` },
  ]

  // 如果贡献度不是NaN，把贡献度添加进去
  if (!Number.isNaN(formattedValue.contribution)) {
    attributionAnalysisResult.push({
      text: '贡献度',
      value: `${formattedValue.contribution}${formattedValue.contribution ? '%' : ''}`,
    })
  }
  // 此属性作为graph的items
  node.attribution_analysis_result = attributionAnalysisResult

  if (node.children_names && node.children_names.length > 0) {
    // 在处理子节点之前按rank从小到大排序
    node.children_names.sort((a, b) => a.attribution_analysis_result.rank - b.attribution_analysis_result.rank)

    node.children_names.forEach((child) => {
      processNode(child)
    })
  }
}

/** 归因分析报告 */
const AttrAnalysisReport = ({
  children,
  title,
  baseCompare,
  baseAnalysisResult,
  attrParams,
}: {
  children: JSX.Element | React.ReactNode
  title?: string
  baseCompare: AttrTimeDiffParams
  baseAnalysisResult: { values: [number, number]; abs_change: number; rate_change: number; contribution: number }
  attrParams: { metric: string[]; filter: string }
}) => {
  // 不满足展示归因分析报告的条件
  if (!baseCompare || !Array.isArray(baseAnalysisResult.values) || baseAnalysisResult.values.length < 2) {
    return null
  }

  const { startTimeStr, endTimeStr } = getTimeStr(baseCompare)

  const reportData = [
    { value: baseAnalysisResult.values[0], date: startTimeStr },
    { value: baseAnalysisResult.values[1], date: endTimeStr },
  ]

  // 展示比值与比例
  const metricChange = formatNumberWithChineseUnit({ originNum: baseAnalysisResult.abs_change, decimals: 2 })
  const changeOrRate = `${(baseAnalysisResult.rate_change * 100).toFixed(2)}%`

  return (
    <>
      <div className="attr-header-wrapper flex w-full justify-between">
        <div className="flex flex-col items-start gap-2">
          <div className="text-lg font-medium">{title}</div>
          <div className="rounded-sm bg-orange-200 p-1 font-medium text-yellow-700">
            {metricChange}
            {` (${changeOrRate})`}
          </div>
          <div className="flex gap-2">
            {reportData.map((item, index) => {
              return (
                <React.Fragment key={index}>
                  <div>
                    <div className="text-2xl">
                      {formatNumberWithChineseUnit({ originNum: item.value, decimals: 2 })}
                    </div>
                    <span className="ml-1 text-gray-500 dark:text-gray-200">{item.date}</span>
                  </div>
                  {index === 0 && <SvgIcon className="mt-2 h-5 w-5" icon={arrowToggleRightIcon} />}
                </React.Fragment>
              )
            })}
          </div>
        </div>
        <div className="h-24 w-40">{children}</div>
      </div>
      {attrParams?.filter && (
        <div className="flex w-fit items-center gap-1 rounded-lg bg-purple-100 px-2 py-1 text-xs">
          <Square3Stack3DIcon className="h-4 w-4 text-purple-500" />
          <div className="text-gray-700">{attrParams.filter}</div>
        </div>
      )}
    </>
  )
}

function getTimeStr(attrTimeDiff: AttrTimeDiffParams): { startTimeStr: string; endTimeStr: string } {
  const { baseTime, compareTime } = attrTimeDiff
  const startTimeStr = timeFunctionToString({ func: baseTime })
  const endTimeStr = timeFunctionToString({ func: compareTime, isEndTime: true })

  return { startTimeStr, endTimeStr }
}

function AttrAnalysisDimensionComponent({ dimensionArray }: { dimensionArray: AttrAnalysisDimension[] }) {
  const dimensionNames = dimensionArray.map((d) => d.dim)

  const [currentSelectedDimension, setCurrentSelectedDimension] = useState<string | null>(
    dimensionNames.length > 0 ? dimensionNames[0] : null,
  )

  if (dimensionArray.length === 0) {
    return <>当前暂无维度拆解数据</>
  }

  return (
    <div>
      <div className="divider-line my-2 border-t" />
      <div className="font-medium">维度贡献度排名</div>
      <div className="my-1 flex gap-2">
        {dimensionNames.map((name) => {
          return (
            <div
              key={name}
              className={clsx('cursor-pointer border px-2 py-0.5', {
                'border-primary bg-[#F9F7FF] dark:bg-slate-600': name === currentSelectedDimension,
              })}
              onClick={() => {
                setCurrentSelectedDimension(name)
              }}
            >
              {name}
            </div>
          )
        })}
      </div>
      {currentSelectedDimension && (
        <DimensionComponent attrDimension={dimensionArray.find((i) => i.dim === currentSelectedDimension)} />
      )}
    </div>
  )
}

const contributionTitleWrapperClassName = 'flex items-center gap-1 text-[#575757] dark:text-white'
function DimensionComponent({ attrDimension }: { attrDimension?: AttrAnalysisDimension }) {
  if (!attrDimension) {
    return <>当前暂无维度拆解详细数据</>
  }

  const renderContributionList = (elements: string[], contributions: number[], changes: number[], color: string) => (
    <div className="flex flex-col gap-1 self-end">
      {elements.map((m, index) => {
        const progressRatio = contributions[index] / contributions[0]
        const progressWidth = `${progressRatio * 100}%`
        return (
          <div key={m} className="flex">
            <div className="flex items-center gap-1">
              <TextTruncate className="w-24 md:w-24">{m}</TextTruncate>
              <div className="h-2 w-24 overflow-hidden rounded-md bg-gray-200 md:w-32">
                <div style={{ width: progressWidth }} className={`h-full rounded-md bg-[${color}] transition-all`} />
              </div>
            </div>
            <div className={`ml-2 truncate text-sm text-[${color}]`}>
              <TextTruncate className="w-32 md:w-40">
                {formatNumberWithChineseUnit({ originNum: changes[index], decimals: 2 })} (
                {(contributions[index] * 100).toFixed(2)}%)
              </TextTruncate>
            </div>
          </div>
        )
      })}
    </div>
  )

  return (
    <div className="flex flex-col gap-2">
      <div className={contributionTitleWrapperClassName}>
        <div className="h-3 w-3 rounded-full bg-[#25A011]" />
        <div>同向贡献度前三</div>
      </div>
      {renderContributionList(
        attrDimension.element_pos,
        attrDimension.contribution_pos,
        attrDimension.change_pos,
        '#25A011',
      )}
      <div className={contributionTitleWrapperClassName}>
        <div className="h-3 w-3 rounded-full bg-[#FD8516]" />
        <div>反向贡献度前三</div>
      </div>
      {renderContributionList(
        [...attrDimension.element_neg].reverse(),
        [...attrDimension.contribution_neg].reverse(),
        [...attrDimension.change_neg].reverse(),
        '#FD8516',
      )}
    </div>
  )
}

export default forwardRef(AttrAnalysisChart)
