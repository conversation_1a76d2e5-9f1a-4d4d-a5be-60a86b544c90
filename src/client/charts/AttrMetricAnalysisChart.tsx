import { App } from 'antd'
import clsx from 'clsx'
import { useAtomValue } from 'jotai'
import html2canvas from 'html2canvas'
import React, { forwardRef, useImperativeHandle, useMemo, useRef, useState } from 'react'
import { Metric } from 'src/shared/metric-types'
import { formatNumber } from 'src/shared/common-utils'
import { AttrMetricAnalysisResult, BlobWithRatio, ThemeType } from 'src/shared/common-types'
import { generateBlobForCopy } from '../utils/generateBlobForCopy'
import TextTruncate from '../components/TextTruncate'
import { metricConfigAtom } from '../pages/AskBI/askBIAtoms'

const AttrMetricAnalysisChart = (
  props: { data: AttrMetricAnalysisResult; theme: ThemeType },
  ref: React.Ref<{
    downloadPNG: () => void
    copyPNG: () => void
    getBlob: () => Promise<BlobWithRatio | string | undefined>
  }>,
) => {
  const metricConfig = useAtomValue(metricConfigAtom)
  const { data } = props
  const metricData = useMemo(() => {
    return metricConfig?.allMetrics.find((v) => v.label === data.metricName) ?? null
  }, [data.metricName, metricConfig])
  const attrMetricAnalysisChartRef = useRef<HTMLDivElement>(null)
  const { message } = App.useApp()

  useImperativeHandle(ref, () => ({
    downloadPNG: () => {
      if (attrMetricAnalysisChartRef.current == null) {
        console.error('当前界面不存在此图表的dom元素!')
        return
      }
      html2canvas(attrMetricAnalysisChartRef.current).then((canvas: HTMLCanvasElement) => {
        const pngUrl = canvas.toDataURL('image/png')
        const link = document.createElement('a')
        link.href = pngUrl
        link.download = 'AttrAnalysis.png'
        link.click()
      })
    },
    copyPNG: async () => {
      if (attrMetricAnalysisChartRef.current == null) {
        console.error('当前界面不存在此图表的dom元素!')
        message.error('图表复制失败!')
        return
      }

      const pngItem = new ClipboardItem({
        'image/png': generateBlobForCopy(attrMetricAnalysisChartRef.current).then((blob) => blob),
      })

      navigator.clipboard
        .write([pngItem])
        .then(() => {
          message.success('图表复制成功')
        })
        .catch((error) => {
          message.error('图表复制失败!')
          console.error(error)
        })
    },
    getBlob: async () => {
      if (attrMetricAnalysisChartRef.current == null) {
        console.error('当前界面不存在此图表的dom元素!')
        message.error('导出报告失败')
        return
      }
      return await generateBlobForCopy(attrMetricAnalysisChartRef.current)
    },
  }))

  return (
    <div ref={attrMetricAnalysisChartRef} className="mb-2 flex w-full flex-col gap-4">
      <div className="Attr-Header flex flex-col gap-3">
        <div className="text-2xl font-bold">
          {formatNumber(data.metricValue[data.metricName], metricData?.formatTemplate ?? '')}
        </div>
        <div className="flex flex-row items-center gap-2">
          <div className="rounded-md border border-[#C8C3F0] bg-[#F3F2FB] px-3 py-1">{data.metricName}</div>
          {data.metricExpr && <div>=</div>}
          {data.metricExpr &&
            data.metricExpr.split(' ').map((part, index) => (
              <div
                key={index}
                className={clsx({
                  'rounded-md border border-[#C8C3F0] bg-[#F3F2FB] px-3 py-1': index % 2 === 0,
                })}
              >
                {part}
              </div>
            ))}
        </div>
      </div>
      <AttrDimensionDetail metric={metricData} dimensionDetail={data.dimensionDetail} metricName={data.metricName} />
    </div>
  )
}

function AttrDimensionDetail(props: {
  dimensionDetail: { name: string; data: { [key: string]: string | number }[] }[]
  metricName: string
  metric: Metric | null
}) {
  const { dimensionDetail, metricName } = props
  const [selectedDimension, setSelectedDimension] = useState<string>(dimensionDetail[0]?.name)
  if (!dimensionDetail || dimensionDetail.length === 0) {
    return <div className="Attr-Container rounded-2xl bg-[#F8F8F8] p-5">当前暂无维度相关数据</div>
  }
  const resultData = dimensionDetail.find((e) => e.name === selectedDimension)?.data
  // 此处暂时先取10个展示 后续做分页
  const dimensionData = resultData
    ? resultData.length > 10
      ? [...resultData.slice(0, 5), ...resultData.slice(-5)]
      : resultData
    : []

  const maxVal = dimensionData ? Math.max(...dimensionData.map((e) => (e[metricName] as number) || 0)) : null

  return (
    <div className="Attr-Container flex flex-col gap-4 rounded-2xl bg-[#F8F8F8] p-5">
      <div className="Dimension-select flex gap-2">
        {dimensionDetail.map((e, index) => {
          return (
            <div
              key={index}
              className={clsx('cursor-pointer rounded-md border px-3 py-1', {
                'border-transparent bg-[#8BECD4]': selectedDimension === e.name,
                'border-[#A8DED1] bg-[#F7FFFD]': selectedDimension !== e.name,
              })}
              onClick={() => {
                setSelectedDimension(e.name)
              }}
            >
              {e.name}
            </div>
          )
        })}
      </div>

      <div className="Dimension-display">
        {dimensionData?.map((d, index) => {
          const progressWidth = maxVal ? Math.max((d[metricName] as number) / maxVal, 0) : 0
          return (
            <div className="flex flex-row items-center gap-4 border-b border-[#E4E4E4] py-[6px]" key={index}>
              <TextTruncate className="w-24">{d[selectedDimension]}</TextTruncate>
              <div className="h-2 flex-1 overflow-hidden rounded-md bg-gray-200">
                <div
                  style={{ width: `${progressWidth * 100}%` }}
                  className="h-full rounded-md bg-primary transition-all"
                />
              </div>
              <TextTruncate className="w-28 font-bold">
                {formatNumber(d[metricName], props.metric?.formatTemplate)}
              </TextTruncate>
            </div>
          )
        })}
      </div>
    </div>
  )
}
export default forwardRef(AttrMetricAnalysisChart)
