/**
 * @description 所有图表的包装组件。接收一个 props，可以渲染任何类型的图表
 * 注：不可访问 atoms
 * 在这里拼装 chartOptions，对应 echarts 或者自研图表配置的数据格式
 */
import React, { forwardRef, useImperativeHandle, useRef, Suspense, useMemo } from 'react'
import { InformationCircleIcon } from '@heroicons/react/24/outline'
import { useAtomValue } from 'jotai'
import {
  ChartType,
  ThemeType,
  AttrAnalysisResult,
  BlobWithRatio,
  RowsMetadata,
  isDimension,
  isMetric,
  OlapRow,
  AttrMetricAnalysisResult,
} from '@shared/common-types'
import { assertExhaustive, formatNumber, formatOrderBy, sortByOrderBy } from '@shared/common-utils'
import { Dimension, MetricWithPeriodOverPeriod, QueryParamsVerified } from 'src/shared/metric-types'
import { isBaoWu } from 'src/shared/baowu-share-utils'
import {
  formatXLableLength,
  getMaxAndMinValues,
  getColumnNamesForCategoricalColumns,
  getSeriesOptions,
  getVisualMapOptions,
  mapValueToRange,
  transformArrayObject,
  AssistantChartChatItem,
} from '../utils'
import {
  BAR_CHART_NUM,
  DATAZOOM_SHOW_LIMIT,
  ChartThemeType,
  CHART_THEMES,
  PIE_CHART_DATA_LIMIT,
  IS_H5,
} from '../../shared/constants'
import { currentChartThemeTypeAtom, metricConfigAtom, themeAtom } from '../pages/AskBI/askBIAtoms'
import Kpi from './Kpi'
import SimpleTable from './SimpleTable'
import RankBarChart from './RankBarChart'
import AttrMetricAnalysisChart from './AttrMetricAnalysisChart'
const EChartsReact = React.lazy(() => import('./EChartsReact'))
const AttrAnalysisChart = React.lazy(() => import('./AttrAnalysisChart'))

function getChartFormatter(
  params: {
    name: string
    marker: string
    seriesName: string
    value: number
  }[],
  formatTemplate: string,
) {
  if (!params.length) return
  // 使用HTML表格实现数字右对齐
  let tooltipHtml = '<table style="table-layout: fixed; border-collapse:separate; border-spacing:0px 8px;">'
  tooltipHtml += `<tr>
                          <td style="text-align: left; padding-right: 10px;">${params[0].name}</td>
                      </tr>`
  params.forEach((param) => {
    // 只展示有数据的内容, 这里的数值认为是字符串,所以不用特意判断0
    if (param.value) {
      const formattedValue = formatNumber(+param.value, formatTemplate)
      tooltipHtml += `<tr>
      <td style="padding-bottom: 8px; border-bottom: 1px dashed #E1E1E1; text-align: left; padding-right: 10px; word-break: break-all; white-space: break-spaces; word-wrap: break-word;">${param.marker}${param.seriesName}</td>
      <td style="padding-bottom: 8px; border-bottom: 1px dashed #E1E1E1; text-align: right; font-weight: 500; vertical-align: top;">${formattedValue}</td>
      </tr>`
    }
  })
  tooltipHtml += '</table>'
  return `
<div style="max-width: 80vw; max-height: 300px; overflow-y: auto;">
${tooltipHtml}
</div>
  `
}

function getMultiChartOptionValues(rows: OlapRow[], rowsMetadata: RowsMetadata) {
  // 将维度 度量抽离出来
  const dimensions: Dimension[] = rowsMetadata.filter(isDimension).map((item) => item.value)
  const metrics: MetricWithPeriodOverPeriod[] = rowsMetadata.filter(isMetric).map((item) => item.value)

  // 第1列为 X 轴
  const firstDimName = dimensions[0].name
  const firstDimValueSet = new Set<string>()
  // 第2列为多线图的一条线
  const secondDimName = dimensions[1].name
  const secondDimValueSet = new Set<string>()
  const firstMetricName = metrics[0].name

  const firstDimRecord: Record<string, any[]> = {}
  const secondDimRecord: Record<string, any[]> = {}

  const details = new Map<string, Map<string, number>>(new Map())

  for (const row of rows) {
    const firstVal = row[firstDimName]
    const secondVal = row[secondDimName]
    firstDimValueSet.add(firstVal)
    secondDimValueSet.add(secondVal)

    if (!details.has(secondVal)) details.set(secondVal, new Map())
    const subMap = details.get(secondVal)!
    if (!subMap.has(firstVal)) subMap.set(firstVal, 0)
    const val = subMap.get(firstVal)!
    subMap.set(firstVal, val + Number(row[firstMetricName]))

    if (!firstDimRecord[firstVal]) firstDimRecord[firstVal] = []
    firstDimRecord[firstVal].push(row[secondVal])

    if (!secondDimRecord[firstVal]) secondDimRecord[secondVal] = []
    secondDimRecord[secondVal].push(row[firstVal])
  }

  const firstDimValues = Array.from(firstDimValueSet)
  const secondDimValues = Array.from(secondDimValueSet)

  return {
    firstDimName: firstDimName,
    firstDimValues,
    secondDimName: secondDimName,
    secondDimValues,
    firstMetricName: firstMetricName,
    firstDimRecord,
    secondDimRecord,
    details,
  }
}

const gridLeft = '3%'

interface Props {
  theme: ThemeType
  data: AssistantChartChatItem
  onTableDataChange: (sortedData: OlapRow[]) => void
}

/**
 *  PieChart => sort by second property & slice 15 items
 */
function sortDataOfPieChart(
  rows: Array<{ [key: string]: any }>,
  rowsMetaData: RowsMetadata,
): Array<{ [key: string]: any }> {
  if (rows.length < 2 || Object.keys(rows[0]).length < 2) {
    return rows
  }

  const metricInfo = rowsMetaData.find(isMetric)
  const dimensionInfo = rowsMetaData.find(isDimension)
  if (!metricInfo || !dimensionInfo) {
    return rows // Or handle the error as appropriate
  }
  const metricName = metricInfo.value.name
  const dimensionName = dimensionInfo.value.name

  const sortedArray = [...rows].sort((a, b) => b[metricName] - a[metricName])

  const displayedData = sortedArray.slice(0, PIE_CHART_DATA_LIMIT - 1)
  const others = sortedArray.slice(PIE_CHART_DATA_LIMIT - 1)
  if (others.length > 0) {
    const totalSum = others.reduce((sum, item) => sum + (Number(item[metricName]) || 0), 0)

    const othersDataPoint = { [dimensionName]: '其他', [metricName]: totalSum }
    displayedData.push(othersDataPoint)
  }
  return displayedData
}

function getChartOption(
  rows: OlapRow[],
  rowsMetadata: RowsMetadata,
  chartType: ChartType,
  theme: string,
  chartThemeType: ChartThemeType,
  queryParamsVerified?: QueryParamsVerified,
) {
  const backgroundColor = theme === 'dark' ? 'rgb(51, 65, 85)' : 'white'
  // 将维度 度量抽离出来
  const dimensions: Dimension[] = rowsMetadata.filter(isDimension).map((item) => item.value)
  const metrics: MetricWithPeriodOverPeriod[] = rowsMetadata.filter(isMetric).map((item) => item.value)
  const chartColors = CHART_THEMES.find((item) => {
    return item.type === chartThemeType
  })?.colors
  const orderByList = formatOrderBy({
    queryParamsVerified,
    defaultValue: {
      metricName: metrics[0]?.name || '',
      orderBy: 'desc',
    },
  })
  const yAxisLabelFormatTemplate = metrics.length > 0 ? metrics[0].formatTemplate.replace(/\.\d+f/, '.1f') : ''

  switch (chartType) {
    case 'SimpleTable':
    case 'Kpi': {
      return {
        dataset: {
          // dimensions 会返回多个列，第一列为 x，非首列为 y。有的时候 y 会返回 string，这些列需要去掉
          source: rows,
        },
      }
    }
    case 'LineChart': {
      const sortedRows =
        metrics.length > 0 && dimensions.filter((dimension) => dimension.type === 'categorical').length > 0
          ? sortByOrderBy({
              rows,
              orderByList,
              toValue: (v) => parseFloat(v),
            })
          : rows
      return {
        color: chartColors,
        backgroundColor,
        grid: {
          containLabel: true,
          left: gridLeft,
          bottom: 0,
          right: 0,
          top: 30,
        },
        legend: {
          orient: 'horizontal',
          type: 'scroll',
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            animation: false,
          },
          hideDelay: IS_H5 ? 0 : 300,
          enterable: true,
          confine: true,
        },
        xAxis: {
          type: 'category',
          data: sortedRows.map((row) => row[dimensions[0].name]),
          show: true,
          axisLabel: {
            formatter: formatXLableLength,
          },
          axisTick: {
            alignWithLabel: true,
          },
        },
        yAxis: {
          show: true,
          axisLabel: {
            formatter: (value: number) => formatNumber(value, yAxisLabelFormatTemplate),
          },
        },
        dataset: {
          // dimensions 会返回多个列，第一列为 x，非首列为 y。有的时候 y 会返回 string，这些列需要去掉
          source: sortedRows,
        },
        series: [
          {
            type: 'line',
            name: metrics[0].label,
            data: sortedRows.map((row) => row[metrics[0].name]),
            encode: {
              x: dimensions[0].name,
              y: metrics[0].name,
            },
          },
        ],
      }
    }
    case 'ColumnChart': {
      const sortedRows =
        metrics.length > 0 && dimensions.filter((dimension) => dimension.type === 'categorical').length > 0
          ? sortByOrderBy({
              rows,
              orderByList,
              toValue: (v) => parseFloat(v),
            })
          : rows
      return {
        color: chartColors,
        backgroundColor,
        grid: {
          containLabel: true,
          left: gridLeft,
          bottom: 0,
          right: 0,
          top: 30,
        },
        legend: {
          orient: 'horizontal',
          type: 'scroll',
        },
        tooltip: {
          trigger: 'axis',
          formatter: function (params: any[]) {
            return getChartFormatter(params, metrics[0].formatTemplate)
          },
          axisPointer: {
            animation: false,
          },
          hideDelay: IS_H5 ? 0 : 300,
          enterable: true,
          confine: true,
        },
        xAxis: {
          type: 'category',
          data: sortedRows.map((row) => row[dimensions[0].name]),
          show: true,
          axisLabel: {
            formatter: formatXLableLength,
          },
          axisTick: {
            alignWithLabel: true,
          },
        },
        yAxis: {
          show: true,
          axisLabel: {
            formatter: (value: number) => formatNumber(value, yAxisLabelFormatTemplate),
          },
        },
        dataset: {
          // dimensions 会返回多个列，第一列为 x，非首列为 y。有的时候 y 会返回 string，这些列需要去掉
          source: sortedRows,
        },
        series: [
          {
            type: 'bar',
            name: metrics[0].label,
            data: sortedRows.map((row) => row[metrics[0].name]),
            encode: {
              x: dimensions[0].name,
              y: metrics[0].name,
            },
            itemStyle: {
              borderRadius: [5, 5, 0, 0],
            },
          },
        ],
      }
    }
    case 'PieChart': {
      const data = sortDataOfPieChart(rows, rowsMetadata).map((item) => {
        return {
          value: item[metrics[0].name],
          name: item[dimensions[0].name] == null ? dimensions[0].name : item[dimensions[0].name],
        }
      })
      return {
        color: chartColors,
        backgroundColor,
        legend: {
          orient: 'vertical',
          type: 'scroll',
          right: 0,
          textStyle: {
            width: 80,
            overflow: 'truncate',
          },
          /*
          if (theme === 'dark') {
            options.legend.pageIconColor = '#ffffff'
            options.legend.pageIconInactiveColor = '#b3b3b3'
            options.legend.pageTextStyle = {
              color: '#ffffff',
            }
          }
          */
        },
        tooltip: {
          trigger: 'item',
          // TODO: pie 的 tooltip formatter 有 bug
          // formatter: function (params: any[]) {
          //   return getChartFormatter(params, metrics[0].formatTemplate)
          // },
          axisPointer: {
            animation: false,
          },
          formatter: (item: any) => {
            return `${item.seriesName}: ${formatNumber(parseFloat(item.data.value), metrics[0].formatTemplate)}`
          },
          hideDelay: IS_H5 ? 0 : 300,
          enterable: true,
          confine: true,
        },
        // pie 不使用 dataset
        series: [
          {
            type: 'pie',
            avoidLabelOverlap: true,
            name: metrics[0].label,
            data: data,
            center: ['35%', '50%'],
            itemStyle: {
              borderRadius: 5,
              borderColor: backgroundColor,
              borderWidth: 2,
            },
            label: {
              width: 80,
              overflow: 'truncate',
              formatter: '{b}\n{d}%',
            },
          },
        ],
      }
    }
    case 'ScatterChart': {
      const chartOption = {
        color: chartColors,
        backgroundColor,
        grid: {
          containLabel: true,
          left: 0,
          bottom: 0,
          right: 0,
          top: 30,
        },
        legend: {
          orient: 'horizontal',
          type: 'scroll',
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            animation: false,
          },
          hideDelay: IS_H5 ? 0 : 300,
          enterable: true,
          confine: true,
        },
        xAxis: {
          type: 'category',
          show: true,
          formatter: formatXLableLength,
          axisTick: {
            alignWithLabel: true,
          },
        },
        yAxis: {
          show: true,
          type: 'value',
        },
        dataset: {
          // dimensions 会返回多个列，第一列为 x，非首列为 y。有的时候 y 会返回 string，这些列需要去掉
          source: rows,
        },
        series: {
          type: 'scatter',
          // FIXME:
          encode: {
            // 从 0 开始，第一个是 x 轴，后面的是 y 轴
            x: dimensions[0].name,
            y: metrics.map((item) => item.name),
            tooltip: [dimensions[0].name, ...metrics.map((item) => item.name)],
          },
        },
      } as any
      // 记录一下列、维度、度量的数量
      const columnsNum = rowsMetadata.length
      const metricsNum = metrics.length

      // 两列都是度量
      if (metricsNum === 2 && columnsNum === 2) {
        chartOption.xAxis.type = 'value'
      }
      // 三列 有两列是度量 展示气泡图
      else if (metricsNum === 2 && rows.length && Object.keys(rows[0]).length === 3) {
        const yAxisLabel = metrics[0].name
        const scatterLabel = metrics[1].name

        chartOption.series.encode.x = dimensions[0].name
        chartOption.series.encode.y = yAxisLabel
        chartOption.xAxis.max = rows.length
        chartOption.yAxis.min = 0
        chartOption.grid.right = '50px'
        // 有三个通道 将其中一个度量作为气泡大小
        const [maxValue, minValue] = getMaxAndMinValues(rows, scatterLabel)
        chartOption.series.symbolSize = function (data: any) {
          const convertData = Number(data[scatterLabel])
          if (!isNaN(convertData)) return mapValueToRange(convertData, maxValue, minValue)
          return 10
        }
        chartOption.visualMap = getVisualMapOptions(maxValue, minValue, scatterLabel)
      }
      // 三列 全都是度量
      else if (metricsNum === 3 && Object.keys(rows[0]).length === 3) {
        const xAxisLabel = metrics[0].name
        const yAxisLabel = metrics[1].name
        const scatterLabel = metrics[2].name

        chartOption.series.encode.x = xAxisLabel
        chartOption.series.encode.y = yAxisLabel
        chartOption.yAxis.min = 0
        chartOption.grid.right = '50px'
        // 有三个通道 将其中一个度量作为气泡大小
        const [maxValue, minValue] = getMaxAndMinValues(rows, scatterLabel)
        chartOption.series.symbolSize = function (data: any) {
          const convertData = Number(data[scatterLabel])
          if (!isNaN(convertData)) return mapValueToRange(convertData, maxValue, minValue)
          return 10
        }
        chartOption.visualMap = getVisualMapOptions(maxValue, minValue, scatterLabel)
      }
      // 暂时不会走到这种情况，散点气泡分类图
      else if (rows.length && dimensions.length === 1 && metrics.length === 3) {
        const doubleRows = rows.map((item) => Object.values(item)) // 转化为二维数组
        const yLabelIndex = metrics[0].name
        const bubbleLabelIndex = metrics[1].name // 将某一列作为气泡大小
        const classifiedColumnIndex = dimensions[0].name

        if (getColumnNamesForCategoricalColumns(rows, classifiedColumnIndex).length > 0) {
          const classifiedColumn = getColumnNamesForCategoricalColumns(rows, classifiedColumnIndex)
          const xLabelIndex = metrics[2].name
          const [maxValue, minValue] = getMaxAndMinValues(rows, bubbleLabelIndex)

          chartOption.series = getSeriesOptions(
            classifiedColumn,
            doubleRows,
            xLabelIndex,
            yLabelIndex,
            classifiedColumnIndex,
            [maxValue, minValue],
            bubbleLabelIndex,
          )

          chartOption.visualMap = getVisualMapOptions(maxValue, minValue, bubbleLabelIndex)
        } else {
          const [maxValue, minValue] = getMaxAndMinValues(rows, bubbleLabelIndex)
          chartOption.series.symbolSize = function (data: any) {
            const convertData = Number(data[Number(bubbleLabelIndex)])
            if (!isNaN(convertData)) return mapValueToRange(convertData, maxValue, minValue)
            return 10
          }
        }
        chartOption.xAxis.type = 'value'
      }
      return chartOption
    }
    case 'TreemapChart': {
      return {
        color: chartColors,
        backgroundColor,
        grid: {
          containLabel: true,
          left: 0,
          bottom: 0,
          right: 0,
          top: 50,
        },
        legend: {
          orient: 'horizontal',
          type: 'scroll',
        },
        tooltip: {
          hideDelay: IS_H5 ? 0 : 300,
          enterable: true,
          confine: true,
        },
        xAxis: {
          type: 'category',
          data: rows.map((row) => row[dimensions[0].name]),
          show: false,
          axisLabel: {
            formatter: formatXLableLength,
          },
          axisTick: {
            alignWithLabel: true,
          },
        },
        yAxis: {
          show: false,
        },
        // treemap 不支持 dataset
        series: [
          {
            type: 'treemap',
            breadcrumb: { show: false },
            name: metrics[0].label,
            // treemap 不支持 dataset
            data: transformArrayObject(rows, rowsMetadata),
            tooltip: {
              formatter: function (params: any) {
                let val = Number(params?.data?.value)
                if (Number.isNaN(val)) {
                  val = 0
                }
                return `${params.name}${params.seriesName}: ${formatNumber(val, metrics[0].formatTemplate)}`
              },
              hideDelay: IS_H5 ? 0 : 300,
              enterable: true,
              confine: true,
            },
          },
        ],
      }
    }
    case 'GroupColumnChart':
    case 'StackedColumnChart':
    case 'MultiLineChart': {
      if (dimensions.length < 2) {
        throw new Error('GroupColumnChart, StackedColumnChart, MultiLineChart need at least 2 dimensions')
      }
      const data = getMultiChartOptionValues(rows, rowsMetadata)
      const chartOption = {
        color: chartColors,
        backgroundColor,
        grid: {
          containLabel: true,
          left: gridLeft,
          bottom: 0 as number | string,
          right: 0,
          top: 30,
        },
        legend: {
          orient: 'horizontal',
          type: 'scroll',
        },
        tooltip: {
          trigger: 'axis',
          formatter: function (params: any[]) {
            return getChartFormatter(params, metrics[0].formatTemplate)
          },
          axisPointer: {
            animation: false,
          },
          hideDelay: IS_H5 ? 0 : 300,
          enterable: true,
          confine: true,
        },

        xAxis: {
          type: 'category',
          data: data.firstDimValues,
          show: true,
          axisLabel: {
            formatter: formatXLableLength,
          },
          axisTick: {
            alignWithLabel: true,
          },
        },
        yAxis: {
          show: true,
          type: 'value',
          axisLabel: {
            formatter: (value: number) => formatNumber(value, yAxisLabelFormatTemplate),
          },
        },
        dataset: {
          // dimensions 会返回多个列，第一列为 x，非首列为 y。有的时候 y 会返回 string，这些列需要去掉
          source: rows,
        },
        series: [] as any[], // set in below
        dataZoom: null as any,
      }
      if (data.firstDimValues.length > DATAZOOM_SHOW_LIMIT) {
        chartOption.grid.bottom = '40%'
        chartOption.dataZoom = [
          {
            type: 'slider',
            xAxisIndex: 0, // 指定滚动条关联的x轴索引
            start: 0, // 初始显示数据的起始位置百分比
            end: (BAR_CHART_NUM / data.firstDimValues.length) * 100,
            width: '80%',
            height: 10,
          },
        ]
      }

      if (chartType === 'GroupColumnChart') {
        chartOption.series = data.secondDimValues.map((secondDimValue) => ({
          type: 'bar',
          barWidth: 10,
          smooth: true,
          name: secondDimValue,
          data: data.firstDimValues.map((firstDimValue) => {
            return data.details.get(secondDimValue)?.get(firstDimValue) ?? 0
          }),
        }))
      } else if (chartType === 'StackedColumnChart') {
        chartOption.series = data.secondDimValues.map((secondDimValue) => ({
          type: 'bar',
          stack: '总量', // 设置堆叠
          name: secondDimValue,
          data: data.firstDimValues.map((firstDimValue) => {
            return data.details.get(secondDimValue)?.get(firstDimValue) ?? 0
          }),
        }))
      } else if (chartType === 'MultiLineChart') {
        chartOption.series = data.secondDimValues.map((secondDimValue) => ({
          type: 'line',
          smooth: true,
          name: secondDimValue,
          data: data.firstDimValues.map((firstDimValue) => {
            return data.details.get(secondDimValue)?.get(firstDimValue) ?? 0
          }),
        }))
      }
      return chartOption
    }
    case 'RankBarChart': {
      return {
        dataset: {
          // dimensions 会返回多个列，第一列为 x，非首列为 y。有的时候 y 会返回 string，这些列需要去掉
          source: rows,
          rowsMetadata: rowsMetadata,
        },
      }
    }
    case 'AttrAnalysis':
    case 'AttrMetricAnalysis': {
      // TODO: 不支持
      return {}
    }
    default: {
      console.error('Unknown chart type', chartType)
      assertExhaustive(chartType)
    }
  }
}

const ChartWrapper = forwardRef(function Greeting(
  props: Props,
  ref: React.Ref<{
    downloadPNG: () => void
    copyPNG: () => void
    getBlob: () => Promise<BlobWithRatio | string> | undefined
  }>,
) {
  const chartRef = useRef<{
    downloadPNG: () => void
    copyPNG: () => void
    getBlob: () => Promise<BlobWithRatio | string>
  }>(null)
  const currentChartThemeType = useAtomValue(currentChartThemeTypeAtom)
  const theme = useAtomValue(themeAtom)
  const metricConfig = useAtomValue(metricConfigAtom)

  const rows = useMemo(() => {
    const type = ['ColumnChart', 'LineChart', 'GroupColumnChart', 'MultiLineChart']
    // 如果是宝武场景且需要过滤数据的图表, 就过滤一下数据
    if (isBaoWu(metricConfig?.metricTableName) && type.includes(props.data.chartType)) {
      const metricNames = props.data.queryParamsVerified?.queryParams.metricNames
      const result = props.data.rows.filter((item: any) => {
        return Object.keys(item).every((key) => {
          if (metricNames?.includes(key)) {
            // 正常查询的指标数据即使是0 也是'0',如果是0,则证明是前端后处理补0的,在图表中排除
            return item[key] !== 0 && item[key] !== null && item[key] !== ''
          }
          return true
        })
      })
      return result
    } else {
      return props.data.rows
    }
  }, [
    props.data.rows,
    props.data.queryParamsVerified?.queryParams.metricNames,
    props.data.chartType,
    metricConfig?.metricTableName,
  ])

  useImperativeHandle(ref, () => ({
    downloadPNG: () => {
      chartRef.current?.downloadPNG()
    },
    copyPNG: () => {
      chartRef.current?.copyPNG()
    },
    getBlob: () => {
      return chartRef.current?.getBlob()
    },
  }))

  const chartOption = useMemo(() => {
    const option = getChartOption(
      rows as OlapRow[],
      props.data.rowsMetadata,
      props.data.chartType,
      props.theme,
      props.data.chartThemeType || currentChartThemeType,
      props.data.queryParamsVerified,
    )
    return option
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [rows, props])

  // 确保rows是数组
  // [] [{}] 这两种情况也会走这个分支
  if (
    !Array.isArray(props.data.rows) ||
    props.data.rows.length === 0 ||
    (props.data.rows.length === 1 && Object.keys(props.data.rows[0]).length === 0)
  ) {
    return (
      <div>
        <p className="flex items-center gap-1">
          <InformationCircleIcon className="h-4 w-4" />
          对不起，当前查询条件下数据为空。
        </p>
        <p>可能是当前时间区间无数据，你可以问下其他时间区间的数据。</p>
      </div>
    )
  }

  if (props.data.chartType === 'AttrAnalysis') {
    return (
      <Suspense fallback={<div>Loading...</div>}>
        <AttrAnalysisChart ref={chartRef} data={props.data.rows[0] as AttrAnalysisResult} theme={theme} />
      </Suspense>
    )
  }

  if (props.data.chartType === 'AttrMetricAnalysis') {
    return (
      <Suspense fallback={<div>Loading...</div>}>
        <AttrMetricAnalysisChart ref={chartRef} data={props.data.rows[0] as AttrMetricAnalysisResult} theme={theme} />
      </Suspense>
    )
  }

  switch (props.data.chartType) {
    case 'Kpi':
      return <Kpi data={props.data} ref={chartRef} option={chartOption} />
    case 'ColumnChart':
    case 'LineChart':
    case 'PieChart':
    case 'ScatterChart':
    case 'TreemapChart':
    case 'GroupColumnChart':
    case 'StackedColumnChart':
    case 'MultiLineChart':
      return (
        <Suspense fallback={<div>Loading...</div>}>
          <EChartsReact ref={chartRef} option={chartOption} chartType={props.data.chartType} theme={props.theme} />
        </Suspense>
      )
    case 'SimpleTable':
      return (
        <SimpleTable
          ref={chartRef}
          option={chartOption}
          rowsMetadata={props.data.rowsMetadata}
          originRows={props.data.originRows as OlapRow[]}
          onTableDataChange={props.onTableDataChange}
        />
      )
    case 'RankBarChart':
      return <RankBarChart ref={chartRef} option={chartOption} data={props.data} />
    default:
      return assertExhaustive(props.data.chartType, '未支持的图表类型')
  }
})

export default React.memo(ChartWrapper)
