/**
 * @description RankBarChart 横向排行榜柱状图
 */
import React, { forwardRef, useImperativeHandle, useMemo, useRef, useState } from 'react'
import { useAtomValue } from 'jotai'
import { App, Pagination, Tooltip } from 'antd'
import clsx from 'clsx'
import html2canvas from 'html2canvas'
import { BlobWithRatio, OlapRow, RowsMetadata, isDimension, isMetric } from 'src/shared/common-types'
import { Metric } from 'src/shared/metric-types'
import { CHART_THEMES, IS_H5 } from 'src/shared/constants'
import { formatNumber, formatOrderBy, sortByOrderBy } from 'src/shared/common-utils'
import { currentChartThemeTypeAtom } from '../pages/AskBI/askBIAtoms'
import { generateBlobForCopy } from '../utils/generateBlobForCopy'
import { AssistantChartChatItem } from '../utils'

interface Props {
  option: {
    dataset: {
      source: OlapRow[]
      rowsMetadata: RowsMetadata
    }
  }
  data: AssistantChartChatItem
}

function RankBarChart(
  props: Props,
  ref: React.Ref<{
    downloadPNG: () => void
    copyPNG: () => void
    getBlob: () => Promise<BlobWithRatio | string | undefined>
  }>,
) {
  const [pageSize, setPageSize] = useState(10)
  const [currentPage, setCurrentPage] = useState(0)
  const { source, rowsMetadata } = props.option.dataset
  const { message } = App.useApp()
  const rankBarRef = useRef<HTMLDivElement>(null)
  const currentChartThemeType = useAtomValue(currentChartThemeTypeAtom)
  const colorList = CHART_THEMES.find((item) => {
    return item.type === currentChartThemeType
  })?.colors

  useImperativeHandle(ref, () => ({
    downloadPNG: () => {
      if (rankBarRef.current == null) {
        console.error('当前界面不存在此图表的dom元素!')
        return
      }
      html2canvas(rankBarRef.current).then((canvas: HTMLCanvasElement) => {
        const pngUrl = canvas.toDataURL('image/png')
        const link = document.createElement('a')
        link.href = pngUrl
        link.download = 'RankBar.png'
        link.click()
      })
    },
    copyPNG: async () => {
      if (rankBarRef.current == null) {
        console.error('当前界面不存在此图表的dom元素!')
        message.error('图表复制失败!')
        return
      }

      const pngItem = new ClipboardItem({
        'image/png': generateBlobForCopy(rankBarRef.current).then((blob) => blob),
      })

      navigator.clipboard
        .write([pngItem])
        .then(() => {
          message.success('图表复制成功')
        })
        .catch((error) => {
          message.error('图表复制失败!')
          console.error(error)
        })
    },
    getBlob: async () => {
      if (rankBarRef.current == null) {
        console.error('当前界面不存在此图表的dom元素!')
        message.error('导出报告失败')
        return
      }
      return await generateBlobForCopy(rankBarRef.current)
    },
  }))

  // 将维度 度量抽离出来
  const dimensions = rowsMetadata.filter(isDimension).map((item) => item.value)
  const metrics = rowsMetadata
    .filter((item) => isMetric(item) && item.value.type !== 'periodOverPeriod')
    .map((item) => item.value as Metric)

  const orderByList = formatOrderBy({
    queryParamsVerified: props.data.queryParamsVerified,
    defaultValue: {
      metricName: metrics[0].name,
      orderBy: 'desc',
    },
  })
  const sortedData = sortByOrderBy({
    rows: source,
    orderByList,
  })
  // 根据第一个度量筛选出最大值
  const maxValueList = useMemo(() => {
    return metrics.map((item) => {
      return Math.max(...sortedData.map((v) => v[item.name]))
    })
  }, [metrics, sortedData])
  // 满足条件后渲染下面的排行榜
  const showChart = sortedData && sortedData.length > 0
  const gridTemplateColumns = `repeat(${dimensions.length}, 3fr) repeat(${metrics.length}, 1fr)`
  return (
    <div className="h-full w-full">
      <div
        className="mt-2 flex w-full max-w-[780px] flex-col overflow-auto bg-white dark:bg-slate-700"
        ref={rankBarRef}
      >
        <div className="flex w-full flex-col gap-[8px]">
          <div className="grid w-fit min-w-full gap-[4px] border-b border-dashed" style={{ gridTemplateColumns }}>
            {dimensions.map((item, index) => {
              return (
                <div key={index} className={clsx('min-w-[140px] font-bold')}>
                  <Tooltip placement="top" title={item.label}>
                    <p className="overflow-hidden truncate whitespace-nowrap py-2">{item.label}</p>
                  </Tooltip>
                </div>
              )
            })}
            {metrics.map((item, index) => {
              return (
                <div key={index} className={clsx('min-w-[100px] font-bold')}>
                  <Tooltip placement="top" title={item.label}>
                    <p className="overflow-hidden truncate whitespace-nowrap py-2">{item.label}</p>
                  </Tooltip>
                </div>
              )
            })}
            <div />
          </div>
          {showChart &&
            sortedData.slice(currentPage * pageSize, (currentPage + 1) * pageSize).map((item, i) => {
              return (
                <div
                  key={i}
                  className="grid w-fit min-w-full gap-[4px] border-b border-dashed pb-[8px]"
                  style={{ gridTemplateColumns }}
                >
                  {dimensions.map((dimensionsItem, j) => {
                    return (
                      <div key={`dimension-${i}-${j}`} className="min-w-[140px] self-center">
                        <Tooltip placement="top" title={item[dimensionsItem.name]}>
                          {item[dimensionsItem.name]}
                        </Tooltip>
                      </div>
                    )
                  })}
                  {metrics.map((metric, j) => {
                    return (
                      <div
                        key={`metric-${i}-${j}`}
                        className="flex min-w-[100px] flex-col justify-start self-center font-mono"
                      >
                        <div>{formatNumber(item[metric.name], metric.formatTemplate)}</div>
                        {maxValueList[j] > 0 && (
                          <div>
                            <div
                              className="relative h-[4px] w-full max-w-full flex-1 self-center rounded bg-gray-200"
                              key={`CHART-${i}-${j}`}
                            >
                              <div
                                className="absolute h-[4px] rounded-[4px]"
                                style={{
                                  width: `${Math.floor(Math.abs((Math.max(item[metric.name], 0) / maxValueList[j]) * 100))}%`,
                                  backgroundColor: colorList && colorList.length > 0 ? `${colorList[0]}` : '#5470c6',
                                }}
                              />
                            </div>
                          </div>
                        )}
                      </div>
                    )
                  })}
                </div>
              )
            })}
        </div>
      </div>
      <Pagination
        showLessItems={IS_H5}
        className="mt-[8px] flex w-full justify-center"
        pageSize={pageSize}
        current={currentPage + 1}
        onChange={(page, pageSize) => {
          setCurrentPage(page - 1)
          setPageSize(pageSize)
        }}
        total={sortedData.length}
      />
    </div>
  )
}

export default forwardRef(RankBarChart)
