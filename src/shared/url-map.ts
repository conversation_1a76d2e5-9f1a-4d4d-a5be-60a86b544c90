/**
 * @description 所有产品的 URL 地址的映射表
 */

import { chatPath, XEngineHomePath } from './constants'
import { concatBaseUrl } from './common-utils'

/**
 * Ask BI 相关
 */
export const ASK_BI_BASE = ''
/** Ask BI 页面地址汇总 */
export const askBIPageUrlsWithoutBaseUrl = {
  home: `${ASK_BI_BASE}${chatPath}`,
  chatNew: `${ASK_BI_BASE}/chat/new`,
  chatDetail: (conversationId: string) => `${ASK_BI_BASE}/chat/${conversationId}`,
  scenarios: {
    manage: '/manage/scenario',
    create: '/scenario/create',
    detail: `/manage/scenario/detail`,
  },
  metricStore: {
    dimensions: {
      overview: '/metric-store/dimensions/overview',
      manage: '/metric-store/dimensions/manage',
      create: '/metric-store/dimensions/create',
    },
    measures: {
      manage: '/metric-store/measures/manage',
      create: '/metric-store/measures/create',
    },
    metrics: {
      overview: '/metric-store/metrics/overview',
      detail: (modelId: string, metricName: string) => `/metric-store/metrics/detail/${modelId}/${metricName}`,
      manage: '/metric-store/metrics/manage',
      create: '/metric-store/metrics/create',
    },
    metricTree: {
      overview: '/metric-store/metric-tree/overview',
      detail: (projectId: string, treeId: string) => `/metric-store/metric-tree/detail/${projectId}/${treeId}`,
    },
    charts: {
      list: `/metric-store/charts/list`,
      embed: (chartId: string) => `/metric-store/charts/embed/${chartId}`,
      edit: (chartId: string) => `/metric-store/charts/edit/${chartId}`,
      copy: (chartId: string) => `/metric-store/charts/copy/${chartId}`,
      detail: (chartId: string) => `/metric-store/charts/detail/${chartId}`,
    },
    document: {
      list: '/metric-store/document/list',
    },
    askHistory: {
      overview: '/metric-store/ask-history/overview',
    },
    hint: {
      overview: '/metric-store/hint/overview',
    },
    smartReport: {
      templateManagement: '/metric-store/smart-report/template',
      reportManagement: '/metric-store/smart-report/report',
      reportGeneration: '/metric-store/smart-report/template/report-generation',
      markingTaskManagement: '/metric-store/smart-report/marking-task',
    },
    externalReport: {
      management: '/metric-store/external-report/management',
    },
  },
  manage: {
    roles: '/manage/admin/roles',
    scenarios: {
      manage: '/manage/scenario',
      create: '/scenario/create',
    },
    users: '/manage/admin/users',
    xengine: {
      dataSceneList: '/manage/data-scene',
      dataSceneDetail: '/manage/operation/data-scene/detail',
      sqlQueryEditor: '/manage/sql-query/sql-query-editor',
      virtualTable: '/manage/data-model/virtual-table',
      businessDataModel: '/manage/data-model/business-data-model',
      catalogManager: '/manage/data-model/catalog-manager',
      virtualTableMaterializationRecommend: '/manage/data-model/virtual-table-materialization-recommend',
      materialViewList: '/manage/materialization/material-view-list',
      materiaViewDetail: '/manage/materialization/material-view-list/material-view-detail',
      jobList: '/manage/materialization/job-list',
      materialViewScan: '/manage/materialization/material-view-scan',
      queryRelatedMv: '/manage/er-management/query-related-mv',
      erManager: '/manage/er-management/er-manager',
      erQuery: '/manage/er-management/er-query',
      logFileList: '/manage/operation/advance/log-file-list',
      toolsBox: '/manage/operation/advance/tools-box',
      dataComparison: '/manage/operation/advance/data-comparison',
      sqlHistory: '/manage/sql-query/sql-history',
      sqlQueryManage: '/manage/sql-query/sql-query-manage',
      streamJobManage: '/manage/operation/advance/stream-job-manage',
      dataModel: {
        businessVirtualTableDetail: '/manage/data-model/business-data-model/business-virtual-table-detail',
        materialViewDetail: '/manage/materialization/material-view-list/material-view-detail',
        createFileTable: '/manage/data-model/virtual-table/create-file-table',
      },
      tenant: {
        selectAllTenants: '/manage/tenant/tenant-management',
      },
      fileDatasource: '/manage/file-datasource',
    },
    permissions: '/manage/admin/permissions',
    datasources: '/manage/admin/datasources',
    systemInfo: '/manage/admin/system-info',
    testOverview: '/manage/admin/test-overview',
    executeSql: '/manage/admin/execute-sql',
    project: '/manage/project/metadata/project',
    sync: '/manage/project/metadata/sync',
    metricModel: {
      list: '/manage/metric-model/list',
      query: '/manage/metric-model/query',
    },
    externalDatasource: {
      catalogList: XEngineHomePath,
      tableList: '/manage/external-datasource/table-list',
      tableDetail: '/manage/external-datasource/table-list/table-detail',
      catalogUpdate: '/manage/external-datasource/catalog-list/catalog-update',
      batchInfo: '/manage/external-datasource/batch-info',
    },
    manageProject: {
      list: '/manage/project/list',
      detail: '/manage/project/detail',
    },
    cluster: {
      list: '/manage/operation/cluster',
    },
    admin: {
      user: '/manage/admin/user',
      role: '/manage/admin/role',
      group: '/manage/admin/group',
      resource: '/manage/admin/resource',
    },
    system: {
      appearance: '/manage/system/appearance-setting',
    },
  },
  dashboard: {
    info: '/dashboard/info',
    codeStatistics: '/dashboard/code-statistics',
    codeOverview: '/dashboard/code-overview',
  },
  login: `${ASK_BI_BASE}/login`,
  loginError: `${ASK_BI_BASE}/login-error`,
} as const

export const askBIPageUrls = concatBaseUrl(askBIPageUrlsWithoutBaseUrl)

/** Ask BI API 地址汇总 */
export const askBIApiUrlsWithoutBaseUrl = {
  home: `${ASK_BI_BASE}/`,
  chat: '/api/chats/chat',
  chitchat: '/api/chats/chitchat',
  resultAnalysis: '/api/chats/result-analysis',
  condense: '/api/chats/condense',
  chatNoCheck: '/api/chats/chat-nocheck',
  chatMultiScenes: '/api/chats/chatMultiScenes',
  metricQuery: '/api/chats/metric-query',
  queryMetric: '/api/chats/query-metric',
  suggestions: '/api/chats/suggestions',
  docReport: '/api/chats/docReport',
  chatProgress: '/api/chats/chat-progress',
  chatProgressCallback: '/api/chats/chat-progress-callback',
  chartInsightStream: '/api/chats/insight-streaming',
  attrInsightStream: '/api/chats/attr-analysis-insight-streaming',
  chartTooltip: '/api/charts/tooltip',
  users: {
    list: '/api/users',
    create: '/api/users/',
    detail: (userId: string) => `/api/users/${userId}`,
    update: (userId: string) => `/api/users/${userId}`,
    delete: (userId: string) => `/api/users/${userId}`,
  },

  roles: {
    list: '/api/roles',
    create: '/api/roles/',
    detail: (roleId: string) => `/api/roles/${roleId}`,
    update: (roleId: string) => `/api/roles/${roleId}`,
    delete: (roleId: string) => `/api/roles/${roleId}`,
  },
  llms: {
    all: '/api/llms/',
    listByUsername: '/api/llms/list-by-username',
    byType: (type: string) => `/api/llms/${type}`,
  },
  metrics: {
    list: `/api/metrics/list/`,
    listInScene: (sceneId: string) => `/api/metrics/list/${sceneId}`,
    listInProject: (projectId: string) => `/api/metrics/list/project/${projectId}`,
    getAllDimensionsValues: '/api/metrics/getAllDimensionsValues',
    recommendQuestion: '/api/metrics/recommend-question',
    create: '/api/metrics/',
    createFromFile: `/api/metrics/upload`,
    updateFromFile: `/api/metrics/upload/update`,
    update: (metricId: string) => `/api/metrics/${metricId}`,
    detail: (metricId: string) => `/api/metrics/detail/${metricId}`,
    detailWithTime: (sceneId: string, metricName: string) => `/api/metrics/detail/time/${sceneId}/${metricName}`,
    delete: (metricId: string) => `/api/metrics/${metricId}`,
    batchDelete: `/api/metrics/batch`,
    metric2sql2data: '/api/metrics/metric2sql2data',
    metric2sql2dataAttr: '/api/metrics/metric2sql2data-attr',
    trendInScene: (sceneId: string, metricName: string) => `/api/metrics/trend/${sceneId}/${metricName}`,
    deleteByScene: (sceneId: string) => `/api/metrics/scene/${sceneId}`,
    deleteByProject: (projectId: string) => `/api/metrics/project/${projectId}`,
    listPublishedMetrics: `/api/engine/v1/metricmodel/listPublishedMetrics`,
    publishMetrics: `/api/engine/v1/metricmodel/publishMetrics`,
    offlineMetrics: `/api/engine/v1/metricmodel/offlineMetrics`,
    getMeasuresDep: `/api/metrics/measuresDep`,
    downloadSceneMetric: `/api/metrics/download`,
    creator: '/api/metrics/creator',
  },
  stream: {
    VTStreamTask: '/api/engine/v1/streamTask/vt',
    metricsStreamTask: '/api/engine/v1/streamTask/metrics',
    metricsStreamTaskForFE: '/api/streamTask/metrics',
    pauseOrResumeStreamTask: (action: string) => `/api/engine/v1/metricmodel/batch/${action}`,
    pauseVTableStreamTask: (action: string) => `/api/engine/v1/vtable/${action}`,
    getModelName: '/api/streamTask/metrics/modelName',
  },
  model: {
    list: '/api/engine/v1/metricmodel/listMeta',
    meta: '/api/engine/v1/metricmodel/meta',
    updateColumnCodeValues: '/api/engine/v1/metricmodel/updateColumnCodeValues',
    updateSynonyms: '/api/engine/v1/metricmodel/updateSynonyms',
    delete: '/api/engine/v1/metricmodel/bi/delete',
    jsonUpload: '/api/engine/v1/metricmodel/bi/json',
    csvUpload: '/api/engine/v1/metricmodel/bi/csv',
    simpleCSVUpload: '/api/engine/v1/metricmodel/csv',
    listColumnCodeValues: '/api/engine/v1/metricmodel/listColumnCodeValues',
    CSVDownloadAllColumnCodeValues: '/api/engine/v1/metricmodel/export/allColumnCodeValues',
    DownloadAllColumnCodeValues: '/api/engine/v1/metricmodel/listAllColumnCodeValues',
    batchUpdateColumnCodeValueSynonyms: '/api/engine/v1/metricmodel/import/batchUpdateColumnCodeValueSynonyms',
    updateColumnCodeValueSynonyms: '/api/engine/v1/metricmodel/updateColumnCodeValueSynonyms',
    hasPublishedMetrics: `/api/engine/v1/metricmodel/hasPublishedMetrics`,
    CSVUploadTemplate: '/api/engine/v1/tool/export/template',
    listAvailableMaterializeTimeColumn: '/api/engine/v1/metricmodel/listAvailableMaterializeTimeColumn',
    getMvIncrementGranularityList: '/api/engine/v1/metricmodel/getMvIncrementGranularityList',
    getSupportedQueryTimePrecision: '/api/engine/v1/metricmodel/getSupportedQueryTimePrecision',
    simpleModelExport: '/api/engine/v1/metricmodel/export/simple/model',
    modelExport: '/api/engine/v1/metricmodel/export/bi/model',
  },
  scenarios: {
    list: '/api/operation/scenes',
    create: '/api/operation/scenes',
    delete: (id: string) => `/api/operation/scenes/${id}`,
    update: (id: string) => `/api/operation/scenes/${id}`,
    detail: (id: string) => `/api/operation/scenes/${id}`,
  },
  tableColumn: {
    list: '/api/table-meta/column',
  },
  measure: {
    listInScene: (sceneId: string) => `/api/measures/list/${sceneId}`,
    create: '/api/measures/',
    createBatch: '/api/measures/batch',
    update: (measureId: string) => `/api/measures/${measureId}`,
    detail: (measureId: string) => `/api/measures/${measureId}`,
    delete: (measureId: string) => `/api/measures/${measureId}`,
  },
  dimension: {
    listInScene: (sceneId: string) => `/api/dimensions/list/${sceneId}`,
    create: '/api/dimensions/',
    createBatch: '/api/dimensions/batch',
    update: (dimensionId: string) => `/api/dimensions/${dimensionId}`,
    detail: (dimensionId: string) => `/api/dimensions/${dimensionId}`,
    delete: (dimensionId: string) => `/api/dimensions/${dimensionId}`,
    trend: (tableName: string, dimensionName: string) => `/api/dimensions/trend/${tableName}/${dimensionName}`,
  },
  externalReport: {
    create: '/api/external-reports/',
    createFromFile: '/api/external-reports/upload',
    updateFromFile: '/api/external-reports/upload/update',
    delete: (externalReportId: string) => `/api/external-reports/${externalReportId}`,
    deleteBatch: `/api/external-reports/batch`,
    update: (externalReportId: string) => `/api/external-reports/${externalReportId}`,
    list: (externalReportId: string) => `/api/external-reports/${externalReportId}`,
    baowu: '/api/external-reports/baowu-report/detail',
  },
  hint: {
    create: '/api/hints/',
    update: (hintId: string) => `/api/hints/${hintId}`,
    delete: '/api/hints/batch',
    listBySceneId: (projectId: string, sceneId: string) => `/api/hints/list/scene/${projectId}/${sceneId}`,
    listByProjectId: (projectId: string) => `/api/hints/list/project/${projectId}`,
    downloadExample: '/api/hints/download-example',
    createFromFile: '/api/hints/upload',
    downloadFile: '/api/hints/download',
  },
  datasets: {
    sampleData: (sceneId: string) => `/api/datasets/sample-data/${sceneId}`,
    defaultDatasetInfo: '/api/datasets/default',
    // TODO 确认使用
    tableList: '/api/datasets/table/list',
    tableColumns: '/api/datasets/table/columns',
    tableListByUsername: '/api/datasets/table/list-by-username',
  },
  charts: {
    list: '/api/charts',
    detail: (chartId: string) => `/api/charts/${chartId}`,
    create: '/api/charts/',
    update: (chartId: string) => `/api/charts/${chartId}`,
    delete: (chartId: string) => `/api/charts/${chartId}`,
  },
  datasources: {
    list: '/api/datasources',
    detail: (datasourceId: string) => `/api/datasources/${datasourceId}`,
    create: '/api/datasources/',
    update: (datasourceId: string) => `/api/datasources/${datasourceId}`,
    delete: (datasourceId: string) => `/api/datasources/${datasourceId}`,
    testMysqlConnection: '/api/datasources/test-mysql-connection',
  },
  token: {
    getToken: '/api/engine/v1/log/getToken',
  },
  login: {
    nodeLogin: '/api/auth/login',
    logout: '/api/sso/logout',
    requestSignature: '/api/sso/request-signature',
    appLogin: '/api/sso/app-login',
    currentUserBasicInfo: '/api/sso/current-user-basic-info',
    tianhongLogin: '/api/sso/tianhong-login',
    checkToken: '/api/sso/check-token',
    getAuthToken: '/api/sso/getToken',
    checkTokenFromUrl: '/api/sso/checkUrlToken',
  },
  agent: {
    chat: '/api/agent/chat',
    metricQuery: '/api/agent/metric-query',
    cotStream: '/api/agent/cot-stream',
    readCsv: '/api/agent/read-csv',
    readImage: '/api/agent/read-image',
    downloadFile: '/api/agent/download-file',
    metricDetail: `/api/agent/metric-detail`,
    afterMatch: `/api/agent/after-match`,
  },
  auth: {
    union: '/api/auth/union',
    login: '/api/auth/login',
    logout: '/api/auth/logout',
    userInfo: '/api/auth/user-info',
    projects: '/api/auth/projects',
    llms: '/api/auth/llms',
    metrics: '/api/auth/metrics',
    enforce: '/api/auth/enforce',
    scene: {
      list: '/api/auth/scene/list',
      rest: '/api/auth/scene',
    },
    project: {
      list: '/api/auth/project/list',
      rest: '/api/auth/project',
    },
    admin: {
      user: {
        rest: '/api/auth/admin/user',
        update: '/api/auth/admin/user/update',
        list: '/api/auth/admin/user/list',
      },
      role: {
        rest: '/api/auth/admin/role',
        update: '/api/auth/admin/role/update',
        list: '/api/auth/admin/role/list',
      },
      group: {
        rest: '/api/auth/admin/group',
        update: '/api/auth/admin/group/update',
        list: '/api/auth/admin/group/list',
      },
      resource: {
        rest: '/api/auth/admin/resource',
        list: '/api/auth/admin/resource/list',
        data: '/api/auth/admin/resource/data',
      },
    },
    resource: {
      rest: '/api/auth/resource',
    },
  },
  env: {
    list: '/api/env/list',
    paramsExtractUrlList: '/api/env/get-params-extract-url',
    getEnvTag: '/api/env/get-tag',
  },
  diProxyLogin: {
    namespace: '/api/ditest/get-kube-namespace',
    service: '/api/ditest/get-kube-service',
    pod: '/api/ditest/get-kube-pod',
    diDefaultProxy: '/api/ditest/get-default-proxy',
  },
  checkContainer: {
    checkContainerStatus: '/api/ditest/check-containers-status',
    checkK8sStatus: '/api/ditest/check-k8s-status',
    operationContainers: (pk: number) => `/api/ditest/operation-containers/${pk}`,
  },
  permission: {
    pageAccess: '/api/auth/service/',
    datasource: {
      detail: (datasourceId: string) => `/api/permission/datasource/${datasourceId}`,
      create: '/api/permission/datasource',
      delete: (permissionDatasourceId: string) => `/api/permission/datasource/${permissionDatasourceId}`,
    },
    llm: {
      detail: (llmId: string) => `/api/permission/llm/${llmId}`,
      create: '/api/permission/llm',
      delete: (permissionLlmId: string) => `/api/permission/llm/${permissionLlmId}`,
    },
    project: {
      detail: (projectId: string) => `/api/permission/project/${projectId}`,
      create: '/api/permission/project',
      delete: (permissionProjectId: string) => `/api/permission/project/${permissionProjectId}`,
    },
  },
  rolesPermission: {
    pageList: '/api/roles-permission/page-list/list',
    datasource: {
      detail: (datasourceId: string) => `/api/roles-permission/datasource/${datasourceId}`,
      create: '/api/roles-permission/datasource',
      delete: (permissionDatasourceId: string) => `/api/roles-permission/datasource/${permissionDatasourceId}`,
    },
    llm: {
      detail: (llmId: string) => `/api/roles-permission/llm/${llmId}`,
      create: '/api/roles-permission/llm',
      delete: (permissionLlmId: string) => `/api/roles-permission/llm/${permissionLlmId}`,
    },
    project: {
      detail: (projectId: string) => `/api/roles-permission/project/${projectId}`,
      create: '/api/roles-permission/project',
      delete: (permissionProjectId: string) => `/api/roles-permission/project/${permissionProjectId}`,
    },
  },
  systemInfo: {
    status: '/api/system-info/status',
    clearCache: '/api/system-info/clear-cache',
  },
  convers: {
    list: '/api/convers/list',
    upsert: `/api/convers/upsert`,
    upsertNext: `/api/convers/upsert-next`,
    askHistory: `/api/convers/ask-history`,
    delete: (conversationId: string) => `/api/convers/${conversationId}`,
    update: (conversationId: string) => `/api/convers/${conversationId}`,
    updateAll: (sceneId: string) => `/api/convers/update/all-conversations/${sceneId}`,
  },
  converChats: {
    listByConversationId: (conversationId: string) => `/api/conver-chats/${conversationId}`,
    askHistory: `/api/conver-chats/history-list`,
    users: `/api/convers/users`,
    dataFeedback: `/api/conver-chats/data-feedback`,
    updateChat: '/api/conver-chats/update-chat',
    upsertChat: '/api/conver-chats/upsert-chat',
    updateMultiAgentChat: '/api/conver-chats/update-multi-agent-chat',
    updateDocChat: '/api/conver-chats/update-doc-chat',
    getLogFromFile: (traceId: string) => `/api/conver-chats//log-from-file/${traceId}`,
    stopChat: '/api/conver-chats/stop-chat',
  },
  semanticModel: {
    detail: (semanticModelId: string) => `/api/semantic-model/${semanticModelId}`,
    update: (semanticModelId: string) => `/api/semantic-model/${semanticModelId}`,
    delete: (semanticModelId: string) => `/api/semantic-model/${semanticModelId}`,
    measuresList: '/api/engine/v1/metricmodel/listMeasureFunction',
    create: '/api/engine/v1/metricmodel/bi',
    modelUpdate: '/api/engine/v1/metricmodel/bi/update',
    listTimePatterns: '/api/engine/v1/metricmodel/listTimePattern',
  },
  semanticMetricTree: {
    create: '/api/semantic-metric-tree/',
    list: (projectId: string) => `/api/semantic-metric-tree/${projectId}`,
    detail: (projectId: string, treeId: string) => `/api/semantic-metric-tree/${projectId}/${treeId}`,
    update: (projectId: string, treeId: string) => `/api/semantic-metric-tree/${projectId}/${treeId}`,
    delete: (projectId: string, treeId: string) => `/api/semantic-metric-tree/${projectId}/${treeId}`,
  },
  semanticProject: {
    list: '/api/semantic-project/list',
    create: '/api/semantic-project',
    update: (projectId: string) => `/api/semantic-project/${projectId}`,
    delete: (projectId: string) => `/api/semantic-project/${projectId}`,
    infoWithTableMeta: '/api/semantic-project/infoWithTableMeta',
    listAllMetrics: (projectId: string) => `/api/semantic-project/listAllMetrics/${projectId}`,
    listAllMetricsWithDisplayValue: (projectId: string) =>
      `/api/semantic-project/listAllMetricsWithDisplayValue/${projectId}`,
    metricDisplayValue: (projectId: string, metricName: string) =>
      `/api/semantic-project/metricDisplayValue/${projectId}/${metricName}`,
  },
  private: {
    executeSql: '/api/execute-sql',
    testOverview: '/api/test-tasks/overview',
    importTestData: '/api/test-tasks/import-data',
  },
  xengineDatasource: {
    add: `/api/engine/v1/datasource/add`,
    catalogList: `/api/engine/v1/datasource/catalogList`,
    tableList: `/api/engine/v1/datasource/tableList`,
    del: `/api/engine/v1/datasource/del`,
    edit: `/api/engine/v1/datasource/edit`,
    testing: `/api/engine/v1/datasource/testing`,
    tableListDetail: `/api/engine/v1/datasource/tableListDetail`,
    createVirtualTable: `/api/engine/v1/datasource/createVirtualTable`,
    getPrincipalList: `/api/engine/v1/datasource/getPrincipalList`,
    tableInfo: `/api/engine/v1/datasource/tableInfo`,
    previewData: `/api/engine/v1/datasource/previewData`,
    saveHiveDDLInFile: `/api/engine/v1/datasource/saveHiveDDLInFile`,
    getCatalogId: `/api/engine/v1/datasource/getCatalogId`,
    databaseList: `/api/engine/v1/datasource/databaseList`,
    partitions: `/api/engine/v1/datasource/partitions`,
    tableListForEditor: `/api/engine/v1/datasource/tableListForEditor`,
    relateMvs: `/api/engine/v1/datasource/relateMvs`,
    listNames: `/api/engine/v1/datasource/listNames`,
    detail: `/api/engine/v1/datasource/detail`,
    executeSql: '/api/engine/v1/sql/query',
  },
  xengine: {
    outputTarget: {
      list: '/api/engine/v1/metricmodel/listOutputTarget',
    },
    codeTemplate: {
      list: '/api/engine/v1/codeTemplate',
      create: '/api/engine/v1/codeTemplate',
    },
    catalogList: '/api/engine/v1/catalog/list',
    databaseList: '/api/engine/v1/database/list',
    vTableDetail: '/api/engine/v1/vtable/detail',
    vTableList: '/api/engine/v1/vtable/search',
    vTableCreator: '/api/engine/v1/vtable/creator',
    publisher: (type: 'VT' | 'METRICS') => `/api/engine/v1/streamTask/publisher/${type}`,
    uploadCSV: '/api/engine/v1/csv',
    inferColumnType: '/api/engine/v1/csv/inferColumnType',
    columnType: '/api/engine/v1/ptable/columnType',
    deleteVTable: '/api/engine/v1/vtable/delete',
    publishStreamVtable: '/api/engine/v1/vtable/publishStreamVtable',
    businessVTablePublishInfo: '/api/engine/v1/vtable/getPublishInfo',
    ptable: {
      parseColumns: '/api/engine/v1/ptable/parseColumns',
      createPTable: '/api/engine/v1/ptable',
      delete: '/api/engine/v1/ptable/delete',
      columnType: '/api/engine/v1/ptable/columnType',
      updateComment: '/api/engine/v1/ptable',
      list: '/api/engine/v1/ptable/list',
    },
    diagnosis: {
      startTask: '/api/engine/v1/diagnosis/task',
      cancelTask: '/api/engine/v1/diagnosis/cancel_task',
      status: '/api/engine/v1/diagnosis/status',
      log: '/api/engine/v1/diagnosis/log',
      componentList: '/api/engine/v1/diagnosis/component_list',
      taskList: '/api/engine/v1/diagnosis/task_list',
    },
    tenant: {
      getTenantState: '/api/engine/v1/tenant/state',
      list: '/api/engine/v1/tenant/listTenants',
      create: '/api/engine/v1/tenant/createTenant',
      update: '/api/engine/v1/tenant/updateTenant',
      delete: '/api/engine/v1/tenant/dropTenant',
      restore: '/api/engine/v1/tenant/resumeTenant',
      addProject: '/api/engine/v1/tenant/addProject',
      addUserToProject: '/api/engine/v1/tenant/addUserToProject',
      getProjects: '/api/engine/v1/tenant/getTenant',
      deleteProject: '/api/engine/v1/tenant/removeProject',
      updateProject: '/api/engine/v1/tenant/updateProject',
      // getAllUsers: '/api/engine/auth/list',
      getAllUsers: '/api/engine/auth/listUserNames',
      getUserTenants: '/api/engine/v1/tenant/listUserTenants',
    },
    smart: {
      publishModelJson: '/api/engine/v1/mv/publishModelJson',
      queryByModelJson: '/api/engine/v1/mv/queryByModelJson',
      erQuery: {
        listProjects: '/api/engine/v1/mv/listProjects',
        searchErQueryHistories: '/api/engine/v1/mv/searchErQueryHistories',
        queryByModelJson: '/api/engine/v1/mv/queryByModelJson',
      },
      xengineDatasource: {
        recommendationProject: '/api/engine/v1/mv/recommendation/config/project',
        recommendationModel: '/api/engine/v1/mv/recommendation/config/model',
        setRecommendationProject: '/api/engine/v1/mv/recommendation/config/project',
        setRecommendationModel: '/api/engine/v1/mv/recommendation/config/model',
        dailyStatistics: '/api/engine/v1/mv/recommendation/dailyStatistics',
        recommendationList: '/api/engine/v1/mv/recommendation/recommendations/new',
        acceptedList: '/api/engine/v1/mv/recommendation/recommendations/accepted',
        acceptedActive: '/api/engine/v1/mv/recommendation/recommendations/accept',
        getProjectName: '/api/engine/v1/mv/listProjects',
        getModelName: '/api/engine/v1/mv/listModels',
        getTaskProgress: '/api/engine/v1/mv/recommendation/taskProgress',
      },
    },
    dataScene: {
      dataSceneList: '/api/engine/v1/dataScene/page',
      listProjects: '/api/engine/v1/dataScene/listProjects',
      createProject: '/api/engine/v1/dataScene/createProject',
      listTypes: '/api/engine/v1/dataScene/listTypes',
      createType: '/api/engine/v1/dataScene/createType',
      create: '/api/engine/v1/dataScene/create',
      delete: '/api/engine/v1/dataScene/delete',
      canvasDetail: '/api/engine/v1/dataScene/canvas/info',
      canvasEdit: '/api/engine/v1/dataScene/canvas/edit',
      editByCsv: '/api/engine/v1/dataScene/canvas/editByCsv',
      update: '/api/engine/v1/dataScene/update',
      deleteProject: '/api/engine/v1/dataScene/deleteProject',
      deleteType: '/api/engine/v1/dataScene/deleteType',
    },
    metricModel: {
      list: '/api/engine/v1/metricmodel/list',
      operateStreamTask: (action: string) => `/api/engine/v1/metricmodel/${action}`,
      batchOperateStreamTask: (action: string) => `/api/engine/v1/metricmodel/batch/${action}`,
      listAvailableMaterializeTimeColumn: '/api/engine/v1/metricmodel/listAvailableMaterializeTimeColumn',
    },
    metric: {
      createRelateMv: '/api/engine/v1/metrics/createRelateMv',
    },
    VTable: {
      createBatchVT: '/api/engine/v1/vtable/createBatchVT',
      offlineStreamVtable: '/api/engine/v1/vtable/offlineStreamVtable',
      createErModelVtableByCsv: '/api/engine/v1/vtable/createErModelVtableByCsv',
      updateComment: '/api/engine/v1/vtable/update',
      operateStreamTask: (action: string) => `/api/engine/v1/vtable/${action}`,
      search: '/api/engine/v1/vtable/search',
      materializeDeduceLineage: '/api/engine/v1/vtable/materializeDeduceLineage',
      createMv: '/api/engine/v1/vtable/createMv',
      createLikeTable: '/api/engine/v1/vtable/createLikeTable',
      incrementColumns: '/api/engine/v1/vtable/incrementColumns',
    },
    timeGranularity: {
      list: '/api/engine/v1/metricmodel/listTimeUnit',
    },
    sql: {
      audit: '/api/engine/v1/sql/audit',
      explain: '/api/engine/v1/sql/audit/explain',
    },
    mv: {
      list: '/api/engine/v1/mv/list',
      dropMv: '/api/engine/v1/mv/drop',
    },
    datasource: {
      listKafkaSecurityProtocol: '/api/engine/v1/datasource/listKafkaSecurityProtocol',
      listKafkaSASLMechanism: '/api/engine/v1/datasource/listKafkaSASLMechanism',
      listFileSystemSourceType: '/api/engine/v1/datasource/listFileSystemSourceType',
      listFileSystemTableType: '/api/engine/v1/datasource/listFileSystemTableType',
      listFileSystemFormat: '/api/engine/v1/datasource/listFileSystemFormat',
      listCharsets: '/api/engine/v1/ptable/listCharsets',
      listKafkaTopics: '/api/engine/v1/datasource/listKafkaTopics',
    },
    checker: {
      getAllPlans: '/api/engine/v1/checker/getAllPlans',
      createPlan: '/api/engine/v1/checker/createPlan',
      removePlan: '/api/engine/v1/checker/removePlan',
      createPlanJob: '/api/engine/v1/checker/createPlanJob',
      getPlanJobs: '/api/engine/v1/checker/getPlanJobs',
      deleteJob: '/api/engine/v1/checker/removeJob',
      pausePlan: '/api/engine/v1/checker/pausePlan',
      resumePlan: '/api/engine/v1/checker/resumePlan',
    },
    dataMasking: {
      createDataMasking: '/api/engine/v1/vtable/mask',
      getDataMasking: '/api/engine/v1/vtable/mask',
      updateMasking: '/api/engine/v1/vtable/mask',
      deleteMasking: '/api/engine/v1/vtable/mask',
      getMaskType: '/api/engine/v1/vtable/mask/type',
    },
    scheduler: {
      rerunTask: '/api/engine/v1/scheduler/rerunTask',
    },
    materializedRecommendation: {
      materializationRule: '/api/engine/v1/recommend/config/rvt',
      updateMaterializationRule: '/api/engine/v1/recommend/config',
      recommendSql: '/api/engine/v1/recommend/virtualTable',
      recommendTable: '/api/engine/v1/recommend/toMaterializedView',
      getSqlColumn: '/api/engine/v1/sql/columnNames',
      materializationSql: '/api/engine/v1/recommend/virtualTable',
      relativeVTables: '/api/engine/v1/sql/relativeVTables',
      materializationRulesToUpdate: '/api/engine/v1/recommend/manyConfigs',
    },
  },

  xengineSql: {
    queryHistory: '/api/engine/v1/sql/queryHistory',
  },
  xengineScenario: {
    getLineage: '/api/engine/v1/scenario/getLineage',
  },
  xengineVTable: {
    unionType: '/api/engine/v1/vtable/unionType',
    compareOperator: '/api/engine/v1/vtable/compareOperator',
    create: '/api/engine/v1/vtable',
    getStreamVtableModel: '/api/engine/v1/vtable/getStreamVtableModel',
  },
  cluster: {
    list: '/api/cluster/list',
    transferAsync: '/api/cluster/transferAsync',
  },
  project: {
    // TODO: 确认使用
    list: '/api/operation/project',
    create: '/api/operation/project',
    delete: (id: string) => `/api/operation/project/${id}`,
    update: (id: string) => `/api/operation/project/${id}`,
    detail: (id: string) => `/api/operation/project/${id}`,
  },
  ai: {
    measureDimRecommend: '/api/ai/v1/measure_dim_recommend',
  },
  reportGenerate: {
    getColumnValue: '/api/report-generate/column-value', // 获取维度码值_new
    getTemplateList: '/api/report-generate/template/list', // 模板列表
    postOutlineCreate: '/api/report-generate/outline/create', // 生成大纲阶段的保存
    dataFilterOperator: '/api/report-generate/data-filter-operator', // 获取运算符列表_new
    getColumnClassify: '/api/report-generate/column-classify', // 获取指标模型字段分类情况
    getReportList: '/api/report-generate/report/list', // 获取报告列表
    deleteReport: '/api/report-generate/report', // 删除报告
    getReportDetail: '/api/report-generate/report-detail', // 获取报告详情
    saveReport: '/api/report-generate/report/save', // 保存报告
    exportReport: '/api/report-generate/report/export', // 导出到本地
    updateSectionName: '/api/report-generate/section-name', // 段落重命名
    updateOutline: '/api/report-generate/template/outline/update', // 更新大纲接口 这个应该用来替换
    updateSection: '/api/report-generate/section/update', // 更新目标段落
    sectionConfig: '/api/report-generate/template/section-config', // 获取报告段落配置
    templateSectionConfig: '/api/report-generate/template/section-config', // 获取模版段落配置
    reportCopy: '/api/report-generate/report-copy', // 复制报告
    computeTypeList: '/api/report-generate/data-operator-compute-type', // 获取数据算子计算类型列表
    textOperatorType: '/api/report-generate/text-operator-type', // 获取文本算子类型
    dataOperatorType: '/api/report-generate/data-operator-type', // 获取算子的类型列表
    listDataOperatorColumnValue: '/api/report-generate/list-data-operator-column-value', // 获取数据算子的码值
    createOrUpdateDataOperator: '/api/report-generate/create-or-update-data-operator', // 新增或者更新段落数据算子
    createOrUpdateTextOperator: '/api/report-generate/create-or-update-text-operator', //  新增或者更新段落文本算子
    templateDetail: '/api/report-generate/template/detail', // 获取模版详情
    dataOperatorPreview: '/api/report-generate/get-data-operator-preview', // 数据算子预览
    textOperatorPreview: '/api/report-generate/get-text-operator-preview', // 文本算子预览
    timeGranularity: '/api/report-generate/time_granularity', // 分析时间粒度
    getSectionOperators: '/api/report-generate/template/section-operators', // 获取模版段落算子详情 包含文本算子和数据算子
    saveTemplate: '/api/report-generate/template/save', // 保存模板
    createTemplate: '/api/report-generate/template/create', // 新增模板
    deleteTemplate: '/api/report-generate/template/delete', // 删除模板
    markingUploadFile: '/api/report-generate/data-parser/upload-file', // 上传打标文件
    markingFileList: '/api/report-generate/data-parser/file-list', // 打标文件列表
    startMarking: '/api/report-generate/data-parser/start', // 开始打标
    getDepartmentValue: '/api/report-generate/department_value', // 部门
    getProvinceValue: '/api/report-generate/province_value', // 省份
    authTemplateCreateReport: `/api/report-generate/auth-template/create-report`, // 模版生成报告
    markFiledownloadProxy: (filePath: string) => `/api/report-generate/markFiledownloadProxy?p=${filePath}`, // 打标文件下载
  },
  baowu: {
    baowuDatasets: '/api/datasets/baowu/dataset-list',
    baowuRecommendQuestion: '/api/metrics/baowu/recommend-question',
    baowuCheckUserAuth: '/api/auth/baowu/check-user-auth',
    baowuSpeechRecognition: '/api/baowu/audio/createRec',
    baowuLoginLog: '/api/auth/baowu/login-log',
    baowuTestAuth: '/api/baowu/test-auth',
    baowuTestSubCompany: '/api/baowu/test-subCompany',
    baowuTestTimeout: '/api/baowu/test-timeout',
  },
  pptGenerate: {
    pptTemplateList: '/api/ppt-generate/template/list', // ppt模版列表
    pptGenerate: '/api/ppt-generate/generate', // 生成ppt
  },
  elk: {
    fetchLogByTraceId: (id: string) => `/api/elk/log/${id}`,
  },
  alipaySdk: {
    init: '/api/alipaySdk/init',
    asr: '/api/alipaySdk/asr',
    tts: '/api/alipaySdk/tts',
  },
  systemSetting: {
    appearance: {
      get: '/api/system-setting/appearance/get',
      update: '/api/system-setting/appearance/update',
    },
  },
} as const

export const askBIApiUrls = concatBaseUrl(askBIApiUrlsWithoutBaseUrl)

/**
 * Ask Doc 相关
 */
const ASK_DOC_BASE = '/askdoc'
/** Ask Doc 页面地址汇总 */
export const askDocPageUrls = {
  detail: `${ASK_DOC_BASE}/docs`,
} as const
/** Ask BI API 地址汇总 */
const ASK_DOC_API_BASE = '/api/askdoc'
export const askDocApiUrlsWithoutBaseUrl = {
  askQuestion: `${ASK_DOC_API_BASE}/docs/query-document`,
  suggestions: `${ASK_DOC_API_BASE}/docs/suggestion-questions`,
  fileList: `${ASK_DOC_API_BASE}/docs/file-list`,
  fileListV2: `${ASK_DOC_API_BASE}/docs/list-documents`,
  createFolder: `${ASK_DOC_API_BASE}/docs/create-folder`,
  updateFolderName: `${ASK_DOC_API_BASE}/docs/update-folder`,
  deleteFolder: `${ASK_DOC_API_BASE}/docs/delete-folder`,
  deleteDoc: `${ASK_DOC_API_BASE}/docs/delete-doc`,
  uploadFile: `${ASK_DOC_API_BASE}/docs/upload-file`,
  getLibraryId: `${ASK_DOC_API_BASE}/docs/get-library-id`,
  deleteSceneFile: `${ASK_DOC_API_BASE}/docs/delete-scene-file`,
  editSceneFile: `${ASK_DOC_API_BASE}/docs/edit-file`,
  addSceneFile: `${ASK_DOC_API_BASE}/docs/add-scene-file`, // 关联场景文件
  docColumnClassify: `${ASK_DOC_API_BASE}/docs/column-classify`,
  docGenerateReport: `${ASK_DOC_API_BASE}/docs/generate-report`,
  getReportColumnValue: `${ASK_DOC_API_BASE}/docs/column-value`,
  getReportOpList: `${ASK_DOC_API_BASE}/docs/op-list`,
  // 前端调用server最新版DOC目录权限接口 - start -
  createDocDir: `${ASK_DOC_API_BASE}/docs/dir/create`,
  setDocPermissions: `${ASK_DOC_API_BASE}/docs/perm`,
  updateDocDirName: `${ASK_DOC_API_BASE}/docs/dir/update`,
  deleteDocDir: `${ASK_DOC_API_BASE}/docs/dir-document-delete`,
  getDocDirList: `${ASK_DOC_API_BASE}/docs/list-dir-documents`,
  getDocPermissions: `${ASK_DOC_API_BASE}/docs/document-perm`,
  getDocInfoById: `${ASK_DOC_API_BASE}/docs/document-info-list`,
  searchDocIsPermissions: `${ASK_DOC_API_BASE}/docs/document-max-perm`,
  //  - end -
  fileUrlProxy: (filePath: string, mimeType: string) =>
    `${ASK_DOC_API_BASE}/docs/fileUrlProxy?p=${filePath}&m=${mimeType}`,
  downloadFileProxy: (filePath: string) => `${ASK_DOC_API_BASE}/docs/downloadFileProxy?p=${filePath}`,
} as const

export const askDocApiUrls = concatBaseUrl(askDocApiUrlsWithoutBaseUrl)

/** Ask Doc 调用的 AI 接口 */
export const aiAskDocFileListV2 = `/chatdoc/list-documents-v2`
export const aiDocSuggestions = `/chatdoc/suggestion-questions`
export const aiAskQuestion = `/chatdoc/query-document`
export const aiAskDocCreateFolder = `/chatdoc/create-folder`
export const aiAskDocUploadFile = `/chatdoc/upload-file`
export const aiAskDocUploadFolderName = `/chatdoc/update-folder`
export const aiAskDocDeleteFolder = `/chatdoc/folder`
export const aiAskDocDeleteDoc = `/chatdoc/doc`
export const getAskDocLibraryId = `/chatdoc/get-library-id`
export const getAskDocColumnClassify = `/chatdoc/column-classify` // 获取文档字段分类情况 / 修改文档字段分类情况
export const getAskDocGenerateReport = `/chatdoc/generate-report` // 生成报告
export const aiAskDocDeleteSceneFIle = `/chatdoc/scene-file/delete` // 删除场景文件
export const aiAskDocEditSceneFile = `/chatdoc/scene-file/edit` // 编辑场景文件
export const aiAskDocAddSceneFile = `/chatdoc/scene-file/add` // 关联场景文件
export const getReportColumnValue = `/chatdoc/column-value` // 获取维度码值
export const getReportOpList = `/chatdoc/op-list` // 获取运算符列表
export const authTemplateCreateReport = `/chatdoc/auth-template/create-report` // 模版生成报告

// 最新版DOC 目录权限
export const createDocDir = `/chatdoc/dir/create` // POST 新建文件夹
export const setDocPermissions = `/chatdoc/perm` // POST 设置权限
export const updateDocDirName = `/chatdoc/dir/update` // POST 修改文件夹名称
export const deleteDocDir = `/chatdoc/dir-document-delete` // POST 删除文档
export const getDocDirList = `/chatdoc/list-dir-documents` // POST 文档列表(目录版)
export const getDocPermissions = `/chatdoc/document-perm` // GET 查询目录权限
export const getDocInfoById = `/chatdoc/document-info-list` // GET 根据文档id列表查询文档信息
export const searchDocIsPermissions = `/chatdoc/document-max-perm` // POST 判断是否有无编辑权限

// 不用校验登录的页面
export const loginWhitelist: string[] = [
  askBIApiUrlsWithoutBaseUrl.auth.login,
  askBIApiUrlsWithoutBaseUrl.auth.logout,
  askBIApiUrlsWithoutBaseUrl.login.logout,
  askBIApiUrlsWithoutBaseUrl.login.requestSignature,
  askBIApiUrlsWithoutBaseUrl.login.appLogin,
  askBIApiUrlsWithoutBaseUrl.login.currentUserBasicInfo,
  askBIApiUrlsWithoutBaseUrl.login.tianhongLogin,
  askBIApiUrlsWithoutBaseUrl.login.checkToken,
  askBIApiUrlsWithoutBaseUrl.agent.downloadFile,
  askBIApiUrlsWithoutBaseUrl.agent.readCsv,
  askBIApiUrlsWithoutBaseUrl.agent.readImage,
  askBIApiUrlsWithoutBaseUrl.agent.metricDetail,
  askBIPageUrlsWithoutBaseUrl.login,
  askBIApiUrlsWithoutBaseUrl.metrics.listInScene(''),
  askBIApiUrlsWithoutBaseUrl.metrics.listInProject(''),
  askBIApiUrlsWithoutBaseUrl.metrics.metric2sql2data, // TODO: remove this
  askBIApiUrlsWithoutBaseUrl.metrics.metric2sql2dataAttr, // TODO: remove this
  askBIApiUrlsWithoutBaseUrl.diProxyLogin.namespace,
  askBIApiUrlsWithoutBaseUrl.diProxyLogin.service,
  askBIApiUrlsWithoutBaseUrl.diProxyLogin.pod,
  askBIApiUrlsWithoutBaseUrl.diProxyLogin.diDefaultProxy,
  askBIApiUrlsWithoutBaseUrl.env.list,
  askBIApiUrlsWithoutBaseUrl.env.getEnvTag,
  askBIApiUrlsWithoutBaseUrl.chatNoCheck,
  askBIApiUrlsWithoutBaseUrl.baowu.baowuRecommendQuestion,
  askBIApiUrlsWithoutBaseUrl.baowu.baowuDatasets,
  askBIApiUrlsWithoutBaseUrl.baowu.baowuCheckUserAuth,
  askBIApiUrlsWithoutBaseUrl.baowu.baowuLoginLog,
  askBIApiUrlsWithoutBaseUrl.baowu.baowuTestAuth,
  askBIApiUrlsWithoutBaseUrl.baowu.baowuTestSubCompany,
  askBIApiUrlsWithoutBaseUrl.baowu.baowuTestTimeout,
  askBIApiUrlsWithoutBaseUrl.chatProgress,
  askBIApiUrlsWithoutBaseUrl.chatProgressCallback,
  askBIApiUrlsWithoutBaseUrl.metricQuery,
  askBIApiUrlsWithoutBaseUrl.auth.metrics,
  askBIApiUrlsWithoutBaseUrl.systemSetting.appearance.get,
  '/api/dimensions/trend',
  '/api/agent/metric-detail',
  '/api/metrics/detail/time',
  '/api/metrics/trend',
  '/api/datasets/sample-data',
  askBIApiUrls.auth.enforce,
  askBIApiUrlsWithoutBaseUrl.login.getAuthToken,
  askBIApiUrlsWithoutBaseUrl.login.checkTokenFromUrl,
  `${ASK_DOC_API_BASE}/docs/fileUrlProxy`,
  `${ASK_DOC_API_BASE}/docs/downloadFileProxy`,
  `/api/report-generate/markFiledownloadProxy`,
  // askBIApiUrlsWithoutBaseUrl.auth.admin.user.rest
  askBIApiUrlsWithoutBaseUrl.xengine.tenant.getTenantState,
]
