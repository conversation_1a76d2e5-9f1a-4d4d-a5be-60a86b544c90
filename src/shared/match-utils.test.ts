import { describe, expect, it } from 'vitest'
import { nanoid } from 'nanoid'
import { MetricConfigResponse } from './metric-types'
import { parseWhereConditions } from './match-utils'

const metricConfig = {
  allDimensions: [
    { id: nanoid(), name: 'COMPANY_INNER_CODE_DES', label: '帐套名称' },
    { id: nanoid(), name: 'MOCK_DIMENSION_NAME', label: 'MOCK_DIMENSION_LABEL' },
  ],
  allMetrics: [
    { id: nanoid(), name: 'SUM_I6000', label: '营业成本' },
    { id: nanoid(), name: 'SUM_I7000', label: '营业利润' },
  ],
} as MetricConfigResponse

describe('parseWhereConditions', () => {
  it('Single dimension part where', () => {
    const onlyInWhere = `COMPANY_INNER_CODE_DES IN ('宝山', '青山')`
    expect(parseWhereConditions(onlyInWhere, metricConfig)).toEqual([
      {
        type: 'dimension',
        value: { codeValues: ['宝山', '青山'], name: 'COMPANY_INNER_CODE_DES', label: '帐套名称' },
      },
    ])

    const onlyORWhere = `COMPANY_INNER_CODE_DES = '宝武共享'`
    expect(parseWhereConditions(onlyORWhere, metricConfig)).toEqual([
      {
        type: 'dimension',
        value: { codeValues: ['宝武共享'], name: 'COMPANY_INNER_CODE_DES', label: '帐套名称' },
      },
    ])

    const onlyEqualWhere = `COMPANY_INNER_CODE_DES = '宝武智维' OR COMPANY_INNER_CODE_DES = '宝武资源'`
    expect(parseWhereConditions(onlyEqualWhere, metricConfig)).toEqual([
      {
        type: 'dimension',
        value: { codeValues: ['宝武智维', '宝武资源'], name: 'COMPANY_INNER_CODE_DES', label: '帐套名称' },
      },
    ])
  })

  it('dimension & metric part where', () => {
    const dimensionInAndMetricEqual = `COMPANY_INNER_CODE_DES IN ('宝山', '青山') AND SUM_I6000 = '3333'`
    expect(parseWhereConditions(dimensionInAndMetricEqual, metricConfig)).toEqual([
      {
        type: 'dimension',
        value: { codeValues: ['宝山', '青山'], name: 'COMPANY_INNER_CODE_DES', label: '帐套名称' },
      },
      {
        type: 'metric',
        value: { codeValues: ['3333'], name: 'SUM_I6000', label: '营业成本' },
      },
    ])

    const dimensionEqualAndMetricIn = `COMPANY_INNER_CODE_DES = '宝武共享' AND SUM_I6000 IN ('4444', '5555')`
    expect(parseWhereConditions(dimensionEqualAndMetricIn, metricConfig)).toEqual([
      {
        type: 'dimension',
        value: { codeValues: ['宝武共享'], name: 'COMPANY_INNER_CODE_DES', label: '帐套名称' },
      },
      {
        type: 'metric',
        value: { codeValues: ['4444', '5555'], name: 'SUM_I6000', label: '营业成本' },
      },
    ])

    const dimensionOrAndMetricOr = `(COMPANY_INNER_CODE_DES = '宝武智维' OR COMPANY_INNER_CODE_DES = '宝武资源') AND (SUM_I7000 = '6666' OR SUM_I7000 = '7777')`
    expect(parseWhereConditions(dimensionOrAndMetricOr, metricConfig)).toEqual([
      {
        type: 'dimension',
        value: { codeValues: ['宝武智维', '宝武资源'], name: 'COMPANY_INNER_CODE_DES', label: '帐套名称' },
      },
      {
        type: 'metric',
        value: { codeValues: ['6666', '7777'], name: 'SUM_I7000', label: '营业利润' },
      },
    ])
  })

  it('corner case', () => {
    const dimensionInAndMetricEqual = `COMPANY_INNER_CODE_DES IN ('宝山', '青山') AND SUM_I6000 = '3333'`
    expect(parseWhereConditions(dimensionInAndMetricEqual)).toEqual([
      {
        type: 'unknown',
        value: { codeValues: ['宝山', '青山'], name: 'COMPANY_INNER_CODE_DES', label: 'COMPANY_INNER_CODE_DES' },
      },
      {
        type: 'unknown',
        value: { codeValues: ['3333'], name: 'SUM_I6000', label: 'SUM_I6000' },
      },
    ])
  })
})
