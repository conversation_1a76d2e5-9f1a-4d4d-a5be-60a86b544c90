import { codeValueSplitChar } from './constants'
import { Dimension } from './metric-types'

export const BaowuReportType = {
  Simplified: 'Simplified',
  ExpenseAndAssets: 'ExpenseAndAssets',
  Overview: 'Overview',
} as const

const reportTypeMapping: { [key: string]: keyof typeof BaowuReportType } = {
  // 三张简表
  BAOWU_DEFAULT_REPORT: BaowuReportType.Simplified,
  BAOWU_ZICHAN_REPORT: BaowuReportType.Simplified,
  BAOWU_LIRUN_REPORT: BaowuReportType.Simplified,
  BAOWU_XIANJIN_REPORT: BaowuReportType.Simplified,

  // 费用支出和资产负债表
  BAOWU_FEIYONGZHICHU_REPORT: BaowuReportType.ExpenseAndAssets,
  BAOWU_ZICHANFUZHAILV_REPORT: BaowuReportType.ExpenseAndAssets,

  // 概况
  BAOWU_GAIKUANG_REPORT: BaowuReportType.Overview,
}

export const determineBaowuReportType = (reportName: string): keyof typeof BaowuReportType => {
  return reportTypeMapping[reportName] || BaowuReportType.Simplified
}

/**
 * 检查是不是同一个公司的码值
 */
export function isValidCodeValuesForOneCompany(codeValues: string[]) {
  if (codeValues.length === 0) {
    return false
  }
  const firstPrefix = getOriginCompanyName(codeValues[0])

  return codeValues.every((i) => getOriginCompanyName(i) === firstPrefix)
}

export const getAllCompaniesFromAllDimensions = (allDimensions: Dimension[]) => {
  const companies =
    allDimensions
      .find((item) => item.name === 'COMPANY_INNER_CODE_DES')
      ?.values?.map((item) => item.split(codeValueSplitChar)[0]) || []

  return companies
}

/**
 * 判断是否是宝武场景
 * @param metricTableName string
 * @returns boolean
 */
export function isBaoWu(metricTableName?: string | string[]) {
  const isNode = typeof process !== 'undefined' && process.versions != null && process.versions.node != null
  // doc场景下会有问题, 仅适用于宝武内部场景
  // 如果是浏览器下, 只要有isBaoWu=1就认为是宝武环境, 适配新增场景
  if (!isNode && window?.location?.search) {
    const search = window.location.search

    // 如果匹配不到则返回 -1,在布尔上下文中 -1 被认为是 true
    if (search.indexOf('isBaoWu=1') !== -1) {
      return true
    }
  }
  if (!metricTableName) {
    return false
  }
  if (typeof metricTableName === 'string') {
    // 如果表名路径的第二个层包含baowu就认为是宝武场景
    if (metricTableName.split('.')[1].indexOf('baowu') > -1) {
      return true
    }
    const judgeFns = [isBaoWuFinancial, isBaoWuCost, isBaoWuAmt]
    return judgeFns.some((fn) => typeof fn === 'function' && fn(metricTableName))
  }
  return metricTableName.some((name) => {
    return isBaoWuFinancial(name) || isBaoWuCost(name)
  })
}
// 是否是宝武财务场景
export function isBaoWuFinancial(metricTableName?: string) {
  return !!metricTableName && metricTableName.indexOf('T_ADS_FACT_WSSJ_TOTAL_INDEX') > -1
}
// 是否是宝武成本场景
export function isBaoWuCost(metricTableName?: string) {
  const validTableNames = [
    'T_ADS_FACT_WSSJ_COST_INDEX',
    'T_ADS_FACT_INDICATOR_COMPARISON_EXTENDS',
    'T_ADS_FACT_INDICATOR',
  ]
  return !!metricTableName && validTableNames.some((table) => metricTableName.indexOf(table) > -1)
}
// 是否是宝武总账场景
export function isBaoWuAmt(metricTableName?: string) {
  const validTableNames = ['AMT_WIDE_TABLE']
  return !!metricTableName && validTableNames.some((table) => metricTableName.indexOf(table) > -1)
}

// 是否是宝武排名类问题
export function isBaoWuTopN(orderBys?: string[]) {
  return orderBys && orderBys.length > 0
}

export function getBaoWuCodeValueList(dimensions: Dimension[]) {
  const codeValueList =
    dimensions
      .find((item) => {
        return item.name === 'COMPANY_INNER_CODE_DES'
      })
      ?.values?.map((item) => {
        return item.split(codeValueSplitChar)[0]
      }) || []
  return codeValueList
}

export const companyRegExp = /-法人|-管理合并|-资产合并/

export function getOriginCompanyName(companyName: string) {
  return companyName.replace(companyRegExp, '')
}
