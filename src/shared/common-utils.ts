/* eslint-disable @typescript-eslint/naming-convention */
/**
 * client 和 server 共享的一些工具函数
 * 注意：不要依赖 DOM 和 Node.js 的 API
 */
import dayjs from 'dayjs'
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter'
import { throttle } from 'lodash'
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore'
import { customAlphabet } from 'nanoid'
import React, { lazy, Suspense } from 'react'
import { SemanticProject, SemanticScene } from '@prisma/client'
import axios from 'axios'
import CryptoJS from 'crypto-js'
import { ConcatBaseUrlReturnType, DatasetDatum, ExtraInfo, OlapRow, QueryState } from './common-types'
import {
  ExternalReport,
  Metric,
  ProjectType,
  QueryParams,
  QueryParamsVerified,
  SceneType,
  TimeQueryParams,
} from './metric-types'
import { BASE_URL, FormatNumberReg, XENGINE_AES_IV, XENGINE_AES_KEY, SensitiveWordList } from './constants'
import { convertTimeToSpecificDate, getDateFormat } from './time-utils'

dayjs.extend(isSameOrAfter)
dayjs.extend(isSameOrBefore)

/** 检查 switch case 分支是否对所有 union type 或 enum 的值做了穷举，用在 default 分支中 */
export function assertExhaustive(_value: never, message: string = 'Exhaustive check failed'): never {
  const error = new Error(message)
  console.error('assertExhaustive error', _value, error)
  throw new Error(`${message}: ${JSON.stringify(_value)}`)
}

/** 按照和旭东约定的生成traceId的规则 生成 traceId https://mk70znjkuv.feishu.cn/wiki/F97gwygjIi4VJqkyUIJcEefIn2b*/
export function createTraceId() {
  const alphabet = '0123456789'
  const generate = customAlphabet(alphabet, 5)
  return `frontend_${dayjs(new Date()).format('YYYYMMDDHHmmss')}_${generate()}`
}
export function createNonaId() {
  const alphabet = '1234567890abcdef'
  return customAlphabet(alphabet, 10)
}

/** 判断当前是否是node环境，前端环境不支持env配置 */
export function isNodeEnv() {
  // 在Node.js环境中，process对象是全局可用的
  return typeof process !== 'undefined' && process.versions && process.versions.node
}

// 判断是否是数字并且不为null并且是整数
export function isPositiveInteger(value: number | string | string[]) {
  return typeof value === 'number' && !isNaN(value) && Number.isInteger(value)
}

/**
 * 把毫秒转成可读的字符串，如 1天 2小时 3分钟 4秒
 * @param ms 毫秒
 * @returns 可读的字符串
 */
export function secondsToReadableTime(ms: number) {
  const seconds = ms / 1000
  const days = Math.floor(seconds / 86400)
  const hours = Math.floor((seconds % 86400) / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const remainingSeconds = Math.floor(seconds % 60)

  let result = ''

  if (days > 0) {
    result += `${days}天 `
  }
  if (hours > 0) {
    result += `${hours}小时 `
  }
  if (minutes > 0) {
    result += `${minutes}分钟 `
  }
  if (remainingSeconds > 0) {
    result += `${remainingSeconds}秒`
  }

  return result.trim()
}

// 是数字类型的列
export const isColumnTypeBelongsNumber = (type: string) => {
  return ['INT', 'BIGINT', 'DECIMAL', 'NUMERIC', 'FLOAT', 'DOUBLE'].some((i) => {
    return new RegExp(i, 'i').test(type)
  })
}

export function padZero(number: number, totalLength: number): string {
  return number.toString().padStart(totalLength, '0')
}

type FormatOptions = {
  prefix?: string
  decimals?: number
  suffix?: string
  isPercentage?: boolean
  useThousandSeparator?: boolean
}

function parseTemplate(template: string): FormatOptions {
  const options: FormatOptions = {}
  const match = template.match(FormatNumberReg)

  if (match && match.groups) {
    options.prefix = match.groups.prefix || ''
    options.decimals = match.groups.decimals !== undefined ? parseInt(match.groups.decimals[1]) : undefined
    options.suffix = match.groups.suffix || ''
    options.isPercentage = options.suffix === '%'
    options.useThousandSeparator = !!match.groups.integer
  }

  return options
}

/**
 * @param num number 类型的数值
 * @param decimals 保留小数位数
 * @returns 带有单位的数值 万 千万 亿
 */
export function formatNumberWithChineseUnit({
  originNum,
  decimals,
  suffix,
}: {
  originNum: number
  decimals?: number
  suffix?: string
}): string {
  if (typeof originNum !== 'number' || Number.isNaN(originNum)) {
    return originNum + ''
  }
  const flag = originNum >= 0 ? 1 : -1
  const num = Math.abs(originNum)
  const newSuffix = suffix || ''
  const magnitude = ['十', '百', '千', '万', '亿']

  const format = (num: number): string => {
    if (Number.isInteger(num)) {
      return (flag * num).toString()
    }
    if (decimals !== undefined) {
      return roundToFixed(flag * num, decimals ?? 2)
    }
    return flag * num + ''
  }

  const isMagnitudeSuffix = newSuffix && magnitude.some((item) => newSuffix.indexOf(item) > -1)
  if (isMagnitudeSuffix) {
    // 如果有量级,则直接带上后缀返回
    return format(num) + newSuffix
  }

  const units = [
    { value: 1e8, unit: '亿' + newSuffix },
    { value: 1e4, unit: '万' + newSuffix },
  ]

  for (const { value, unit } of units) {
    if (num >= value && suffix !== '% ') {
      return roundToFixed(flag * (num / value), decimals ?? 2) + unit
    }
  }
  return format(num) + newSuffix
}

export function formatNumber(number: number | string, template: string | undefined): string {
  if (!number) {
    number === 0 ? number.toString() : ''
  }
  if ((typeof number === 'string' && !IS_VALID_STRING2NUMBER_REGEXP.test(number)) || !template) {
    return number.toString()
  }

  const { prefix, decimals, suffix, useThousandSeparator, isPercentage } = parseTemplate(template)

  // If formatting as percentage, multiply the number by 100.
  const formattedNumber = isPercentage ? +number * 100 : +number

  let result = formatNumberWithChineseUnit({ originNum: formattedNumber, decimals, suffix })

  if (useThousandSeparator) {
    result = thousandSeparateNum(result)
  }

  return `${prefix || ''}${result}`
}

function thousandSeparateNum(num: string) {
  const parts = num.split('.')
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  return parts.join('.')
}

/**
 * 四舍五入指定小数位数，并返回格式化字符串[标准四舍五入，非银行家舍入法]
 *
 * @param value - 需要进行四舍五入的数值
 * @param decimalPlaces - 需要保留的小数位数
 * @returns 格式化后的字符串，保留指定小数位数
 */
function roundToFixed(value: number, decimalPlaces: number): string {
  const factor = 10 ** decimalPlaces
  return (Math.round(value * factor) / factor).toFixed(decimalPlaces)
}

export function isValidFormatTemplate(formatTemplate: string): boolean {
  if (formatTemplate === '%') {
    return false
  }
  const regex = /^[^\d]*(,?)(\.\df)?(%.*)?$/
  return regex.test(formatTemplate)
}

export const IS_VALID_STRING2NUMBER_REGEXP = /^[+-]?(\d+(\.\d*)?|\.\d+)([eE][+-]?\d+)?$/

/** 2位小数的百分比 */
export const DEFAULT_FORMAT_RATIO = ',.2f%'
/** 千分位2位整数 */
export const DEFAULT_FORMAT_DECIMAL = ',.2f'
/** 千分位整数 */
export const DEFAULT_FORMAT_INT = ',.0f'

/**
 * 将object对象转变成FormData对象
 */

export function objectToFormData(obj: Record<string, string | Blob>) {
  const formData = new FormData()
  Object.keys(obj || {}).forEach((key) => {
    formData.append(key, obj[key])
  })
  return formData
}

/**
 * 匹配字符串是否是由下划线、字母、数字组成，并且是由字母为开头
 */

export function isAlphaNumberAndAlphaStart(str: string) {
  if (!str) {
    return true
  }
  const reg = /^[a-zA-Z]+\w*$/
  const valid = reg.test(str)
  return valid
}

/**
 * 获取当前北京时间的ISO字符串表示
 *
 * @returns 返回当前北京时间的ISO字符串表示
 */
export function getCurrentBeijingTimeIsoString(): string {
  const now = new Date() // 获取当前时间
  const beijingOffset = 8 * 60 * 60 * 1000 // 东八区相对于UTC的毫秒偏移量
  const beijingTime = new Date(now.getTime() + beijingOffset)
  return beijingTime.toISOString()
}

/**
 * 判断是否是科学城场景
 */
export function isKeXueCheng(dataset: DatasetDatum | null | undefined): boolean {
  return (
    dataset?.projectId === 'x7EspVv8x3YZ9frL' || // pre
    dataset?.projectId === 'qZEU36071BcRDMdR' // p
  )
}

/*
 * 根据指标code查指标中文名
 * @param metrics Metric[]
 * @param metricCodes string[]
 * @returns string[]
 */
export function getMetricLabelsByMetricNames(
  metrics: Metric[],
  externalReport: ExternalReport[],
  metricCodes: string[],
) {
  const result: string[] = []
  metrics.forEach((item) => {
    if (metricCodes.includes(item.name)) {
      result.push(item.label)
    }
  })
  externalReport.forEach((item) => {
    if (metricCodes.includes(item.name)) {
      result.push(item.label)
    }
  })
  return result
}

/**
 * 获取url上的query并赋予类型
 */
export function getQueryState(fromStorage = false, queryParams?: Record<string, string>): QueryState {
  const query: Record<string, string> = queryParams ?? {}
  if (!queryParams) {
    const defaultQueryState = {
      enableOnlyChat: false,
      enableAutoLogin: false,
      enableReloadQueryState: false,
      enableLangfuse: false,
    }
    if (!globalThis.window) {
      return defaultQueryState
    }
    try {
      if (fromStorage) {
        const storageData = localStorage.getItem('queryState')
        if (storageData) {
          return JSON.parse(storageData)
        }
      }
    } catch (err) {
      console.info('getQueryState From Storage Fail', err)
    }
    const searchParams = new URLSearchParams(globalThis.window.location.search ?? '')
    for (const [k, v] of searchParams.entries()) {
      query[k] = v
    }
  }
  // TODO 待优化，后续username和password参数需要加个前缀来标识是否是用于免登操作的 类似xxx_username
  // 是否只有chat页面
  const enableOnlyChat = query['only-chat']
    ? query['only-chat'] === 'true' || query['only-chat'] === '1'
    : Boolean(query['username'] || query['password'] || query['proxy'])
  // 是否自动登录，目前和onlyChat保持一致
  const enableAutoLogin = enableOnlyChat
  const queryState: QueryState = {
    username: query.username,
    password: query.password,
    proxy: query.proxy,
    appid: query.appid,
    enableOnlyChat,
    enableAutoLogin,
    enableLangfuse: Boolean(query['enable-langfuse']),
    enableReloadQueryState: Boolean(query['enable-reload-query-state']) || Boolean(query['reload-data']),
    hideHeader: query.hideHeader === 'true' || false,
  }
  return queryState
}

/**
 * 仓库中原有的Fallback组件
 */
function Fallback() {
  return React.createElement('div', { className: 'flex items-center justify-center' }, 'Loading...')
}

/**
 * 缓存动态导入的组件，后续增加路由探测时会增加node端读取路由文件，此时该函数也会被node所读取到
 * 考虑到utils是以ts为结尾，替换文件后缀后会使commit信息变化，使用React.createElement的方式创建组件
 * 在首次加载完后，会把组件存在CachedComponent中，第二次不会再次调用import函数，直接发回CachedComponent
 * 该函数会在引入在路由的时候直接被调用，每一个路由只会调用一次该函数
 * @param load 动态加载组件的函数
 */
export function cachedDynamicImportComponent(load: () => Promise<{ default: React.ComponentType<any> }>) {
  // 如果不为null，表明请求过一次，已经缓存了
  let CachedComponent: React.ComponentType | null = null

  // 封装一次load函数，获取到实际的组件用于缓存
  function dynamicImport() {
    return load().then((mod) => {
      CachedComponent = mod.default
      return mod
    })
  }

  // 仅在浏览器端执行，该函数会因为node端需要路由猜测，所以该文件在node端也会被执行
  // 暂时先禁用
  // globalThis.window?.addEventListener('load', () => {
  //   const pathname = globalThis.window?.location.pathname
  //   if (pathname !== askBIPageUrls.login && pathname !== askBIPageUrls.loginError) {
  //     idleConcurrencyQueue.addTaskAndRun(dynamicImport)
  //   }
  // })
  // 实际被渲染的组件
  function DynamicImportComponent(props: any) {
    if (CachedComponent) return React.createElement(CachedComponent, { ...props })
    const Component = lazy(dynamicImport)
    return React.createElement(
      Suspense,
      {
        fallback: React.createElement(Fallback),
      },
      React.createElement(Component, { ...props }),
    )
  }

  // 挂载一下本身的import函数方便后期处理import
  DynamicImportComponent.load = load
  return DynamicImportComponent
}

/**
 * 递归处理每一条url，如果它是字符串，且没有BASE_URL前缀，就加上
 * 如果是对象就遍历后递归加上
 */
export function concatBaseUrl<T>(value: T): ConcatBaseUrlReturnType<T> {
  let res!: any
  if (!value) res = value
  else if (typeof value === 'string') {
    if (!value.startsWith(BASE_URL)) {
      res = BASE_URL + value
    } else {
      res = value
    }
  } else if (typeof value === 'function') {
    res = (...args: any[]) => concatBaseUrl(value(...args))
  } else if (typeof value === 'object') {
    res = Object.fromEntries(Object.entries(value).map(([k, v]) => [k, concatBaseUrl(v)]))
  } else {
    res = value
  }
  return res
}

/**
 * @param arr
 * @returns length of array
 */
export const length = (arr?: any[]) => {
  return arr?.length || 0
}
/**
 *
 * @param num
 * @returns chinese number
 */
export const numberToChineseNumber = (num: number): string => {
  if (num === 0) return '零'

  const chineseNumbers = ['一', '二', '三', '四', '五', '六', '七', '八', '九']
  const unit = ['', '十', '百', '千', '万', '亿']

  let result = ''
  let unitIndex = 0

  while (num > 0) {
    const digit = num % 10
    if (digit > 0) {
      result = chineseNumbers[digit - 1] + unit[unitIndex] + result
    } else if (result && !result.startsWith('零')) {
      result = '零' + result
    }

    num = Math.floor(num / 10)
    unitIndex++
  }

  // Remove leading zero if present
  return result.replace(/^零+/, '')
}

/**
 * 生成查询数据日期文案
 * @param timeQueryParams {TimeQueryParams}
 * @param isbaowuFinancial {boolean}
 * @returns {string}
 */
export function getTimeQuery(
  timeQueryParams?: TimeQueryParams,
  isbaowuFinancial?: boolean,
  extraInfo?: ExtraInfo,
): string {
  let finalStr = ''

  if (timeQueryParams) {
    const { timeStartFunction, timeEndFunction, timeGranularity } = timeQueryParams
    const dateFormat = getDateFormat(timeGranularity)
    const timeStartDate = convertTimeToSpecificDate(timeStartFunction, 'start')
    const timeEndDate = convertTimeToSpecificDate(timeEndFunction, 'end')
    const startTime = dayjs(new Date(timeStartDate.year, timeStartDate.month - 1, timeStartDate.day))
    const endTime = dayjs(new Date(timeEndDate.year, timeEndDate.month - 1, timeEndDate.day))

    if (startTime && endTime) {
      const isRangeType = (unit: dayjs.OpUnitType) => {
        if (unit === 'month') {
          // 当起始时间为1号,结束时间为月最后一天, 则认为是问月
          return startTime.startOf(unit).isSame(startTime, 'date') && endTime.endOf(unit).isSame(endTime, 'date')
        }
        return startTime.startOf(unit).isSame(startTime, 'date') && endTime.endOf(unit).isSame(endTime, 'date')
      }

      const formatRange = (format: string) => {
        if (format === 'YYYYMMDD' || format === 'YYYY-MM-DD') {
          format = 'YYYY年MM月DD日'
        } else if (format === 'YYYYMM' || format === 'YYYY-MM') {
          format = 'YYYY年MM月'
        }
        if (isbaowuFinancial && format === 'YYYY年MM月DD日') {
          format = 'YYYY年MM月'
        }
        const formattedStartTime = startTime.format(format)
        const formattedEndTime = endTime.format(format)
        return formattedStartTime === formattedEndTime
          ? formattedStartTime
          : `${formattedStartTime} 至 ${formattedEndTime}`
      }
      if (isRangeType('year')) {
        finalStr = formatRange('YYYY年')
      } else if (isRangeType('month')) {
        finalStr = formatRange('YYYY年MM月')
      } else {
        finalStr = formatRange(dateFormat)
      }
      if (extraInfo?.timeQueryType) {
        console.info('extraInfo.timeQueryType', extraInfo.timeQueryType)
        switch (extraInfo.timeQueryType) {
          case '日':
            break
          case '年':
            finalStr = formatRange('YYYY年')
            break
          case '季':
          case '月':
            finalStr = formatRange('YYYY年MM月')
            break
          default:
            break
        }
      }
    }
  }
  return finalStr
}

/**
 * axios的CancelToken.source控制器。
 */
export class CancelTokenControl {
  private tokenSource = axios.CancelToken.source()
  getToken = () => this.tokenSource.token

  update() {
    this.tokenSource = axios.CancelToken.source()
  }

  cancel() {
    this.tokenSource.cancel()
  }
}

/**
 * 解析orderBy
 */
export function formatOrderBy({
  queryParamsVerified,
  defaultValue,
}: {
  queryParamsVerified?: QueryParamsVerified
  defaultValue?: ReturnType<typeof formatOrderBy>[number]
}): { metricName: string; orderBy: 'asc' | 'desc' }[] {
  const res: ReturnType<typeof formatOrderBy> = []
  if (queryParamsVerified) {
    res.push(
      ...(queryParamsVerified.queryParams.orderBys?.map((v) => {
        const [metricName, orderBy] = v.split(' ')
        return {
          metricName,
          orderBy: orderBy as 'asc' | 'desc',
        }
      }) ?? []),
    )
  }

  if (defaultValue) {
    res.push(defaultValue)
  }
  return res
}

export function sortByOrderBy({
  rows,
  orderByList,
  toValue = (v) => v,
}: {
  rows: OlapRow[]
  orderByList: ReturnType<typeof formatOrderBy>
  toValue?: (v: string) => any
}) {
  return rows.slice().sort((a, b) => {
    for (const { metricName, orderBy } of orderByList) {
      if (a[metricName] !== b[metricName]) {
        if (orderBy === 'desc') {
          // eslint-disable-next-line no-extra-semi, @typescript-eslint/no-extra-semi
          ;[a, b] = [b, a]
        }
        return toValue(a[metricName]) - toValue(b[metricName])
      }
    }
    return 0
  })
}

/**
 * Base64 编码
 * @param str
 * @returns
 */
export const getCryptoJsBase64 = (str: string) => {
  // 将密码字符串转换为 UTF-8 编码的字节数组
  const utf8Bytes = CryptoJS.enc.Utf8.parse(str)
  // 将字节数组编码为 Base64 字符串
  const base64String = CryptoJS.enc.Base64.stringify(utf8Bytes)
  return base64String
}

/**
 * Base64 解码
 * @param base64String
 * @returns
 */
export const getCryptoJsParseBase64 = (base64String: string) => {
  const decodedBytes = CryptoJS.enc.Base64.parse(base64String)
  const decodedString = CryptoJS.enc.Utf8.stringify(decodedBytes)
  return decodedString
}

/**
 * 加密用户ID
 *
 * @param k 密钥
 * @param i 初始化向量
 * @param text 待加密的文本或WordArray对象
 * @returns 加密后的Base64字符串
 */
export function encryptUserId(text: string | CryptoJS.lib.WordArray, aesKey: string, aesIV: string) {
  const key = CryptoJS.enc.Utf8.parse(aesKey)
  const iv = CryptoJS.enc.Utf8.parse(aesIV)
  const encrypted = CryptoJS.AES.encrypt(text, key, {
    iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  })
  return encrypted.ciphertext.toString(CryptoJS.enc.Base64)
}

/**
 * 从 WHERE 子句中提取维度名称和对应的值列表。
 * @param whereClause 包含条件的字符串，例如：
 *   - `"xxx IN ('a', 'b')"` 提取 `dimensionName: 'xxx', codeValues: ['a', 'b']`
 *   - `"xxx = 'a' OR xxx = 'b'"` 提取 `dimensionName: 'xxx', codeValues: ['a', 'b']`
 * @returns 提取出的维度名称和码值列表，格式：
 *   `{ dimensionName: string, codeValues: string[] }`
 *   若解析失败，则返回 `{ dimensionName: '', codeValues: [] }`
 */
export const extractDimensionAndValues = (whereClause: string | undefined) => {
  const DEFAULT_RESULT = { dimensionName: '', codeValues: [] }
  if (!whereClause) {
    return DEFAULT_RESULT
  }

  // 1. 处理 IN 语句
  const inRegex = /(\w+)\s+IN\s+\(((?:'[^']*'(?:,\s*)?)*)\)/
  const inMatch = whereClause.match(inRegex)
  if (inMatch) {
    const dimensionName = inMatch[1]
    const codeValuesString = inMatch[2]
    const codeValues = codeValuesString
      .split(',')
      .map((value) => value.replace(/'/g, '').trim())
      .filter((value) => value !== '')
    return { dimensionName, codeValues }
  }

  // 处理 `=` 和 `OR`
  const eqOrRegex = /(\w+)\s*=\s*'([^']*)'/g
  const eqOrMatches = [...whereClause.matchAll(eqOrRegex)]

  if (eqOrMatches.length > 0) {
    const dimensionNames = new Set(eqOrMatches.map((match) => match[1])) // 收集所有出现的字段名
    if (dimensionNames.size > 1) {
      return DEFAULT_RESULT
    }
    const dimensionName = eqOrMatches[0][1]
    const codeValues = eqOrMatches.map((match) => match[2])
    return { dimensionName, codeValues }
  }

  return DEFAULT_RESULT
}

/**
 * 提取Where中码值 whereExample "xxx='a' OR xxx='b'"
 * @param whereClause
 * @returns
 */
export const extractCodeValues = (whereClause: string | undefined): string[] => {
  if (!whereClause) {
    return []
  }
  const regex = /'([^']+)'/g
  const matches = []
  let match

  while ((match = regex.exec(whereClause)) !== null) {
    matches.push(match[1])
  }
  return matches
}

export function semanticToDataset({
  scene,
  project,
}: {
  scene: SemanticScene
  project: SemanticProject
}): DatasetDatum {
  return {
    projectId: project.id,
    projectName: project.name,
    sceneId: scene.id,
    sceneLabel: scene.label,
    tableName: scene.tableName,
    enableFollowUpQuestion: !!scene.enableFollowUpQuestion,
    enableMetricExactMatch: !!scene.enableMetricExactMatch,
    enableTryQueryUp: !!scene.enableTryQueryUp,
    enableSelectToastWhenEmptyData: !!scene.enableSelectToastWhenEmptyData,
    enableAccMetricToastWhenEmptyData: !!scene.enableAccMetricToastWhenEmptyData,
  }
}

/**
 * 转换scene到dataset
 * @returns
 */
export function sceneToDataset({ scene, project }: { scene: SceneType; project: ProjectType }): DatasetDatum {
  return {
    projectId: project.id,
    projectName: project.name,
    sceneId: project.scenes ? scene.id : '',
    sceneLabel: project.scenes ? scene.label : '',
    tableName: project.scenes ? scene.tableName : '',
    enableFollowUpQuestion: project.scenes ? !!scene.enableFollowUpQuestion : false,
    enableMetricExactMatch: project.scenes ? !!scene.enableMetricExactMatch : false,
    enableTryQueryUp: project.scenes ? !!scene.enableTryQueryUp : false,
    enableSelectToastWhenEmptyData: project.scenes ? !!scene.enableSelectToastWhenEmptyData : false,
    enableAccMetricToastWhenEmptyData: project.scenes ? !!scene.enableAccMetricToastWhenEmptyData : false,
  }
}

/**
 * 通过queryParams探测当前的时间维度
 * 如果起始时间是：
 * 最近天、明确天：天
 * 最近月、明确月：月
 * 最近年、明确年：年
 * 最近季、明确季：季
 * 如果起始时间和结束时间是：
 * 同年 年初和年末 或 年初和今天 或 年初和上月末且上月是同年：年
 * 同月 月初和月末 或 月初和今天：月
 * 同年 年初和上半年末 或 下半年初和年末： 半年
 * 季初和季末：季
 */
export function detectTimeType(queryParams?: QueryParams): 'day' | 'month' | 'year' | 'quarter' | 'half-year' | null {
  const timeQueryType = queryParams?.extraInfo?.timeQueryType
  switch (timeQueryType) {
    case '日':
      return 'day'
    case '月':
      return 'month'
    case '季':
      return 'quarter'
    case '年':
      return 'year'
    default:
      break
  }
  const timeQueryParams = queryParams?.timeQueryParams
  if (!timeQueryParams) return null
  let dateType: NonNullable<ReturnType<typeof detectTimeType>> = 'day'
  const type = timeQueryParams.timeStartFunction.type
  switch (type) {
    case 'recentDays':
    case 'specificDate':
      dateType = 'day'
      break
    case 'recentMonths':
    case 'specificMonth':
      dateType = 'month'
      break
    case 'recentYears':
    case 'specificYear':
      dateType = 'year'
      break
    case 'recentQuarters':
    case 'specificQuarter':
      dateType = 'quarter'
      break
    default:
      assertExhaustive(type)
  }

  const specTimeStartFunction = convertTimeToSpecificDate(timeQueryParams.timeStartFunction, 'start')
  const specTimeEndFunction = convertTimeToSpecificDate(timeQueryParams.timeEndFunction, 'end')

  const startDayjs = dayjs(
    new Date(specTimeStartFunction.year, specTimeStartFunction.month - 1, specTimeStartFunction.day),
  )
  const endDayjs = dayjs(new Date(specTimeEndFunction.year, specTimeEndFunction.month - 1, specTimeEndFunction.day))
  const now = dayjs()

  if (
    (startDayjs.format('YYYY-MM') === endDayjs.format('YYYY-MM') &&
      startDayjs.format('YYYY-MM-DD') === startDayjs.startOf('month').format('YYYY-MM-DD') &&
      endDayjs.format('YYYY-MM-DD') === endDayjs.endOf('month').format('YYYY-MM-DD')) ||
    (startDayjs.format('YYYY-MM') === endDayjs.format('YYYY-MM') &&
      startDayjs.format('YYYY-MM-DD') === startDayjs.startOf('month').format('YYYY-MM-DD') &&
      endDayjs.format('YYYY-MM-DD') === now.format('YYYY-MM-DD')) ||
    (endDayjs.format('YYYY-MM-DD') === endDayjs.endOf('month').format('YYYY-MM-DD') &&
      startDayjs.format('YYYY-MM-DD') === now.format('YYYY-MM-DD'))
  ) {
    dateType = 'month'
  } else if (
    (startDayjs.format('YYYY-MM-DD') === startDayjs.month(0).startOf('month').format('YYYY-MM-DD') &&
      endDayjs.format('YYYY-MM-DD') === endDayjs.month(2).endOf('month').format('YYYY-MM-DD')) ||
    (startDayjs.format('YYYY-MM-DD') === startDayjs.month(3).startOf('month').format('YYYY-MM-DD') &&
      endDayjs.format('YYYY-MM-DD') === endDayjs.month(5).endOf('month').format('YYYY-MM-DD')) ||
    (startDayjs.format('YYYY-MM-DD') === startDayjs.month(6).startOf('month').format('YYYY-MM-DD') &&
      endDayjs.format('YYYY-MM-DD') === endDayjs.month(8).endOf('month').format('YYYY-MM-DD')) ||
    (startDayjs.format('YYYY-MM-DD') === startDayjs.month(9).startOf('month').format('YYYY-MM-DD') &&
      endDayjs.format('YYYY-MM-DD') === endDayjs.month(11).endOf('month').format('YYYY-MM-DD'))
  ) {
    dateType = 'quarter'
  } else if (
    startDayjs.format('YYYY') === endDayjs.format('YYYY') &&
    ((startDayjs.format('YYYY-MM-DD') === startDayjs.startOf('year').format('YYYY-MM-DD') &&
      endDayjs.format('YYYY-MM-DD') === endDayjs.month(5).format('YYYY-MM-DD')) ||
      (startDayjs.format('YYYY-MM-DD') === startDayjs.month(6).format('YYYY-MM-DD') &&
        endDayjs.format('YYYY-MM-DD') === endDayjs.endOf('year').format('YYYY-MM-DD')))
  ) {
    dateType = 'half-year'
  } else if (
    startDayjs.format('YYYY') === endDayjs.format('YYYY') &&
    ((startDayjs.format('YYYY-MM-DD') === startDayjs.startOf('year').format('YYYY-MM-DD') &&
      endDayjs.format('YYYY-MM-DD') === endDayjs.endOf('year').format('YYYY-MM-DD')) ||
      (startDayjs.format('YYYY-MM-DD') === startDayjs.startOf('year').format('YYYY-MM-DD') &&
        endDayjs.format('YYYY-MM-DD') === now.format('YYYY-MM-DD')) ||
      (startDayjs.format('YYYY-MM-DD') === startDayjs.startOf('year').format('YYYY-MM-DD') &&
        startDayjs.format('YYYY') === now.subtract(1, 'month').format('YYYY') &&
        endDayjs.format('YYYY-MM-DD') === now.subtract(1, 'month').endOf('month').format('YYYY-MM-DD')))
  ) {
    dateType = 'year'
  }
  return dateType
}

/**
 * 筛选 rows，使其在指定的时间范围内
 * @param rows 待筛选的 rows
 * @param schema rows 相关的 schema
 * @param timeQueryParams 包含时间范围的查询参数
 */
export function filterRowsByTimeRange(
  rows: { [key: string]: any }[],
  schema: Array<
    | {
        column_name: string
        column_type: string
        original_name: string
        original_param_key: string
      }
    | {
        column_name: string
        column_type: string
        metric_name: string
        original_param_key: string
      }
  >,
  timeQueryParams: TimeQueryParams,
) {
  const columnMap = schema.reduce(
    (map, item) => {
      if ('original_name' in item) {
        map[item.column_name] = item.original_name
      } else if ('metric_name' in item) {
        map[item.column_name] = item.metric_name
      }
      return map
    },
    {} as Record<string, string>,
  )
  const newRows = rows.map((row) => {
    const newRow: Record<string, any> = {}
    Object.entries(row).forEach(([key, value]) => {
      const newKey = columnMap[key] || key // 如果有映射就替换，否则保持原样
      newRow[newKey] = value
    })
    return newRow
  })

  // 获取时间范围的起止时间
  const startDate = convertTimeToSpecificDate(timeQueryParams.timeStartFunction, 'start')
  const endDate = convertTimeToSpecificDate(timeQueryParams.timeEndFunction, 'end')

  const startDay = dayjs(`${startDate.year}-${startDate.month}-${startDate.day}`)
  const endDay = dayjs(`${endDate.year}-${endDate.month}-${endDate.day}`)

  const DATE_ALIAS = 'V_DATE_'
  // 筛选在范围内的 rows
  const result = newRows.filter((row) => {
    let rowDate: dayjs.Dayjs | null = null
    if (row[DATE_ALIAS].length === 4) {
      rowDate = dayjs(row[DATE_ALIAS], 'YYYY')
    } else if (row[DATE_ALIAS].length === 7) {
      rowDate = dayjs(row[DATE_ALIAS], 'YYYY-MM')
    } else if (row[DATE_ALIAS].length === 10) {
      rowDate = dayjs(row[DATE_ALIAS], 'YYYY-MM-DD')
    }
    if (!rowDate || !rowDate.isValid()) {
      console.warn(`无法解析日期: ${row[DATE_ALIAS]}`)
      return false
    }
    return rowDate.isSameOrAfter(startDay) && rowDate.isSameOrBefore(endDay)
  })

  const reversedColumnMap = Object.fromEntries(
    Object.entries(columnMap).map(([column_name, original_key]) => [original_key, column_name]),
  )
  // 处理数据，转换 key
  return result.map((res) => {
    return Object.keys(res).reduce((newObj: any, key) => {
      newObj[reversedColumnMap[key] || key] = res[key]
      return newObj
    }, {})
  })
}

export function jsonTryParse<T>(data: string): T | null {
  try {
    return JSON.parse(data)
  } catch (_) {
    return null
  }
}

export function filterSensitiveWord<T>(data: T, escaped = false): T {
  if (data === null || data === undefined) return data
  if (Array.isArray(data)) {
    return data.map((v) => filterSensitiveWord(v)) as T
  }
  if (typeof data === 'string') {
    return SensitiveWordList.reduce(
      (str, word) => str.replaceAll(word, (escaped ? '\\*' : '*').repeat(word.length)),
      data,
    ) as T
  }
  if (typeof data === 'object') {
    return Object.fromEntries(
      Object.entries(data).map(([k, v]) => {
        return [k, filterSensitiveWord(v)]
      }),
    ) as T
  }
  return data
}

export function getThrottleLog(...args: Partial<Parameters<typeof throttle>>) {
  args[0] ??= console.info
  args[1] ??= 3000
  args[2] ??= { leading: false, trailing: true }
  return throttle(...(args as Parameters<typeof throttle>))
}

export const loop = <T extends { key: string; children?: T[] }>(
  data: T[],
  key: string,
  callback: (item: T, index: number, data: T[], level: number) => void,
  level = 0,
) => {
  data.forEach((item, index) => {
    if (item.key === key) {
      callback(item, index, data, level)
    } else {
      item.children?.length && loop(item.children, key, callback, level + 1)
    }
  })
}
export const filterTreeByMap = <T extends { key: string; children?: T[] }>(
  data: T[],
  map: Record<string, boolean>,
  callback: (item: T, level: number) => void,
  level = 0,
) => {
  return data
    .filter((item) => {
      return callback(item, level)
    })
    .map((item) => {
      if (item.children?.length) {
        item.children = filterTreeByMap(item.children, map, callback, level + 1)
      }
      return item
    })
}

// 加密函数
export function encryptAES(plaintext: string) {
  const key = CryptoJS.enc.Utf8.parse(XENGINE_AES_KEY)
  const iv = CryptoJS.enc.Utf8.parse(XENGINE_AES_IV)

  // 使用AES加密
  const encrypted = CryptoJS.AES.encrypt(plaintext, key, {
    iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  })

  // 返回Base64编码的加密结果
  return encrypted.toString()
}

// 解密函数
export function decryptAES(cipherText: string) {
  const key = CryptoJS.enc.Utf8.parse(XENGINE_AES_KEY)
  const iv = CryptoJS.enc.Utf8.parse(XENGINE_AES_IV)

  // 解密数据
  const decrypted = CryptoJS.AES.decrypt(cipherText, key, {
    iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  })

  // 将解密后的数据从Base64解码为字符串
  return decrypted.toString(CryptoJS.enc.Utf8)
}

// 下载文件
export const downloadFile = async (
  url: string,
  customFilename?: string,
  cfg?: {
    onSuccess?: () => any
    onError?: (err: any) => any
  },
) => {
  try {
    // 发起GET请求，指定响应类型为流
    const response = await axios.get(url, { responseType: 'blob' })

    // 获取Content-Disposition中的文件名
    const contentDisposition = response.headers['content-disposition']
    const match = contentDisposition && contentDisposition.match(/filename=(.+)/)
    const filename = customFilename ? customFilename : match ? match[1] : 'downloaded-file'

    // 创建Blob对象并生成URL
    const blob = new Blob([response.data], { type: response.headers['content-type'] })
    const fileUrl = window.URL.createObjectURL(blob)

    // 创建a标签并模拟点击下载
    const link = document.createElement('a')
    link.href = fileUrl
    link.download = filename
    document.body.appendChild(link)
    link.click()

    // 移除临时创建的a标签
    document.body.removeChild(link)
    window.URL.revokeObjectURL(fileUrl)
    cfg?.onSuccess?.()
  } catch (error) {
    console.error('下载文件出错:', error)
    cfg?.onError?.(error)
  }
}

/**
 * 格式化 bytes，返回对应的单位
 * @param bytes
 * @returns
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes >= 1099511627776) {
    // 1 TB = 1024^4
    return `${parseFloat((bytes / 1099511627776).toFixed(2))} TB`
  } else if (bytes >= 1073741824) {
    // 1 GB = 1024^3
    return `${parseFloat((bytes / 1073741824).toFixed(2))} GB`
  } else if (bytes >= 1048576) {
    // 1 MB = 1024^2
    return `${parseFloat((bytes / 1048576).toFixed(2))} MB`
  } else {
    return `${parseFloat((bytes / 1024).toFixed(2))} KB`
  }
}

export const getKeyToIndexMap = (data: string[], startIndex: number = 0, filter?: (a: string) => boolean) => {
  const keyToIndexMap: Record<string, number> = {}
  data.forEach((key, index) => {
    if (filter && !filter(key)) return
    keyToIndexMap[key] = index + startIndex
  })
  return keyToIndexMap
}
type StringKeys<T> = {
  [K in keyof T]: T[K] extends string ? K : never
}[keyof T]
export const getObjectKeyToIndexMap = <T extends Record<string, any>, K extends StringKeys<T>>(
  data: T[],
  key: K,
  startIndex: number = 0,
  filter?: (i: T) => boolean,
) => {
  const keyToIndexMap: Record<string, number> = {}
  data.forEach((item, index) => {
    if (filter && !filter(item)) return
    keyToIndexMap[item[key]] = index + startIndex
  })
  return keyToIndexMap
}

export async function sleep(t: number = 1000) {
  return new Promise((r) => setTimeout(r, t))
}
