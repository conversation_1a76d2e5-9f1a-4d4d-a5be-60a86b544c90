import axios, { AxiosStatic } from 'axios'
import { askBIApiUrls, loginWhitelist } from './url-map'

// CSRF Token 获取接口和请求头名称（可以通过参数配置）
const defaultConfig = {
  csrfTokenUrl: askBIApiUrls.token.getToken, // 获取 CSRF Token 的接口
  csrfTokenHeader: 'unique-token', // 请求头名称
  retryStatus: 419, // 触发重试的状态码
}

// 创建 Axios CSRF 插件
export function createCsrfPlugin(config = {}) {
  const { csrfTokenUrl, csrfTokenHeader } = {
    ...defaultConfig,
    ...config,
  }

  return (axiosInstance: AxiosStatic) => {
    // 请求拦截器
    axiosInstance.interceptors.request.use(
      async (config) => {
        // 如果是 GET 请求，直接返回
        if (
          config?.method?.toLowerCase() === 'get' ||
          [...loginWhitelist, askBIApiUrls.login.nodeLogin].some((whitelistedUrl) =>
            config?.url?.startsWith(whitelistedUrl),
          )
        ) {
          return config
        }

        // 获取 CSRF Token
        try {
          // 如果是浏览器环境，获取 CSRF Token
          if (typeof XMLHttpRequest !== 'undefined') {
            const tokenResponse = await axios.get(csrfTokenUrl)
            const csrfToken = tokenResponse.data?.data
            if (!csrfToken) {
              throw new Error('CSRF Token not found in response')
            }

            // 将 CSRF Token 添加到请求头中
            config.headers[csrfTokenHeader] = csrfToken
          }
        } catch (error) {
          console.error('Failed to fetch CSRF token:', error)
          return config
          // 后端可能会没有部署 token 接口 所以失败了先不报错
          // return Promise.reject(error)
        }

        return config
      },
      (error) => {
        return Promise.reject(error)
      },
    )

    // 响应拦截器（可选）
    // axiosInstance.interceptors.response.use(
    //   (response) => {
    //     return response
    //   },
    //   async (error) => {
    //     // 如果后端返回指定状态码（例如 419），重新获取 Token 并重试请求
    //     if (error.response && error.response.status === retryStatus && error.config.method.toLowerCase() !== 'get') {
    //       try {
    //         const tokenResponse = await axios.get(csrfTokenUrl)
    //         const csrfToken = tokenResponse.data?.csrfToken

    //         if (!csrfToken) {
    //           throw new Error('CSRF Token not found in response')
    //         }

    //         // 更新请求头并重新发送请求
    //         error.config.headers[csrfTokenHeader] = csrfToken
    //         return axiosInstance(error.config)
    //       } catch (retryError) {
    //         console.error('Failed to retry request with new CSRF token:', retryError)
    //         return Promise.reject(retryError)
    //       }
    //     }
    //     return Promise.reject(error)
    //   },
    // )
  }
}

// import createCsrfPlugin from './axiosCsrfPlugin'

// // 创建 Axios 实例
// const axiosInstance = axios.create({
//   baseURL: '你的API基础路径', // 根据实际情况设置
// })

// // 配置 CSRF 插件
// const csrfPlugin = createCsrfPlugin({
//   csrfTokenUrl: '/api/csrf-token', // 获取 CSRF Token 的接口
//   csrfTokenHeader: 'X-CSRF-Token', // 请求头名称
//   retryStatus: 419, // 触发重试的状态码
// })

// // 应用插件
// csrfPlugin(axiosInstance)

// // 使用封装后的 Axios 实例
// axiosInstance
//   .post('/api/some-endpoint', { data: 'some data' })
//   .then((response) => console.log(response))
//   .catch((error) => console.error(error))
