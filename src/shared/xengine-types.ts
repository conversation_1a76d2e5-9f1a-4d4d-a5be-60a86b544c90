import type { ColumnDescType, MetricModelDimensionType } from '@shared/metric-types'
export type DataModelDescFilterType = {
  filterExpr: string
  conditions: {
    operator: string
    alias: string
    params: {
      type: 'COLUMN' | 'CONSTANT'
      value: string
      valueType?: 'STRING' | 'NUMERIC'
      vertexId: string
    }[]
  }[]
}

export type BackendEnumType = {
  value: string
  desc: string
}

export type VirtualTableCreateType = 'JOIN' | 'SQL_CREATE' | 'SQL_FILTER' | 'FILTER' | 'COPY'

export type TableCreateColumnType = {
  name: string
  columnType: string
  columnPrecision?: number
  columnScale?: number
}

export type ModificationVO = {
  creator?: string
  gmtCreated?: string
  gmtModified?: string
  modifier?: string
}

type PropertyKey = string | number | symbol

export type AnyObject = Record<PropertyKey, any>

export type CascaderOptionType = {
  value: string
  label: string
  children?: CascaderOptionType[]
}

export type VTableBaseInfo = {
  catalog: string
  database: string
  table: string
}

export type VirtualTableType = {
  id: string
  catalogName: string
  tag: null
  user: string
  databaseName: string
  name: string
  columns: {
    id: number
    name: string
    columnType: string
    columnPrecision: number
    columnScale: number
    comment: string
    displayList: null
    alias?: string
    derived?: boolean
    vertexId?: string
  }[]
  query: string
  virtualTableType: string
  hot: number
  timeColumn: string
  like: null
  computeType: string
  settings: null
  modification: null
  displayList: string[]
  joinVO: null
  unionVO: null
  streamBatchVO: null
  status: string // CREATE=未上线/ONLINE=已上线/OFFLINE=已下线/PAUSED=已暂停
}

export type CommonListType<T = AnyObject> = {
  list: T[]
  total: number
}

export type SmartXMaterialViewDetailQueryParams = {
  catalogName: string
  dbName: string
  mvName: string
}

export interface OverAggRangeIntervalDesc {
  rangeIntervalSize?: number
  rangeIntervalType: string
  rangeTimeIntervalUint?: string // 可选，表示时间间隔的单位，仅在范围间隔类型为 `RANGE_INTERVAL` 时有效
}

export interface OverAggDesc {
  orderBy: ColumnDescType // 旧版使用
  orderByDim?: MetricModelDimensionType | ColumnDescType // 新版使用
  partitionByDims?: (MetricModelDimensionType | ColumnDescType)[] // 新版使用
  partitionBys?: ColumnDescType[] // 旧版使用
  rangeIntervalDesc: OverAggRangeIntervalDesc
}

export type WindowDescType = {
  offset?: number
  overAggDesc?: OverAggDesc
  size?: number
  slide?: number
  timeUnit?: string
  timeUnitOfSlide?: string
  windowTimeColumn?: ColumnDescType //旧版使用
  windowTimeDim?: MetricModelDimensionType | ColumnDescType // 新版使用
  windowType: string
}

export type ErFlowNodeDataType = Omit<VirtualTableType, 'columns'> & {
  columns: (VirtualTableType['columns'][number] & { pk?: string[]; fk?: string[] })[]
  isFact: boolean
}
export type ErFlowEdgeDataType = {
  // todo: 类型id
  id?: string
  joinType: string
  source: {
    cell: string
    port: string
    collapse?: boolean
  }
  sourceData?: ErFlowNodeDataType
  target: {
    cell: string
    port: string
    collapse?: boolean
  }
  targetData?: ErFlowNodeDataType
  virtual?: boolean
}

// 物化支持的时间粒度
export type MaterializeSupportTypes = 'HOUR' | 'DAY' | 'MONTH' | 'YEAR'

// 获取指标模型可用的分区列接口返回类型
export type AvailableFormatColumnData = {
  columnName: string // 列名
  columnType: string // 列类型
  needFormat: boolean // 是否需要选择 format 格式
  availableFormats?: {
    displayName: string // 展示的名称
    sampleFormatDate: string // 展示的示例数据
    timeColumnFormat: string // column format 的名称，用于后续传参，无需展示
    biSupported: boolean // bi是否支持该格式，true：支持，false：不支持
    biGranularity: string // bi使用到的字段，该格式支持的最小粒度，DAY、MONTH、YEAR
    minGranularity: string // 该格式支持的最小物化粒度，HOUR、DAY、MONTH、YEAR
  }[] // 可用格式
}
