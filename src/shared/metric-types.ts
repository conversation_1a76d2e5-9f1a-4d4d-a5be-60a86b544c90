/* eslint-disable @typescript-eslint/naming-convention */
import { SemanticProject, TableMeta, SemanticScene as SemanticScenePrisma, SemanticMetricTree } from '@prisma/client'
import {
  TimeGranularityOptions,
  type AttrAnalysisItem,
  type DateType,
  type TimeDimensionFormat,
  type TimeGranularityMinType,
  OlapRow,
  AttrAnalysisDimension,
  AttrMetricAnalysisResult,
  TimeDimensionFormats,
  ChatResponseErrorStatus,
  AuthData,
  ExtraInfo,
} from './common-types'
import { DEFAULT_FORMAT_RATIO, assertExhaustive } from './common-utils'

/** entity */
export type Entity = {
  name: string
  label: string
  description: string
  type: 'foreign' | 'primary'
  expr: string
}

/** dimension */
export type CategoricalDimension = {
  id: string
  name: string
  label: string
  synonyms: string[]
  filterSwitch: boolean
  description?: string
  expr: string
  type: 'categorical'
  typeParams: any
  values?: string[]
}
export type VirtualTimeDimension = {
  id: string
  name: string
  label: string
  synonyms: string[]
  filterSwitch: true
  description?: string
  expr: string
  type: 'virtual-time'
  typeParams?: {
    timeGranularity: DateType
  }
  values?: string[]
}
export type TimeDimension = {
  id: string
  name: string
  label: string
  synonyms: string[]
  filterSwitch: boolean
  description?: string
  expr: string
  type: 'time' | 'time_default'
  typeParams: {
    timeGranularity?: TimeGranularityMinType
    timeType: TimeDimensionType
    timeFormat?: TimeDimensionFormat
  }
  values?: string[]
}

export function isTimeDimension(d: Dimension): d is TimeDimension {
  return d.type === 'time' || d.type === 'time_default'
}

export type DimensionTypeWithoutVirtual = TimeDimension['type'] | CategoricalDimension['type']
export type DimensionWithoutVirtual = TimeDimension | CategoricalDimension
export type Dimension = TimeDimension | CategoricalDimension | VirtualTimeDimension

export const TimeDimensionTypes = ['string', 'date', 'datetime'] as const
export type TimeDimensionType = (typeof TimeDimensionTypes)[number]

/** semantic model 上的 time dimension datum */
export type TimeDimensionDatum = {
  timeDimensionName: string
  timeDimensionType: TimeDimensionType
  timeDimensionFormat?: TimeDimensionFormat
  timeGranularityMin: TimeGranularityMinType
}

/** 各种指标类型的中文名 */
export const DimensionTypeWithoutVirtualNames = {
  time: '时间维度',
  time_default: '主时间维度',
  categorical: '类目维度',
} as const

export const DimensionTypeNames = {
  time: '时间维度',
  categorical: '类目维度',
  time_default: '主时间维度',
  'virtual-time': '虚拟时间维度',
} as const

export const DimensionTypeAllNames = {
  all: '全部维度',
  time: '时间维度',
  time_default: '主时间维度',
  categorical: '类目维度',
  'virtual-time': '虚拟时间维度',
} as const

/** semantic model */
export type SemanticModel = {
  name: string
  tableName: string
  label?: string
  timeDimensionName?: string
  timeDimensionType?: string
  timeDimensionFormat?: string
  timeGranularityMin?: TimeGranularityMinType
  sourceCreateTime: number
}

/** measure */
export const MeasureAggOptions = [
  { label: '求和', value: 'sum' },
  { label: '平均值', value: 'avg' },
  { label: '最小值', value: 'min' },
  { label: '最大值', value: 'max' },
  { label: '求和（布尔型）', value: 'sum_boolean' },
  { label: '去重计数', value: 'count_distinct' },
] as const

export type MeasureAgg = (typeof MeasureAggOptions)[number]['value']

export type Measure = {
  id: string
  name: string
  label: string
  synonyms: string[]
  description?: string | null
  expr: string
  agg: MeasureAgg
  formatTemplate?: string
  createMetric?: boolean
  createRankMetric?: boolean
}

/** metrics */
// 原子指标
export type SimpleMetric = {
  id: string
  name: string
  label: string
  synonyms: string[]
  description?: string
  type: 'simple'
  formatTemplate: string
  typeParams: {
    measure: string
  }
  filter?: string // {{Dimension('customer.order_total_dim')}} >= 20
  displayExpr?: string
  displayValue?: number | string
  rank?: number
  category?: string
  keypoint?: boolean
  createByMeasure?: boolean
  windowDescConfigStr?: string
  isCumulative: boolean
  updatedAt: Date
  config?: any
  meta?: any
}

// 复合指标
export type DerivedMetric = {
  id: string
  name: string
  label: string
  synonyms: string[]
  description?: string
  type: 'derived'
  formatTemplate: string
  typeParams: {
    expr: string // (current_revenue - revenue_prev_month)*100/revenue_prev_month
    metrics: {
      name: string
    }[]
  }
  displayExpr?: string
  displayValue?: number | string
  rank?: number
  category?: string
  keypoint?: boolean
  isCumulative: boolean
  updatedAt: Date
  config?: any
  meta?: any
}

// 列表指标（指标集）
export type ListMetric = {
  id: string
  name: string
  label: string
  synonyms: string[]
  description?: string
  type: 'list'
  /** list metric 没有 formatTemplate，保存为空字符串即可 */
  formatTemplate: string
  typeParams: {
    metrics: {
      name: string
    }[]
  }
  displayExpr?: string
  displayValue?: number | string // 最近一个月的指标数据 无数据展示为 '-'
  rank?: number
  category?: string
  keypoint?: boolean
  updatedAt: Date
  config?: any
  meta?: any
}

// 比值指标
export const RatioName = {
  numerator: '分子',
  denominator: '分母',
} as const

/** Ratio 指标的特点是：where 和 groupBy 对 分子 生效，对 分母 不生效 */
export type RatioMetric = {
  id: string
  name: string
  label: string
  synonyms: string[]
  description?: string
  type: 'ratio'
  formatTemplate: string
  typeParams: {
    numerator: string // 分子的 metric name
    /*
    | {
        name: string // metric name
        // filter?: string
        // alias?: string
      }
    */
    denominator: string // 分母的 metric name
    /*
    | {
        name: string // metric name
        // filter?: string
        // alias?: string
      }
    */
  }
  displayExpr?: string
  displayValue?: number | string
  rank?: number
  category?: string
  keypoint?: boolean
  isCumulative: boolean
  updatedAt: Date
  config?: any
  meta?: any
}

// TODO: 使用 SqlStatement 来生成 join 的 sql
export type SqlStatement = {
  select: string[] | string
  from: string
  join?: string
  where?: string
  groupBys?: string[]
  orderBys?: string[]
  limit?: number
}

export type CTEStatement = {
  cteName: string
  sqlStatement: SqlStatement
}

export const WindowOptions = [
  { value: '1 month', label: '1 个月' },
  { value: '1 year', label: '1 年' },
  { value: '7 days', label: '7 天' },
]

export const OrderByOptions = [
  { value: 'asc', label: '升序' },
  { value: 'desc', label: '降序' },
]
export type PeriodOverPeriodMetric = {
  id: string
  name: string
  label: string
  synonyms: string[]
  description?: string
  type: 'periodOverPeriod'
  formatTemplate: string
  typeParams: {
    /** 同环比的类型 */
    type: PeriodOverPeriodType
    /** 原始指标的名字 */
    metric: string
  }
  displayExpr?: string
  displayValue?: number | string
}
/*
export type RankMetric = {
  id: string
  name: string
  label: string
  synonyms: string[]
  description?: string
  type: 'rank'
  formatTemplate: string
  typeParams: {
    metric: string
    orderBy?: (typeof OrderByOptions)[number]['value'] // 默认为 desc
  }
  createdByMeasure: boolean
  displayExpr?: string
  displayValue?: number | string
}
*/

export type MetricType =
  | SimpleMetric['type']
  | RatioMetric['type']
  | DerivedMetric['type']
  // | RankMetric['type']
  | ListMetric['type']
export type NoListMetric = SimpleMetric | DerivedMetric | RatioMetric
export type Metric = NoListMetric | ListMetric
export type MetricWithPeriodOverPeriod = Metric | PeriodOverPeriodMetric
// export type Metric = SimpleMetric | RatioMetric | DerivedMetric | RankMetric | ListMetric

export type ExternalReport = {
  id: string
  name: string // 外部报表name
  label: string // 外部报表中文名称
  type: string // enum: baowu_report
  synonyms: string[] // 同义词 JSONArray
  description?: string | null // 可选描述
  semanticSceneId: string // 关联的场景ID
  semanticProjectId: string // 关联的项目ID
  createdAt: Date
  updatedAt: Date
}

// 可选的 Agent 类型
export enum HintAgentType {
  Chat = 'chat',
  BI = 'bi',
  Doc = 'doc',
  Condense = 'condense',
  Brain = 'brain',
  Judge = 'judge',
  Where = 'where',
  Metric = 'metric',
  GroupBy = 'groupby',
  TimeRange = 'time_range',
  TableTools = 'table_tools',
}

// 可选的 Tag 类型
export enum HintTagType {
  Thinking = '思考逻辑',
  TimeHandling = '时间处理',
  MetricStandardization = '指标标准化',
  BusinessCalculation = '业务计算',
}

export const HintAgentTypeOptions = Object.values(HintAgentType).map((val) => ({
  label: val,
  value: val,
}))

export const HintTagTypeOptions = Object.entries(HintTagType).map(([_key, value]) => ({
  label: value,
  value,
}))

// 业务术语
export type Hint = {
  id: string
  text?: string
  extraInfo: string
  type: 'fixed' | 'dynamic'
  creator: string
  agents: HintAgentType[]
  tags: HintTagType[]
  semanticProjectId: string
  semanticSceneId: string
  createdAt: number
  updatedAt: number
}

export type Scenario = {
  id: number
  label: string
  description: string
  agent: {
    name: string
    icon: string
  }[]
  project: string
  metric: string
  data: string
  createdAt: string
  createdBy: string
  modelNames: string[]
  semanticProjectId: string
}

// 因为 MetricConfigResponse 已经被占了。
export interface MetricListResponse {
  list: (Metric & Id)[]
  total: number
}

export type MeasureListResponse = {
  list: (Measure & Id)[]
  total: number
}

export type DimensionListResponse = {
  list: (Dimension & Id)[]
  total: number
}

export type TableStructureResponse<ITEM> = {
  list: ITEM[]
  total?: number
}

export type ColumnsTitleMapItem = {
  field: string
  title: string
}

// 表的前5条数据样例
export type SampleDataResponse = {
  /** 行数据 */
  data: OlapRow[]
  /** 列信息 */
  columns: string[]
  // 列中英文映射
  columnsTitleList: ColumnsTitleMapItem[]
  /** 总行数 */
  total: number
  datasourceName: string
  tableName: string
}

/** 各种指标类型的中文名 */
export const MetricTypeNames = {
  simple: '原子指标',
  ratio: '比值指标',
  derived: '派生指标',
  // rank: '排名指标',
  list: '列表指标',
} as const

/** 指标类型选择的时候，加上 all */
export const MetricTypeNamesWithAll = {
  all: '全部',
  simple: '原子指标',
  ratio: '比值指标',
  derived: '派生指标',
  // rank: '排名指标',
  list: '列表指标',
} as const

/** growth 增量，growthRate 是增量变化率 */
export type PeriodOverPeriodType =
  | 'momGrowth' /** 本月 - 上月 */
  | 'yoyMonthGrowth' /** 本月 - 去年同月 */
  | 'momGrowthRate' /** （本月 - 上月）/ 上月 */
  | 'yoyMonthGrowthRate' /** （本月 - 去年同月）/ 去年同月 */
  | 'qoqGrowth' /** 本季度 - 上季度 */
  | 'yoyQuarterGrowth' /** 本季度 - 去年同季度 */
  | 'qoqGrowthRate' /** （本季度 - 上季度）/ 上季度 */
  | 'yoyQuarterGrowthRate' /** （本季度 - 去年同季度）/ 去年同季度 */
/** 月份的同环比 */
export const PeriodOverPeriodMonthNames: PeriodOverPeriodType[] = [
  'momGrowth',
  'yoyMonthGrowth',
  'momGrowthRate',
  'yoyMonthGrowthRate',
] as const
/** 季度的同环比 */
export const PeriodOverPeriodQuarterNames: PeriodOverPeriodType[] = [
  'qoqGrowth',
  'yoyQuarterGrowth',
  'qoqGrowthRate',
  'yoyQuarterGrowthRate',
] as const
export const PeriodOverPeriodNames = [...PeriodOverPeriodMonthNames, ...PeriodOverPeriodQuarterNames] as const

/** 同环比指标的配置 */
export const PeriodOverPeriodMetricConfig: Record<
  PeriodOverPeriodType,
  {
    metricPrefix: string
    labelSuffix: string
    formatTemplate: 'same_with_metric' | string
  }
> = {
  momGrowth: {
    metricPrefix: 'mom_growth_',
    labelSuffix: '环比增长额',
    formatTemplate: 'same_with_metric',
  },
  yoyMonthGrowth: {
    metricPrefix: 'yoy_month_growth_',
    labelSuffix: '同比增长额',
    formatTemplate: 'same_with_metric',
  },
  momGrowthRate: {
    metricPrefix: 'mom_growth_rate_',
    labelSuffix: '环比增长率',
    formatTemplate: DEFAULT_FORMAT_RATIO,
  },
  yoyMonthGrowthRate: {
    metricPrefix: 'yoy_month_growth_rate_',
    labelSuffix: '同比增长率',
    formatTemplate: DEFAULT_FORMAT_RATIO,
  },
  qoqGrowth: {
    metricPrefix: 'qoq_growth_',
    labelSuffix: '环比增长额',
    formatTemplate: 'same_with_metric',
  },
  yoyQuarterGrowth: {
    metricPrefix: 'yoy_quarter_growth_',
    labelSuffix: '同比增长额',
    formatTemplate: 'same_with_metric',
  },
  qoqGrowthRate: {
    metricPrefix: 'qoq_growth_rate_',
    labelSuffix: '环比增长率',
    formatTemplate: DEFAULT_FORMAT_RATIO,
  },
  yoyQuarterGrowthRate: {
    metricPrefix: 'yoy_quarter_growth_rate_',
    labelSuffix: '同比增长率',
    formatTemplate: DEFAULT_FORMAT_RATIO,
  },
} as const

export type QueryParams = {
  metricNames: string[] // 所有用到的 metric name 的列表
  externalReportNames?: string[]
  groupBys?: string[]
  originGroupBys?: string[]
  where?: string
  originWhere?: string // python 返回的原始where
  timeQueryParams?: TimeQueryParams
  limit?: number // Limit the number of rows out using an int or leave blank for no limit. For example: --limit 100
  orderBys?: string[] // 格式为 ["name desc", "age asc"], dbt 的格式为 metrics or group bys to order by ("-" prefix for DESC). For example: --order -ds or --order ds,-revenue
  /** 同环比的配置 */
  periodOverPeriods?: PeriodOverPeriodType[]
  notExistMetricNames?: string[] | null
  notExistGroupBys?: string[] | null
  notExistOrderBys?: string[] | null
  isMetricNamesExactMatch?: boolean
  isWhereExactMatch?: boolean
  /***** python返回的 额外信息 : 是否追问, 是否连续提问, 是否问子公司... *****/
  extraInfo?: ExtraInfo
}

/** 校验过的 QueryParams，extraParams 为提参错误的内容 */
export type QueryParamsVerified = {
  originalQueryParams: QueryParams // 原始QueryParams
  queryParams: QueryParams
  // FIXME: 把 extraParams 改为 notExistParams，因为代表了不存在的参数
  extraParams: {
    extraMetricNames: string[]
    extraGroupBys: string[]
    extraOrderBys: string[] // 格式为 ["name desc", "age asc"]
  }
  // 是不是同环比
  hasPop?: boolean
}

export type MetricConfigResponse = {
  metricTableName: string
  allDimensions: Dimension[]
  allMeasures: Measure[]
  allMetrics: Metric[]
  hotMetrics: Metric[]
  allExternalReports: ExternalReport[]
  // 时间维度为可选。如果没有时间维度，那么不要返回 timeDimensionDatum
  timeDimensionDatum?: TimeDimensionDatum
}

export type MetricConfigResponseForProject = Array<{
  sceneId: string
  data: MetricConfigResponse
  updateTime?: number
}>

export type MetricTrendResponse = {
  date: string[]
  value: string[] // 数字也当中字符串返回
}

export type Id = {
  id: string
}

/** 意图识别的提参结果 */
export type IntentParams = {
  intent: '查数' | '归因' | '闲聊'
}

export type SemanticProjectInfo = SemanticProject & {
  semanticScenes: (SemanticScenePrisma & { tableMeta: TableMeta | null })[]
}

/**
 * 日期提参的格式
 */
/** 相对时间计算的函数，4选1 */
export type RelativeTimeFunction =
  | { type: 'recentDays'; days: number }
  | { type: 'recentMonths'; months: number }
  | { type: 'recentQuarters'; quarters: number }
  | { type: 'recentYears'; years: number }
/** 绝对时间计算的函数，4选1 */
export type SpecificTimeFunction =
  | { type: 'specificDate'; year: number; month: number; day: number }
  | { type: 'specificMonth'; year: number; month: number }
  | { type: 'specificQuarter'; year: number; quarter: number }
  | { type: 'specificYear'; year: number }
/** 时间计算函数的类型 */
export type TimeFunction = RelativeTimeFunction | SpecificTimeFunction
export type TimeQueryParamGranularity = 'day' | 'month' | 'quarter' | 'year' | 'total'
/** 取数场景-时间参数的类型定义 */
export type TimeQueryParams = {
  /** 因为有多个时间维度，如果为空则使用主时间维度，不为空则使用此时间维度 */
  timeDimensionName?: string
  timeStartFunction: TimeFunction
  timeEndFunction: TimeFunction
  timeGranularity: TimeQueryParamGranularity
}
/** 归因分析-时间差值对比场景中提参的类型定义 */
export type AttrTimeDiffParams = {
  baseTime: TimeFunction
  compareTime: TimeFunction
}

interface ParamsExtractQueryMetricResultBase {
  originGroupBys: string[]
  groupBys: string[]
  metricNames: string[]
  orderBys: string[]
  where: string
  originWhere: string
  limit?: number
  timeQueryParams?: TimeQueryParams
  isMetricNamesExactMatch?: boolean // 标识指标是否精确匹配，如果为false，则没有精确匹配，前端界面上提供选项供用户选择
  isWhereExactMatch?: boolean
  externalReportNames?: string[]
  extraInfo?: any // python返回的多余参数
  confidenceOriginData?: { where: string; isWhereExactMatch: boolean; originWhere: string }
  authData?: AuthData
  notExistMetricNames?: string[]
  notExistGroupBys?: string[]
  notExistOrderBys?: string[]
}

export interface ParamsExtractQueryMetricResult extends ParamsExtractQueryMetricResultBase {
  type: 'query-metric'
  infoTexts: string[]
}

export interface ParamsExtractExactMatch extends ParamsExtractQueryMetricResultBase {
  type: 'metric-exact-match'
  infoTexts: string[]
}

export interface ParamsExtractExternalReport {
  type: 'query-external-report'
  externalReports: string[]
  where: string
  timeQueryParams?: TimeQueryParams
  confidenceOriginData?: { where: string; isWhereExactMatch: boolean; originWhere: string }
}

export interface ParamsExtractAttributionAnalysisResult {
  type: 'attribution-analysis'
  // FIXME: update name to camelCase
  tree: AttrAnalysisItem
  dimension: AttrAnalysisDimension[]
  base_compare: AttrTimeDiffParams
  attr_params: {
    metric: string[]
    filter: string
  }
  metric_sql: {
    sqlBase: string
    sqlCompare: string
  }
}

export interface ParamsExtractAttributionMetricAnalysisResult extends AttrMetricAnalysisResult {
  type: 'attribution-metric-analysis'
}

export interface ParamsExtractDataOverview {
  type: 'data-overview'
  content: string
}

export interface ParamsExtractTableList {
  type: 'table-list'
  content: string
}

export interface ParamsExtractDimensionList {
  type: 'dimension-list'
  content: string
}

export interface ParamsExtractDimensionDetail {
  type: 'dimension-detail'
  content: string
}

export interface ParamsExtractMetricList {
  type: 'metric-list'
  content: string
}

export interface ParamsExtractMetricDetail {
  type: 'metric-detail'
  content: string
}

export interface ParamsExtractMetricTree {
  type: 'metric-tree'
  content: string
}

export interface ParamsExtractChitChat {
  type: 'chitchat'
  content: string
}

export interface ParamsExtractLlmError {
  type: 'llm-error'
  content: string
}

export interface ParamsExtractChatError {
  type: 'chat-error'
  errType: ChatResponseErrorStatus
  content: string
}

export interface ParamsExtractPercentage {
  type: 'percentage'
  content: string
}

export interface ParamsExtractPeriodOnPeriod {
  type: 'period_on_period'
  content: string
}

export type ResultOfParamsExtract =
  | ParamsExtractQueryMetricResult
  | ParamsExtractExactMatch
  | ParamsExtractExternalReport
  | ParamsExtractAttributionAnalysisResult
  | ParamsExtractAttributionMetricAnalysisResult
  | ParamsExtractDataOverview
  | ParamsExtractTableList
  | ParamsExtractDimensionList
  | ParamsExtractDimensionDetail
  | ParamsExtractMetricList
  | ParamsExtractMetricDetail
  | ParamsExtractMetricTree
  | ParamsExtractChitChat
  | ParamsExtractLlmError
  | ParamsExtractChatError
  | ParamsExtractPercentage
  | ParamsExtractPeriodOnPeriod

export type TimeSqlPart = {
  where: string
  timeDimensionName: string // 模型可能有多个时间维度，从中选择一个
  groupBy?: string // 如果是 total，那么不需要 group by 语句，如果是指定某一天，时间提参 timeGranularity=total
  whereForPeriodOverPeriod?: string // 同环比的 where 语句
}

export const DATE_ALIAS = 'V_DATE_'

/** 默认的 sceneId，用于指标列表中，如果还没有加载出默认的 sceneId，就用这个，避免前端页面等待接口 */
export const DEFAULT_SCENE_ID = 'DEFAULT_SCENE_ID_PLACEHOLDER'

export const MetaDataTaskTypes = [
  'data-overview',
  'table-list',
  'dimension-list',
  'dimension-detail',
  'metric-list',
  'metric-detail',
  'metric-tree',
  'chitchat',
  'llm-error',
  'percentage',
  'period_on_period',
] as const

export const TaskTypes = [
  'query-metric',
  'attribution-analysis',
  'attribution-metric-analysis',
  ...MetaDataTaskTypes,
] as const

export type TaskType = (typeof TaskTypes)[number]

/**
 * 获取比最小时间粒度更大的时间粒度
 * @param timeGranularityMin 最小的时间粒度
 * @returns
 */
export function getAvailableTimeGranularityOptions(timeGranularityMin: TimeGranularityMinType) {
  switch (timeGranularityMin) {
    case 'day':
      return TimeGranularityOptions
    case 'month':
      return TimeGranularityOptions.filter((option) => option.value !== 'day')
    case 'year':
      return TimeGranularityOptions.filter((option) => option.value === 'year' || option.value === 'total')
    default:
      assertExhaustive(timeGranularityMin)
  }
}

/** MetricTree 的信息，树状结构 */
export type MetricTreeNodeDataType = Pick<
  SemanticMetricTree,
  Exclude<
    keyof SemanticMetricTree,
    'createdAt' | 'updatedAt' | 'semanticProjectId' | 'childrenNames' | 'semanticMetricTreeRootId'
  >
> & {
  treeName?: string
  label: string
  displayValue: string | number
  children: MetricTreeNodeDataType[]
  collapsed?: boolean
}

export type BaseClickInfoType = {
  clientX: number
  clientY: number
  canvasX: number
  canvasY: number
}

export type GraphCfgType = {
  width?: number
  height?: number
  isShowMiniMap?: boolean
  interactive?: boolean
  defaultZoom?: number
  isShowToolbar?: boolean
}

export type MetricTreeGraphPropsType = {
  data: MetricTreeNodeDataType | null
  handleClickAdd?: (info: MetricTreeNodeDataType & BaseClickInfoType) => void
  handleClickDelete?: (info: MetricTreeNodeDataType & BaseClickInfoType) => void
  graphCfg?: GraphCfgType
  className?: string
}

export class ResponseErrorType extends Error {
  public msg?: string
  public error?: string
  constructor(msg?: string) {
    super(msg)
    this.msg = msg
    this.error = msg
  }
}

export const TimeGranularityMap = {
  day: '天',
  month: '月',
  year: '年',
} as const

export type MetricModelType = {
  available: boolean
  name: string
  catalogName: string
  databaseName: string
  realTimeUpdate?: boolean
  publishMv: boolean
  createTime: number
  updateTime?: number // 新加修改时间，切换期间过度可能会存在null，适配一下
  dataModelDesc: {
    catalog: string
    factTable: string
    modelType: string
    joinDag: {
      vertices: {
        id: string
        kind: string
        table: string
        dummy: boolean
      }[]
      edges: []
    }
    defaultDtPartition?: boolean
    dataModelDescType: string
    measures?: (MetricFuncType & { columnDesc: { name: string; type: string; comment: string } })[]
    dimensions?: (MetricFuncType & {
      dimensionType?: string
      timeFormatPattern?: string
      granularity?: string
      filterSwitch?: boolean
      columnDesc: { name: string; type: string; comment: string }
    })[]
    timeColumnDesc?: {
      column: {
        type: string
        vertexId: string
        name: string
      }
      formatPattern: (typeof TimeDimensionFormats)[number]
      partition: boolean
      granularity: keyof typeof TimeGranularityMap
    }
    partitionDesc?: {
      partitionMetas: {
        [key: string]: {
          partitionColumn: string
          partitionFormat: string
          isPartitionedTable: boolean
        }
      }
    }
  }
  computeType?: string
  columns?: ColumnDescType[]
  // 物化相关
  refreshType?: 'ADD' | 'INCREMENT' // 物化类型：增量物化、全量物化
  mvIncrementGranularity?: string // 增量物化时间粒度
  maxPartition?: number // 物化数据保留时间
  refreshCron?: string // 全量物化调度时，cron表达式
}

export type MetricModelMeasureType = Exclude<MetricModelType['dataModelDesc']['measures'], undefined>[number]

export type MetricModelDimensionType = Exclude<MetricModelType['dataModelDesc']['dimensions'], undefined>[number]

export type MetricFuncType = {
  name: string
  expression: string
  alias?: string
  nameZh: string
  function?: any
  comment?: string
  synonyms?: string[]
  values?: string[]
  columnDesc?: { vertexId: string; name: string }
  createMetric?: boolean
  filterSwitch?: boolean
  formatTemplate?: string
  granularity?: string
  timeFormatPattern?: string | null
}

export type ColumnDescType = {
  alias?: string
  comment?: string
  derived: boolean
  name: string
  columnType: string
  vertexId: string
}

// 定义 Scene 类型
export interface SceneType {
  id: string
  label: string
  description: string | null
  agent: string | null
  projectName: string | null
  creationUser: string | null
  iconType: number
  creationTime: string
  projectId: string
  /** 场景下对应的 DataModelDesc 的 name，目前最多只有1个，未来可能有多个 */
  modelNames: string[]
  tableName: string
  enableFollowUpQuestion: boolean
  enableMetricExactMatch: boolean
  enableTryQueryUp: boolean
  enableSelectToastWhenEmptyData: boolean
  enableAccMetricToastWhenEmptyData: boolean
}

// 定义 Project 类型
export interface ProjectType {
  id: string
  name: string
  description: string | null
  scenes: SceneType[]
  creationTime: string | null
  updateTime: string | null
  creationUser: string | null
  updateUser: string | null
}

// 定义模型码值类型
// api/engine/v1/metricmodel/listAllColumnCodeValues?modelName=dianxin01_inner_0715&withoutTimeDimensionCodes=true
export type columnCodeValue = {
  columnName: string // 列名
  codeValues: {
    codeValue: string // 主码值
    synonyms: string[] // 码值同义词
  }[]
}
// 指标的发布信息
export type PublishMetric = {
  jobId: string
  status: 'CREATED' | 'RUNNING' | 'SUSPENDED' | 'COMPLETED' | 'FAILED'
  metricId: string
  publishId: string
  jobUrl: string
  mvName: string
  mvCatalog: string
  mvDatabase: string
  metricName: string
}

export interface Tenant {
  id: string
  tenantName: string
  cpuCoreCount: number
  memorySize: number
  diskCapacity: string[]
  createTimestamp: number
  state: 'NORMAL' | 'DISCARDED'
  autoCreateByMeasure?: boolean
  comment?: string
  users: string[]
  catalogName: string
}

export interface TenantListResponse {
  list: Tenant[]
  total: number
}

export interface Tenant {
  id: string
  tenantName: string
  cpuCoreCount: number
  memorySize: number
  diskCapacity: string[]
  userCount: number
  createTimestamp: number
  state: 'NORMAL' | 'DISCARDED'
  autoCreateByMeasure?: boolean
  comment?: string
  users: string[]
}

export interface TenantListResponse {
  list: Tenant[]
  total: number
}

export interface Tenant {
  id: string
  tenantName: string
  cpuCoreCount: number
  memorySize: number
  diskCapacity: string[]
  userCount: number
  createTimestamp: number
  state: 'NORMAL' | 'DISCARDED'
  autoCreateByMeasure?: boolean
  comment?: string
  users: string[]
}

export interface TenantListResponse {
  list: Tenant[]
  total: number
}
