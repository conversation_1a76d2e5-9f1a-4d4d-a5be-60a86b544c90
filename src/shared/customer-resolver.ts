import axios from 'axios'
import { Customer } from './constants'
import { askBIApiUrls } from './url-map'
import { isNodeEnv } from './common-utils'

let CURRENT_CUSTOMER = (isNodeEnv() && process.env['VITE_CUSTOMER']) || ''
let isGetCustomerSuccess = false

function getCustomer() {
  if (!isNodeEnv()) {
    axios.get(askBIApiUrls.env.list).then((res) => {
      isGetCustomerSuccess = true
      const { VITE_CUSTOMER } = res.data?.data || {}
      CURRENT_CUSTOMER = VITE_CUSTOMER || ''
    })
  }
}
// 初始化获取值
getCustomer()
/**
 * 根据环境变量判断是否支持该type
 */
export const customerIsSupportType = (type: string) => {
  !isGetCustomerSuccess && getCustomer()
  if (Customer[CURRENT_CUSTOMER]?.[type] !== undefined) {
    return Boolean(Customer[CURRENT_CUSTOMER]?.[type])
  }
  return Boolean(Customer['DEFAULT']?.[type])
}

/**
 * 根据环境变量去过滤变量
 */
export const customerFilterValue = <T>(type: string, value: T) => {
  !isGetCustomerSuccess && getCustomer()
  const filterItem = Customer[CURRENT_CUSTOMER]?.[type] ?? Customer['DEFAULT']?.[type]
  if (filterItem && typeof filterItem === 'function') {
    return filterItem(value)
  }
  return value as T
}
