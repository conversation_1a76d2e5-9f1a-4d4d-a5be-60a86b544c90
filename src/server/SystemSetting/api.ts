import express, { Router, Request, Response } from 'express'
import { prisma } from '../dao/db'
// import { prisma } from 'src/server/dao/db'
const router: Router = express.Router()

const appearanceConfigKey = 'appearanceConfig'

router.get('/system-setting/appearance/get', async (req: Request, res: Response) => {
  try {
    const data = await prisma.configManage.findUnique({ where: { configKey: appearanceConfigKey } })
    const result = JSON.parse(data?.configValue || '{}')
    return res.json({ code: 0, data: { id: data?.id, config: result } })
  } catch (error: any) {
    console.error(`出现错误，请求地址${req.url}`, error)
    return res.status(500).json({ code: 500, msg: error.message || '服务端错误' })
  }
})

router.post('/system-setting/appearance/update', async (req: Request, res: Response) => {
  try {
    const { body } = req
    const { config } = body
    const result = await prisma.configManage.upsert({
      where: { configKey: appearanceConfigKey },
      update: {
        configValue: JSON.stringify(config || ''),
      },
      create: {
        configValue: JSON.stringify(config || ''),
        configKey: appearanceConfigKey,
        description: '系统设置-外观',
      },
    })

    // const data = { config }
    return res.json({ code: 0, data: result.id })
  } catch (error: any) {
    console.error(`出现错误，请求地址${req.url}`, error)
    return res.status(500).json({ code: 500, msg: error.message || '服务端错误' })
  }
})

export default router
