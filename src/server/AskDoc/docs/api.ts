/**
 * @description 文档上传和管理。
 * 支持多个pdf或者单个pdf上传，支持知识库管理
 * 知识库就是知识库
 */
import express, { Router, Request, Response } from 'express'
import multer from 'multer'
import axios from 'axios'
import FormData from 'form-data'
import { Doc } from '@shared/askdoc-types'
import {
  aiAskDocAddSceneFile,
  aiAskDocCreateFolder,
  aiAskDocDeleteDoc,
  aiAskDocDeleteFolder,
  aiAskDocDeleteSceneFIle,
  aiAskDocEditSceneFile,
  aiAskDocFileListV2,
  aiAskDocUploadFile,
  aiAskDocUploadFolderName,
  aiAskQuestion,
  aiDocSuggestions,
  createDocDir,
  deleteDocDir,
  getAskDocColumnClassify,
  getAskDocGenerateReport,
  getAskDocLibraryId,
  getDocDirList,
  getDocInfoById,
  getDocPermissions,
  getReportColumnValue,
  getReportOpList,
  searchDocIsPermissions,
  setDocPermissions,
  updateDocDirName,
} from '@shared/url-map'
import { DEFAULT_TIMEOUT, MAX_COUNT_UPLOAD_LIMIT } from '@shared/constants'
import { PROCESS_ENV } from 'src/server/server-constants'
import { saveAskBotBusinessLogs } from 'src/server/winston-log'
import { removeDomainFromUrl } from 'src/server/utils'
import { enforcer } from 'src/server/auth'

const router: Router = express.Router()
const upload = multer()

/** ---------------新版接口--------------- */

/**
 * 获取get-library-id
 */
router.post('/get-library-id', async (req: Request, res: Response) => {
  try {
    console.info('ASKDOC_API_FILE_URL', PROCESS_ENV.ASKDOC_API_FILE_URL + getAskDocLibraryId)
    const result = await axios.get(PROCESS_ENV.ASKDOC_API_FILE_URL + getAskDocLibraryId, { params: req.body })
    return res.status(200).json(result.data)
  } catch (error) {
    console.error('【AskDoc】-get-library-id', (error as any).response?.data?.msg || (error as any).message || '')
    return res.status(500).json({
      code: 0,
      msg: '获取get-library-id' + (error as any).response?.data?.msg || (error as any).message || '',
    })
  }
})

/**
 * 根据场景id查询文件列表 融合版本v2
 */
router.get('/list-documents', async (req: Request, res: Response) => {
  try {
    console.info('list-documents', req.body)
    const result = await axios.get(PROCESS_ENV.ASKDOC_API_FILE_URL + aiAskDocFileListV2, { params: req.query })
    const handleData = { ...result.data }
    if (handleData?.data.files.length > 0) {
      handleData?.data.files.forEach((item: Doc) => {
        if (item.thumbnailUrl) {
          item.thumbnailUrl = removeDomainFromUrl(item.thumbnailUrl)
        }
        if (item.sourceUrl) {
          item.sourceUrl = removeDomainFromUrl(item.sourceUrl)
        }
      })
    }

    return res.status(200).json(handleData)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('【AskDoc】-fileList', error)
    return res.status(200).json({
      code: 500,
      msg: errorInfo || '',
    })
  }
})

/**
 * 关联场景文件
 */
router.post('/add-scene-file', async (req: Request, res: Response) => {
  try {
    console.info('add-scene-file', req.body)
    const result = await axios.post(PROCESS_ENV.ASKDOC_API_FILE_URL + aiAskDocAddSceneFile, req.body)
    return res.status(200).json(result.data)
  } catch (error: any) {
    const errorInfo = error?.response?.data?.message || error?.response?.msg || ''
    console.error('【AskDoc】-add-scene-file', error?.response?.data?.message || error?.response?.msg)
    return res.status(200).json({
      code: 500,
      msg: errorInfo || '',
    })
  }
})

/**
 * 推荐的三个问题
 */
router.post('/suggestion-questions', async (req: Request, res: Response) => {
  try {
    console.info('suggestion-questions', req.body)
    const result = await axios.post(PROCESS_ENV.ASKDOC_API_FILE_URL + aiDocSuggestions, req.body)
    return res.status(200).json(result.data)
  } catch (error) {
    console.error('【AskDoc】-suggestion-questions', (error as any).response?.data?.msg || (error as any).message || '')
    return res.status(500).json({
      code: 0,
      msg: '获取推荐问题失败' + (error as any).response?.data?.msg || (error as any).message || '',
    })
  }
})

/**
 * 问问题接口
 */
router.post('/query-document', async (req: Request, res: Response) => {
  const startTime = Date.now()
  const traceId = req.header('traceId') as string
  const username = req.user?.username || ''
  const host = req.header('Host') || ''
  const reqUrl = PROCESS_ENV.ASKDOC_API_FILE_URL + aiAskQuestion
  try {
    res.setHeader('Content-Type', 'text/event-stream')
    res.setHeader('Cache-Control', 'no-cache')
    res.setHeader('Connection', 'keep-alive')
    console.info('query-document', req.body)

    res.flushHeaders()

    axios
      .post(reqUrl, req.body, { responseType: 'stream', timeout: DEFAULT_TIMEOUT * 20 })
      .then((response) => {
        let result = ''
        response.data.on('data', (chunk: Buffer) => {
          const chunkStr = chunk.toString('utf-8')
          // const chunkStr = iconv.decode(chunk, 'utf8')

          result += chunkStr
          // process.stdout.write(chunkStr)
          res.write(chunkStr)
          res.flush()
        })

        response.data.on('end', () => {
          res.end()

          saveAskBotBusinessLogs({
            moduleType: 'docs-query-document',
            host,
            username,
            traceId,
            startTime,
            input: req.body,
            output: result,
            resultCode: 0,
            debug: {
              url: reqUrl,
              payload: req.body,
              method: 'POST',
              response: result,
            },
            req,
          })
        })
        response.data.on('error', (err: Error) => {
          throw err
        })
      })
      .catch((err) => {
        return err
      })
    // 暂时node处理掉后端返回的url中带ip和端口前缀的，需要在下次迭代的时候让后端去掉前缀，不要暴露后台内部的 url 给浏览器
    // const handleData = { ...result.data }
    // if (handleData?.data) {
    //   handleData.data.indexType = req.body.indexType
    //   if (handleData?.data.sourceNodes.imageNodes.length > 0) {
    //     handleData?.data.sourceNodes.imageNodes.forEach((imageNodeItem: AskDocImageNode) => {
    //       if (imageNodeItem.url) {
    //         imageNodeItem.url = removeDomainFromUrl(imageNodeItem.url)
    //       }
    //     })
    //   }
    // }

    // return res.status(200).json(handleData)
  } catch (error: any) {
    saveAskBotBusinessLogs({
      moduleType: 'docs-query-document',
      host,
      username,
      traceId,
      startTime,
      input: req.body,
      output: error,
      resultCode: 500,
      debug: {
        url: reqUrl,
        payload: req.body,
        method: 'POST',
        response: error,
      },
      req,
    })
    console.error('【AskDoc】-query-document-error', error)
    return res.status(200).json({
      code: 0,
      msg: error?.response?.data?.message || error?.response?.msg || '',
      data: null,
    })
  }
})

/**
 *  创建文件夹接口
 */
router.post('/create-folder', async (req: Request, res: Response) => {
  try {
    console.info('create-folder', req.body)
    const result = await axios.post(PROCESS_ENV.ASKDOC_API_FILE_URL + aiAskDocCreateFolder, req.body)
    return res.status(200).json(result.data)
  } catch (error) {
    console.error('【AskDoc】-create-folder', (error as any).response?.data?.msg || (error as any).message || '')
    return res.status(200).json({
      code: 500,
      msg: '创建文件夹失败' + (error as any).response?.data?.msg || (error as any).message || '',
    })
  }
})

/**
 *  修改文件夹名称接口
 */
router.put('/update-folder', async (req: Request, res: Response) => {
  try {
    console.info('update-folder', req.body)
    const result = await axios.put(PROCESS_ENV.ASKDOC_API_FILE_URL + aiAskDocUploadFolderName, req.body)
    return res.status(200).json(result.data)
  } catch (error) {
    console.error('【AskDoc】-update-folder', (error as any).response?.data?.msg || (error as any).message || '')
    return res.status(200).json({
      code: 500,
      msg: '修改文件夹名称失败' + (error as any).response?.data?.msg || (error as any).message || '',
    })
  }
})

/**
 * 删除文件夹接口
 */
router.delete('/delete-folder', async (req: Request, res: Response) => {
  try {
    const result = await axios.delete(PROCESS_ENV.ASKDOC_API_FILE_URL + aiAskDocDeleteFolder, {
      params: req.query,
    })
    return res.status(200).json(result.data)
  } catch (error) {
    console.error('【AskDoc】-delete-folder', (error as any).response?.data?.msg || (error as any).message || '')
    return res.status(200).json({
      code: 500,
      msg: '删除文件夹失败' + (error as any).response?.data?.msg || (error as any).message || '',
    })
  }
})

/**
 * 删除单个文件的接口
 */
router.delete('/delete-doc', async (req: Request, res: Response) => {
  try {
    console.info('delete-doc', req.query)
    const result = await axios.delete(PROCESS_ENV.ASKDOC_API_FILE_URL + aiAskDocDeleteDoc, {
      params: req.query,
    })
    return res.status(200).json(result.data)
  } catch (error: any) {
    console.error('【AskDoc】-delete-doc', error?.response?.data?.message || error?.response?.msg || '')
    return res.json({ code: 500, msg: '删除文件失败' + error?.response?.data?.message || error?.response?.msg || '' })
  }
})

/**
 *  新的上传文件接口
 */
router.post('/upload-file', upload.array('files', MAX_COUNT_UPLOAD_LIMIT), async (req: Request, res: Response) => {
  const { libraryId, folderId, parserType, uploadType, dirId } = req.body
  const file = (req.files as Express.Multer.File[])[0]
  const originalnameUTF8 = Buffer.from(file.originalname, 'latin1').toString('utf8')
  const startTime = Date.now()
  const traceId = req.header('traceId') as string
  const username = req.user?.username || ''
  const host = req.header('Host') || ''
  const reqUrl = PROCESS_ENV.ASKDOC_API_FILE_URL + aiAskDocUploadFile
  if (!file) {
    return res.status(400).json({ code: 400, msg: '未接收到文件' })
  }

  const form = new FormData()
  form.append('file', file.buffer, { filename: originalnameUTF8 })
  libraryId && form.append('libraryId', libraryId)
  folderId && form.append('folderId', folderId)
  dirId && form.append('dirId', dirId)
  form.append('parserType', parserType)
  form.append('uploadType', uploadType)
  form.append('username', req.user?.username)

  try {
    const result = await axios.post(reqUrl, form, {
      timeout: 2 * 60 * 1000, // 可能会有大文件上传， 所以设置2分钟
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
    saveAskBotBusinessLogs({
      moduleType: 'docs-upload-file',
      host,
      username,
      traceId,
      startTime,
      input: form,
      output: result.data,
      resultCode: result.data.code,
      debug: {
        url: reqUrl,
        payload: req.body,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        method: 'POST',
        response: result.data,
      },
      req,
    })
    return res.status(200).json(result.data)
  } catch (error: any) {
    saveAskBotBusinessLogs({
      moduleType: 'docs-upload-file',
      host,
      username,
      traceId,
      startTime,
      input: form,
      output: error,
      resultCode: 500,
      debug: {
        url: reqUrl,
        payload: req.body,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        method: 'POST',
        response: error,
      },
      req,
    })
    console.error('【AskDoc】-upload-file', error?.response?.data?.message || error?.response?.msg)
    return res.json({ code: 500, msg: error?.response?.data?.message || error?.response?.msg || '' })
  }
})

/**
 *  获取文档字段分类情况
 */
router.get('/column-classify', async (req: Request, res: Response) => {
  try {
    console.info('get column-classify', req.query)
    const result = await axios.get(PROCESS_ENV.ASKDOC_API_FILE_URL + getAskDocColumnClassify, {
      params: req.query,
      timeout: 2 * 60 * 1000,
    })
    return res.status(200).json(result.data)
  } catch (error: any) {
    console.error('【AskDoc】获取column-classify', error?.response?.data?.message || error?.response?.msg)
    return res.json({
      code: 500,
      msg: `获取文档字段分类情况失败${error?.response?.data?.message || error?.response?.msg || ''}`,
    })
  }
})

/**
 *  修改字段分类情况
 */
router.post('/column-classify', async (req: Request, res: Response) => {
  try {
    console.info('column-classify', req.body)
    const result = await axios.post(PROCESS_ENV.ASKDOC_API_FILE_URL + getAskDocColumnClassify, req.body)
    return res.status(200).json(result.data)
  } catch (error: any) {
    console.error('【AskDoc】修改 column-classify', error?.response?.data?.message || error?.response?.msg)
    return res.json({
      code: 500,
      msg: `修改字段分类情况失败${error?.response?.data?.message || error?.response?.msg || ''}`,
    })
  }
})

/**
 *  生成报告
 */
router.post('/generate-report', async (req: Request, res: Response) => {
  const startTime = Date.now()
  const traceId = req.header('traceId') as string
  const username = req.user?.username || ''
  const host = req.header('Host') || ''
  const reqUrl = PROCESS_ENV.ASKDOC_API_FILE_URL + getAskDocGenerateReport
  try {
    const result = await axios.post(reqUrl, req.body, {
      timeout: 5 * 60 * 1000,
    })
    saveAskBotBusinessLogs({
      moduleType: 'docs-generate-report',
      host,
      username,
      traceId,
      startTime,
      input: req.body,
      output: result.data,
      resultCode: result.data.code,
      debug: {
        url: reqUrl,
        payload: req.body,
        method: 'POST',
        response: result.data,
      },
      req,
    })
    return res.status(200).json(result.data)
  } catch (error: any) {
    saveAskBotBusinessLogs({
      moduleType: 'docs-generate-report',
      host,
      username,
      traceId,
      startTime,
      input: req.body,
      output: error,
      resultCode: 500,
      debug: {
        url: reqUrl,
        payload: req.body,
        method: 'POST',
        response: error,
      },
      req,
    })
    console.error('【AskDoc】generate-report', error?.response?.data?.message || error?.response?.msg || '')
    return res.json({
      code: 500,
      msg: `生成报告失败${error?.response?.data?.message || error?.response?.msg || ''}`,
    })
  }
})

/**
 *  删除场景文件
 */
router.post('/delete-scene-file', async (req: Request, res: Response) => {
  try {
    console.info('post delete-scene-file', req.body)
    const result = await axios.post(PROCESS_ENV.ASKDOC_API_FILE_URL + aiAskDocDeleteSceneFIle, req.body)
    return res.status(200).json(result.data)
  } catch (error: any) {
    console.error('【AskDoc】delete-scene-file', error?.response?.data?.message || error?.response?.msg)
    return res.json({
      code: 500,
      msg: `删除场景文件失败${error?.response?.data?.message || error?.response?.msg || ''}`,
    })
  }
})

/**
 *  编辑文件
 */
router.post('/edit-file', async (req: Request, res: Response) => {
  try {
    console.info('post edit-file', req.body)
    const result = await axios.post(PROCESS_ENV.ASKDOC_API_FILE_URL + aiAskDocEditSceneFile, req.body)
    return res.status(200).json(result.data)
  } catch (error: any) {
    console.error('【AskDoc】edit-file', error?.response?.data?.message || error?.response?.msg || '')
    return res.json({
      code: 500,
      msg: `编辑文件失败${error?.response?.data?.message || error?.response?.msg || ''}`,
    })
  }
})

/**
 *  获取运算符列表
 */
router.get('/op-list', async (req: Request, res: Response) => {
  try {
    console.info('get op-list', req.query)
    const result = await axios.get(PROCESS_ENV.ASKDOC_API_FILE_URL + getReportOpList)
    return res.status(200).json(result.data)
  } catch (error: any) {
    console.error('get op-list', error?.response?.data?.message || error?.response?.msg)
    return res.json({
      code: 500,
      msg: `获取运算符列表失败${error?.response?.data?.message || error?.response?.msg || ''}`,
    })
  }
})

/**
 *  获取维度码值
 */
router.get('/column-value', async (req: Request, res: Response) => {
  try {
    console.info('get column-value', req.query)
    const result = await axios.get(PROCESS_ENV.ASKDOC_API_FILE_URL + getReportColumnValue, {
      params: req.query,
      timeout: 2 * 60 * 1000,
    })
    return res.status(200).json(result.data)
  } catch (error: any) {
    console.error('获取column-value', error?.response?.data?.message || error?.response?.msg)
    return res.json({
      code: 500,
      msg: `获取维度码值失败${error?.response?.data?.message || error?.response?.msg || ''}`,
    })
  }
})

router.get('/fileUrlProxy', async (req: Request, res: Response) => {
  try {
    const fileType = decodeURIComponent(req.query.m as string)
    const backendUrl = PROCESS_ENV.ASKDOC_FILE_PREVIEW_URL + req.query.p
    const response = await axios.get(backendUrl, { responseType: 'arraybuffer' })
    console.info(' mimeType', fileType)
    console.info(' req.query.p', req.query.p)
    console.info('1111fileType', fileType)
    switch (fileType) {
      case 'application/pdf':
      case 'application/vnd.ms-powerpoint':
      case 'application/vnd.openxmlformats-officedocument.presentationml.presentation':
        res.set('Content-Disposition', 'inline')
        res.send(Buffer.from(response.data, 'binary'))
        break
      case 'text/html':
        res.removeHeader('Content-Disposition')
        res.set('Content-Type', fileType)
        res.send(Buffer.from(response.data, 'binary'))
        break
      case 'application/msword':
      case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document': {
        const fileResponse = await fetch(backendUrl)
        const data = await fileResponse.blob()
        res.setHeader('Content-Type', data.type)
        res.setHeader('Content-Disposition', 'attachment')
        const buffer = await data.arrayBuffer()
        const uint8Array = new Uint8Array(buffer)
        res.end(uint8Array)
        break
      }
      case 'application/vnd.ms-excel':
      case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
      case 'text/csv':
      case 'image/jpeg':
      default:
        res.send(response.data)
        break
    }
  } catch (err) {
    console.error('文件解析失败', err)
    return res.status(200).json({
      code: 500,
      data: null,
      msg: `文件解析失败${(err as Error)?.message}`,
    })
  }
})

router.get('/downloadFileProxy', async (req: Request, res: Response) => {
  try {
    const backendUrl = PROCESS_ENV.ASKDOC_FILE_PREVIEW_URL + req.query.p
    const parsedUrl = new URL(backendUrl)
    const pathname = parsedUrl.pathname
    const filename = pathname.split('/').pop()
    const response = await axios.get(backendUrl, { responseType: 'stream' })
    res.setHeader('Content-disposition', `attachment; filename=${filename}`)
    response.data.pipe(res)
  } catch (err) {
    console.error('文件下载失败', err)
    return res.status(200).json({
      code: 500,
      data: null,
      msg: `文件下载失败${(err as Error)?.message}`,
    })
  }
})

/**
 * 推荐问题，调用 AI
 */
router.get('/suggestions', async (req: Request, res: Response) => {
  const { folderId } = req.query
  if (!folderId) {
    return res.status(200).json({
      code: 400,
      data: null,
      msg: 'folderId 不能为空',
    })
  }

  try {
    const result = await axios.get(PROCESS_ENV.ASKDOC_API_FILE_URL + aiDocSuggestions, {
      params: {
        folderId,
      },
    })
    return res.status(200).json(result.data)
  } catch (err) {
    console.error('推荐问题接口调用失败', err)
    return res.status(200).json({
      code: 500,
      data: null,
      msg: '调用 AI 接口失败' + (err as Error)?.message,
    })
  }
})

/**
 * DOC 目录权限
 */

/**
 *  POST 新建文件夹
 */
router.post('/dir/create', async (req: Request, res: Response) => {
  try {
    console.info('post dir/create', req.body)
    const result = await axios.post(PROCESS_ENV.ASKDOC_API_FILE_URL + createDocDir, req.body)
    return res.status(200).json(result.data)
  } catch (error: any) {
    console.error('【AskDoc目录权限】dir/create', error)
    console.error('【AskDoc目录权限】dir/create', error?.response?.data?.message || error?.response?.msg || '')
    return res.json({
      code: 500,
      msg: `新建文件夹失败${error?.response?.data?.message || error?.response?.msg || ''}`,
    })
  }
})
/**
 *  POST 设置权限
 */
router.post('/perm', async (req: Request, res: Response) => {
  try {
    console.info('post docs/perm', req.body)
    const result = await axios.post(PROCESS_ENV.ASKDOC_API_FILE_URL + setDocPermissions, req.body)
    return res.status(200).json(result.data)
  } catch (error: any) {
    console.error('【AskDoc目录权限】docs/perm', error?.response?.data?.message || error?.response?.msg || '')
    return res.json({
      code: 500,
      msg: `设置权限失败${error?.response?.data?.message || error?.response?.msg || ''}`,
    })
  }
})
/**
 *  POST 修改文件夹名称
 */
router.post('/dir/update', async (req: Request, res: Response) => {
  try {
    console.info('post dir/update', req.body)
    const result = await axios.post(PROCESS_ENV.ASKDOC_API_FILE_URL + updateDocDirName, req.body)
    return res.status(200).json(result.data)
  } catch (error: any) {
    console.error('【AskDoc目录权限】dir/update', error?.response?.data?.message || error?.response?.msg || '')
    return res.json({
      code: 500,
      msg: `修改文件夹名称失败${error?.response?.data?.message || error?.response?.msg || ''}`,
    })
  }
})
/**
 *  POST 删除文档
 */
router.post('/dir-document-delete', async (req: Request, res: Response) => {
  try {
    console.info('post dir-document-delete', req.body)
    const result = await axios.post(PROCESS_ENV.ASKDOC_API_FILE_URL + deleteDocDir, req.body)
    return res.status(200).json(result.data)
  } catch (error: any) {
    console.error('【AskDoc目录权限】dir-document-delete', error?.response?.data?.message || error?.response?.msg || '')
    return res.json({
      code: 500,
      msg: `删除文档失败${error?.response?.data?.message || error?.response?.msg || ''}`,
    })
  }
})
/**
 *  POST 文档列表(目录版)
 */
router.post('/list-dir-documents', async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id || ''
    console.info('list-dir-documents接口 - user：', req.user?.id, req.user?.username)
    const { roleIds } = req.body
    if (!Array.isArray(roleIds) || roleIds.length === 0) {
      const roles = await enforcer.getRolesFromUserId(userId)
      if (roles?.length > 0) {
        req.body.roleIds = roles.map((role) => role.id)
      } else {
        return res.json({
          code: 403,
          msg: '该用户角色为空，无权限访问，请联系管理员配置',
        })
      }
    }
    console.info('post list-dir-documents', req.body)

    const result = await axios.post(PROCESS_ENV.ASKDOC_API_FILE_URL + getDocDirList, req.body)
    const handleData = { ...result.data }
    if (handleData?.data.files.length > 0) {
      handleData?.data.files.forEach((item: Doc) => {
        if (item.thumbnailUrl) {
          item.thumbnailUrl = removeDomainFromUrl(item.thumbnailUrl)
        }
        if (item.sourceUrl) {
          item.sourceUrl = removeDomainFromUrl(item.sourceUrl)
        }
      })
    }

    return res.json(handleData)
  } catch (error: any) {
    console.error('【AskDoc目录权限】list-dir-documents', error?.response?.data?.message || error?.response?.msg || '')
    return res.json({
      code: 500,
      msg: `获取文档列表失败${error?.response?.data?.message || error?.response?.msg || ''}`,
    })
  }
})
/**
 * 查询目录权限
 */
router.get('/document-perm', async (req: Request, res: Response) => {
  try {
    const result = await axios.get(PROCESS_ENV.ASKDOC_API_FILE_URL + getDocPermissions, {
      params: req.query,
    })
    return res.json(result.data)
  } catch (error: any) {
    console.error('查询目录权限权限失败', error?.response?.data?.message || error?.response?.msg)
    return res.json({
      code: 500,
      msg: `查询目录权限失败${error?.response?.data?.message || error?.response?.msg || ''}`,
    })
  }
})
/**
 * 根据文档id列表查询文档信息
 */
router.get('/document-info-list', async (req: Request, res: Response) => {
  try {
    const result = await axios.get(PROCESS_ENV.ASKDOC_API_FILE_URL + getDocInfoById, {
      params: req.query,
    })
    const handleData = { ...result.data }
    if (handleData?.data.files.length > 0) {
      handleData?.data.files.forEach((item: Doc) => {
        if (item.thumbnailUrl) {
          item.thumbnailUrl = removeDomainFromUrl(item.thumbnailUrl)
        }
        if (item.sourceUrl) {
          item.sourceUrl = removeDomainFromUrl(item.sourceUrl)
        }
      })
    }

    return res.json(handleData)
  } catch (error: any) {
    console.error('根据文档id列表查询文档信息失败', error?.response?.data?.message || error?.response?.msg)
    return res.json({
      code: 500,
      msg: `根据文档id列表查询文档信息失败${error?.response?.data?.message || error?.response?.msg || ''}`,
    })
  }
})

/**
 * 判断是否有无编辑权限
 */
router.post('/document-max-perm', async (req: Request, res: Response) => {
  try {
    console.info('post document-max-perm', req.body)
    const result = await axios.post(PROCESS_ENV.ASKDOC_API_FILE_URL + searchDocIsPermissions, req.body)
    return res.json(result.data)
  } catch (error: any) {
    console.error('【AskDoc目录权限】document-max-perm', error?.response?.data?.message || error?.response?.msg || '')
    return res.json({
      code: 500,
      msg: `获取权限失败${error?.response?.data?.message || error?.response?.msg || ''}`,
    })
  }
})

export default router
