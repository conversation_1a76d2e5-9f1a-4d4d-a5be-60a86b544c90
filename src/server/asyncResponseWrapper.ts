import { Handler } from 'express'

export function asyncResponseWrapper(fn: Handler) {
  return async (...args: Parameters<Handler>) => {
    const [, res] = args
    try {
      await fn(...args)
    } catch (err: any) {
      console.info('===> ERROR', err?.message ?? err?.toString() ?? 'Server Error')
      console.error(err)
      return res.json({ code: 1, msg: err?.message ?? err?.toString() ?? 'Server Error' })
    }
  }
}
