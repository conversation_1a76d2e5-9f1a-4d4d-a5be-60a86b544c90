/* eslint-disable @typescript-eslint/naming-convention */
import { XGroup, XRole } from '@prisma/client'
import express, { Router, Request, Response } from 'express'
import _ from 'lodash'
import axios from 'axios'
import jwt from 'jsonwebtoken'
import {
  AuthAdminUserListPostRequestSchema,
  contactUserData,
  encodeResourceRule,
  extractInfoFromEnforcer,
  resourceTypeList,
  ResourceTypes,
} from 'src/shared/auth'
import { LlmType } from 'src/shared/common-types'
import { PageParam } from 'src/shared/page-param'
import { DEFAULT_SCENE_ID } from 'src/shared/metric-types'
import { asyncResponseWrapper } from 'src/server/asyncResponseWrapper'
import { createUser, ALL_LLMS, decryptUserId, createResource } from '../utils'
import { PROCESS_ENV, rangerGetAuthToken, rangerLoginUrl } from '../server-constants'
import MetricConfig from '../MetricStore/metric-config'
import Metric2Sql from '../MetricStore/metric2sql/metric2sql'
import { prisma } from '../dao/db'
import { jwtOptions, passportMiddleware } from './passport'
import { enforcer } from './enforcer'

async function getTokenFromRanger(username: string, password: string) {
  const base64Encoded = Buffer.from(password).toString('base64')
  const jsonData: {
    userName: string
    password: string
  } = {
    userName: username,
    password: base64Encoded,
  }
  console.info('Login Ranger Data', jsonData)
  try {
    let authToken = ''
    try {
      authToken = (await axios.get(rangerGetAuthToken)).data.data
    } catch (error) {
      console.error('rangerGetAuthToken error', error)
    }
    const res: any = await Promise.race([
      await axios.post(rangerLoginUrl, jsonData, {
        headers: {
          'unique-token': authToken, // 将 token 添加到 header
        },
      }),
      // 超时
      new Promise((_, reject) => {
        setTimeout(() => {
          reject(new Error('Timeout'))
        }, 3000)
      }),
    ])
    if (res.data.code === 0) {
      return res.data.data
    } else {
      console.error('Login with range does not have cookie.')
      return null
    }
  } catch (error: any) {
    if (error instanceof Error && error.message) {
      console.error(error.message, 'login ranger error')
    }
    if (error?.response?.code === 273) {
      console.error('Ranger账号或密码错误')
    }
    return null
  }
}
function createRouter() {
  const router: Router = express.Router()

  router.post(
    '/login',
    asyncResponseWrapper(async (req: Request, res: Response) => {
      const { username, password, isEncryption } = req.body
      const localUsername = isEncryption ? decryptUserId(username) : username
      const data: Record<string, any> = {}
      // 只用ranger 进行登录
      if (PROCESS_ENV.AUTH_LOGIN_SYSTEM === 'ranger') {
        const userInfo = await getTokenFromRanger(username, decryptUserId(password))
        console.info('Login with ranger', userInfo)
        if (userInfo) {
          data.ranger = { token: userInfo.jwtToken, uToken: userInfo.token }
        } else {
          return res.json({
            code: 400,
            data: {},
            msg: '账号或密码错误',
            message: '账号或密码错误',
          })
        }

        // 初始化角色 已经在 prisma seed.ts 做了兼容。
        let initRole = await prisma.xRole.findFirst({
          where: {
            roleName: 'init_role_with_all_scene_project_read_permission',
          },
        })

        if (!initRole) {
          initRole = await prisma.xRole.create({
            data: {
              roleName: 'init_role_with_all_scene_project_read_permission',
            },
          })
        }

        let user = await prisma.xUser.findFirst({ where: { username: localUsername } })
        // 客户不提供 mysql 的情况下，节点漂移登录态丢失，重新登录 ranger token 需要创建用户。
        // prisma/seed.ts 已经创建了角色和对应的资源
        if (!user) {
          user = await createUser({ username: localUsername, password, roles: [initRole.id] })
        }

        if (!user) throw new Error('登录失败')

        const token = jwt.sign({ username: user.username, id: user.id }, jwtOptions.secretOrKey, {
          expiresIn: '24h',
        })
        data.token = token

        return res.json({
          code: 0,
          data,
        })
      }
      const user = await prisma.xUser.findFirst({ where: { username: localUsername } })
      if (!(user && user.password === password)) throw new Error('用户名或密码错误')
      // 如果用户验证通过，生成JWT
      const token = jwt.sign({ username: user.username, id: user.id }, jwtOptions.secretOrKey, {
        expiresIn: '24h',
      })
      data.token = token
      if (user.rangerUsername && user.rangerPassword) {
        const userInfo = await getTokenFromRanger(user.rangerUsername, decryptUserId(user.rangerPassword))
        console.info('Login with ranger', userInfo)
        if (userInfo) {
          data.ranger = { token: userInfo.jwtToken, uToken: userInfo.token }
        }
      }
      res.json({
        code: 0,
        data,
      })
    }),
  )

  // TODO 这个 在浦发上可能不行  浦发的登录信息存储在 session storage 里面
  router.post(
    '/logout',
    asyncResponseWrapper(async (req, res) => {
      try {
        if (req.headers.cookie) {
          const cookies = req.headers.cookie.split('; ')
          cookies.forEach((cookie) => {
            const [name] = cookie.split('=')
            res.clearCookie(name)
          })
        }
        return res.json({ code: 0, data: {}, msg: '退出登录成功' })
      } catch (error) {
        console.error('Logout error: ' + error)
        return res.json({ code: 0, msg: '退出登录失败' })
      }
    }),
  )

  // 需要JWT验证的受保护路由示例
  router.get(
    '/user-info',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      const userInfo = req.user!
      const groups = await enforcer.getGroupsFromUserId(userInfo.id)
      // 兼容老版本代码，把role提取到group数组中
      const roles = await enforcer.getRolesFromUserId(userInfo.id)
      return res.json({
        code: 0,
        data: {
          id: userInfo.id,
          username: userInfo.username,
          groups: groups.concat(roles.map((v) => ({ id: v.id, groupName: v.roleName }))),
        },
      })
    }),
  )

  // const projectsCache: Record<string, any> = {}
  router.get(
    '/projects',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      console.info('====> GET /api/auth/projects')
      const userInfo = req.user!
      // const projects =
      //   projectsCache[userInfo.id] ?? (projectsCache[userInfo.id] = await enforcer.getProjectsFromUserId(userInfo.id))
      return res.json({
        code: 0,
        data: {
          projects: await enforcer.getProjectsFromUserId(userInfo.id),
          DEFAULT_SELECT_PROJECT: PROCESS_ENV.DEFAULT_SELECT_PROJECT,
          DEFAULT_PROJECT: PROCESS_ENV.DEFAULT_PROJECT,
          DEFAULT_SCENE: PROCESS_ENV.DEFAULT_SCENE,
        },
      })
    }),
  )

  router.get(
    '/llms',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      const userInfo = req.user!
      const llmList = await enforcer.getLlmsFromUserId(userInfo.id)
      // 获取当前用户的默认数据源
      const defaultLlmType: LlmType = llmList.some((llm) => llm.type === PROCESS_ENV.DEFAULT_LLM_MODEL)
        ? (PROCESS_ENV.DEFAULT_LLM_MODEL as LlmType)
        : llmList.length > 0
          ? llmList[0].type
          : (PROCESS_ENV.DEFAULT_LLM_MODEL as LlmType)
      return res.json({ code: 0, data: { llmList, defaultLlmType } })
    }),
  )

  router.get(
    '/metrics',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      let sceneId = req.query.sceneId as string
      console.info('Get metric list with query:', req.query)
      // console.info('Get metric req.user?.username:', req.user?.username)
      const userInfo = req.user!
      const username = userInfo.username || ''
      if (sceneId === DEFAULT_SCENE_ID) {
        const projects = await enforcer.getProjectsFromUserId(username)
        sceneId = projects[0]?.semanticScenes[0].id || ''
      }

      try {
        const metricConfig = await MetricConfig.createBySceneId(sceneId, false)
        const metric2Sql = new Metric2Sql(metricConfig)
        const metricList = metricConfig.allMetrics
        const hotMetricList = metricConfig.hotMetrics

        // Assign displayExpr to each metric
        metricList.forEach((metric) => (metric.displayExpr = metric2Sql.getMetricDisplayExpr(metric)))
        hotMetricList.forEach((metric) => (metric.displayExpr = metric2Sql.getMetricDisplayExpr(metric)))
        return res.json({
          code: 0,
          data: {
            metricTableName: metricConfig.name,
            allDimensions: metricConfig.allDimensions,
            allMeasures: metricConfig.allMeasures,
            allMetrics: metricList,
            hotMetrics: hotMetricList,
            allExternalReports: metricConfig.allExternalReports,
            timeDimensionDatum: metricConfig.timeDimensionDatum,
          },
        })
      } catch (error) {
        console.error('获取该model下面的metric信息失败 ' + (error as Error)?.message, error)
        return res.json({ code: 500, msg: '获取该model下面的metric信息失败 ' + (error as Error)?.message })
      }
    }),
  )

  router.get(
    '/project/list',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      console.info('GET /project/list', req.query)
      const user = req.user!
      const projects = await enforcer.getProjectsFromUserId(user.id)
      return res.json({ code: 0, data: { list: projects, total: projects.length } })
    }),
  )

  router.post(
    '/project',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      console.info('POST', req.url, req.body)
      const { description, name } = req.body
      const data = await prisma.semanticProject.create({
        data: {
          name,
          description,
        },
      })
      return res.json({ code: 0, data })
    }),
  )

  router.get(
    '/project',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      console.info('GET /project', req.query)
      const { id } = req.query
      const data = await prisma.semanticProject.findFirst({
        where: { id: id as string },
      })
      if (!data) throw new Error(`项目${id}不存在`)

      return res.json({ code: 0, data })
    }),
  )

  router.delete(
    '/project',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      console.info('DELETE /project', req.body)
      const { id } = req.body

      await prisma.$transaction(async () => {
        await prisma.semanticScene.deleteMany({ where: { semanticProjectId: id } })
        await prisma.semanticMetric.deleteMany({ where: { semanticProjectId: id } })
        await prisma.semanticExternalReport.deleteMany({ where: { semanticProjectId: id } })
        await prisma.semanticMetricTreeRoot.deleteMany({ where: { semanticProjectId: id } })
        await prisma.semanticMetricTree.deleteMany({ where: { semanticProjectId: id } })
        await prisma.semanticProject.delete({
          where: { id: id as string },
        })
      })

      return res.json({ code: 0 })
    }),
  )

  router.get(
    '/scene/list',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      console.info('/scene/list', req.query)
      const user = req.user!
      const projects = await enforcer.getProjectsFromUserId(user.id)

      const { projectId } = req.query

      if (projectId) {
        const project = projects.find((v) => v.id === projectId)
        if (!project) throw new Error('没有项目权限')
        return res.json({ code: 0, data: { list: project.semanticScenes, total: project.semanticScenes.length } })
      }
      const scenes = projects.flatMap((v) => v.semanticScenes)
      return res.json({ code: 0, data: { list: scenes, total: scenes.length } })
    }),
  )

  router.get(
    '/scene',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      console.info('/scene', req.query)
      const { id } = req.query
      const data = await prisma.semanticScene.findFirst({
        where: { id: id as string },
        include: {
          semanticProject: {
            select: { id: true, name: true },
          },
        },
      })
      if (!data) throw new Error(`场景${id}不存在`)

      const modelNames = await prisma.semanticScenesModels.findMany({
        where: { sceneId: data.id },
      })
      ;(data as any).modelNames = modelNames.map((v) => v.modelName)

      return res.json({ code: 0, data })
    }),
  )

  router.delete(
    '/scene',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      console.info('DELETE /scene', req.body)
      const { id } = req.body
      const data = Promise.all([
        prisma.semanticScene.delete({ where: { id } }),
        prisma.semanticScenesModels.deleteMany({ where: { sceneId: id } }),
      ])
      return res.json({ code: 0, data })
    }),
  )

  router.post(
    '/scene',
    asyncResponseWrapper(async (req, res) => {
      const {
        agent,
        creationUser,
        label,
        modelId,
        modelNames,
        projectId,
        tableName,
        timeDimensionFormat,
        timeGranularityMin,
      } = req.body

      if (await prisma.semanticScene.count({ where: { label } })) throw new Error('场景已存在')
      const scene = await prisma.semanticScene.create({
        data: {
          label,
          tableName,
          agent,
          semanticProjectId: projectId,
          timeDimensionFormat,
          timeGranularityMin,
          createdBy: creationUser,
          updatedBy: creationUser,
          modelId,
        },
      })
      modelNames &&
        modelNames.length > 0 &&
        (await prisma.semanticScenesModels.createMany({
          data: modelNames.map((modelName: string) => ({
            sceneId: scene.id,
            modelName,
          })),
        }))
      return res.json({ code: 0, data: scene })
    }),
  )

  router.put(
    '/scene',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      console.info('PUT /scene', req.body)
      const {
        id,
        modelNames,
        tableName,
        timeDimensionFormat,
        timeDimensionType,
        timeGranularityMin,
        enableAccMetricToastWhenEmptyData,
        enableFollowUpQuestion,
        enableMetricExactMatch,
        enableSelectToastWhenEmptyData,
        enableTryQueryUp,
      } = req.body
      const scene = await prisma.semanticScene.update({
        where: { id },
        data: {
          tableName,
          timeDimensionFormat,
          timeDimensionType,
          timeGranularityMin,
          enableAccMetricToastWhenEmptyData,
          enableFollowUpQuestion,
          enableMetricExactMatch,
          enableSelectToastWhenEmptyData,
          enableTryQueryUp,
        },
      })
      if (Array.isArray(modelNames)) {
        await prisma.$transaction(async () => {
          await prisma.semanticScenesModels.deleteMany({ where: { sceneId: id } })
          await prisma.semanticScenesModels.createMany({
            data: modelNames.map((modelName: string) => ({ sceneId: id, modelName })),
          })
        })
      }
      return res.json({ code: 0, data: scene })
    }),
  )

  router.get(
    '/admin/user/list',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      console.info('GET /admin/user/list', req.query)
      const { success, data, error } = AuthAdminUserListPostRequestSchema.safeParse(req.query)
      if (!success) {
        const message = error.errors
          .map((error) => `${error.path.join('.')} is ${error.message.toLowerCase()}`)
          .join(', ')
        throw new Error(`参数错误: ${message}`)
      }
      const pageParam = PageParam.from(data)
      const total = await prisma.xUser.count({
        where: {
          username: { contains: data.name },
        },
      })
      const users = await prisma.xUser.findMany({
        skip: pageParam.skip,
        take: pageParam.take,
        select: {
          id: true,
          username: true,
          createdAt: true,
          rangerUsername: true,
        },
        where: {
          username: { contains: data.name },
        },
      })
      type User = (typeof users)[number] & { roles: XRole[]; groups: XGroup[] }

      const gPolicies = await Promise.all(
        users.map((v) => enforcer.e.getFilteredGroupingPolicy(0, contactUserData(v.id), '')),
      )

      const userInfoList: { groups: string[]; roles: string[] }[] = []
      for (let i = 0; i < users.length; i++) {
        const user = users[i] as User
        user.groups = []
        user.roles = []
        const item: (typeof userInfoList)[number] = { groups: [], roles: [] }
        for (const p of gPolicies[i]) {
          const { type, id } = extractInfoFromEnforcer(p[1])
          if (type === 'group') {
            item.groups.push(id)
          } else if (type === 'role') {
            item.roles.push(id)
          }
        }
        userInfoList.push(item)
      }

      await Promise.all(
        users.map(async (v, i) => {
          const info = userInfoList[i]
          const user = v as User
          user.groups = await prisma.xGroup.findMany({ where: { id: { in: info.groups } } })
          user.roles = await prisma.xRole.findMany({ where: { id: { in: info.roles } } })
        }),
      )

      return res.json({ code: 0, data: { list: users, total } })
    }),
  )

  router.post(
    '/admin/user',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      console.info('POST /admin/user', req.body)
      const { username, password, roles, groups, rangerUsername, rangerPassword, roleNames, groupNames } = req.body
      const isExist = await prisma.xUser.findFirst({ where: { username } })
      if (isExist) throw new Error('用户名不能重复')
      const user = await prisma.xUser.create({
        data: {
          username,
          password,
          rangerUsername,
          rangerPassword,
        },
      })
      if (Array.isArray(roles)) {
        await enforcer.unionUserRole({
          v0: { id: user.id, type: 'user' },
          v1: { type: 'roles', ids: roles },
        })
      }
      if (Array.isArray(roleNames)) {
        const idListFromNames = (
          await prisma.xRole.findMany({ where: { roleName: { in: roleNames } }, select: { id: true } })
        ).map((v) => v.id)
        await enforcer.unionUserRole({
          v0: { id: user.id, type: 'user' },
          v1: { type: 'roles', ids: idListFromNames },
        })
      }
      if (Array.isArray(groups)) {
        await enforcer.unionUserRole({
          v0: { id: user.id, type: 'user' },
          v1: { type: 'groups', ids: groups },
        })
      }
      if (Array.isArray(groupNames)) {
        const idListFromNames = (
          await prisma.xGroup.findMany({ where: { groupName: { in: groupNames } }, select: { id: true } })
        ).map((v) => v.id)
        await enforcer.unionUserRole({
          v0: { id: user.id, type: 'user' },
          v1: { type: 'groups', ids: idListFromNames },
        })
      }
      return res.json({ code: 0, data: user })
    }),
  )

  router.post(
    '/admin/user/update',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      console.info('UPDATE /admin/user', req.body)
      const { username, id, password, roles, groups, rangerUsername, rangerPassword } = req.body
      if (!id) throw new Error('ID不存在')
      if (username) {
        const isExist = await prisma.xUser.findFirst({ where: { username } })
        if (isExist) throw new Error('用户名不能重复')
      }
      const data = { username, password, rangerUsername, rangerPassword }
      const updateRes = await prisma.xUser.update({
        data,
        where: { id },
        select: { id: true, username: true, createdAt: true },
      })
      if (Array.isArray(roles)) {
        await enforcer.unionUserRole({ v0: { id, type: 'user' }, v1: { type: 'roles', ids: roles } })
      }
      if (Array.isArray(groups)) {
        await enforcer.unionUserRole({ v0: { id, type: 'user' }, v1: { type: 'groups', ids: groups } })
      }
      return res.json({ code: 0, data: updateRes })
    }),
  )

  router.delete(
    '/admin/user',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      const { id } = req.body
      if (!id) throw new Error('ID不存在')
      const user = await prisma.xUser.findFirst({ where: { id } })
      if (!user) throw new Error('用户不存在')
      await prisma.xUser.delete({ where: { id } })
      await prisma.casbinRule.deleteMany({ where: { id: contactUserData(id) } })
      await enforcer.refresh()
      return res.json({ code: 0 })
    }),
  )

  router.get(
    '/admin/role/list',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      console.info('GET /admin/role/list', req.query)
      const roles = await prisma.xRole.findMany({
        // skip,
        // take: pageSize,
        orderBy: {
          createdAt: 'desc',
        },
        select: {
          id: true,
          roleName: true,
          createdAt: true,
        },
        where: {
          roleName:
            typeof req.query.roleName === 'string'
              ? {
                  contains: req.query.roleName,
                }
              : undefined,
        },
      })

      return res.json({ code: 0, data: { list: roles, total: roles.length } })
    }),
  )

  router.post(
    '/admin/role',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      const { roleName } = req.body
      const isExist = await prisma.xRole.findFirst({ where: { roleName } })
      if (isExist) {
        return res.json({ code: 1, msg: '角色名不能重复' })
      }
      const createRes = await prisma.xRole.create({
        data: {
          roleName,
        },
      })
      return res.json({ code: 0, data: createRes })
    }),
  )

  router.post(
    '/admin/role/update',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      const { id, roleName } = req.body
      if (!id) {
        return res.json({ code: 1, msg: 'ID不存在' })
      }
      if (roleName) {
        const isExist = await prisma.xRole.findFirst({ where: { roleName } })
        if (isExist) {
          return res.json({ code: 1, msg: '角色名不能重复' })
        }
      }
      const updateRes = await prisma.xRole.update({
        data: { roleName },
        where: { id },
        select: { id: true, roleName: true, createdAt: true },
      })
      return res.json({ code: 0, data: updateRes })
    }),
  )

  router.delete(
    '/admin/role',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      const { id } = req.body
      if (!id) {
        return res.json({ code: 1, msg: 'ID不存在' })
      }
      const user = await prisma.xRole.findFirst({ where: { id } })
      if (!user) {
        return res.json({ code: 1, msg: '角色不存在' })
      }
      await prisma.xRole.delete({ where: { id } })
      return res.json({ code: 0 })
    }),
  )

  router.get(
    '/admin/group/list',
    passportMiddleware,
    asyncResponseWrapper(async (_, res) => {
      const roles = await prisma.xGroup.findMany({
        // skip,
        // take: pageSize,
        orderBy: {
          createdAt: 'desc',
        },
        select: {
          id: true,
          groupName: true,
          createdAt: true,
        },
      })

      return res.json({ code: 0, data: { list: roles, total: roles.length } })
    }),
  )

  router.post(
    '/admin/group',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      const { groupName } = req.body
      const isExist = await prisma.xGroup.findFirst({ where: { groupName } })
      if (isExist) {
        return res.json({ code: 1, msg: '用户组名不能重复' })
      }
      const createRes = await prisma.xGroup.create({
        data: {
          groupName,
        },
      })
      return res.json({ code: 0, data: createRes })
    }),
  )

  router.post(
    '/admin/group/update',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      const { id, groupName } = req.body
      if (!id) {
        return res.json({ code: 1, msg: 'ID不存在' })
      }
      if (groupName) {
        const isExist = await prisma.xGroup.findFirst({ where: { groupName } })
        if (isExist) {
          return res.json({ code: 1, msg: '用户组名不能重复' })
        }
      }
      const updateRes = await prisma.xGroup.update({
        data: { groupName },
        where: { id },
        select: { id: true, groupName: true, createdAt: true },
      })
      return res.json({ code: 0, data: updateRes })
    }),
  )

  router.delete(
    '/admin/group',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      const { id } = req.body
      if (!id) {
        return res.json({ code: 1, msg: 'ID不存在' })
      }
      const user = await prisma.xGroup.findFirst({ where: { id } })
      if (!user) {
        return res.json({ code: 1, msg: '用户组不存在' })
      }
      await prisma.xGroup.delete({ where: { id } })
      return res.json({ code: 0 })
    }),
  )

  router.post(
    '/union',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      const { v0, v1 } = req.body
      await enforcer.unionUserRole({ v0, v1 })
      return res.json({ code: 0 })
    }),
  )

  router.get(
    '/admin/resource/list',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      console.info('GET /admin/resource/list', req.query)
      const resList = await prisma.xResource.findMany({
        include: {
          rules: {
            distinct: ['v0'],
            select: { v0: true, v2: true },
          },
        },
        where: {
          name: typeof req.query.name === 'string' ? { contains: req.query.name } : undefined,
          type: typeof req.query.type === 'string' ? req.query.type : undefined,
        },
      })
      const infoData: Record<Partial<ResourceTypes>, string[]> = { project: [], scene: [], llm: [], page: [], key: [] }
      for (const resource of resList) {
        const { type, typeData } = resource
        if (typeData && (type === 'project' || type === 'llm' || type === 'scene' || type === 'key')) {
          infoData[type as ResourceTypes] ??= []
          if (typeof typeData === 'string') {
            infoData[type as ResourceTypes].push(typeData)
          } else if (Array.isArray(typeData)) {
            infoData[type as ResourceTypes].push(...(typeData as string[]))
          }
        }
      }
      const infoResult: Record<ResourceTypes, any[]> = { project: [], scene: [], llm: [], page: [], key: [] }
      for (const { value } of resourceTypeList) {
        const ids = infoData[value]
        if (!ids) continue
        if (value === 'project') {
          infoResult[value] = await prisma.semanticProject.findMany({ where: { id: { in: ids } } })
        } else if (value === 'llm') {
          infoResult[value] = ALL_LLMS.filter((v) => ids.includes(v.id))
        } else if (value === 'scene') {
          infoResult[value] = await prisma.semanticScene.findMany({
            where: { id: { in: ids } },
            include: {
              semanticProject: {
                select: { id: true, name: true },
              },
            },
          })
        }
      }
      return res.json({ code: 0, data: { list: resList, total: resList.length, info: infoResult } })
    }),
  )

  router.get(
    '/admin/resource/data',
    passportMiddleware,
    asyncResponseWrapper(async (_, res) => {
      const [projects, llms, roles, groups, users] = await Promise.all([
        enforcer.normalizeProjects(await prisma.semanticProject.findMany({ include: { semanticScenes: true } })),
        Promise.resolve(ALL_LLMS),
        prisma.xRole.findMany({
          select: { id: true, roleName: true },
        }),
        prisma.xGroup.findMany({
          select: { id: true, groupName: true },
        }),
        prisma.xUser.findMany({
          select: { id: true, username: true },
        }),
      ])
      return res.json({ code: 0, data: { projects, llms, roles, groups, users } })
    }),
  )

  router.post(
    '/admin/resource',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      console.info('POST /admin/resource', req.body)
      const { name, type, typeData, rules = [] } = req.body
      const resource = await createResource(name, type, typeData, rules)
      await enforcer.refresh()
      return res.json({ code: 0, data: { resource } })
    }),
  )

  router.put(
    '/admin/resource',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      console.info('PUT /admin/resource', req.body)
      const { id: resourceId, name, type, typeData, rules } = req.body
      await prisma.$transaction(async () => {
        await prisma.xResource.update({
          where: { id: resourceId },
          data: { name, type, typeData },
        })
        await prisma.casbinRule.deleteMany({ where: { resourceId } })
        await prisma.casbinRule.createMany({
          data: rules
            .map(({ type: ownerType, id: ownerId, action }: Record<string, string>) => {
              return encodeResourceRule(type, typeData).map((v1) => {
                return {
                  ptype: 'p',
                  v0: `${ownerType}:${ownerId}`,
                  v1,
                  v2: action,
                  resourceId,
                }
              })
            })
            .flat(),
        })
        await enforcer.refresh()
      })
      return res.json({ code: 0 })
    }),
  )

  router.delete(
    '/admin/resource',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      const { id } = req.body
      await prisma.casbinRule.deleteMany({ where: { resourceId: id } })
      const data = await prisma.xResource.delete({
        where: { id },
      })
      await enforcer.refresh()
      return res.json({ code: 0, data })
    }),
  )

  router.get(
    '/enforce',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      if (!req.user) return res.json({ code: 0, data: false })
      const userInfo = req.user!
      console.info('GET /auth/enforce', req.query, userInfo.id)
      const { resource, action } = req.query
      const can = resource && action ? await enforcer.e.enforce(contactUserData(userInfo.id), resource, action) : false
      return res.json({ code: 0, data: can })
    }),
  )

  router.post(
    '/enforce',
    passportMiddleware,
    asyncResponseWrapper(async (req, res) => {
      if (!req.user) return res.json({ code: 0, data: [] })
      const userInfo = req.user!
      console.info('POST /auth/enforce', req.query, userInfo.id)
      const list: { resource: string; action: string }[] = req.body.list
      const resultList = await enforcer.e.batchEnforce(
        list.map(({ resource, action }) => [contactUserData(userInfo.id), resource, action]),
      )
      return res.json({ code: 0, data: resultList })
    }),
  )

  router.get(
    '/authorization',
    asyncResponseWrapper(async (req, res) => {
      const { v0 = '', v1 = '', v2 = '' } = req.query
      const can = await enforcer.e.enforceEx(v0, v1, v2)
      return res.json({ code: 0, data: can })
    }),
  )

  return router
}

export const router = createRouter()
