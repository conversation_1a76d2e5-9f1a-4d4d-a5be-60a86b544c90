/* eslint-disable @typescript-eslint/naming-convention */
import winston from 'winston'
import { Request } from 'express'
import DailyRotateFile from 'winston-daily-rotate-file'
import { ElkModuleType, ElkResponse } from 'src/shared/common-types'
import { fetchProjectInfoByProjectId, fetchProjectInfoBySceneId } from 'src/server/AskBI/datasets/dao'
import { PROCESS_ENV } from './server-constants'
const { combine, timestamp } = winston.format

const defaultOptions = {
  datePattern: 'YYYY-MM-DD',
  zippedArchive: false, // 禁用压缩归档。
  maxSize: '500m',
  maxFiles: '30d', // 表示保留最近 30 天的日志文件，自动删除更早的日志文件。
}

// 创建Winston日志记录器  winston
export const logger = winston.createLogger({
  format: combine(timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }), winston.format.json()),
  defaultMeta: { service: 'user-service' },
  transports: [
    new DailyRotateFile({
      filename: 'logs/%DATE%.log',
      level: 'info',
      ...defaultOptions,
    }),
  ],
})

interface SaveAskBotBusinessLogsParams {
  moduleType: ElkModuleType
  host: string
  username: string
  traceId: string
  startTime: number
  resultCode: number
  input: Record<string, any>
  output: Record<string, any> | string
  debug: Record<string, any>
  semanticProjectId?: string
  semanticSceneId?: string
  req?: Request
}

// AskBot业务日志整理
export async function saveAskBotBusinessLogs(params: SaveAskBotBusinessLogsParams) {
  const {
    startTime,
    username,
    traceId,
    host,
    resultCode,
    moduleType,
    input,
    output,
    debug,
    semanticProjectId,
    semanticSceneId,
    req,
  } = params

  const xUsername = req?.headers['x-username']
  const userId = xUsername && username !== xUsername ? `${username}-${xUsername}` : username
  const sceneInfo = await fetchProjectInfoBySceneId(semanticSceneId)
  const projectInfo = await fetchProjectInfoByProjectId(semanticProjectId)

  const result: ElkResponse = {
    timestamp: new Date().toISOString(),
    user_id: userId,
    request_id: traceId,
    host,
    service_type: 'web_service',
    start_time: new Date(startTime).toISOString(),
    end_time: new Date(Date.now()).toISOString(),
    duration: Date.now() - startTime,
    result_code: resultCode,
    module_type: moduleType,
    input,
    output,
    debug,
    url: debug?.url ?? 'empty_need_to_handle',
    semantic_scene_id: semanticSceneId,
    semantic_project_id: semanticProjectId,
    semantic_scene_name: sceneInfo.sceneLabel,
    semantic_project_name: projectInfo.projectLabel,
    cluster_id: PROCESS_ENV.CLUSTER_ID,
  }
  logger.info(result)
}
