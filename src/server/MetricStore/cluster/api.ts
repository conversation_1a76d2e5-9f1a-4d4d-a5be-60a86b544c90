/**
 * @description 代理到cluster到接口，做一层转发
 */

import express from 'express'
import multer from 'multer'
const storage = multer.memoryStorage()
const upload = multer({ storage: storage })
import { generateClusterUrl } from 'src/server/utils'
import { commonProxy } from 'src/server/commonProxy'

const router = express.Router()
router.all('/*', upload.none(), async (req, res) => {
  try {
    const { path, baseUrl } = req
    const url = generateClusterUrl(path, baseUrl)
    commonProxy(req, res, url)
  } catch (error: any) {
    console.error(`出现错误，请求地址${req.url}`, error)
    return res.status(500).json({ code: 500, msg: '服务端错误' })
  }
})
export default router
