/**
 * 业务术语 Hint 相关接口
 */
import fs from 'fs'
import path from 'path'
import express, { Router, Request, Response } from 'express'
import axios from 'axios'
import multer from 'multer'
import csv, { parse } from 'csv-parse/sync'
import FormData from 'form-data'
import { Hint } from '@shared/metric-types'
import { PROCESS_ENV } from 'src/server/server-constants'

const router: Router = express.Router()

const PROJECT_HINT_SCENE_PLACEHOLDER = '-'
const HINT_BASE_URL = PROCESS_ENV.PARAMS_EXTRACT_URL_HOST + '/api/v1/business_term'

const HINT_UPSERT_URL = HINT_BASE_URL + '/upsert'
const HINT_DELETE_BATCH_URL = HINT_BASE_URL + '/batch/delete'
const HINT_LIST_URL = HINT_BASE_URL
const HINT_UPLOAD_URL = HINT_BASE_URL + '/batch/upload'
const HINT_DOWNLOAD_URL = HINT_BASE_URL + '/download'

/**
 * 创建业务术语
 */
router.post('/', async (req: Request, res: Response) => {
  const body: Omit<Hint, 'id' | 'createdAt' | 'updatedAt'> = req.body
  console.info('Create a hint with data: ', body)

  try {
    const list = await axios.get(HINT_LIST_URL, {
      params: {
        page: 1,
        pageSize: 1000,
        semanticProjectId: body.semanticProjectId,
        semanticSceneId: body.semanticSceneId,
      },
    })
    const result = await axios.post(HINT_UPSERT_URL, body)
    if (result.data?.code === 0) {
      return res.json({ code: 0, data: result.data.data, list: list.data.data })
    } else {
      throw new Error('创建业务术语失败')
    }
  } catch (error) {
    console.error('Create a Hint failed.', error)
    return res.json({ code: 500, msg: '创建业务术语失败: ' + (error as Error).message })
  }
})

/**
 * 更新某个Hint的信息
 */
router.put('/:id', async (req: Request, res: Response) => {
  console.info('Update Hint data with params', req.params, req.body)
  try {
    const hintId = req.params.id
    if (hintId == null) {
      return res.json({ code: 411, data: {}, msg: '缺少 hintId，更新业务术语失败!' })
    }
    const body: Omit<Hint, 'id' | 'createdAt' | 'updatedAt'> = req.body
    console.info(`Update hint id=${hintId}, data :`, body)
    const result = await axios.post(HINT_UPSERT_URL, { ...body, id: hintId })
    if (result.data?.code === 0) {
      return res.json({ code: 0, data: result.data.data })
    } else {
      throw new Error('更新业务术语失败')
    }
  } catch (error) {
    console.error('Update a Hint failed.', error)
    return res.json({ code: 500, msg: '更新业务术语失败: ' + (error as Error).message })
  }
})

/**
 * 删除某个/多个 业务术语
 */
router.delete('/batch', async (req: Request, res: Response) => {
  const ids: string[] = req.body.ids
  console.info('Batch Delete hints with params: ', ids)
  try {
    if (!Array.isArray(ids) || !ids.length) {
      return res.json({ code: 400, msg: 'id列表 存在问题' })
    }
    const result = await axios.post(HINT_DELETE_BATCH_URL, ids)
    if (result.data?.code === 0) {
      return res.json({ code: 0, data: result.data })
    } else {
      throw new Error('删除业务术语数据失败')
    }
  } catch (error) {
    console.error('Delete hints with error', error)
    return res.json({ code: 500, msg: '删除业务术语数据失败:' + (error as Error).message })
  }
})

/**
 * 获取某一个场景下的 Hint 数据
 */
router.get('/list/scene/:projectId/:sceneId', async (req: Request, res: Response) => {
  console.info('Get hints data with params', req.params)
  try {
    const projectId = req.params.projectId
    const sceneId = req.params.sceneId
    const result = await axios.get(HINT_LIST_URL, {
      params: {
        page: 1,
        pageSize: 1000,
        semanticProjectId: projectId,
        semanticSceneId: sceneId,
      },
    })
    if (result.data?.code === 0) {
      return res.json({
        code: 0,
        data: {
          list: result.data.data,
          total: result.data.data.length,
        },
      })
    } else {
      throw new Error('获取业务术语数据失败')
    }
  } catch (error) {
    console.error('Get hints data with error', error)
    return res.json({ code: 500, msg: '获取场景下业务术语数据失败:' + (error as Error).message })
  }
})

/**
 * 获取某一个项目下的 Hint 数据
 */
router.get('/list/project/:projectId', async (req: Request, res: Response) => {
  console.info('Get hints data with params', req.params)
  try {
    const projectId = req.params.projectId

    const result = await axios.get(HINT_LIST_URL, {
      params: {
        page: 1,
        pageSize: 1000,
        semanticProjectId: projectId,
        semanticSceneId: PROJECT_HINT_SCENE_PLACEHOLDER,
      },
    })
    if (result.data?.code === 0) {
      return res.json({
        code: 0,
        data: {
          list: result.data.data,
          total: result.data.data.length,
        },
      })
    } else {
      throw new Error('获取业务术语数据失败')
    }
  } catch (error) {
    console.error('Get hints data with error', error)
    return res.json({ code: 500, msg: '获取项目下业务术语数据失败:' + (error as Error).message })
  }
})

/**
 * 下载 CSV 示例文件
 */
router.get('/download-example', async (_req: Request, res: Response) => {
  try {
    const csvHeader = ['extraInfo', 'text', 'agents', 'tags', 'type'].join(',') + '\n'

    const csvData =
      [
        [
          '业务术语1',
          '召回关键描述1',
          '"[""chat"",""bi"",""doc"",""condense"",""brain""] 可多选 用数组格式"',
          '"[""思考逻辑"",""时间处理"",""指标标准化"",""业务计算""] 单选 用数组格式"',
          'dynamic fixed 选一个',
        ],
        ['业务术语2', '这是第二个术语的说明信息', '"[""brain""]"', '"[""业务计算""]"', 'fixed'],
      ]
        .map((row) => row.join(','))
        .join('\n') + '\n'

    const csvContent = csvHeader + csvData

    res.setHeader('Content-Type', 'text/csv')
    res.setHeader('Content-Disposition', 'attachment; filename=example.csv')
    res.send(csvContent)
  } catch (error) {
    console.error('下载 CSV 示例文件失败', error)
    res.status(500).json({ code: 500, msg: '下载 CSV 示例文件失败' })
  }
})

const storage = multer.memoryStorage()
const upload = multer({ storage: storage })

router.post('/upload', upload.single('file'), async (req: Request, res: Response) => {
  try {
    const file = req.file
    if (!file) {
      return res.json({ code: 500, msg: '文件异常' })
    }
    const { semanticSceneId, semanticProjectId, creator } = req.body
    const csvData = file.buffer.toString('utf-8')
    console.info('upload csvData-----', csvData)

    const records = parse(csvData, {
      columns: true,
      skip_empty_lines: true,
      trim: true,
      quote: '"',
      relax_quotes: true,
      relax_column_count: true,
    })

    const dataArray = records.map((record: any, index: number) => {
      const { extraInfo, text, agents, tags, type } = record
      if (!extraInfo || !text || !agents || !tags || !type) {
        throw new Error(`第 ${index + 2} 行存在必填字段为空`)
      }
      let parsedAgents: string[]
      let parsedTags: string[]
      try {
        parsedAgents = JSON.parse(agents)
        parsedTags = JSON.parse(tags)
      } catch (err) {
        throw new Error(`第 ${index + 2} 行 agents/tags 格式错误，必须是 JSON 数组格式`)
      }
      return {
        extraInfo,
        text,
        agents: parsedAgents,
        tags: parsedTags,
        type,
        semanticProjectId,
        semanticSceneId,
        creator,
      }
    })

    // 安全转义 CSV 字段
    const escapeCsvField = (val: string) => {
      if (typeof val !== 'string') val = JSON.stringify(val)
      if (val.includes(',') || val.includes('"') || val.includes('\n')) {
        return `"${val.replace(/"/g, '""')}"`
      }
      return val
    }

    const csvContent = [
      'extra_info,text,agents,tags,type,semantic_project_id,semantic_scene_id,creator',
      ...dataArray.map((item: any) =>
        [
          item.extraInfo,
          item.text,
          JSON.stringify(item.agents),
          JSON.stringify(item.tags),
          item.type,
          item.semanticProjectId,
          item.semanticSceneId,
          item.creator,
        ]
          .map(escapeCsvField)
          .join(','),
      ),
    ].join('\n')

    const tempFilePath = path.join('/tmp', `example_${Date.now()}.csv`)
    fs.writeFileSync(tempFilePath, csvContent, 'utf-8')
    const formData = new FormData()
    formData.append('file', fs.createReadStream(tempFilePath))
    console.info('csvContent-----', csvContent)
    try {
      const response = await axios.post(HINT_UPLOAD_URL, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          Accept: 'application/json',
        },
      })
      res.json({ code: 0, msg: '创建成功', data: response.data.data })
    } catch (uploadErr) {
      console.error('上传 CSV 失败', uploadErr)
      res.json({ code: 500, msg: '上传 CSV 失败', error: uploadErr })
    } finally {
      fs.unlinkSync(tempFilePath)
    }
  } catch (e: any) {
    console.error('Create hints failed', e)
    res.json({ code: 500, msg: e.message ?? e.toString() })
  }
})

/**
 * 下载业务术语 CSV 文件
 */
router.get('/download', async (req: Request, res: Response) => {
  const { semanticProjectId, semanticSceneId } = req.query

  if (!semanticProjectId || !semanticSceneId) {
    return res.status(400).json({ code: 400, msg: '缺少 semanticProjectId 或 semanticSceneId' })
  }

  const downloadUrl = `${HINT_DOWNLOAD_URL}?semanticProjectId=${semanticProjectId}&semanticSceneId=${semanticSceneId}`

  try {
    const rawResponse = await axios.get(downloadUrl)
    const rawCsv = rawResponse.data

    const records = csv.parse(rawCsv, {
      columns: true,
      skip_empty_lines: true,
    })

    const headers = ['extraInfo', 'text', 'agents', 'tags', 'type']
    const lines = [headers.join(',')]

    const escapeCsvValue = (val: string) => `"${val.replace(/"/g, '""')}"`

    for (const row of records) {
      const line = [row.extra_info, row.text, row.agents, row.tags, row.type].map(escapeCsvValue).join(',')
      lines.push(line)
    }

    const newCsv = lines.join('\n')

    res.setHeader('Content-Disposition', 'attachment; filename=hint_simplified.csv')
    res.setHeader('Content-Type', 'text/csv')
    res.send(newCsv)
  } catch (err) {
    console.error('下载并格式化业务术语失败', err)
    res.status(500).json({ code: 500, msg: '下载失败: ' + (err as Error).message })
  }
})

export default router
