/**
 * index.ts 存放全局通用的m2s后置修改逻辑
 */
import chalk from 'chalk'
import dayjs from 'dayjs'
import { assertExhaustive } from 'src/shared/common-utils'
import { TimeGranularityMinType } from 'src/shared/common-types'
import { convertTimeToSpecificDate } from 'src/shared/time-utils'
import { NoListMetric, QueryParams, TimeFunction, TimeQueryParams } from 'src/shared/metric-types'
import MetricConfig from '../../metric-config'
// import { isBaoWuCost } from '@shared/baowu-share-utils'

/**
 * 处理不可累加指标：
 * - 展开 Python 提到的相关指标
 * - 若指标 isCumulative: false，则调整时间参数
 * - 时间参数取 Python 时间提参结果中最后一个 timeGranularityMin 的时间
 *
 * @param queryParams - 查询参数
 * @param metricConfig - 指标模型建模
 * @returns queryParams {QueryParams}
 */
export function processNonCumulativeMetrics(queryParams: QueryParams, metricConfig: MetricConfig): QueryParams {
  if (!queryParams?.timeQueryParams?.timeGranularity || !metricConfig?.timeDimensionDatum?.timeGranularityMin) {
    return queryParams
  }
  let newTimeQueryParams = queryParams.timeQueryParams
  const timeGranularityMin = metricConfig.timeDimensionDatum.timeGranularityMin

  // 获取所有指标名称，包括非列表指标和列表指标展开的子指标名称
  const allMetricNames = metricConfig.allMetrics.flatMap((metric) => {
    if (queryParams.metricNames.includes(metric.name)) {
      return metric.type === 'list' ? metric.typeParams.metrics.map((i) => i.name) : metric.name
    }
    return []
  })
  // 判断所有非列表指标是否都可以累加
  const allMetricsCanBeCumulative = metricConfig.allMetrics
    .filter((metric) => allMetricNames.includes(metric.name))
    .every((metric) => (metric as NoListMetric).isCumulative)
  console.info('allMetricNames, allMetricsCanBeCumulative=>', allMetricNames, allMetricsCanBeCumulative)

  // 查询一个区间内的total取值，并且包含不可累加指标
  if (queryParams.timeQueryParams.timeGranularity === 'total' && !allMetricsCanBeCumulative) {
    newTimeQueryParams = adjustTimeQueryParamsForNonCumulativeMetrics(
      queryParams.timeQueryParams.timeStartFunction,
      queryParams.timeQueryParams.timeEndFunction,
      timeGranularityMin,
      newTimeQueryParams,
    )
  }

  return { ...queryParams, timeQueryParams: newTimeQueryParams }
}

type SpecificDateType = { type: 'specificDate'; year: number; month: number; day: number }

export function adjustTimeQueryParamsForNonCumulativeMetrics(
  timeStartFunction: TimeFunction,
  timeEndFunction: TimeFunction,
  timeGranularityMin: TimeGranularityMinType,
  currentParams: TimeQueryParams,
): TimeQueryParams {
  console.info(chalk.green(`当前场景的时间颗粒度为${timeGranularityMin}`))
  console.info('timeStartFunction, timeEndFunction', timeStartFunction, timeEndFunction)
  const startDate: SpecificDateType = convertTimeToSpecificDate(timeStartFunction, 'start')
  const endDate: SpecificDateType = convertTimeToSpecificDate(timeEndFunction, 'end')

  const specificStartDate = dayjs(`${startDate.year}-${startDate.month}-${startDate.day}`, 'YYYY-M-D')
  const specificEndDate = dayjs(`${endDate.year}-${endDate.month}-${endDate.day}`, 'YYYY-M-D')

  const endFunction = currentParams?.timeEndFunction

  switch (timeGranularityMin) {
    case 'day':
      if (specificEndDate.diff(specificStartDate, 'day') > 0) {
        console.info(chalk.green(`[Rewrite TimeQueryParamsForNonCumulativeMetrics] 时间范围大于1天，重写为最后一天`))
        return {
          timeStartFunction: endFunction,
          timeEndFunction: endFunction,
          timeGranularity: 'total',
        }
      }
      break

    case 'month':
      if (
        specificEndDate.diff(specificStartDate, 'month') > 0 ||
        specificEndDate.month() !== specificStartDate.month()
      ) {
        console.info(chalk.green(`[Rewrite TimeQueryParamsForNonCumulativeMetrics] 时间范围大于1月，重写为最后一月`))
        return {
          timeStartFunction: endFunction.type === 'specificDate' ? { ...endFunction, day: 1 } : endFunction,
          timeEndFunction: endFunction,
          timeGranularity: 'total',
        }
      }
      break

    case 'year':
      if (specificEndDate.diff(specificStartDate, 'year') > 0 || specificEndDate.year() !== specificStartDate.year()) {
        console.info(chalk.green(`[Rewrite TimeQueryParamsForNonCumulativeMetrics] 时间范围大于1年，重写为最后一年`))
        return {
          timeStartFunction: endFunction.type === 'specificDate' ? { ...endFunction, day: 1, month: 1 } : endFunction,
          timeEndFunction: endFunction,
          timeGranularity: 'total',
        }
      }
      break

    default:
      assertExhaustive(timeGranularityMin)
  }

  return currentParams
}
