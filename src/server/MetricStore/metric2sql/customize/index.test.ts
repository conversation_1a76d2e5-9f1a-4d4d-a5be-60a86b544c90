import { describe, it, expect } from 'vitest'
import { TimeFunction, TimeQueryParams } from 'src/shared/metric-types'
import { TimeGranularityMinType } from 'src/shared/common-types'
import { adjustTimeQueryParamsForNonCumulativeMetrics } from './index'

describe('adjustTimeQueryParamsForNonCumulativeMetricsForNonCumulativeMetrics', () => {
  it('当时间粒度为 day 且范围超过1天时，应调整为endFunction', () => {
    const timeStartFunction: TimeFunction = { type: 'specificDate', year: 2025, month: 3, day: 21 }
    const timeEndFunction: TimeFunction = { type: 'specificDate', year: 2025, month: 3, day: 24 }
    const currentParams: TimeQueryParams = { timeStartFunction, timeEndFunction, timeGranularity: 'total' }

    const result = adjustTimeQueryParamsForNonCumulativeMetrics(
      timeStartFunction,
      timeEndFunction,
      'day',
      currentParams,
    )

    expect(result).toEqual({
      timeStartFunction: { type: 'specificDate', year: 2025, month: 3, day: 24 },
      timeEndFunction: { type: 'specificDate', year: 2025, month: 3, day: 24 },
      timeGranularity: 'total',
    })
  })

  it('当时间粒度为 month 且范围超过1个月时，应调整为endFunction', () => {
    const timeStartFunction: TimeFunction = { type: 'specificDate', year: 2024, month: 12, day: 25 }
    const timeEndFunction: TimeFunction = { type: 'specificDate', year: 2025, month: 3, day: 25 }
    const currentParams: TimeQueryParams = { timeStartFunction, timeEndFunction, timeGranularity: 'month' }

    const result = adjustTimeQueryParamsForNonCumulativeMetrics(
      timeStartFunction,
      timeEndFunction,
      'month',
      currentParams,
    )

    expect(result).toEqual({
      timeStartFunction: { day: 1, month: 3, type: 'specificDate', year: 2025 },
      timeEndFunction: { day: 25, month: 3, type: 'specificDate', year: 2025 },
      timeGranularity: 'total',
    })
  })

  it('当时间粒度为 year 且范围超过1年时，应调整为endFunction', () => {
    const timeStartFunction: TimeFunction = { type: 'specificDate', year: 2022, month: 1, day: 1 }
    const timeEndFunction: TimeFunction = { type: 'specificDate', year: 2024, month: 12, day: 31 }
    const currentParams: TimeQueryParams = { timeStartFunction, timeEndFunction, timeGranularity: 'year' }

    const result = adjustTimeQueryParamsForNonCumulativeMetrics(
      timeStartFunction,
      timeEndFunction,
      'year',
      currentParams,
    )

    expect(result).toEqual({
      timeStartFunction: { day: 1, month: 1, type: 'specificDate', year: 2024 },
      timeEndFunction: { day: 31, month: 12, type: 'specificDate', year: 2024 },
      timeGranularity: 'total',
    })
  })

  it('当时间粒度为 day 且范围超过1天时，应调整为endFunction-1', () => {
    const timeStartFunction: TimeFunction = { type: 'specificDate', year: 2025, month: 3, day: 23 }
    const timeEndFunction: TimeFunction = { type: 'specificDate', year: 2025, month: 3, day: 24 }
    const currentParams: TimeQueryParams = { timeStartFunction, timeEndFunction, timeGranularity: 'total' }

    const result = adjustTimeQueryParamsForNonCumulativeMetrics(
      timeStartFunction,
      timeEndFunction,
      'day',
      currentParams,
    )

    expect(result).toEqual({
      timeStartFunction: { type: 'specificDate', year: 2025, month: 3, day: 24 },
      timeEndFunction: { type: 'specificDate', year: 2025, month: 3, day: 24 },
      timeGranularity: 'total',
    })
  })

  it('当时间粒度为 month，范围不超过1个月且不是同一个月份时，应调整为endFunction-1', () => {
    const timeStartFunction: TimeFunction = { type: 'specificDate', year: 2025, month: 2, day: 26 }
    const timeEndFunction: TimeFunction = { type: 'specificDate', year: 2025, month: 3, day: 12 }
    const currentParams: TimeQueryParams = { timeStartFunction, timeEndFunction, timeGranularity: 'total' }

    const result = adjustTimeQueryParamsForNonCumulativeMetrics(
      timeStartFunction,
      timeEndFunction,
      'month',
      currentParams,
    )

    expect(result).toEqual({
      timeStartFunction: { type: 'specificDate', year: 2025, month: 3, day: 1 },
      timeEndFunction: { type: 'specificDate', year: 2025, month: 3, day: 12 },
      timeGranularity: 'total',
    })
  })

  it('当时间粒度为 year，且范围不超过1年且不是同一年，应调整为endFunction-1', () => {
    const timeStartFunction: TimeFunction = { type: 'specificDate', year: 2024, month: 6, day: 10 }
    const timeEndFunction: TimeFunction = { type: 'specificDate', year: 2025, month: 3, day: 24 }
    const currentParams: TimeQueryParams = { timeStartFunction, timeEndFunction, timeGranularity: 'total' }

    const result = adjustTimeQueryParamsForNonCumulativeMetrics(
      timeStartFunction,
      timeEndFunction,
      'year',
      currentParams,
    )

    expect(result).toEqual({
      timeStartFunction: { type: 'specificDate', year: 2025, month: 1, day: 1 },
      timeEndFunction: { type: 'specificDate', year: 2025, month: 3, day: 24 },
      timeGranularity: 'total',
    })
  })

  it('当时间粒度为 day 且范围不超过1天时，不调整', () => {
    const timeStartFunction: TimeFunction = { type: 'specificDate', year: 2025, month: 3, day: 24 }
    const timeEndFunction: TimeFunction = { type: 'specificDate', year: 2025, month: 3, day: 24 }
    const currentParams: TimeQueryParams = { timeStartFunction, timeEndFunction, timeGranularity: 'total' }

    const result = adjustTimeQueryParamsForNonCumulativeMetrics(
      timeStartFunction,
      timeEndFunction,
      'day',
      currentParams,
    )

    expect(result).toEqual(currentParams)
  })

  it('当时间粒度为 month 且范围不超过1个月时，不调整', () => {
    const timeStartFunction: TimeFunction = { type: 'specificDate', year: 2025, month: 3, day: 2 }
    const timeEndFunction: TimeFunction = { type: 'specificDate', year: 2025, month: 3, day: 24 }
    const currentParams: TimeQueryParams = { timeStartFunction, timeEndFunction, timeGranularity: 'total' }

    const result = adjustTimeQueryParamsForNonCumulativeMetrics(
      timeStartFunction,
      timeEndFunction,
      'month',
      currentParams,
    )

    expect(result).toEqual(currentParams)
  })

  it('当时间粒度为 year 且范围不超过1年时，不调整', () => {
    const timeStartFunction: TimeFunction = { type: 'specificDate', year: 2025, month: 1, day: 10 }
    const timeEndFunction: TimeFunction = { type: 'specificDate', year: 2025, month: 3, day: 24 }
    const currentParams: TimeQueryParams = { timeStartFunction, timeEndFunction, timeGranularity: 'total' }

    const result = adjustTimeQueryParamsForNonCumulativeMetrics(
      timeStartFunction,
      timeEndFunction,
      'year',
      currentParams,
    )

    expect(result).toEqual(currentParams)
  })

  it('当时间粒度类型无效时，应该触发 assertExhaustive', () => {
    expect(() =>
      adjustTimeQueryParamsForNonCumulativeMetrics(
        { type: 'specificDate', year: 2025, month: 3, day: 21 },
        { type: 'specificDate', year: 2025, month: 3, day: 24 },
        'invalid' as TimeGranularityMinType,
        {
          timeStartFunction: { type: 'specificDate', year: 2025, month: 3, day: 21 },
          timeEndFunction: { type: 'specificDate', year: 2025, month: 3, day: 24 },
          timeGranularity: 'day',
        },
      ),
    ).toThrowError()
  })
})
