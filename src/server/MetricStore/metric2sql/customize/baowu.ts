/**
 * 存放宝武场景的定制逻辑
 * 1. 如果没有时间周期，就使用上个月
 * 2. 如果问今年的数据 截止时期默认到上个月
 * 3. 区分 可累加/不可累加 指标的取数逻辑(不可累加的指标 取时间区间内最后一个月的数据)
 * 4. groupBy 里面一定要有子公司名
 *
 * TODO: 覆盖单元测试
 * 保证多次执行 结果一致
 */
import chalk from 'chalk'
import dayjs from 'dayjs'
import { convertTimeToSpecificDate, convertTimeToSpecificMonth } from 'src/shared/time-utils'
import { assertExhaustive } from 'src/shared/common-utils'
import { QueryParams } from 'src/shared/metric-types'
import MetricConfig from '../../metric-config'

export function baowuFinancialCustomLogic(queryParams: QueryParams, metricConfig: MetricConfig): QueryParams {
  console.info(chalk.yellow('宝武定制逻辑--->>>'), queryParams?.timeQueryParams, metricConfig.name)

  let baowuTimeQueryParams = queryParams.timeQueryParams
  // groupBy 里面一定要有子公司名
  const baowuGroupBys = ['COMPANY_INNER_CODE_DES']
  if (queryParams.timeQueryParams == null) {
    baowuTimeQueryParams = {
      timeStartFunction: { type: 'recentMonths', months: 1 },
      timeEndFunction: { type: 'recentMonths', months: 1 },
      timeGranularity: 'total',
    }
  } else if (queryParams.timeQueryParams.timeGranularity) {
    const startMonthFunction = convertTimeToSpecificMonth(queryParams.timeQueryParams.timeStartFunction)
    const endMonthFunction = convertTimeToSpecificMonth(queryParams.timeQueryParams.timeEndFunction)
    const timeSpan = Math.abs(
      startMonthFunction.year * 12 + startMonthFunction.month - (endMonthFunction.year * 12 + endMonthFunction.month),
    )
    // 判断时间跨度  1< x <=12
    const isWithinTwelveMonths = timeSpan <= 12

    console.info(
      'startMonthFunction, endMonthFunction, timeSpan, isWithinTwelveMonths',
      startMonthFunction,
      endMonthFunction,
      timeSpan,
      isWithinTwelveMonths,
    )

    const startDate: { type: 'specificDate'; year: number; month: number; day: number } = convertTimeToSpecificDate(
      queryParams.timeQueryParams.timeStartFunction,
      'start',
    )
    const endDate: { type: 'specificDate'; year: number; month: number; day: number } = convertTimeToSpecificDate(
      queryParams.timeQueryParams.timeEndFunction,
      'end',
    )
    const specificStartDate = dayjs(`${startDate.year}-${startDate.month}-${startDate.day}`, 'YYYY-M-D')
    const specificEndDate = dayjs(`${endDate.year}-${endDate.month}-${endDate.day}`, 'YYYY-M-D')
    const today = dayjs().startOf('day')

    if (queryParams.timeQueryParams.timeGranularity === 'total' && isWithinTwelveMonths) {
      if (startMonthFunction.year !== endMonthFunction.year || startMonthFunction.month !== endMonthFunction.month) {
        // 如果开始时间小于今天 结束时间大于今天 结束时间修正为上个月
        if (!specificStartDate.isAfter(today) && !specificEndDate.isBefore(today)) {
          console.info(chalk.yellow('开始时间早于今天，结束时间晚于今天，结束时间修正为上个月'))
          baowuTimeQueryParams = {
            timeStartFunction: queryParams.timeQueryParams.timeStartFunction,
            timeEndFunction: { type: 'recentMonths', months: 1 },
            timeGranularity: 'total',
          }
        }
      }
      if (specificStartDate.isSame(today) && specificEndDate.isSame(today)) {
        console.info(chalk.yellow('开始时间结束时间都等于今天，结束时间修正为上个月'))
        baowuTimeQueryParams = {
          timeStartFunction: { type: 'recentMonths', months: 1 },
          timeEndFunction: { type: 'recentMonths', months: 1 },
          timeGranularity: 'total',
        }
      }
      if (specificStartDate.isSame(today, 'day') && specificEndDate.isSame(today.endOf('month'), 'day')) {
        const year = specificEndDate.year()
        const month = specificEndDate.month() + 1
        console.info(
          chalk.green('开始时间是今天，结束时间是本月底，整体时间修正为上个月。 curYear, curMonth', year, month),
        )
        baowuTimeQueryParams = {
          timeStartFunction: { type: 'recentMonths', months: 1 },
          timeEndFunction: { type: 'recentMonths', months: 1 },
          timeGranularity: 'total',
        }
      }
    }
    // 如果是同一个月份，开始时间设置为第1天，结束时间设置为最后一天
    if (baowuTimeQueryParams) {
      const startDateScoped: { type: 'specificDate'; year: number; month: number; day: number } =
        convertTimeToSpecificDate(baowuTimeQueryParams.timeStartFunction, 'start')
      const endDateScoped: { type: 'specificDate'; year: number; month: number; day: number } =
        convertTimeToSpecificDate(baowuTimeQueryParams.timeEndFunction, 'end')

      const specificStartDateScoped = dayjs(
        `${startDateScoped.year}-${startDateScoped.month}-${startDateScoped.day}`,
        'YYYY-M-D',
      ).startOf('day')
      const specificEndDateScoped = dayjs(
        `${endDateScoped.year}-${endDateScoped.month}-${endDateScoped.day}`,
        'YYYY-M-D',
      ).endOf('day')

      // 判断是否是同一个月份
      if (specificStartDateScoped.isSame(specificEndDateScoped, 'month')) {
        const firstDayOfMonth = specificStartDateScoped.startOf('month') // 该月第一天 00:00:00
        const lastDayOfMonth = specificEndDateScoped.endOf('month') // 该月最后一天 23:59:59
        console.info(
          chalk.yellow(
            `开始结束时间是同一个月份，开始时间设置为第一天，结束时间设置为最后一天：
            startDateScoped: ${JSON.stringify(startDateScoped)},
            endDateScoped: ${JSON.stringify(endDateScoped)},
            specificStartDateScoped: ${specificStartDateScoped},
            specificEndDateScoped: ${specificEndDateScoped},
            firstDayOfMonth: ${firstDayOfMonth},
            lastDayOfMonth: ${lastDayOfMonth}`,
          ),
        )
        baowuTimeQueryParams = {
          timeGranularity: baowuTimeQueryParams.timeGranularity,
          timeStartFunction: {
            type: 'specificDate',
            year: firstDayOfMonth.year(),
            month: firstDayOfMonth.month() + 1,
            day: firstDayOfMonth.date(),
          },
          timeEndFunction: {
            type: 'specificDate',
            year: lastDayOfMonth.year(),
            month: lastDayOfMonth.month() + 1,
            day: lastDayOfMonth.date(),
          },
        }
      }
    }
  }

  return {
    ...queryParams,
    timeQueryParams: baowuTimeQueryParams,
    groupBys: baowuGroupBys,
  }
}

export function baowuCostCustomLogic(queryParams: QueryParams, metricConfig: MetricConfig): QueryParams {
  console.info(chalk.yellow('宝武成本场景定制逻辑--->>>'), queryParams?.timeQueryParams, metricConfig.name)

  let baowuTimeQueryParams = queryParams.timeQueryParams
  if (queryParams.timeQueryParams == null) {
    baowuTimeQueryParams = {
      timeStartFunction: { type: 'recentMonths', months: 1 },
      timeEndFunction: { type: 'recentMonths', months: 1 },
      timeGranularity: 'total',
    }
  }

  const baowuGroupBys = ['COMPANY_INNER_CODE_DES']
  const FLAG_COLUMN_NAME = 'ml_time_granularity'
  const hasMlTimeGranularity = metricConfig.allDimensions.some((i) => i.name === FLAG_COLUMN_NAME)
  const timeQueryType = queryParams.extraInfo?.timeQueryType

  if (!hasMlTimeGranularity) {
    return { ...queryParams, timeQueryParams: baowuTimeQueryParams, groupBys: baowuGroupBys }
  }

  queryParams.where = queryParams.where || ''
  const originWhere = queryParams.where.trim()
  const andPrefix = originWhere ? ' AND ' : ''

  switch (timeQueryType) {
    case '日':
      queryParams.where += `${andPrefix}${FLAG_COLUMN_NAME}='day'`
      break
    case '月':
    case '季':
    case '年':
    case null:
    case undefined:
      queryParams.where += `${andPrefix}${FLAG_COLUMN_NAME}='month'`
      break
    default:
      assertExhaustive(timeQueryType, `不支持的 timeQueryType: ${timeQueryType}`)
  }
  console.info('Original where: ', originWhere)
  console.info('Rewritten where: ', queryParams.where)
  return { ...queryParams, timeQueryParams: baowuTimeQueryParams, groupBys: baowuGroupBys }
}

export function baowuAmtCustomLogic(queryParams: QueryParams, metricConfig: MetricConfig): QueryParams {
  console.info(chalk.yellow('宝武总帐场景定制逻辑--->>>'), queryParams?.timeQueryParams, metricConfig.name)

  let baowuTimeQueryParams = queryParams.timeQueryParams
  if (queryParams.timeQueryParams == null) {
    baowuTimeQueryParams = {
      timeStartFunction: { type: 'recentMonths', months: 1 },
      timeEndFunction: { type: 'recentMonths', months: 1 },
      timeGranularity: 'total',
    }
  } else if (queryParams.timeQueryParams.timeGranularity) {
    const startMonthFunction = convertTimeToSpecificMonth(queryParams.timeQueryParams.timeStartFunction)
    const endMonthFunction = convertTimeToSpecificMonth(queryParams.timeQueryParams.timeEndFunction)
    const timeSpan = Math.abs(
      startMonthFunction.year * 12 + startMonthFunction.month - (endMonthFunction.year * 12 + endMonthFunction.month),
    )
    // 判断时间跨度  1< x <=12
    const isWithinTwelveMonths = timeSpan <= 12

    console.info(
      'startMonthFunction, endMonthFunction, timeSpan, isWithinTwelveMonths',
      startMonthFunction,
      endMonthFunction,
      timeSpan,
      isWithinTwelveMonths,
    )

    const startDate: { type: 'specificDate'; year: number; month: number; day: number } = convertTimeToSpecificDate(
      queryParams.timeQueryParams.timeStartFunction,
      'start',
    )
    const endDate: { type: 'specificDate'; year: number; month: number; day: number } = convertTimeToSpecificDate(
      queryParams.timeQueryParams.timeEndFunction,
      'end',
    )
    const specificStartDate = dayjs(`${startDate.year}-${startDate.month}-${startDate.day}`, 'YYYY-M-D')
    const specificEndDate = dayjs(`${endDate.year}-${endDate.month}-${endDate.day}`, 'YYYY-M-D')
    const today = dayjs().startOf('day')

    if (queryParams.timeQueryParams.timeGranularity === 'total' && isWithinTwelveMonths) {
      if (startMonthFunction.year !== endMonthFunction.year || startMonthFunction.month !== endMonthFunction.month) {
        // 如果开始时间小于今天 结束时间大于今天 结束时间修正为本月
        if (!specificStartDate.isAfter(today) && !specificEndDate.isBefore(today)) {
          const thisMonth = dayjs()
          console.info(thisMonth.year(), thisMonth.month())
          console.info(chalk.yellow('开始时间早于今天，结束时间晚于今天，结束时间修正为本月'))
          baowuTimeQueryParams = {
            timeStartFunction: { type: 'specificMonth', year: thisMonth.year(), month: thisMonth.month() + 1 },
            timeEndFunction: { type: 'specificMonth', year: thisMonth.year(), month: thisMonth.month() + 1 },
            timeGranularity: 'total',
          }
        }
      }
    }
  }
  return { ...queryParams, timeQueryParams: baowuTimeQueryParams }
}
