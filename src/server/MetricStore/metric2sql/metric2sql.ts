import { ColumnRef, Expr, Param, Function, Parser, Value } from 'node-sql-parser'
import { nanoid } from 'nanoid'
import { cloneDeep } from 'lodash'
import chalk from 'chalk'
import { assertExhaustive } from 'src/shared/common-utils'
import {
  DATE_ALIAS,
  DerivedMetric,
  Dimension,
  Measure,
  Metric,
  PeriodOverPeriodMonthNames,
  QueryParams,
  QueryParamsVerified,
  NoListMetric,
  SimpleMetric,
  TimeSqlPart,
  VirtualTimeDimension,
  RatioMetric,
  PeriodOverPeriodType,
  TimeDimensionDatum,
  PeriodOverPeriodQuarterNames,
  PeriodOverPeriodMetricConfig,
  SqlStatement,
  TimeDimension,
  isTimeDimension,
} from 'src/shared/metric-types'
import { RowsMetadata } from 'src/shared/common-types'
import { isBaoWuAmt, isBaoWuCost, isBaoWuFinancial } from 'src/shared/baowu-share-utils'
import { PROCESS_ENV } from 'src/server/server-constants'
import MetricConfig from '../metric-config'
import Time2Sql, { VIRTUAL_TIME_DIM_NAMES } from './time2sql'
import { baowuCostCustomLogic, baowuFinancialCustomLogic, baowuAmtCustomLogic } from './customize/baowu'
import { convertTimeParamsForZhongHua, isZhongHua } from './customize/zhonghua'
import { processNonCumulativeMetrics } from './customize'
/** node-sql-parser 当中 where 表达式的 type 取值类型 */
type WhereNodeType =
  | 'binary_expr'
  | 'column_ref'
  | 'param'
  | 'null'
  | 'expr_list'
  | 'number' // Value 的 type 为 string，Value 的一种
  | 'double_quote_string' // Value 的 type 为 string，Value 的一种
  | 'single_quote_string' // Value 的 type 为 string，Value 的一种
  | 'unary_expr' // NOT
  | 'function'
  | 'interval'

const ExcludeDateDimensions = ['date_month', 'date_day', 'date_quarter', 'date_year']

/**
 * 生成一个 Metric name 的依赖关系的顺序列表，被依赖的放到前面。
 * @param metric
 */
export function getMetricOrderList(metrics: MetricWithDependency[]): string[] {
  // 依次获取每个 metric 的 3 层 children。然后把所有的 metricName 放到一个拍平的数组里面
  const dependentOfEveryMetrics = metrics.map((metric) => {
    const resultNames: string[] = []
    // 对 metric，后序遍历，把所有的 children 的 metricName 放到 resultNames 里面
    function getChildrenNames(metric: MetricWithDependency) {
      if (metric.type === 'derived') {
        metric.children.forEach((child) => {
          if (metric.type === 'derived') {
            getChildrenNames(child)
          } else {
            resultNames.push(child.metricName)
          }
        })
        resultNames.push(metric.metricName)
      } else {
        resultNames.push(metric.metricName)
      }
    }
    getChildrenNames(metric)
    return resultNames
  })
  const flatDependentOfEveryMetrics = dependentOfEveryMetrics.reduce((acc, cur) => acc.concat(cur), [])

  const allDependents: string[] = [...flatDependentOfEveryMetrics, ...metrics.map((metric) => metric.metricName)]

  // 对 allDependents 去重，只保留最先出现的一个
  const final: string[] = allDependents.reduce((acc, cur) => {
    if (!acc.includes(cur)) {
      acc.push(cur)
    }
    return acc
  }, [] as string[])
  return final
}

// 定义指标间的依赖关系，派生指标的计算逻辑
export type MetricWithDependencySimple = {
  type: 'simple'
  metricName: string
  measure: string
  filter?: string
}
export type MetricWithDependencyDerived = {
  type: 'derived'
  metricName: string
  expr: string
  children: MetricWithDependency[] // 如果是 simple 指标，没有 children；如果是 derived 指标，children 为派生指标的依赖关系
}
export type MetricWithDependency = MetricWithDependencySimple | MetricWithDependencyDerived

function addDependencyToSimpleMetric(metric: SimpleMetric): MetricWithDependency {
  let measure = ''
  if ('measure' in metric.typeParams) {
    measure = metric.typeParams.measure as string
  } else {
    console.error('simple 指标没有 measure', metric)
    throw new Error('simple 指标没有 measure')
  }
  return {
    type: 'simple',
    metricName: metric.name,
    measure,
    filter: metric.filter,
  }
}

/** ratio 指标的分子可能是派生指标。分母目前只支持普通指标。ratio 依赖的 children 为分子指标 */
function addDependencyToRatioMetric(metric: RatioMetric, allMetrics: NoListMetric[]): MetricWithDependency {
  const numeratorMetricName = metric.typeParams.numerator
  const numeratorMetric = allMetrics.find(
    (metric) => metric.name.toLowerCase().trim() === numeratorMetricName.toLowerCase().trim(),
  )
  if (numeratorMetric == null) {
    console.error('占比指标的分子指标不存在', metric, numeratorMetricName)
    throw new Error(`占比指标的分子指标不存在，占比指标分子指标名 = ${numeratorMetricName}`)
  }

  let numeratorMetricWithDependency: MetricWithDependency
  const numeratorMetricType = numeratorMetric.type
  switch (numeratorMetricType) {
    case 'simple':
      numeratorMetricWithDependency = addDependencyToSimpleMetric(numeratorMetric)
      break
    case 'ratio':
      numeratorMetricWithDependency = addDependencyToRatioMetric(numeratorMetric, allMetrics)
      break
    case 'derived':
      numeratorMetricWithDependency = addDependencyToDerivedMetric(numeratorMetric, allMetrics)
      break

    default:
      assertExhaustive(numeratorMetricType)
  }

  return {
    type: 'derived',
    metricName: metric.name,
    expr: '',
    children: [numeratorMetricWithDependency],
  }
}

// 递归生成派生指标的依赖关系
function addDependencyToDerivedMetric(metric: DerivedMetric, allMetrics: NoListMetric[]): MetricWithDependency {
  // 这里递归生成派生指标的依赖关系
  let metricChildren: string[]
  if ('metrics' in metric.typeParams) {
    metricChildren = metric.typeParams.metrics.map((m) => {
      if (m.name) {
        return m.name
      }
      throw new Error(`Invalid metric typeParams: ${JSON.stringify(metric.typeParams)}`)
    })
  } else {
    console.error('派生指标没有 metrics 或在 metrics 格式不正确', metric)
    throw new Error('派生指标没有 metrics 或在 metrics 格式不正确')
  }
  let expr = ''
  if ('expr' in metric.typeParams) {
    expr = metric.typeParams.expr as string
  } else {
    console.error('派生指标没有 expr', metric)
    throw new Error('派生指标没有 expr')
  }
  return {
    type: 'derived',
    metricName: metric.name,
    expr: expr,
    children: metricChildren.map((childName) => {
      const child = allMetrics.find((metric) => metric.name.toLowerCase().trim() === childName.toLowerCase().trim())
      if (child == null) {
        console.error('派生指标的 children 没有找到', metric, childName)
        throw new Error(`派生指标的 children 没有找到，派生指标 name = ${metric.name}，children name =${childName}`)
      }
      switch (child.type) {
        case 'simple':
          return addDependencyToSimpleMetric(child)
        case 'derived':
          return addDependencyToDerivedMetric(child, allMetrics)
        case 'ratio':
          return addDependencyToRatioMetric(child, allMetrics)
        default:
          assertExhaustive(child)
      }
    }),
  }
}

export function getAllMetricsWithDependency(allMetrics: NoListMetric[]) {
  // 从 allMetrics 构建指标间的依赖关系
  const metricsWithDependency: MetricWithDependency[] = allMetrics.map((metric) => {
    switch (metric.type) {
      case 'simple':
        return addDependencyToSimpleMetric(metric)
      case 'derived':
        return addDependencyToDerivedMetric(metric, allMetrics)
      case 'ratio':
        return addDependencyToRatioMetric(metric, allMetrics)
      default:
        assertExhaustive(metric)
    }
  })

  return metricsWithDependency
}

// 支持 filter 的逻辑，filter 即 where 条件。
// 只考虑单表，所有所有 filter 都来源于一个表。
// 检查提取的 metric 中是否有 filter，如果有，就按如下规则拼接 SQL：
// 检查指标间的依赖关系，如果有派生指标，就生成派生指标间的依赖关系
// 1. 判断是否需要生成维度虚拟表：不需要生成的场景为：有多个普通指标且所有指标都没有filter，其他情况都需要生成维度虚拟表。生成维度虚拟表的方法为：查询 groupBys 中的码值，写一个子查询。select distinct groupBy1, groupBy2 from table。
// 1.1：如果 groupBys + time groupBy 为空，那么就直接查询出指标，然后使用 cross join 生成一行数据，相当于列合并。
// 1.2 判断 where 的 left or right 是否有指标，如果有，就把这个指标加到依赖树中，也做成子查询。
// 1.3 AVG 先不做特殊处理，直接求 AVG
// 2. 派生指标的计算，需要多个表 join，方法是先用 groupBys 中码值查询出维度的虚拟表，然后再 join 指标表，join 完成后去掉指标都为 null 的行。此步需要拼接 groupBy
// 3. 普通指标的计算，直接使用子查询加上 filter 即可。此步需要拼接 groupBy
// 4. 最后大查询，把用到的 groupBys 和 metric 都 select 出来。
// 所以当 groupBys + time 非空的时候，需要3部分：维度虚拟表，依赖关系=>指标虚拟表，最后大查询。
// where 要下推，全局的 where 除了指标的过滤之外，其他的都下推到具体的 where 中

/* 同环比的实现逻辑：只支持月和季度的同环比，不支持年和日的同环比。
 // 目前只支持原子指标的同环比
 提问的方法：环比，环比增长额，同比，同比增长额
 1. 上个月的 revenue、cost 以及同环比
 2. 最近3个月的 revenue 和同环比
 3. 最近3个月各部门的 revenue 和同环比
 判断用户提问的日期，需要按照月/季来聚合。如果没有日期，就使用上个月
 把同环比的指标和原来的指标放在同一个指标虚拟表的 sql 中。同环比有3类：
    3.1 上个周期的实际值 pre_month_revenue/pre_year_month_revenue/pre_quarter_revenue/pre_year_quarter_revenue
    3.2 月的变化量和比值 mom_growth_revenue/yoy_month_growth_revenue/mom_growth_rate_revenue/yoy_month_growth_rate_revenue
    3.3 季的变化量和比值 qoq_growth_revenue/yoy_quarter_growth_revenue/qoq_growth_rate_revenue/yoy_quarter_growth_rate_revenue
 4. 指标的计算逻辑：
   1. 先扩充日期，往前推 13 个月、5 个季度。
   2. join 两个表查询
   3. 计算 growth 和 growth_rate
 */

/** 第一步：从大模型提参的参数进行校验和检测，过滤出需要的参数 */
type VerifiedMeta = {
  queryParams: QueryParams
  timeSqlPart?: TimeSqlPart
}

/**
 * 第二步：从上面的参数中，判断出需要 where 派生出来的部分。
 * 同时，对 where 做处理。需要把 where 中的 dimension 转换成对应的 expr
 * Derived 字面意思有点歧义，其中这里包含了所有的参数，这是一个 union 结构
 */
type DerivedMeta = VerifiedMeta & {
  whereDerived: {
    metricNames: string[] // 当 where 中子条件的左值或者右值为 metric 的时候，需要存储到这里
    dimensionNames: string[] // 当 where 中子条件的左值或者右值为 dimension 的时候，需要存储到这里
    sharedWhere: string // 除 lastSqlPart 之外都复用的 where。需要替换掉 where 中的维度表达式。包含时间的 where
    lastWhere: string // lastSql 单独使用的 where，就是 left 或者 right 值有 metric 的 where。需要替换掉 where 中的维度表达式
  }
}

/** 第三步：汇总前面提出的 verifiedMetricParams、time、whereDerived 得到最终需要查询的汇总  */
type SummaryMeta = {
  // 时间的要单独处理，因为时间的 groupBy 要用别名
  time: {
    timeDimensionName: string
    groupBy?: string // 如果是 total，那么不需要 group by 语句
    where: string
    whereForPeriodOverPeriod: string
  }
  groupBys: string[]
  // 集合所有 metric 包括：1.QueryParams 里的 metric; 2.where 中的 metric
  metricNames: string[]
  sharedWhere: string
  lastWhere: string
  limit?: number
  orderBys?: string[] // 格式为 ["name desc", "age asc"], dbt 的格式为 metrics or group bys to order by ("-" prefix for DESC). For example: --order -ds or --order ds,-revenue
  periodOverPeriods?: PeriodOverPeriodType[]
}

/* 第四步：生成最终 SQL 的元信息，包含3部分：维度虚拟表、依赖的指标虚拟表、最后的 SQL
 * 处理派生指标，生成所有指标间的依赖关系
 * 判断是否要生成维度虚拟表，以及做基础的提参和时间提参
 */
type SqlMeta = {
  /** 判断是否需要生成维度虚拟表：不需要生成的场景为：全部为普通指标且都没有filter，其他情况都需要生成维度虚拟表。生成维度虚拟表的方法为：查询 groupBys 中的码值，写一个子查询。select distinct groupBy1, groupBy2 from table。 */
  isAllSimpleNoFilterMetrics: boolean
  /** 找出派生指标依赖的指标，并按指标的依赖关系，组织成按照依赖顺序的 Metric 列表，下面的 Metric 可以依赖上面的，后续为每一个元素创作一个 metric CTE 虚拟表，这里面包含了派生指标依赖的指标 */
  tableMetricNames: string[]
  // metricNames: string[]
  timeGroupBy?: string // 时间的 groupBy 使用别名
  timeWhereForPeriodOverPeriod: string // 时间的 where
  sharedGroupBys: string[] // 除 lastSqlPart 之外都复用的 groupBys，会包含 where 中用到的维度
  sharedWhereWithTime: string // 除 lastSqlPart 之外都复用的 where
  sharedWhereWithoutTime: string // 除去时间过滤的 where，用于同环比的计算
  // lastSql 不需要 groupBy，因为前面的查询都做过了 groupBy
  lastSqlPart: {
    select: string
    metricNames: string[] // 需要 select 出来的 metric 列表
    // lastSql 单独使用的 where，就是 left 或者 right 值有 metric 的 where
    where?: string
    // groupBys: string[]
    orderBys: string[]
    limit?: number
  }
  // 每一列的元信息，对应 lastSqlPart 的 select 部分
  rowsMetadata: RowsMetadata
  periodOverPeriods?: PeriodOverPeriodType[]
  // where中是否有指标
  isMetricInWhere: boolean
}

/**
 * 从大模型提参的 where 中找出 WhereDerived
 * 1. 解析出 where 中包含 metric 的部分与不包含 metric 的部分
 * 2. 替换调 where 中维度的表达式
 * 注:
 * 1. lastWhere 用在最后一个SQL中 包含指标相关的数据
 * 2. sharedWhere 作用在所有SQL中 不能包含指标相关的数据 因为维度的SQL statement中没有指标相关数据
 */
export function generateWhereDeriveMeta(
  verifiedMeta: VerifiedMeta,
  metrics: NoListMetric[],
  dimensions: Dimension[],
): DerivedMeta {
  const where = verifiedMeta.queryParams.where
  if (where == null || where.length === 0) {
    return {
      ...verifiedMeta,
      whereDerived: {
        metricNames: [],
        dimensionNames: [],
        sharedWhere: '',
        lastWhere: '',
      },
    }
  }
  const metricNamesInWhere: string[] = []
  const dimensionNamesInWhere: string[] = []
  const lastWherePredicts: string[] = []

  const sqlParser = new Parser()
  const selectPrefix = 'SELECT * FROM `table1` WHERE '
  const ast = sqlParser.astify(selectPrefix + where)
  if (Array.isArray(ast)) {
    throw new Error('暂不支持 where 中包含多个语句，不应该出现: ' + where)
  }
  if (ast.type !== 'select' || ast.where == null) {
    throw new Error('where 不能为空')
  }
  const whereClause = ast.where
  if (whereClause.type === 'function') {
    throw new Error('暂不支持 where 中只有函数: ' + where)
  }

  // 当 left 为 metric 的时候，需要新建子查询，并拆分 where，这里做个记录
  let isMetricInBinaryExpr = false

  /**
   * 更新 where node 的 AST，递归函数
   * 返回更新后的 AST
   */
  const updateWhereNode = (
    node: Expr | ColumnRef | Param | Value | Function,
    leftOrRight: 'left' | 'right' | 'root',
  ): Expr | ColumnRef | Param | Value | Function => {
    if (leftOrRight === 'root' && node.type !== 'binary_expr') {
      // root 的只支持 'binary_expr' 类型。如果是其他类型，直接返回
      return node
    }
    const nodeType = node.type as WhereNodeType
    switch (nodeType) {
      case 'param':
      case 'unary_expr': // NOT
      case 'interval':
      case 'single_quote_string':
      case 'number':
      case 'null':
      case 'double_quote_string':
      case 'expr_list': // IN (1, 2, 3) 的右值
      case 'function':
        return node
      case 'column_ref': {
        // where 的左值或为 dimension 时，需要把 dimension 替换成 expr，因为 where 在 select 后执行，别名还没有生效
        // where 的右值为 dimension 时，需要把 dimension 替换成 expr。（场景如2个不相关的维度要做比较）
        // where 的左值或右值为 metric 需要创建 metric 子查询
        const theNode = node as ColumnRef
        const dimensionFound = dimensions.find((d) => d.name === theNode.column)
        if (VIRTUAL_TIME_DIM_NAMES.includes(theNode.column)) {
          console.error('大模型提参where的时候不应该包含时间维度', theNode.column)
        }
        if (dimensionFound) {
          theNode.column = dimensionFound.expr // 无论左右，dimension 都替换成 expr
          dimensionNamesInWhere.push(dimensionFound.name)
        }
        const metricFound = metrics.find((m) => m.name === theNode.column)
        if (metricFound) {
          if (leftOrRight === 'left') {
            // 当 where 的左值中用到 metric 的时候，且没有在 metric 当中，需要添加到 select 当中，不然会报错
            metricNamesInWhere.push(metricFound.name)
            isMetricInBinaryExpr = true
          }
          if (leftOrRight === 'right') {
            metricNamesInWhere.push(metricFound.name)
            isMetricInBinaryExpr = true
            // theNode.column = `(SELECT ${this.metric2SqlByType(metricFound)} AS ${metricFound.name} FROM ${this.metricConfig.name})`
          }
        }
        return node
      }
      case 'binary_expr': {
        const theNode = node as Expr
        theNode.left = updateWhereNode(theNode.left, 'left') as Expr
        theNode.right = updateWhereNode(theNode.right, 'right') as Expr
        if (isMetricInBinaryExpr) {
          lastWherePredicts.push(sqlParser.exprToSQL(theNode).replace(/`/g, '')) // 要去掉额外加的 ``
          isMetricInBinaryExpr = false
        }
        return theNode
      }
      default:
        console.error('未知的 nodeType，请添加处理：', nodeType)
        return assertExhaustive(nodeType)
    }
  }

  const updatedNode = updateWhereNode(whereClause, 'root')
  ast.where = updatedNode as Expr
  // 目前先去掉所有的 ``
  const allWhere = sqlParser.sqlify(ast).replace(selectPrefix, '').replace(/`/g, '')
  // 从 allWhere 中剥离出 lastWherePredicts，剩下的就是 sharedWhere
  const allWhereArr = allWhere.split(' AND ')
  const sharedWhere = allWhereArr.filter((w) => !lastWherePredicts.includes(w)).join(' AND ')
  const lastWhere = lastWherePredicts.join(' AND ')
  return {
    ...verifiedMeta,
    whereDerived: {
      metricNames: metricNamesInWhere,
      dimensionNames: dimensionNamesInWhere,
      sharedWhere: sharedWhere.length > 0 ? '(' + sharedWhere + ')' : sharedWhere,
      lastWhere,
    },
  }
}

export function generateSummaryMeta(derivedMeta: DerivedMeta): SummaryMeta {
  // 返回 summary 的结构
  return {
    time: {
      timeDimensionName: derivedMeta.timeSqlPart?.timeDimensionName ?? '',
      groupBy: derivedMeta.timeSqlPart?.groupBy,
      where: derivedMeta.timeSqlPart?.where || '',
      whereForPeriodOverPeriod: derivedMeta.timeSqlPart?.whereForPeriodOverPeriod || '',
    },
    // 这里没有加上 whereDerived.dimensionNames
    groupBys: derivedMeta.queryParams.groupBys ?? [],
    metricNames: Array.from(new Set([...derivedMeta.queryParams.metricNames, ...derivedMeta.whereDerived.metricNames])),
    sharedWhere: derivedMeta.whereDerived.sharedWhere,
    lastWhere: derivedMeta.whereDerived.lastWhere,
    limit: derivedMeta.queryParams.limit,
    orderBys: derivedMeta.queryParams.orderBys,
    periodOverPeriods: derivedMeta.queryParams.periodOverPeriods,
  }
}

export function generateSqlMeta(summaryMeta: SummaryMeta, metrics: NoListMetric[], dimensions: Dimension[]): SqlMeta {
  const isAllSimpleNoFilterMetrics =
    metrics.filter((m) => summaryMeta.metricNames.includes(m.name) && m.type === 'simple' && m.filter == null)
      .length === summaryMeta.metricNames.length
  const tableMetricNames: string[] = getMetricDependents(summaryMeta.metricNames, metrics)

  // 如果指标列表为空 需要注意','
  let selectElements: string[] = []
  const rowsMetadata: RowsMetadata = []
  if (summaryMeta.time.groupBy) {
    // 加上日期提参的 group by
    selectElements.push('DIM_TBL.' + DATE_ALIAS)
    const virtualTimeDimension: VirtualTimeDimension = {
      id: nanoid(),
      type: 'virtual-time',
      name: DATE_ALIAS,
      label: '日期',
      synonyms: [],
      filterSwitch: true,
      expr: summaryMeta.time.groupBy,
    }
    rowsMetadata.push({
      type: 'dimension',
      value: virtualTimeDimension,
    })
  }
  if (summaryMeta.groupBys.length > 0) {
    selectElements = selectElements.concat(summaryMeta.groupBys.map((groupBy) => 'DIM_TBL.' + groupBy))
    const groupByDimensions = dimensions.filter((d) => summaryMeta.groupBys.includes(d.name))
    const items: { type: 'dimension'; value: Dimension }[] = groupByDimensions.map((item) => {
      return {
        type: 'dimension',
        // FIXME: rowsMetaData中暂时去掉码值，否则导致数据太庞大，Out of sort memory
        value: { ...item, values: [] },
      }
    })
    rowsMetadata.push(...items)
  }
  // 添加 metric
  summaryMeta.metricNames.forEach((name) => {
    const metric = metrics.find((m) => m.name === name)
    if (!metric) {
      throw new Error(`metric ${name} not found`)
    }
    selectElements.push(`${name}_TBL.${name}`)
    rowsMetadata.push({
      type: 'metric',
      value: metric,
    })
    // 添加同环比的指标
    if (summaryMeta.periodOverPeriods) {
      summaryMeta.periodOverPeriods.forEach((type) => {
        selectElements.push(`${name}_TBL.${PeriodOverPeriodMetricConfig[type].metricPrefix}${name}`)
        rowsMetadata.push({
          type: 'metric',
          value: {
            id: nanoid(),
            name: `${PeriodOverPeriodMetricConfig[type].metricPrefix}${name}`,
            label: `${metric.label}${PeriodOverPeriodMetricConfig[type].labelSuffix}`,
            synonyms: [],
            type: 'periodOverPeriod',
            formatTemplate:
              PeriodOverPeriodMetricConfig[type].formatTemplate === 'same_with_metric'
                ? metric.formatTemplate
                : PeriodOverPeriodMetricConfig[type].formatTemplate,
            typeParams: {
              type: type,
              metric: metric.name,
            },
          },
        })
      })
    }
  })

  return {
    isAllSimpleNoFilterMetrics,
    tableMetricNames: tableMetricNames,
    timeGroupBy: summaryMeta.time.groupBy,
    timeWhereForPeriodOverPeriod: summaryMeta.time.whereForPeriodOverPeriod,
    sharedGroupBys: summaryMeta.groupBys,
    sharedWhereWithTime: [summaryMeta.time.where, summaryMeta.sharedWhere].filter((w) => w.length > 0).join(' AND '),
    sharedWhereWithoutTime: summaryMeta.sharedWhere,
    lastSqlPart: {
      select: selectElements.length > 0 ? selectElements.join(', ') : '*',
      metricNames: summaryMeta.metricNames,
      where: summaryMeta.lastWhere,
      // groupBys: summaryMeta.groupBys,
      limit: summaryMeta.limit,
      orderBys: summaryMeta.orderBys || [],
    },
    rowsMetadata: rowsMetadata,
    periodOverPeriods: summaryMeta.periodOverPeriods,
    isMetricInWhere: false,
  }
}

function generateDimensionVirtualTable(
  sqlMeta: SqlMeta,
  dimensions: Dimension[],
  tableName: string,
  sharedWhereWithTime: string,
) {
  const dimensionVirtualTableSelectElements: string[] = []
  if (sqlMeta.timeGroupBy) {
    // 加上日期提参的 group by
    dimensionVirtualTableSelectElements.push(sqlMeta.timeGroupBy + ` AS ${DATE_ALIAS}`)
  }
  if (sqlMeta.sharedGroupBys.length > 0) {
    const groupByDimensions = dimensions.filter((d) => sqlMeta.sharedGroupBys.includes(d.name))
    dimensionVirtualTableSelectElements.push(...groupByDimensions.map((d) => dimension2Sql(d)))
  }
  let dimensionVirtualTable: string | null = null
  if (dimensionVirtualTableSelectElements.length > 0) {
    dimensionVirtualTable = sqlStatementToSql({
      select: 'DISTINCT ' + dimensionVirtualTableSelectElements.join(', '),
      from: tableName,
      where: sharedWhereWithTime,
    })
  }
  return dimensionVirtualTable
}

/** 是否启用了月份周期的同环比 */
function hasMonthPeriodOverPeriod(periodOverPeriods?: PeriodOverPeriodType[]) {
  return (
    periodOverPeriods &&
    periodOverPeriods.length > 0 &&
    PeriodOverPeriodMonthNames.some((name) => periodOverPeriods.includes(name as PeriodOverPeriodType))
  )
}
/** 是否启用了季度周期的同环比 */
function hasQuarterPeriodOverPeriod(periodOverPeriods?: PeriodOverPeriodType[]) {
  return (
    periodOverPeriods &&
    periodOverPeriods.length > 0 &&
    PeriodOverPeriodQuarterNames.some((name) => periodOverPeriods.includes(name as PeriodOverPeriodType))
  )
}

/**
 * 把 sqlStatement 拼成 SQL
 */
function sqlStatementToSql(statement: SqlStatement) {
  return [
    `SELECT`,
    Array.isArray(statement.select) ? statement.select.join(', ') : statement.select,
    `FROM`,
    statement.from,
    statement.join && statement.join.length > 0 ? statement.join : '',
    statement.where && statement.where.length > 0 ? 'WHERE ' + statement.where : '',
    statement.groupBys && statement.groupBys?.length > 0 ? `GROUP BY ${statement.groupBys.join(', ')}` : '',
    statement.orderBys && statement.orderBys.length > 0 ? `ORDER BY ${statement.orderBys.join(', ')}` : '',
    statement.limit ? `LIMIT ${statement.limit}` : '',
  ]
    .filter((x) => x.length > 0)
    .join(' ')
}

function getDateSqlPartOfTime(timeDimensionDatum: TimeDimensionDatum): {
  yearAndMonth: string
  year: string
  month: string
  yearAndQuarter: string
  quarter: string
} {
  const timeDimensionType = timeDimensionDatum.timeDimensionType
  const emptyResult = {
    yearAndMonth: '',
    year: '',
    month: '',
    yearAndQuarter: '',
    quarter: '',
  }
  switch (timeDimensionType) {
    case 'date':
    case 'datetime':
      console.error('尚不支持')
      return emptyResult
    case 'string': {
      const timeDimensionFormat = timeDimensionDatum.timeDimensionFormat
      const yyyyMmYearAndMonth = `LEFT(${timeDimensionDatum.timeDimensionName}, 6)`
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const yyMmYearAndMonth = `LEFT(${timeDimensionDatum.timeDimensionName}, 4)`
      const yyyyYear = `CAST(SUBSTRING(${timeDimensionDatum.timeDimensionName}, 1, 4) AS INTEGER)`
      const yyyyMmMonth = `CAST(SUBSTRING(${timeDimensionDatum.timeDimensionName}, 5, 2) AS INTEGER)`
      // eslint-disable-next-line @typescript-eslint/naming-convention
      const yyyy_mmYearAndMonth = `LEFT(${timeDimensionDatum.timeDimensionName}, 7)`
      // eslint-disable-next-line @typescript-eslint/naming-convention
      const yyyy_mmMonth = `CAST(SUBSTRING(${timeDimensionDatum.timeDimensionName}, 6, 2) AS INTEGER)`
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const yyYear = `CAST(SUBSTRING(${timeDimensionDatum.timeDimensionName}, 1, 2) AS INTEGER)`
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const yyMmMonth = `CAST(SUBSTRING(${timeDimensionDatum.timeDimensionName}, 3, 2) AS INTEGER)`
      switch (timeDimensionFormat) {
        case undefined:
          console.error('timeDimensionFormat 不能为空')
          return emptyResult
        case 'yyyy':
          console.error('数据为年周期，无法支持月的同环比')
          return emptyResult
        case 'yyyyMMDD':
        case 'yyyyMMdd':
          return {
            yearAndMonth: yyyyMmYearAndMonth,
            year: yyyyYear,
            month: yyyyMmMonth,
            yearAndQuarter: `CONCAT(YEAR(STR_TO_DATE(${timeDimensionDatum.timeDimensionName}, '%Y%m%d')), '-Q', QUARTER(STR_TO_DATE(${timeDimensionDatum.timeDimensionName}, '%Y%m%d')))`,
            quarter: `QUARTER(STR_TO_DATE(${timeDimensionDatum.timeDimensionName}, '%Y%m%d'))`,
          }
        case 'yyyyMM':
          return {
            yearAndMonth: yyyyMmYearAndMonth,
            year: yyyyYear,
            month: yyyyMmMonth,
            yearAndQuarter: `CONCAT(YEAR(STR_TO_DATE(CONCAT(${timeDimensionDatum.timeDimensionName}, '01'), '%Y%m%d')), '-Q', QUARTER(STR_TO_DATE(CONCAT(${timeDimensionDatum.timeDimensionName}, '01'), '%Y%m%d')))`,
            quarter: `QUARTER(STR_TO_DATE(CONCAT(${timeDimensionDatum.timeDimensionName}, '01'), '%Y%m%d'))`,
          }
        case 'yyyy-MM':
          return {
            yearAndMonth: yyyy_mmYearAndMonth,
            year: yyyyYear,
            month: yyyy_mmMonth,
            yearAndQuarter: `CONCAT(YEAR(STR_TO_DATE(CONCAT(${timeDimensionDatum.timeDimensionName}, '-01'), '%Y-%m-%d')), '-Q', QUARTER(STR_TO_DATE(CONCAT(${timeDimensionDatum.timeDimensionName}, '-01'), '%Y-%m-%d')))`,
            quarter: `QUARTER(STR_TO_DATE(CONCAT(${timeDimensionDatum.timeDimensionName}, '-01'), '%Y-%m-%d'))`,
          }
        case 'yyyy/MM':
          return {
            yearAndMonth: yyyy_mmYearAndMonth,
            year: yyyyYear,
            month: yyyy_mmMonth,
            yearAndQuarter: `CONCAT(YEAR(STR_TO_DATE(CONCAT(${timeDimensionDatum.timeDimensionName}, '/01'), '%Y/%m/%d')), '-Q', QUARTER(STR_TO_DATE(CONCAT(${timeDimensionDatum.timeDimensionName}, '/01'), '%Y/%m/%d')))`,
            quarter: `QUARTER(STR_TO_DATE(CONCAT(${timeDimensionDatum.timeDimensionName}, '/01'), '%Y/%m/%d'))`,
          }
        case 'yyyy-MM-DD':
        case 'yyyy-MM-dd':
          return {
            yearAndMonth: yyyy_mmYearAndMonth,
            year: yyyyYear,
            month: yyyy_mmMonth,
            yearAndQuarter: `CONCAT(YEAR(STR_TO_DATE(${timeDimensionDatum.timeDimensionName}, '%Y-%m-%d')), '-Q', QUARTER(STR_TO_DATE(${timeDimensionDatum.timeDimensionName}, '%Y-%m-%d')))`,
            quarter: `QUARTER(STR_TO_DATE(${timeDimensionDatum.timeDimensionName}, '%Y-%m-%d'))`,
          }
        case 'yyyy_MM_DD':
        case 'yyyy_MM_dd':
          return {
            yearAndMonth: yyyy_mmYearAndMonth,
            year: yyyyYear,
            month: yyyy_mmMonth,
            yearAndQuarter: `CONCAT(YEAR(STR_TO_DATE(${timeDimensionDatum.timeDimensionName}, '%Y_%m_%d')), '-Q', QUARTER(STR_TO_DATE(${timeDimensionDatum.timeDimensionName}, '%Y_%m_%d')))`,
            quarter: `QUARTER(STR_TO_DATE(${timeDimensionDatum.timeDimensionName}, '%Y_%m_%d'))`,
          }
        case 'yyyy/MM/DD':
        case 'yyyy/MM/dd':
          return {
            yearAndMonth: yyyy_mmYearAndMonth,
            year: yyyyYear,
            month: yyyy_mmMonth,
            yearAndQuarter: `CONCAT(YEAR(STR_TO_DATE(${timeDimensionDatum.timeDimensionName}, '%Y/%m/%d')), '-Q', QUARTER(STR_TO_DATE(${timeDimensionDatum.timeDimensionName}, '%Y/%m/%d')))`,
            quarter: `QUARTER(STR_TO_DATE(${timeDimensionDatum.timeDimensionName}, '%Y/%m/%d'))`,
          }
        default:
          return assertExhaustive(timeDimensionFormat)
      }
    }
    default:
      assertExhaustive(timeDimensionType)
  }
}

function generateSqlOfSimpleMetric(
  metric: SimpleMetric,
  sqlMeta: SqlMeta,
  metricNameWithSql: { name: string; sql: string }[],
  tableName: string,
  dimensions: Dimension[],
  timeDimensionDatum?: TimeDimensionDatum,
) {
  // 如果开启了同环比，就需要生成多个子查询语句
  if (hasMonthPeriodOverPeriod(sqlMeta.periodOverPeriods) && timeDimensionDatum != null) {
    const groupByDimensions =
      sqlMeta.sharedGroupBys.length > 0 ? dimensions.filter((d) => sqlMeta.sharedGroupBys.includes(d.name)) : []
    const groupByWithAs = groupByDimensions.map((d) => dimension2Sql(d))
    const groupBys = [DATE_ALIAS, 'year_num', 'month_num', ...groupByDimensions.map((d) => d.name)]
    const dateSqlPartOfTime = getDateSqlPartOfTime(timeDimensionDatum)
    const selectPart = [
      `${dateSqlPartOfTime.yearAndMonth} AS ${DATE_ALIAS}`,
      ...groupByWithAs,
      `${dateSqlPartOfTime.year} AS year_num`,
      `${dateSqlPartOfTime.month} AS month_num`,
      `${metricNameWithSql.find((m) => m.name === metric.name)!.sql} as ${metric.name}`,
    ]
      .filter((x) => x.length > 0)
      .join(', ')
    // 返回多个子查询语句数组
    const monthlyMetricTable = {
      cteName: metric.name + '_MONTHLY_TBL',
      sql: sqlStatementToSql({
        select: selectPart,
        from: tableName,
        where: [sqlMeta.sharedWhereWithoutTime, metric.filter].filter((w) => w).join(' AND '),
        groupBys: groupBys,
        orderBys: [DATE_ALIAS + ' desc'],
      }),
    }
    const recentMetricTable = {
      cteName: metric.name + '_RECENT_TBL',
      // 把 _MONTHLY_TBL 和 DIM_TBL join，因为 DIM_TBL 已经过滤了时间，join 之后就是过滤时间后的数据
      sql: sqlStatementToSql({
        select: `${groupBys.join(', ')}, ${metric.name}`,
        from: `${metric.name}_MONTHLY_TBL`,
        where: sqlMeta.timeWhereForPeriodOverPeriod,
      }),
    }
    const groupByToSelect = groupBys.map((groupBy) => `current_month.${groupBy}`).join(', ')
    const finalMetricTable = {
      cteName: metric.name + '_TBL',
      sql: sqlStatementToSql({
        select: [
          groupByToSelect,
          `current_month.${metric.name}`,
          `previous_month.${metric.name} as pre_month_${metric.name}`,
          `previous_year_month.${metric.name} as pre_year_month_${metric.name}`,
          `(current_month.${metric.name} - previous_month.${metric.name}) as mom_growth_${metric.name}`,
          `(current_month.${metric.name} - previous_year_month.${metric.name}) as yoy_month_growth_${metric.name}`,
          `((current_month.${metric.name} - previous_month.${metric.name}) / previous_month.${metric.name}) as mom_growth_rate_${metric.name}`,
          `((current_month.${metric.name} - previous_year_month.${metric.name}) / previous_year_month.${metric.name}) as yoy_month_growth_rate_${metric.name}`,
        ],
        from: `${metric.name}_RECENT_TBL AS current_month`,
        join: [
          `LEFT JOIN`,
          `${metric.name}_MONTHLY_TBL AS previous_month`,
          `ON`,
          `(current_month.year_num * 12 + current_month.month_num - 1) = (previous_month.year_num * 12 + previous_month.month_num)`,
          // 需要把维度的 group by 加上
          groupByDimensions.length > 0
            ? groupByDimensions.map((d) => `AND current_month.${d.name} = previous_month.${d.name}`).join(' ')
            : '',
          `LEFT JOIN`,
          `${metric.name}_MONTHLY_TBL AS previous_year_month`,
          `ON`,
          `(current_month.year_num - 1) = previous_year_month.year_num AND current_month.month_num = previous_year_month.month_num`,
          // 需要把维度的 group by 加上
          groupByDimensions.length > 0
            ? groupByDimensions.map((d) => `AND current_month.${d.name} = previous_year_month.${d.name}`).join(' ')
            : '',
        ]
          .filter((x) => x.length > 0)
          .join(' '),
      }),
    }
    return [monthlyMetricTable, recentMetricTable, finalMetricTable]
  } else if (hasQuarterPeriodOverPeriod(sqlMeta.periodOverPeriods) && timeDimensionDatum != null) {
    const groupByDimensions =
      sqlMeta.sharedGroupBys.length > 0 ? dimensions.filter((d) => sqlMeta.sharedGroupBys.includes(d.name)) : []
    const groupByWithAs = groupByDimensions.map((d) => dimension2Sql(d))
    const groupBys = [DATE_ALIAS, ...groupByDimensions.map((d) => d.name)]
    const groupBysWithYearQuarter = [DATE_ALIAS, 'year_num', 'quarter_num', ...groupByDimensions.map((d) => d.name)]
    const dateSqlPartOfTime = getDateSqlPartOfTime(timeDimensionDatum)
    const selectPart = [
      `${dateSqlPartOfTime.yearAndQuarter} AS ${DATE_ALIAS}`,
      ...groupByWithAs,
      `${dateSqlPartOfTime.year} AS year_num`,
      // TODO: 目前只有 yyyyMM 的日期格式，未来要支持其他的格式
      `${dateSqlPartOfTime.quarter} AS quarter_num`,
      `${metricNameWithSql.find((m) => m.name === metric.name)!.sql} as ${metric.name}`,
    ]
      .filter((x) => x.length > 0)
      .join(', ')
    // 返回多个子查询语句数组
    const quarterlyMetricTable = {
      cteName: metric.name + '_QUARTERLY_TBL',
      sql: sqlStatementToSql({
        select: selectPart,
        from: tableName,
        where: [sqlMeta.sharedWhereWithoutTime, metric.filter].filter((w) => w).join(' AND '),
        groupBys: groupBysWithYearQuarter,
        orderBys: [DATE_ALIAS + ' desc'],
      }),
    }
    const recentMetricTable = {
      cteName: metric.name + '_RECENT_TBL',
      sql: sqlStatementToSql({
        select: `${groupBysWithYearQuarter.map((v) => `${metric.name}_QUARTERLY_TBL.${v}`).join(', ')}, ${metric.name}`,
        from: composeLastTableJoinPart([`${metric.name}_QUARTERLY`], groupBys),
      }),
    }
    const groupByToSelect = groupBys.map((groupBy) => `current_quarter.${groupBy}`).join(', ')
    const finalMetricTable = {
      cteName: metric.name + '_TBL',
      sql: sqlStatementToSql({
        select: [
          groupByToSelect,
          `current_quarter.${metric.name}`,
          `previous_quarter.${metric.name} as pre_quarter_${metric.name}`,
          `previous_year_quarter.${metric.name} as pre_year_quarter_${metric.name}`,
          `(current_quarter.${metric.name} - previous_quarter.${metric.name}) as qoq_growth_${metric.name}`,
          `(current_quarter.${metric.name} - previous_year_quarter.${metric.name}) as yoy_quarter_growth_${metric.name}`,
          `((current_quarter.${metric.name} - previous_quarter.${metric.name}) / previous_quarter.${metric.name}) as qoq_growth_rate_${metric.name}`,
          `((current_quarter.${metric.name} - previous_year_quarter.${metric.name}) / previous_year_quarter.${metric.name}) as yoy_quarter_growth_rate_${metric.name}`,
        ],

        from: `${metric.name}_RECENT_TBL AS current_quarter`,
        join: [
          `LEFT JOIN`,
          `${metric.name}_QUARTERLY_TBL AS previous_quarter`,
          `ON`,
          `(current_quarter.year_num * 4 + current_quarter.quarter_num - 1) = (previous_quarter.year_num * 4 + previous_quarter.quarter_num)`,
          // 需要把维度的 group by 加上
          groupByDimensions.length > 0
            ? groupByDimensions.map((d) => `AND current_quarter.${d.name} = previous_quarter.${d.name}`).join(' ')
            : '',
          `LEFT JOIN`,
          `${metric.name}_QUARTERLY_TBL AS previous_year_quarter`,
          `ON`,
          `(current_quarter.year_num - 1) = previous_year_quarter.year_num AND current_quarter.quarter_num = previous_year_quarter.quarter_num`,
          // 需要把维度的 group by 加上
          groupByDimensions.length > 0
            ? groupByDimensions.map((d) => `AND current_quarter.${d.name} = previous_year_quarter.${d.name}`).join(' ')
            : '',
        ]
          .filter((x) => x.length > 0)
          .join(' '),
      }),
    }
    return [quarterlyMetricTable, recentMetricTable, finalMetricTable]
  } else {
    const metricSqlPart = metricNameWithSql.find((m) => m.name === metric.name)!.sql
    // 生成这个指标单独的查询语句。和 dimensionVirtualTableSql 关联，如果有
    const dimensionVirtualTableSelectElements: string[] = []
    if (sqlMeta.timeGroupBy) {
      // 加上日期提参的 group by
      dimensionVirtualTableSelectElements.push(sqlMeta.timeGroupBy + ` AS ${DATE_ALIAS}`)
    }
    const groupByDimensions =
      sqlMeta.sharedGroupBys.length > 0 ? dimensions.filter((d) => sqlMeta.sharedGroupBys.includes(d.name)) : []
    dimensionVirtualTableSelectElements.push(...groupByDimensions.map((d) => dimension2Sql(d)))
    const selectElements = [`${metricSqlPart} as ${metric.name}`, ...dimensionVirtualTableSelectElements]
    const wherePart = [sqlMeta.sharedWhereWithTime, metric.filter].filter((w) => w).join(' AND ')
    const groupByPart = sqlMeta.timeGroupBy ? [DATE_ALIAS, ...sqlMeta.sharedGroupBys] : sqlMeta.sharedGroupBys
    return {
      cteName: metric.name + '_TBL',
      sql: sqlStatementToSql({
        select: selectElements.join(', '),
        from: tableName,
        where: wherePart,
        groupBys: groupByPart,
      }),
    }
  }
}

function generateSqlOfDerivedMetric(
  metric: DerivedMetric,
  metricSqlPart: string,
  hasDimensionVirtualTable: boolean,
  sqlMeta: SqlMeta,
) {
  // 派生指标没有 filter
  const requiredMetricNames = [...new Set(metric.typeParams.metrics.map((m) => m.name))]
  if (!hasDimensionVirtualTable) {
    // 当没有 groupBys 的时候，只有一行，可以直接 from 多个 table 即可
    return {
      cteName: metric.name + '_TBL',
      sql: sqlStatementToSql({
        select: `${metricSqlPart} as ${metric.name}`,
        from: requiredMetricNames.map((m) => `${m}_TBL`).join(', '),
      }),
    }
  } else {
    // 生成这个指标单独的查询语句。和 dimensionVirtualTableSql 关联，如果有
    const dimensionVirtualTableSelectElements: string[] = []
    if (sqlMeta.timeGroupBy) {
      // 加上日期提参的 group by
      dimensionVirtualTableSelectElements.push(`DIM_TBL.${DATE_ALIAS}`)
    }
    dimensionVirtualTableSelectElements.push(...sqlMeta.sharedGroupBys.map((d) => `DIM_TBL.${d}`))
    const selectElements = [`${metricSqlPart} as ${metric.name}`, ...dimensionVirtualTableSelectElements]
    // 派生指标的逻辑是从其他指标虚拟表中查询数据并计算，不能直接 from raw table。需要从 DIM_TBL 开始关联起来
    return {
      cteName: metric.name + '_TBL',
      sql: sqlStatementToSql({
        select: selectElements,
        from: composeLastTableJoinPart(
          requiredMetricNames,
          sqlMeta.sharedGroupBys.concat(sqlMeta.timeGroupBy ? [DATE_ALIAS] : []),
        ),
      }),
    }
  }
}

// 旁路：全部原子指标（直接度量自动创建），并且单个指标模板配置不带 filter 条件
function generateSqlOfSimpleWithoutFilter(
  sqlMeta: SqlMeta,
  metricNameWithSql: { name: string; sql: string }[],
  tableName: string,
  dimensions: Dimension[],
  //timeDimensionDatum?: TimeDimensionDatum,
): string {
  // 1.Trick，尝试基于现在单个原子指标 CTE 先生成第一个指标，然后剩下的指标直接塞到 select 子句中
  // 2.虚拟维表DIM_TBL中 select 子句需要挪过来（维表字段来自于Group），塞到 select 子句中
  // 3.Order By 和 Limit 子句需要从 lastSql中拿过来
  // 4.需要适配sql结果信息，和原来多个 CTE 方式能够接上，不能改动前端代码，需要看如何适配？？？

  // TODO:
  const allMetricSqlPart = sqlMeta.tableMetricNames.map((metricName) => {
    return metricNameWithSql.find((m) => m.name === metricName)!.sql + ' AS ' + metricName
  })

  // 生成这个指标单独的查询语句和 dimensionVirtualTableSql 关联，如果有
  const dimensionVirtualTableSelectElements: string[] = []
  if (sqlMeta.timeGroupBy) {
    // 加上日期提参的 group by
    dimensionVirtualTableSelectElements.push(sqlMeta.timeGroupBy + ` AS ${DATE_ALIAS}`)
  }
  const groupByDimensions =
    sqlMeta.sharedGroupBys.length > 0 ? dimensions.filter((d) => sqlMeta.sharedGroupBys.includes(d.name)) : []
  dimensionVirtualTableSelectElements.push(...groupByDimensions.map((d) => dimension2Sql(d)))
  const selectElements = [...allMetricSqlPart, ...dimensionVirtualTableSelectElements]
  const wherePart = [sqlMeta.sharedWhereWithTime].filter((w) => w).join(' AND ')
  const groupByPart = sqlMeta.timeGroupBy ? [DATE_ALIAS, ...sqlMeta.sharedGroupBys] : sqlMeta.sharedGroupBys
  return sqlStatementToSql({
    select: selectElements.join(', '),
    from: tableName,
    where: wherePart,
    groupBys: groupByPart,
    // order by 从 lastSql 中拿过来
    orderBys: sqlMeta.lastSqlPart.orderBys,
    // limit 从 lastSql 中拿过来
    limit: sqlMeta.lastSqlPart.limit,
  })
}

function enableBypass(sqlMeta: SqlMeta, metrics: NoListMetric[]): boolean {
  // 同环比使用 MultiAgent支持，不在 Bypass 这里支持
  if (
    PROCESS_ENV.DISABLE_SQL_GENERATE_BYPASS ||
    sqlMeta.isMetricInWhere ||
    sqlMeta.tableMetricNames.length === 0 ||
    hasMonthPeriodOverPeriod(sqlMeta.periodOverPeriods) ||
    hasQuarterPeriodOverPeriod(sqlMeta.periodOverPeriods)
  ) {
    return false
  }
  // 都是简单指标 不带单独过滤条件
  for (const item of sqlMeta.tableMetricNames) {
    const metric = metrics.find((m) => m.name === item)
    if (metric?.type === 'simple') {
      const simpleMetric = metric as SimpleMetric
      if (simpleMetric.filter && simpleMetric.filter.length > 0) {
        return false
      }
    } else {
      return false
    }
  }
  return true
}

export function generateSql(
  sqlMeta: SqlMeta,
  metricNameWithSql: { name: string; sql: string }[],
  tableName: string,
  metrics: NoListMetric[],
  dimensions: Dimension[],
  timeDimensionDatum?: TimeDimensionDatum,
): string {
  // 如果有 timeGroupBy sharedGroupBys 都不为空，这个时候创建维度虚拟表
  const hasDimensionVirtualTable =
    (sqlMeta.timeGroupBy && sqlMeta.timeGroupBy.length > 0) || sqlMeta.sharedGroupBys.length > 0

  const dimensionVirtualTableSql = generateDimensionVirtualTable(
    sqlMeta,
    dimensions,
    tableName,
    sqlMeta.sharedWhereWithTime,
  )

  // 旁路优化：全部原子指标且无过滤条件，并且无同环比（同环比走 MultiAgent 复杂问题拆解）
  if (enableBypass(sqlMeta, metrics)) {
    console.warn(chalk.red('generate sql use bypass >>>'))
    return generateSqlOfSimpleWithoutFilter(sqlMeta, metricNameWithSql, tableName, dimensions)
  }

  // 生成 metric 虚拟表
  const metricTableSqlArr: { cteName: string; sql: string }[] = sqlMeta.tableMetricNames.flatMap((metricName) => {
    const metric = metrics.find((m) => m.name === metricName)
    if (metric?.type === 'simple') {
      return generateSqlOfSimpleMetric(metric, sqlMeta, metricNameWithSql, tableName, dimensions, timeDimensionDatum)
    } else if (metric?.type === 'derived') {
      const metricSqlPart = metricNameWithSql.find((m) => m.name === metricName)!.sql
      return generateSqlOfDerivedMetric(metric, metricSqlPart, hasDimensionVirtualTable, sqlMeta)
    } else if (metric?.type === 'ratio') {
      // TODO: ratio 指标可以拆解为依赖一个 不带 where&groupBy 的 simple metric 的 derived metric。然后就复用 derived 的逻辑
      // 占比指标的分母只支持普通指标，分子支持普通指标和派生指标，占比指标没有 filter
      const requiredMetricNames = [metric.typeParams.numerator]
      const denominatorMetric = metrics.find((m) => m.name === metric.typeParams.denominator)! as SimpleMetric
      if (denominatorMetric.type !== 'simple') {
        console.error(
          `denominator metric ${metric.typeParams.denominator} type not supported, only support simple metric`,
        )
        throw new Error(
          `denominator metric ${metric.typeParams.denominator} type not supported, only support simple metric`,
        )
      }

      const metricNameSql = metricNameWithSql.find((m) => m.name === denominatorMetric.name)!.sql
      const denominatorSql = getPureSqlOfSimpleMetric(denominatorMetric, tableName, metricNameSql)
      const ratioSelectPart = `${metric.typeParams.numerator}_TBL.${metric.typeParams.numerator} / (${denominatorSql})`
      if (!hasDimensionVirtualTable) {
        // 当没有 groupBys 的时候，只有一行，可以直接 from 多个 table 即可
        return {
          cteName: metricName + '_TBL',
          sql: sqlStatementToSql({
            select: `${ratioSelectPart} as ${metricName}`,
            from: requiredMetricNames.map((m) => `${m}_TBL`).join(', '),
          }),
        }
      } else {
        const dimensionVirtualTableSelectElements: string[] = []
        if (sqlMeta.timeGroupBy) {
          // 加上日期提参的 group by
          dimensionVirtualTableSelectElements.push(`DIM_TBL.${DATE_ALIAS}`)
        }
        dimensionVirtualTableSelectElements.push(...sqlMeta.sharedGroupBys.map((d) => `DIM_TBL.${d}`))
        const selectElements = [`${ratioSelectPart} as ${metricName}`, ...dimensionVirtualTableSelectElements]
        return {
          cteName: metricName + '_TBL',
          sql: sqlStatementToSql({
            select: selectElements,
            from: composeLastTableJoinPart(
              requiredMetricNames,
              sqlMeta.sharedGroupBys.concat(sqlMeta.timeGroupBy ? [DATE_ALIAS] : []),
            ),
          }),
        }
      }
    } else {
      throw new Error(`metric ${metricName} type not supported`)
    }
  })

  if (!hasDimensionVirtualTable) {
    // 最后的查询语句使用第一个 metric 和其他 metric 依次做 cross join，也就是列合并
    const firstMetricName = sqlMeta.lastSqlPart.metricNames[0]
    const lastSql = [
      'SELECT',
      sqlMeta.lastSqlPart.select,
      'FROM',
      firstMetricName + '_TBL',
      ...sqlMeta.lastSqlPart.metricNames.slice(1).map((metricName) => `cross join ${metricName}_TBL`),
      sqlMeta.lastSqlPart.where ? `WHERE ${sqlMeta.lastSqlPart.where}` : '',
      sqlMeta.lastSqlPart.orderBys?.length > 0 ? `ORDER BY ${sqlMeta.lastSqlPart.orderBys.join(', ')}` : '',
      sqlMeta.lastSqlPart.limit ? `LIMIT ${sqlMeta.lastSqlPart.limit}` : '',
    ]
      .filter((x) => x.length > 0)
      .join(' ')
    const withPart = metricTableSqlArr
      .reduce((acc, cur) => {
        const sqlLines = cur.sql
          .split('\n')
          .map((line) => `  ${line}`)
          .join('\n')
        return acc + `, ${cur.cteName} AS (\n${sqlLines}\n)`
      }, '')
      .slice(2) // 去掉第一个逗号
    return 'WITH ' + withPart + '\n' + lastSql.trim()
  } else {
    // 最后的查询语句使用 DIM_TBL 依次 left join 每一个 metric
    const lastSql = sqlStatementToSql({
      select: sqlMeta.lastSqlPart.select,
      from: composeLastTableJoinPart(
        sqlMeta.lastSqlPart.metricNames,
        sqlMeta.sharedGroupBys.concat(sqlMeta.timeGroupBy ? [DATE_ALIAS] : []),
      ),
      where: sqlMeta.lastSqlPart.where,
      orderBys: sqlMeta.lastSqlPart.orderBys,
      limit: sqlMeta.lastSqlPart.limit,
    })

    // 把前 2 部分拼接起来做 with
    const allWith =
      dimensionVirtualTableSql == null
        ? metricTableSqlArr
        : [{ cteName: 'DIM_TBL', sql: dimensionVirtualTableSql }, ...metricTableSqlArr]
    const withPart = allWith
      .reduce((acc, cur) => {
        const sqlLines = cur.sql
          .split('\n')
          .map((line) => `  ${line}`)
          .join('\n')
        return acc + `, ${cur.cteName} AS (\n${sqlLines}\n)`
      }, '')
      .slice(2) // 去掉第一个逗号

    return 'WITH ' + withPart + '\n' + lastSql
  }
}

function composeLastTableJoinPart(metricNames: string[], groupBys: string[]): string {
  // 以 DIM_TBL 为主表，依次 left join 每一个 metric，使用 groupBys 作为 on 进行 join
  const joinPart = metricNames.reduce((acc, cur, _) => {
    const onPart: string[] = []

    groupBys.forEach((groupBy) => {
      onPart.push(`DIM_TBL.${groupBy} = ${cur}_TBL.${groupBy}`)
    })

    acc = acc + ` LEFT JOIN ${cur}_TBL ON ${onPart.join(' AND ')}`
    return acc
  }, '')
  return 'DIM_TBL' + joinPart
}

/** 找出指标依赖的子指标列表 */
export function getMetricDependents(metricNames: string[], allMetrics: NoListMetric[]): string[] {
  const metricsWithDependency = getAllMetricsWithDependency(allMetrics as NoListMetric[])
  const filteredMetrics = metricNames.map((name) => {
    const metric = metricsWithDependency.find((m) => m.metricName === name)
    if (!metric) {
      console.error('没有找到指标', metric)
      throw new Error(`metric ${name} not found`)
    }
    return metric
  })
  return getMetricOrderList(filteredMetrics)
}

/** 生成一个 simple 指标的直接查询语句，目前用于 ratio 指标的分母部分 */
function getPureSqlOfSimpleMetric(metric: SimpleMetric, tableName: string, metricNameSql: string) {
  const denominatorCTE = sqlStatementToSql({
    select: metricNameSql,
    from: tableName,
    where: metric.filter,
  })
  return denominatorCTE
}

/**
 * Metric2Sql 类，接收 metricConfig（包含 allDimensions, allMeasures 和 allMetrics）来做初始化，提供 toSql 方法
 */
export default class Metric2Sql {
  public metricConfig: MetricConfig

  constructor(metricConfig: MetricConfig) {
    this.metricConfig = metricConfig
  }

  /** 根据 MeasureName 查找 Measure */
  findMeasureByName(measureName: string): Measure {
    const measure = this.metricConfig.allMeasures.find((m) => m.name.toUpperCase() === measureName.toUpperCase())
    if (measure) {
      return measure
    }
    throw new Error(`measure ${measureName} 不能为空`)
  }

  hasMetric(metricName: string): boolean {
    return this.metricConfig.allMetrics.some((m) => m.name === metricName)
  }

  findMetricByName(
    metricName:
      | string
      | {
          name: string // metric name
        },
  ) {
    let name: string
    if (typeof metricName === 'string') {
      name = metricName
    } else {
      name = metricName.name
    }
    const metric = this.metricConfig.allMetrics.find((m) => m.name.toUpperCase() === name.toUpperCase())
    if (!metric) {
      throw new Error(`metric ${name} not found`)
    }
    return metric
  }

  /** 根据 MeasureName 查找 Measure
   * 存在返回 Measure，不存在返回null
   */
  findMeasureByNameWithoutErr(measureName: string): Measure | null {
    const measure = this.metricConfig.allMeasures.find((m) => m.name === measureName)
    if (measure) {
      return measure
    }
    return null
  }

  /** 根据 DimensionName 查找 Dimension
   * 存在返回 Dimension，不存在返回null
   */
  findDimensionByNameWithoutErr(dimensionName: string): Dimension | null {
    const dimension = this.metricConfig.allDimensions.find((m) => m.name.toUpperCase() === dimensionName.toUpperCase())
    if (dimension) {
      return dimension
    }
    return null
  }

  /** 根据 MetricName 查找 Measure
   * 存在返回 Metic，不存在返回null
   */
  findMetricByNameWithoutErr(
    metricName:
      | string
      | {
          name: string // metric name
        },
  ): Metric | null {
    let name: string
    if (typeof metricName === 'string') {
      name = metricName
    } else {
      name = metricName.name
    }
    const metric = this.metricConfig.allMetrics.find((m) => m.name.toUpperCase() === name.toUpperCase())
    if (!metric) {
      return null
    }
    return metric
  }

  /**
   * metric 的 displayExpr。以后可能修改
   */
  getMetricDisplayExpr(metric: Metric) {
    const metricType = metric.type

    switch (metricType) {
      case 'simple':
        return this.metric2SqlByType(metric)
      case 'derived': {
        return (metric as DerivedMetric).typeParams.expr
      }
      case 'ratio': {
        return (metric as RatioMetric).typeParams.numerator + ' / ' + (metric as RatioMetric).typeParams.denominator
      }
      case 'list':
        return '引擎生成，包括：' + metric.typeParams.metrics.map((metric) => metric.name).join(', ')
      default:
        return assertExhaustive(metricType)
    }
  }

  /**
   * 把 metric 转换成 sql 片段
   * @param metric 指标
   */
  metric2SqlByType(metric: Metric): string {
    const metricType = metric.type
    switch (metricType) {
      case 'simple': {
        const measure = this.findMeasureByName(metric.typeParams.measure)
        return `${measure2Sql(measure)}`
      }
      case 'derived': {
        // 正则表达式匹配字母或数字组成的词，并确保这些词是独立的（通过边界断言）
        const derivedExpr = metric.typeParams.expr.replace(/\b[a-zA-Z0-9_]+\b/g, (match) => {
          // 只替换指标名
          if (this.metricConfig.allMetrics.find((m) => m.name.toLowerCase().trim() === match.toLowerCase().trim())) {
            return `${match}_TBL.${match}`
          } else {
            return match
          }
        })
        return `(${derivedExpr})`
      }
      case 'ratio': {
        return `Unavailable, create in generateSql`
      }
      case 'list': {
        throw Error('sql片段不支持list metric')
      }
      default:
        return assertExhaustive(metricType)
    }
  }

  /**
   * 生成 sql 的方法，返回 sql 和 selectMeta
   * 在调用之前，请外部先执行下 verifyQueryParams 来检查格式
   * selectMeta 为 select 后面每一行数据对应的 metric 或 dimension 元信息
   */
  toSql(queryParams: QueryParams): {
    sql: string
    rowsMetadata: RowsMetadata
  } {
    // const isZhongyuan = this.metricConfig.name === 'ggj_dtl_info'
    // 先清空再赋值
    const cleanedQueryParams = removeTailingQuestionMark(queryParams)
    const { metricNames = [], groupBys = [] } = cleanedQueryParams

    // 先做检查。注意，后续都需要使用检查后的参数，不能直接使用 queryParams
    const verifiedMetricParams = this.verifyQueryParams(cleanedQueryParams)
    // metricNames 和 groupBys 不能同时为空
    if (metricNames.length === 0 && groupBys.length === 0) {
      throw new Error('metricNames 和 groupBys 不能同时为空')
    }
    if (verifiedMetricParams.extraParams.extraMetricNames.length > 0) {
      throw new Error(`metricNames ${JSON.stringify(verifiedMetricParams.extraParams.extraMetricNames)} 不存在`)
    }
    if (verifiedMetricParams.extraParams.extraGroupBys.length > 0) {
      throw new Error(`groupBys ${verifiedMetricParams.extraParams.extraGroupBys} 不存在`)
    }

    const timeQueryParams = verifiedMetricParams.queryParams.timeQueryParams

    // 把 timeQueryParams 转换后的 groupBy 和 where 添加到 queryParams 的 groupBys 和 where 当中
    // 如果 groupBys 为空，只有 timeQueryParams 中的 groupBy，那么就加一个时间排序。
    let timeSqlPart: TimeSqlPart | undefined
    const timeDimensions: TimeDimension[] = this.metricConfig.allDimensions.filter(isTimeDimension)
    if (this.metricConfig.timeDimensionDatum != null) {
      const time2sql = new Time2Sql(this.metricConfig.timeDimensionDatum, timeDimensions)
      timeSqlPart = timeQueryParams && time2sql.toSql(timeQueryParams)
    }

    // 把 list 指标展开
    let metricNamesExpanded: string[] = []
    verifiedMetricParams.queryParams.metricNames.forEach((name) => {
      const metric = this.findMetricByName(name)
      if (metric.type === 'list') {
        metricNamesExpanded = metricNamesExpanded.concat(metric.typeParams.metrics.map((m) => m.name))
      } else {
        metricNamesExpanded.push(name)
      }
    })

    // 模型提参结果按照 list 指标排序，替换为 list 指标中第一个指标
    console.info('list metric order by begin>>>')
    const listMetricOrderByParams: string[] = []
    verifiedMetricParams.queryParams.orderBys &&
      verifiedMetricParams.queryParams.orderBys.forEach((orderParam) => {
        // 切出指标，格式示例 metric_name desc
        const metricName: string = orderParam.split(' ')[0]
        // 只处理指标，不处理维度或其他
        if (this.hasMetric(metricName)) {
          const metric = this.findMetricByName(metricName)
          if (metric.type === 'list') {
            const listSubMetrics = metricNamesExpanded.concat(metric.typeParams.metrics.map((m) => m.name))
            if (listSubMetrics && listSubMetrics.length > 0) {
              const result: string = orderParam.replace(metricName, listSubMetrics[0])
              listMetricOrderByParams.push(result)
              console.info('list metric order by param [', orderParam, '] replace to [', result, ']')
            }
          } else {
            listMetricOrderByParams.push(orderParam)
          }
        } else {
          listMetricOrderByParams.push(orderParam)
        }
      })
    verifiedMetricParams.queryParams.orderBys = listMetricOrderByParams

    const verifiedMeta: VerifiedMeta = {
      queryParams: {
        ...verifiedMetricParams.queryParams,
        /** 这里把 List 类型的指标展开成 simple 或者 derived */
        metricNames: metricNamesExpanded,
      },
      timeSqlPart,
    }
    // console.info('verifiedMeta >>>', JSON.stringify(verifiedMeta, null, 2))

    const derivedMeta: DerivedMeta = generateWhereDeriveMeta(
      verifiedMeta,
      this.metricConfig.getAllNoListMetrics(),
      this.metricConfig.allDimensions,
    )
    // console.info('derivedMeta >>>', derivedMeta)
    const summaryMeta: SummaryMeta = generateSummaryMeta(derivedMeta)
    // console.info('summaryMeta >>>', summaryMeta)
    const sqlMeta: SqlMeta = generateSqlMeta(
      summaryMeta,
      this.metricConfig.getAllNoListMetrics(),
      this.metricConfig.allDimensions,
    )
    // 整个指标拼接都不支持Having，都是采用多个指标CTE join得到最终大宽表中，在其中过滤实现having的效果
    if (derivedMeta.whereDerived.metricNames.length > 0) {
      sqlMeta.isMetricInWhere = true
    }
    console.info('sqlMeta >>>', sqlMeta)
    const metricNameWithSql = this.metricConfig.getAllNoListMetrics().map((m) => {
      return {
        name: m.name,
        sql: this.metric2SqlByType(m),
      }
    })
    const finalSql = generateSql(
      sqlMeta,
      metricNameWithSql,
      this.metricConfig.name,
      this.metricConfig.getAllNoListMetrics(),
      this.metricConfig.allDimensions,
      this.metricConfig.timeDimensionDatum,
    )
    // console.info('finalSql >>>', finalSql)
    return {
      sql: finalSql,
      rowsMetadata: sqlMeta.rowsMetadata,
    }
  }

  /**
   * 校验提参是否正确，同时还做一些前置处理
   * 包括：
   * 发现有日期类型的 groupBy，就自动添加一个倒序的 orderBy
   * 发现有日期类型的 orderBy，但是没有 groupBy，就自动添加一个 groupBy
   */
  verifyQueryParams(queryParams: QueryParams): QueryParamsVerified {
    const originalQueryParams = cloneDeep(queryParams)
    const { metricNames = [], groupBys = [], orderBys = [] } = queryParams

    const allMetricNames = this.metricConfig.allMetrics.map((m) => m.name)
    const allDimensionNames = this.metricConfig.allDimensions.map((d) => d.name)
    // 前置处理
    // 从 groupBys 中去掉 date_month, date_day, date_quarter, date_year
    const newGroupBys = (queryParams.groupBys || []).filter(
      (name) => !ExcludeDateDimensions.includes(name) && allDimensionNames.includes(name),
    )
    // orderBys 当中也去掉 date_month, date_day, date_quarter, date_year
    const newOrderBys = (queryParams.orderBys || []).filter(
      (name) =>
        !ExcludeDateDimensions.includes(name) &&
        [...allMetricNames, ...allDimensionNames].includes(name.trim().split(' ')[0]),
    )
    // TODO: where 当中去掉包含 date_month, date_day, date_quarter, date_year 的条件
    let newQueryParams: QueryParams = handleOrderByAbsent(
      {
        ...queryParams,
        groupBys: newGroupBys,
        orderBys: newOrderBys,
      },
      this.metricConfig.allDimensions,
    )

    // FIXME: 删除：宝武的定制逻辑：
    if (isBaoWuFinancial(this.metricConfig.name)) {
      newQueryParams = baowuFinancialCustomLogic(newQueryParams, this.metricConfig)
    }

    if (isBaoWuCost(this.metricConfig.name)) {
      newQueryParams = baowuCostCustomLogic(newQueryParams, this.metricConfig)
    }

    if (isBaoWuAmt(this.metricConfig.name)) {
      newQueryParams = baowuAmtCustomLogic(newQueryParams, this.metricConfig)
    }

    // FIXME: 删除：中化POC的定制逻辑
    if (isZhongHua(this.metricConfig.name)) {
      newQueryParams = convertTimeParamsForZhongHua(newQueryParams, this.metricConfig)
    }

    // 处理不可累加的时间区间
    newQueryParams = processNonCumulativeMetrics(newQueryParams, this.metricConfig)

    // 对日期进行分组，排序中没有日期的时候，需要加上日期。
    if (queryParams.timeQueryParams && queryParams.timeQueryParams.timeGranularity !== 'total') {
      if (!newQueryParams.orderBys) {
        newQueryParams.orderBys = [`${DATE_ALIAS} ASC`]
      }
      if (newQueryParams.orderBys.join(',').indexOf(DATE_ALIAS) === -1) {
        newQueryParams.orderBys.push(`${DATE_ALIAS} ASC`)
      }
    }

    // 当有 avg 指标，但没有分组，那么就添加最近1年的时间，按月分组
    const avgMetrics = newQueryParams.metricNames
      .map((name) => this.findMetricByName(name))
      .filter((m) => m.type === 'simple' && this.findMeasureByName(m.typeParams.measure).agg === 'avg')
    // 平均值的先不处理，因为有些数据已经做了汇总去重，可以直接平均
    // if (avgMetrics.length > 0) {
    if (avgMetrics.length < 0) {
      const timeQueryParams = newQueryParams.timeQueryParams
      if (!timeQueryParams) {
        console.info(chalk.yellow('avg 指标缺少时间提参，添加一个最近1年的时间提参'))
        // 如果没有 groupBy，就添加一个时间提参
        newQueryParams.timeQueryParams = {
          timeGranularity: 'total',
          timeStartFunction: {
            type: 'recentMonths',
            months: 12,
          },
          timeEndFunction: {
            type: 'recentMonths',
            months: 0,
          },
        }
      }
    }

    // HACK: 大模型不知道2024年是闰月，经常提成2024.2.28 手动转换成 2024.2.29，方便后续处理
    if (newQueryParams.timeQueryParams && newQueryParams.timeQueryParams.timeEndFunction?.type === 'specificDate') {
      const { year, month, day } = newQueryParams.timeQueryParams.timeEndFunction
      if (year === 2024 && month === 2 && day === 28) {
        newQueryParams.timeQueryParams.timeEndFunction = {
          type: 'specificDate',
          year: 2024,
          month: 2,
          day: 29,
        }
      }
    }
    if (newQueryParams.timeQueryParams && newQueryParams.timeQueryParams.timeStartFunction?.type === 'specificDate') {
      const { year, month, day } = newQueryParams.timeQueryParams.timeStartFunction
      if (year === 2024 && month === 2 && day === 28) {
        newQueryParams.timeQueryParams.timeStartFunction = {
          type: 'specificDate',
          year: 2024,
          month: 2,
          day: 29,
        }
      }
    }

    // 找出提参中不存在的指标和维度放到 extraParams 中
    const extraMetricNames = metricNames.filter((name) => !allMetricNames.includes(name))
    const extraGroupBys = groupBys.filter(
      (name) => !allDimensionNames.includes(name) && !ExcludeDateDimensions.includes(name),
    )
    const extraOrderBys = orderBys.filter((name) => {
      const colName = name.split(' ')[0] // 去掉 asc、desc
      return (
        !allMetricNames.includes(colName) &&
        !allDimensionNames.includes(colName) &&
        !ExcludeDateDimensions.includes(colName)
      )
    })

    return {
      originalQueryParams: originalQueryParams,
      queryParams: newQueryParams,
      extraParams: {
        extraMetricNames,
        extraGroupBys,
        extraOrderBys,
      },
    }
  }
}

/**
 * 把 measure 转成 sql 片段
 */
export function measure2Sql(measure: Measure) {
  if (measure.expr == null) {
    throw new Error(`The expression is null`)
  }
  const aggKeywords = ['sum', 'sum_boolean', 'count', 'avg', 'max', 'min']
  if (!aggKeywords.some((keyword) => measure.expr.toLocaleLowerCase().includes(keyword))) {
    throw new Error(`${measure.expr} expression is illegal`)
  }
  return measure.expr
}

/**
 * 把 dimension 转换成 sql 片段
 */
export function dimension2Sql(dimension: Dimension) {
  // 如果没有 expr，直接返回 name
  if (!dimension.expr) {
    return dimension.name
  }
  // 如果 expr 和 name 不一样，添加 AS
  if (dimension.expr !== dimension.name) {
    return `${dimension.expr} AS ${dimension.name}`
  }
  // 否则直接返回 expr
  return dimension.expr
}

/** 忽略 queryParams 结尾的问号，metricNames 和 groupBys 结尾的问号代表可选，主要用于 NL2Metric 阶段的e2e检查 */
function removeTailingQuestionMark(queryParams: QueryParams) {
  const metricNames = (queryParams.metricNames || []).map((name) => name.replace(/\?$/, ''))
  const groupBys = (queryParams.groupBys || []).map((name) => name.replace(/\?$/, ''))
  const orderBys = (queryParams.orderBys || []).map((name) => name.replace(/\?$/, ''))
  return {
    ...queryParams,
    metricNames,
    groupBys,
    orderBys,
  }
}

/**
 * 当 orderBy 中有一些列不在 groupBy 或者 metricNames 中的时候，需要把这些列加入到 groupBy 或者 metricNames 中
 */
export function handleOrderByAbsent(queryParams: QueryParams, dimensions: Dimension[]) {
  const orderBys = [...(queryParams.orderBys || [])]
  const groupBys = [...(queryParams.groupBys || [])]
  const metricNames = [...(queryParams.metricNames || [])]

  // 去掉 DATE_ALIAS
  const allFieldsInOrderBy = orderBys
    .filter((orderBy) => orderBy.indexOf(DATE_ALIAS) === -1)
    .map((orderBy) => orderBy.split(' ')[0])
  for (const field of allFieldsInOrderBy) {
    if (!groupBys.includes(field) && !metricNames.includes(field)) {
      // 如果是维度，就加到 groupBys 当中。如果是指标，就加到 metricNames 当中
      const dimension = dimensions.find((d) => d.name === field)
      if (dimension) {
        groupBys.push(field)
      } else {
        metricNames.push(field)
      }
    }
  }
  return {
    ...queryParams,
    metricNames,
    groupBys,
    orderBys,
  }
}
