import { SemanticExternalReport, SemanticMetric } from '@prisma/client'
import { DEFAULT_FORMAT_DECIMAL, DEFAULT_FORMAT_RATIO } from 'src/shared/common-utils'
import { ExternalReport, Measure, Metric } from 'src/shared/metric-types'

/**
 * 将SemanticMetric的数据转化为Metric
 */
function convertSemanticMetricToMetric(m: SemanticMetric): Metric {
  switch (m.type) {
    case 'simple':
      return {
        id: m.id,
        name: m.name,
        label: m.label,
        synonyms: m.synonyms as string[],
        description: m.description!,
        type: m.type,
        formatTemplate: m.formatTemplate ?? DEFAULT_FORMAT_DECIMAL,
        typeParams: m.typeParams as any,
        filter: m.filter!,
        rank: m.rank,
        category: m.category || undefined,
        keypoint: m.keypoint,
        createByMeasure: m.autoCreateByMeasure,
        isCumulative: !!m.isCumulative,
        updatedAt: m.updatedAt,
        windowDescConfigStr: m.windowDescConfigStr || undefined,
        config: m.config,
        meta: m.meta,
      }
    case 'derived':
      return {
        id: m.id,
        name: m.name,
        label: m.label,
        synonyms: m.synonyms as string[],
        description: m.description!,
        type: m.type,
        formatTemplate: m.formatTemplate ?? DEFAULT_FORMAT_DECIMAL,
        typeParams: m.typeParams as any,
        rank: m.rank,
        category: m.category || undefined,
        keypoint: m.keypoint,
        isCumulative: !!m.isCumulative,
        updatedAt: m.updatedAt,
        config: m.config,
        meta: m.meta,
      }
    case 'ratio':
      return {
        id: m.id,
        name: m.name,
        label: m.label,
        synonyms: m.synonyms as string[],
        description: m.description!,
        type: m.type,
        formatTemplate: m.formatTemplate ?? DEFAULT_FORMAT_RATIO,
        typeParams: m.typeParams as any,
        rank: m.rank,
        category: m.category || undefined,
        keypoint: m.keypoint,
        isCumulative: !!m.isCumulative,
        updatedAt: m.updatedAt,
        config: m.config,
        meta: m.meta,
      }
    case 'list':
      return {
        id: m.id,
        name: m.name,
        label: m.label,
        synonyms: m.synonyms as string[],
        description: m.description!,
        type: m.type,
        formatTemplate: '',
        typeParams: m.typeParams as any,
        rank: m.rank,
        category: m.category || undefined,
        keypoint: m.keypoint,
        updatedAt: m.updatedAt,
        config: m.config,
        meta: m.meta,
      }
    default:
      console.error('未知的类型' + m.type)
      throw new Error('未知的类型' + m.type)
  }
}

function convertSemanticExternalReportToExternalReport(m: SemanticExternalReport): ExternalReport {
  return {
    ...m,
    synonyms: Array.isArray(m.synonyms) ? (m.synonyms as string[]) : [],
  }
}

/**
 * 将 SemanticMeasure/Measure 类型的数据 转化为Metric[]
 * 可能会同时创建多个 所以结果类型是Metric[]
 * Measure是SemanticMeasure的子集，synonyms类型不一致 在函数中已强转
 */
function convertMeasureToMetric(measure: Measure): Metric[] {
  const result: Metric[] = []
  if (measure.createMetric || measure.createRankMetric) {
    result.push({
      id: measure.id,
      name: measure.name,
      label: measure.label,
      synonyms: measure.synonyms as string[],
      description: measure.description || measure.label,
      type: 'simple',
      formatTemplate: measure.formatTemplate ? measure.formatTemplate : DEFAULT_FORMAT_DECIMAL,
      typeParams: {
        measure: measure.name,
      },
      rank: -1,
      keypoint: false,
      category: undefined,
      isCumulative: false,
      updatedAt: new Date(),
    })
  }
  /*
  if (measure.createRankMetric) {
    result.push({
      id: measure.id + '_rank_desc',
      name: `${measure.name}_rank_desc`,
      label: `${measure.label}降序排名`,
      synonyms: ((measure.synonyms as string[]) || []).map((s) => `${s}降序排名`),
      description: `${measure.label}降序排名，自动生成`,
      type: 'rank',
      formatTemplate: DEFAULT_FORMAT_INT,
      createdByMeasure: true,
      typeParams: {
        metric: measure.name,
        orderBy: 'desc',
      },
    })
    result.push({
      id: measure.id + '_rank_asc',
      name: `${measure.name}_rank_asc`,
      label: `${measure.label}升序排名`,
      synonyms: ((measure.synonyms as string[]) || []).map((s) => `${s}升序排名`),
      description: `${measure.label}升序排名，自动生成`,
      type: 'rank',
      formatTemplate: DEFAULT_FORMAT_INT,
      createdByMeasure: true,
      typeParams: {
        metric: measure.name,
        orderBy: 'asc',
      },
    })
  }
  */
  return result
}

export { convertSemanticExternalReportToExternalReport, convertSemanticMetricToMetric, convertMeasureToMetric }
