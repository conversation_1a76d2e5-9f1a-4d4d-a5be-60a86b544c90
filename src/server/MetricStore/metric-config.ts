/* eslint-disable @typescript-eslint/naming-convention */
import { nanoid } from 'nanoid'
import chalk from 'chalk'
import { TimeDimensionFormat, TimeGranularityMinType } from 'src/shared/common-types'
import {
  Dimension,
  ExternalReport,
  isTimeDimension,
  Measure,
  Metric,
  NoListMetric,
  TimeDimension,
  TimeDimensionDatum,
  TimeDimensionType,
  VirtualTimeDimension,
} from '../../shared/metric-types'
import { prisma } from '../dao/prisma-init'
import { getHotMetricsBySceneId } from '../AskBI/metrics/dao'
// import memoryCache from '../dao/memory-cache'
import { convertSemanticExternalReportToExternalReport, convertSemanticMetricToMetric } from './utils'
import Time2Sql from './metric2sql/time2sql'

// cache 过期时间2分钟
// const cacheTTL = 2 * 60 * 1000

export default class MetricConfig {
  public constructor(
    public id: string,
    public name: string,
    public allDimensions: Dimension[],
    public allMeasures: Measure[],
    public allMetrics: Metric[],
    public hotMetrics: Metric[],
    public allExternalReports: ExternalReport[],
    public updateTime: number,
    public timeDimensionDatum?: TimeDimensionDatum,
    public description?: string,
  ) {}

  public getAllNoListMetrics(): NoListMetric[] {
    return this.allMetrics.filter(
      (m) => m.type === 'simple' || m.type === 'derived' || m.type === 'ratio',
    ) as NoListMetric[]
  }

  // 依据场景构建指标模型
  // 数据构建依据本地DB
  static async createBySceneId(sceneId: string, isShowDimensionsValues: boolean = true): Promise<MetricConfig> {
    // const fromCacheResult = cache.get(sceneId)?.get(isShowDimensionsValues)
    // if (fromCacheResult) {
    //   console.info('createBySceneId from cache', sceneId, isShowDimensionsValues)
    //   return fromCacheResult
    // }
    const startTime = Date.now()
    const model = await prisma.semanticScenesModels.findFirst({
      where: {
        sceneId: sceneId,
      },
    })
    const modelName = model?.modelName
    if (!modelName) {
      throw new Error(`${'此'}-场景没有关联指标模型，请检查`)
    }
    // 增加cache
    // if (memoryCache.get(`${sceneId}/${modelName}`) != null) {
    //   console.info(chalk.blue(`${sceneId}/${modelName} get metric conf from cache`))
    //   return memoryCache.get(`${sceneId}/${modelName}`) as MetricConfig
    // }
    // 查询 semantic model 数据
    const semanticModel = await prisma.semanticModel.findUnique({
      where: {
        name: modelName,
      },
      include: {
        semanticDimensions: {
          select: {
            name: true,
            label: true,
            synonyms: true,
            filterSwitch: true,
            description: true,
            expr: true,
            type: true,
            typeParams: true,
            values: isShowDimensionsValues,
          },
        },
        semanticMeasures: {
          select: {
            name: true,
            label: true,
            synonyms: true,
            description: true,
            expr: true,
            agg: true,
            createMetric: true,
            formatTemplate: true,
          },
        },
      },
    })
    if (semanticModel == null) {
      throw new Error(`model ${modelName} does not exist`)
    }
    console.info(
      chalk.cyan(
        `截止到查询semanticScenesModels和semanticModel DB完成的耗时: ${Date.now() - startTime} - (场景ID：${sceneId})`,
      ),
    )

    // 获取 data model 中的指标和维度
    let allDimensions: Dimension[]
    const semanticDimensions = semanticModel.semanticDimensions as Dimension[]
    // 计算 data model 时间列信息
    let timeDimensionDatum: TimeDimensionDatum | undefined = undefined
    if (semanticModel.timeDimensionName == null) {
      timeDimensionDatum = undefined
      allDimensions = [...semanticDimensions]
    } else {
      timeDimensionDatum = {
        timeDimensionName: semanticModel.timeDimensionName,
        timeDimensionType: semanticModel.timeDimensionType as TimeDimensionType,
        timeDimensionFormat: semanticModel.timeDimensionFormat as TimeDimensionFormat,
        timeGranularityMin: semanticModel.timeGranularityMin as TimeGranularityMinType,
      }
      const allTimeDimensions: TimeDimension[] = semanticDimensions.filter(isTimeDimension)
      // 添加虚拟的时间维度 天、月、季度、年
      const time2sql = new Time2Sql(timeDimensionDatum, allTimeDimensions)
      const virtualTimeDimensions: VirtualTimeDimension[] = time2sql.getVirtualTimeDimension()
      allDimensions = [...semanticDimensions, ...virtualTimeDimensions]
    }

    const allMeasures = semanticModel.semanticMeasures as Measure[]

    // 用户创建指标

    const predefinedSemanticMetrics = await prisma.semanticMetric.findMany({
      where: {
        semanticSceneId: sceneId,
      },
    })
    // 该场景下的外部报表
    const predefinedSemanticExternalReports = await prisma.semanticExternalReport.findMany({
      where: {
        semanticSceneId: sceneId,
      },
    })

    console.info(chalk.cyan(`截止获取到用户创建指标及外部报表耗时: ${Date.now() - startTime} - (场景ID：${sceneId})`))
    const allMetrics = predefinedSemanticMetrics.map(convertSemanticMetricToMetric)

    const allExternalReports: ExternalReport[] = predefinedSemanticExternalReports.map(
      convertSemanticExternalReportToExternalReport,
    )

    // 获取指标配置最新时间戳（取er模型与指标配置更新最大时间戳）
    let maxUpdateTimestamp = semanticModel.updatedAt
    for (const metric of allMetrics) {
      if (metric.updatedAt > maxUpdateTimestamp) {
        maxUpdateTimestamp = metric.updatedAt
      }
    }
    for (const externalReport of allExternalReports) {
      if (externalReport.updatedAt > maxUpdateTimestamp) {
        maxUpdateTimestamp = externalReport.updatedAt
      }
    }

    // 热门指标
    const hotMetrics = await getHotMetricsBySceneId(allMetrics, sceneId)
    console.info(chalk.cyan(`截止获取到热门指标耗时: ${Date.now() - startTime} - (场景ID：${sceneId})`))

    validateMetricUniqueness(allMetrics)

    const result = new MetricConfig(
      nanoid(),
      semanticModel.tableName,
      allDimensions,
      allMeasures,
      allMetrics,
      hotMetrics,
      allExternalReports,
      maxUpdateTimestamp.getTime(),
      timeDimensionDatum,
      '',
    )
    // memoryCache.set(`${sceneId}/${modelName}`, result, cacheTTL)
    // console.info(chalk.blue(`${sceneId}/${modelName} set metric conf into cache, ttl=${cacheTTL}`))
    console.info(chalk.cyan(`截止到 Generate MeticConfig cost time: ${Date.now() - startTime} - (场景ID：${sceneId})`))

    // let item: Map<any, any> = cache.get(sceneId)
    // if (!item) cache.set(sceneId, (item = new Map()))
    // item.set(isShowDimensionsValues, result)

    return result
  }
}

function validateMetricUniqueness(metrics: Metric[]): void {
  // 检查 predefinedMetrics 和 dynamicMetrics 的 name 是否有重复，如果有重复，报错
  const metricNames = new Set(metrics.map((m) => m.name))
  if (metricNames.size !== metrics.length) {
    throw new Error(`Duplicate metric names found.`)
  }
}
