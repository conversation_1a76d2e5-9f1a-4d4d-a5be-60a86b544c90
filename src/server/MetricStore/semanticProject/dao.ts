import chalk from 'chalk'
import { RowDataPacket } from 'mysql2'
import MetricConfig from 'src/server/MetricStore/metric-config'
import Metric2Sql from 'src/server/MetricStore/metric2sql/metric2sql'
import { getDbPoolOfModelId } from 'src/server/dao/mysql-pool'
import { executeAllXengineSql } from 'src/server/dao/xengine-query'

/**
 * 获取某个指标最近一个月的数据值 用于创建指标树时前端展示值
 */
export async function getDisplayValueOfMetric(
  metricConfig: MetricConfig,
  metric2Sql: Metric2Sql,
  modelId: string,
  metricName: string,
): Promise<string | number> {
  try {
    if (metricConfig.allMetrics.some((m) => m.name === metricName) === false) {
      return '-'
    }

    // TODO: 改成其他的日期字段
    const { sql } = metric2Sql.toSql({
      metricNames: [metricName],
      timeQueryParams: metricConfig.timeDimensionDatum && {
        timeDimensionName: metricConfig.timeDimensionDatum.timeDimensionName,
        timeStartFunction: { type: 'recentMonths', months: 1 },
        timeEndFunction: { type: 'recentMonths', months: 0 },
        timeGranularity: 'month',
      },
    })
    console.info(chalk.green('Get single value of Metric:', metricName, ', SQL is:\n' + sql))

    const dbPool = await getDbPoolOfModelId(modelId)
    if (dbPool == null) {
      return '-'
    }

    const response = await dbPool.query<RowDataPacket[]>(sql)
    const valueList = response[0].map((row) => row[metricName])
    return valueList.length > 0 ? valueList[0] : '-'
  } catch (error) {
    console.error('GetDisplayValueOfMetric with error: ' + error)
    return '-'
  }
}

/**
 * 获取一个model下面所有的指标 带有displayExpr displayValue
 */
export async function getAllMetricWithDisplayValue(
  metricConfig: MetricConfig,
  metric2Sql: Metric2Sql,
  modelId: string,
) {
  try {
    const metricList = metricConfig.allMetrics
    const metricNames = metricList.map((metric) => metric.name)

    // TODO: 改成其他的日期字段
    const { sql } = metric2Sql.toSql({
      metricNames: metricNames,
      timeQueryParams: metricConfig.timeDimensionDatum && {
        timeDimensionName: metricConfig.timeDimensionDatum.timeDimensionName,
        timeStartFunction: { type: 'recentMonths', months: 1 },
        timeEndFunction: { type: 'recentMonths', months: 0 },
        timeGranularity: 'month',
      },
    })
    console.info(chalk.green('Get single value of Metric List:', metricNames, ', SQL is:\n' + sql))

    const response = (await executeAllXengineSql(sql)).data
    console.info(`getAllMetricWithDisplayValue of model ${modelId}, Data is: `, response)

    const updatedMetrics = metricList.map((metric) => {
      const displayExpr = metric2Sql.getMetricDisplayExpr(metric)
      metric.displayExpr = displayExpr
      metric.displayValue = response.length > 0 ? response[0][metric.name] || '-' : '-'
      return metric
    })
    return updatedMetrics
  } catch (error) {
    console.error('GetAllMetricWithDisplayValue with error: ' + error)
    return []
  }
}
