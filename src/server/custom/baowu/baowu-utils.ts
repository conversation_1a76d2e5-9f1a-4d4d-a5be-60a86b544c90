import chalk from 'chalk'
import { uniq } from 'lodash'
import { baowuSubCompanies } from 'src/server/custom/baowu/baowu-constant'
import { executeAllXengineSql } from 'src/server/dao/xengine-query'
import MetricConfig from 'src/server/MetricStore/metric-config'
import {
  getAllCompaniesFromAllDimensions,
  getBaoWuCodeValueList,
  getOriginCompanyName,
  isBaoWu,
} from 'src/shared/baowu-share-utils'
import { AuthData, ChatResponseError, ChatResponseErrorTypes, OlapRow } from 'src/shared/common-types'
import { QueryParams, QueryParamsVerified } from 'src/shared/metric-types'
import { getQueriedMetricNames, getUsefulData } from 'src/shared/utils/data-process-utils'
import { isQueryBaoWuSubCompany } from './baowu-sub-company'

type AuthorizedInnerCompany = { companyInnerCode: string; companyInnerCodeDes: string }

type CompanyCodeListItem = {
  COMPANY_INNER_CODE: string
  COMPANY_INNER_CODE_DES: string
}
// 修改获取表名路径的方式
// const tableName = 'dipeak.baowu'

/**
 *
 * @param metricTableName 因为三个表是放一个路径下的,所以根据metricConfig.name来获取权限表的路径, 用来兼容db2跟内表的查询
 * @returns
 */
export function getPermissionTablePath(metricTableName: string) {
  const tables = metricTableName.split('.')
  return tables.slice(0, tables.length - 1).join('.')
}

export async function getBaoWuAuth({
  queryParams,
  username,
  metricConfig,
  conversationId,
  sceneId,
}: {
  queryParams: any
  username: string
  metricConfig: MetricConfig
  conversationId: string
  sceneId: string
}) {
  const queryMetric = queryParams
  const authData = { userId: username }
  const { where } = queryMetric
  const baowuCompanyFilter = await processBaoWuCompanyFilter({
    authData,
    queryMetric: queryMetric,
    metricConfig,
  })
  const whereCompany = getCompaniesFromOriginWhere(where)
  console.info('----chat-metric-query 宝武权限判断结果-baowuCompanyFilter----->>>>', baowuCompanyFilter)
  if (baowuCompanyFilter !== false) {
    // 先存一份原始的提参where
    queryMetric.originWhere = queryMetric.where
    queryMetric.originGroupBys = [...(queryMetric.groupBys || [])]
    // 将合成后的where赋值给queryMetric
    queryMetric.where = baowuCompanyFilter
    return null
  } else {
    return {
      taskType: 'chat-error',
      ready: false,
      errType: ChatResponseErrorTypes.NO_DATA_AUTHORITY,
      conversationId: conversationId,
      sceneId,
      metricNames: queryMetric?.metricNames,
      whereCompany,
    } as ChatResponseError
  }
}

/**
 * 宝武数据权限处理
 * @param authData
 * @param queryMetric
 * @param metricConfig
 * @returns
 */
export async function processBaoWuCompanyFilter({
  authData,
  queryMetric,
  metricConfig,
}: {
  authData?: AuthData
  queryMetric: any
  metricConfig: MetricConfig
}): Promise<string | false> {
  if (!authData || !queryMetric) {
    return false
  }
  const { where } = queryMetric
  const { companyInnerCodeDesWhere, otherWhere } = handleOriginWhere(where)
  let companies: string[] = []
  if (!isQueryBaoWuSubCompany(queryMetric) && !where) {
    // 没有问各子公司,只展示默认公司的数据
    companies = await getAuthorizedCompany({ authData, metricConfig })
  } else {
    // 有问子公司, 展示所有有权限的子公司数据
    // 需要根据where中的company_inner_code_dsc 来判断差了那几个子公司的数据, 有下面两种情况↓需要兼容
    // where: "COMPANY_INNER_CODE_DES IN ('宝武共享服务有限公司-合并', '宝武共享服务有限公司-单体', '上海宝信数据中心有限公司-单体')",
    // where: "COMPANY_INNER_CODE_DES = '上海宝信数据中心有限公司-单体'",
    // 如果没有company_inner_code_dsc, 就查全部有权限的公司

    const queriedCompanyDes = getQueriedCompanyFromWhere(companyInnerCodeDesWhere)
    companies = await getAuthorizedCompany({ authData, queriedCompanyDes, metricConfig })
  }
  if (companies.length === 0) {
    return false
  }

  const newWhere = companyArr2Where(uniq(companies))

  return [newWhere, ...otherWhere].filter(Boolean).join(' AND ')
}

/**
 * 获取有权限的公司列表
 **/
async function getAuthorizedCompany({
  authData,
  queriedCompanyDes = [],
  metricConfig,
}: {
  authData: AuthData
  metricConfig: MetricConfig
  queriedCompanyDes?: string[]
}) {
  try {
    const startDate = Date.now()
    // 获取用户有权限的公司列表
    const userPermissionList = await getUserPermissionList({ authData, metricConfig })

    const userCompanyCodeList = await getUserCompanyCodeList({ metricConfig, userPermissionList })

    if (userCompanyCodeList.length === 0) {
      return []
    }

    let result: string[] = []

    if (queriedCompanyDes.length > 0) {
      console.info('有查指定公司--->>>', queriedCompanyDes)
      // 如果有queriedCompany, 说明是指定查询公司
      result = queriedCompanyDes.filter((desc) => {
        return !!userCompanyCodeList.find((comp) => {
          return comp.companyInnerCodeDes === desc
        })
      })
    } else {
      // 没有指定查公司,就返回默认的公司查询
      result = [...getBiggestCompanyDes({ userCompanyCodeList, userPermissionList })]
    }
    console.info(chalk.bgCyanBright('查公司权限耗时: ', Date.now() - startDate))
    return result
  } catch (error) {
    console.info('getAuthorizedCompany--error', error)
    return []
  }
}

// 获取用户的权限列表
async function getUserPermissionList({ authData, metricConfig }: { authData: AuthData; metricConfig: MetricConfig }) {
  const tableName = getPermissionTablePath(metricConfig.name)
  const { userId } = authData

  const userPermissionSql = `SELECT DISTINCT COMPANY_CODE, FLAG_TYPE FROM ${tableName}.T_ADS_WH_WSSJ_USER_PERMISSION WHERE USER_ID = '${userId}'`
  const userPermissionList = (await executeAllXengineSql(userPermissionSql)).data

  return userPermissionList
}

// 检查是否有全部权限
function checkHasAllPermission(userPermissionList: OlapRow[]) {
  return !!userPermissionList.find((item) => item.COMPANY_CODE.toLocaleUpperCase() === 'AAAA')
}

/** 用户获取公司 company_code及company_name的列表 */
async function getUserCompanyCodeList({
  metricConfig,
  userPermissionList,
}: {
  metricConfig: MetricConfig
  userPermissionList: OlapRow[]
}) {
  const tableName = getPermissionTablePath(metricConfig.name)

  const userShortCompanyCodeList: string[] = userPermissionList.map((item) => {
    return `${item.COMPANY_CODE}`
  })

  // 合并权限code
  const shortCodes = [...new Set(userShortCompanyCodeList)]
  if (shortCodes.length === 0) {
    return []
  }

  // 如果权限包含AAAA,则认为有全部的权限, 则通过1000查四张表, 负责按照有权限的公司code来查
  const hasAllPermission = checkHasAllPermission(userPermissionList)

  const finalOrgCodes = hasAllPermission ? "'1000'" : arr2SqlQueryString(shortCodes)

  const parentWhere = ` WHERE LEFT(COMPANY_INNER_CODE, 4) IN (${finalOrgCodes})`
  const subWhere = ` WHERE LEFT(TARGET_COMPANY_INNER_CODE, 4) IN (${finalOrgCodes})`

  // 获取有权限的管理树母公司列表
  const userParentManageCompanyCodeSql = `SELECT DISTINCT COMPANY_INNER_CODE, COMPANY_INNER_CODE_DES FROM ${tableName}.T_ADS_FACT_PERMISSION_LEVEL_MANAGE ${parentWhere}`
  console.info('--获取有权限的管理树母公司列表--')
  const userParentManageCompanyCodeList = (await executeAllXengineSql(userParentManageCompanyCodeSql))
    .data as CompanyCodeListItem[]
  // 获取有权限的资产树母公司列表
  const userParentPropertyCompanyCodeSql = `SELECT DISTINCT COMPANY_INNER_CODE, COMPANY_INNER_CODE_DES FROM ${tableName}.T_ADS_FACT_PERMISSION_LEVEL_MANAGE_ALLNEXT ${subWhere}`
  console.info('--获取有权限的资产树母公司列表--')
  const userParentPropertyCompanyCodeList = (await executeAllXengineSql(userParentPropertyCompanyCodeSql))
    .data as CompanyCodeListItem[]
  // 获取有权限的管理树子公司列表
  const userSubManageCompanyCodeSql = `SELECT DISTINCT COMPANY_INNER_CODE, COMPANY_INNER_CODE_DES FROM ${tableName}.T_ADS_FACT_PERMISSION_LEVEL_PROPERTY ${parentWhere}`
  console.info('--获取有权限的管理树子公司列表--')
  const userSubManageCompanyCodeList = (await executeAllXengineSql(userSubManageCompanyCodeSql))
    .data as CompanyCodeListItem[]

  // 获取有权限的资产树子公司列表
  const userSubPropertyCompanyCodeSql = `SELECT DISTINCT COMPANY_INNER_CODE, COMPANY_INNER_CODE_DES FROM ${tableName}.T_ADS_FACT_PERMISSION_LEVEL_PROPERTY_ALLNEXT ${subWhere}`
  console.info('--获取有权限的资产树子公司列表--')
  const userSubPropertyCompanyCodeList = (await executeAllXengineSql(userSubPropertyCompanyCodeSql))
    .data as CompanyCodeListItem[]

  const companies = getAllCompaniesFromAllDimensions(metricConfig.allDimensions)

  // 完整的父公司code
  const fullParentCompanyCodeList = excludeNoDesCompany([
    ...userParentManageCompanyCodeList,
    ...userParentPropertyCompanyCodeList,
  ]).reduce((result, item) => {
    const COMPANY_INNER_CODE = item.COMPANY_INNER_CODE.slice(0, 4)
    const SHORT_COMPANY_INNER_CODE_DES = getOriginCompanyName(item.COMPANY_INNER_CODE_DES)
    let foundCompany = false

    companies.forEach((comp) => {
      const originComp = getOriginCompanyName(comp)
      // 新增兼容: 数据内维度码值本身就没有-法人等口径的情况
      if (originComp === SHORT_COMPANY_INNER_CODE_DES || comp === SHORT_COMPANY_INNER_CODE_DES) {
        result.push(COMPANY_INNER_CODE + '\t\t\t' + comp)
        foundCompany = true
      }
    })

    // 如果没有在宽表包含的公司里匹配到，证明数据没录进去，需要把公司仍然放进去，方便后续找出不存在的公司
    if (!foundCompany) {
      result.push(COMPANY_INNER_CODE + '\t\t\t' + item.COMPANY_INNER_CODE_DES)
    }

    return result
  }, [] as string[])

  // 完整的子公司code
  const fullSubCompanyCodeList: string[] = excludeNoDesCompany([
    ...userSubManageCompanyCodeList,
    ...userSubPropertyCompanyCodeList,
  ]).reduce((result, item) => {
    const COMPANY_INNER_CODE = item.COMPANY_INNER_CODE.slice(0, 4)
    const SHORT_COMPANY_INNER_CODE_DES = getOriginCompanyName(item.COMPANY_INNER_CODE_DES)
    let foundCompany = false

    companies.forEach((comp) => {
      const originComp = getOriginCompanyName(comp)
      // 新增兼容: 数据内维度码值本身就没有-法人等口径的情况
      if (originComp === SHORT_COMPANY_INNER_CODE_DES || comp === SHORT_COMPANY_INNER_CODE_DES) {
        result.push(COMPANY_INNER_CODE + '\t\t\t' + comp)
        foundCompany = true
      }
    })

    // 如果没有在宽表包含的公司里匹配到，证明数据没录进去，需要把公司仍然放进去，方便后续找出不存在的公司
    if (!foundCompany) {
      result.push(COMPANY_INNER_CODE + '\t\t\t' + item.COMPANY_INNER_CODE_DES)
    }

    return result
  }, [] as string[])

  // 完整的公司 code-des列表
  const fullCompanyCodeList = [...fullParentCompanyCodeList, ...fullSubCompanyCodeList]

  console.info('fullCompanyCodeList-前5条----->>>>>', JSON.stringify(fullCompanyCodeList.slice(0, 5)), '\n')

  // 合并company_inner_code不一样,但是company_inner_code_des一样的公司, 再排序 ↓
  const result = [...new Set(fullCompanyCodeList)]
    .sort((a, b) => {
      return Number(a.slice(0, 4)) - Number(b.slice(0, 4))
    })
    .map((item) => {
      const [COMPANY_INNER_CODE_DES, COMPANY_INNER_CODE] = item.split('\t\t\t')
      return {
        companyInnerCode: COMPANY_INNER_CODE_DES,
        companyInnerCodeDes: COMPANY_INNER_CODE,
      }
    })

  if (hasAllPermission) {
    // 如果有全部权限, 需要把已经过滤掉companyInnerCode的公司, 再给剩余公司赋值一个自定义的companyInnerCode
    const restCompanies = companies.filter((comp) => {
      return !result.find((item) => {
        return item.companyInnerCodeDes === comp
      })
    })
    restCompanies.forEach((comp) => {
      result.push({
        companyInnerCode: '0000', // 随意写的不会与正常公司重复的code
        companyInnerCodeDes: comp,
      })
    })
  }

  console.info(chalk.green('userParentCompanyCodeList-result 前5条====>>>>', JSON.stringify(result.slice(0, 5))), '\n')
  return result
}

/**获取权重最大的公司 */
function getBiggestCompanyDes({
  userCompanyCodeList,
  userPermissionList,
}: {
  userCompanyCodeList: AuthorizedInnerCompany[]
  userPermissionList: OlapRow[]
}) {
  const defaultCodeMap = userPermissionList.find((item) => item.FLAG_TYPE === '1')
  // 所有默认权限公司为AAAA的都改成1000
  const defaultCompanyCodeMap =
    defaultCodeMap && defaultCodeMap.COMPANY_CODE === 'AAAA' ? { COMPANY_CODE: '1000' } : defaultCodeMap
  if (defaultCompanyCodeMap) {
    // 如果有默认公司, 就返回默认公司
    const defaultCompanies = userCompanyCodeList.filter(
      (item) => item.companyInnerCode === defaultCompanyCodeMap.COMPANY_CODE,
    )
    if (defaultCompanies?.length > 0) {
      return defaultCompanies.map((item) => item.companyInnerCodeDes)
    }
  }
  // 如果没有默认公司,就返回权重最大的公司
  userCompanyCodeList.sort((a, b) => {
    return Number(a.companyInnerCode) - Number(b.companyInnerCode)
  })

  const biggestCompanyCode = userCompanyCodeList[0].companyInnerCode

  return userCompanyCodeList
    .filter((item) => {
      return item.companyInnerCode === biggestCompanyCode
    })
    .map((item) => item.companyInnerCodeDes)
}

/**从原始where中获取companyInnerCodeDes的where */
export function getCompaniesFromOriginWhere(where?: string) {
  if (!where) {
    return []
  }
  const { companyInnerCodeDesWhere } = handleOriginWhere(where)
  if (companyInnerCodeDesWhere) {
    return getQueriedCompanyFromWhere(companyInnerCodeDesWhere)
  }
  return []
}

// 处理多个维度时的companyInnerCodeDes查询
export function handleOriginWhere(where: string): {
  companyInnerCodeDesWhere: string
  otherWhere: string[]
} {
  // where = "d__time_column__ACCT_PERIOD_NO = '202404' AND COMPANY_INNER_CODE_DES = '宝武共享服务有限公司'"
  const whereList = where.split(/\s+AND\s+/)
  let companyInnerCodeDesWhere: string = ''
  const otherWhere: string[] = []
  whereList.map((item) => {
    if (item.startsWith('COMPANY_INNER_CODE_DES')) {
      companyInnerCodeDesWhere = item
    } else {
      otherWhere.push(item)
    }
  })
  return {
    companyInnerCodeDesWhere,
    otherWhere,
  }
}

/**
 * 根据where解析宝武的子公司DESC, 只支持单纯的COMPANY_INNER_CODE_DES在where中
 * @param where
 * @returns { string[] }
 */
export function getQueriedCompanyFromWhere(where?: string): string[] {
  if (!where) {
    return []
  }
  if (/\s+OR\s+/.test(where)) {
    const singleWhere = where.split(/\s+OR\s+/)
    const data = singleWhere.map((item) => {
      return handleGetQueriedCompanyFromWhere(item)
    })
    return data.flat()
  } else {
    return handleGetQueriedCompanyFromWhere(where)
  }
}
/**getQueriedCompanyFromWhere方法使用 不要在其他地方调用 */
function handleGetQueriedCompanyFromWhere(where: string) {
  const inReg = /COMPANY_INNER_CODE_DES\s*IN\s*\((.+)\)/
  const equalReg = /COMPANY_INNER_CODE_DES\s*=\s*(.+)/
  let result = []

  if (where.toLocaleUpperCase().indexOf('COMPANY_INNER_CODE_DES IN') > -1) {
    // 如果是多个公司
    const matchedData = where.match(inReg)?.[1]
    result =
      matchedData
        ?.replaceAll("'", '')
        ?.split(',')
        .map((item) => item.trim()) || []
  } else {
    const matchedData = where.match(equalReg)?.[1]
    result =
      matchedData
        ?.replaceAll("'", '')
        ?.split(',')
        .map((item) => item.trim()) || []
  }
  return result
}

// 数组转成where
export function companyArr2Where(arr: string[]) {
  const codeList = arr2SqlQueryString(arr)
  return codeList ? `COMPANY_INNER_CODE_DES IN (${codeList})` : ''
}

function arr2SqlQueryString(arr?: string[]) {
  if (!arr) {
    return ''
  }
  return arr.map((item) => `'${item}'`).join(',')
}

/**
 * 获取公司code及des
 * @param metricConfig MetricConfig
 * @returns CompanyCodeListItem[]
 */
export async function getCompanyCodeAndDesList(metricConfig: MetricConfig): Promise<string[]> {
  const baowuOriginCodeValues = getBaoWuCodeValueList(metricConfig.allDimensions)
  return baowuOriginCodeValues
}

/**
 * 是否是只有部分公司查到了数据---非宝武暂时默认是完整的
 * 如果有where里问了公司,就取问的公司数量, 否则取所有的公司数量
 */
export async function checkIsPartialRow({
  rowsLength,
  metricConfig,
  where,
  limit,
}: {
  rowsLength: number
  limit?: number
  metricConfig: MetricConfig
  where: string
}) {
  const companyInWhere = await getQueriedCompanyFromWhere(where)

  if (isBaoWu(metricConfig?.name)) {
    if (limit && limit > 0) {
      return limit < rowsLength
    }
    let queriedCompanies: string[] | CompanyCodeListItem[] = companyInWhere
    queriedCompanies = companyInWhere.length > 0 ? companyInWhere : await getCompanyCodeAndDesList(metricConfig)
    return rowsLength < queriedCompanies.length
  } else {
    return false
  }
}

// 获取宝武查询到的公司数量
export function getBaoWuRowsCompanyLength(rows: OlapRow[]) {
  const companyList: string[] = []
  rows.forEach((item) => {
    if (!companyList.includes(item.COMPANY_INNER_CODE_DES)) {
      companyList.push(item.COMPANY_INNER_CODE_DES)
    }
  })
  return companyList.length
}

// 获取目标父公司及子公司Object
function getTargetCompanyObj(where: string) {
  const queriedCompanies = getCompaniesFromOriginWhere(where)
  const targetCompanyObj = baowuSubCompanies.find((item) => {
    if (item.parentCompany && queriedCompanies.includes(item.parentCompany)) {
      return item
    }
  })

  return targetCompanyObj
}

// 生成新的where
export function genNewWhere(where: string) {
  const { otherWhere } = handleOriginWhere(where)
  const { children: subCompanies } = getTargetCompanyObj(where) || {}
  if (subCompanies) {
    return [companyArr2Where(subCompanies), ...otherWhere].filter(Boolean).join(' AND ')
  }
}

// 获取所有公司内码
export async function getCompanyInnerCodes(companyDescriptions: string[], metricConfigTableName: string) {
  try {
    if (companyDescriptions.length === 0) {
      return []
    }

    const sql = `
    SELECT DISTINCT COMPANY_INNER_CODE, COMPANY_INNER_CODE_DES 
    FROM ${metricConfigTableName} 
    WHERE ${companyArr2Where(companyDescriptions)};
  `

    const result = (await executeAllXengineSql(sql)).data
    return result
  } catch (error) {
    console.error(chalk.red('SQL Execution Error:', error))
    return []
  }
}

function excludeNoDesCompany(companyList: CompanyCodeListItem[]) {
  return companyList.filter((item) => !!item.COMPANY_INNER_CODE_DES)
}

const typePriority = {
  管理合并: 0,
  资产合并: 1,
  法人: 2,
} as const

export function sortAndFilterCompanies(data: OlapRow[], verifiedMetricParams: QueryParamsVerified): OlapRow[] {
  const isSubCompanyQuery = isQueryBaoWuSubCompany(verifiedMetricParams.queryParams)
  if (!isSubCompanyQuery) {
    // 如果不是子公司类问题,直接返回原数据
    return data
  }
  // 用于记录每个公司名的最高优先级记录
  const companyRecords: { [key: string]: { priority: number; data: OlapRow } } = {}

  // 遍历原始数据，记录每个公司名的最高优先级数据
  for (const item of data) {
    if (!item.COMPANY_INNER_CODE_DES) {
      // 没有公司的情况就不处理了
      continue
    }
    const parts = item.COMPANY_INNER_CODE_DES.split('-')
    const companyName = parts.slice(0, -1).join('-')
    const type = parts[parts.length - 1]

    // 获取当前记录的优先级
    const currentPriority = typePriority[type as keyof typeof typePriority]

    // 如果公司名不存在记录，或者当前记录优先级更高，则更新
    if (!companyRecords[companyName] || currentPriority < companyRecords[companyName].priority) {
      companyRecords[companyName] = {
        priority: currentPriority,
        data: item,
      }
    }
  }

  // 保留原始数据的顺序，只添加每个公司名的最高优先级记录, 0408增加根据时间来去重
  let filteredData: OlapRow[] = []

  if (Object.values(companyRecords).length > 0) {
    for (const value of Object.values(companyRecords)) {
      filteredData.push(value.data)
    }
  } else {
    filteredData = data
  }

  const realVerifiedMetricParams = resetVerifiedMetricParams(verifiedMetricParams)
  const limit = realVerifiedMetricParams.queryParams.limit

  // if (hasOrderBy && limit && limit > 1) {
  //   // 排名类问题, 不需要展示管理合并的数据 -- 0422排名类不需要排除管理合并了
  //   const excludes = ['管理合并']
  //   filteredData = filteredData.filter((row) => {
  //     const companySuffix = row.COMPANY_INNER_CODE_DES.split('-')[1] || ''
  //     return !excludes.includes(companySuffix)
  //   })
  // }
  const result = limit ? filteredData.slice(0, limit) : filteredData

  return result
}

function resetVerifiedMetricParams(verifiedMetricParams: QueryParamsVerified) {
  const limit = verifiedMetricParams.queryParams.limit
  if (limit && limit > 1) {
    verifiedMetricParams.queryParams.limit = limit / 3
    verifiedMetricParams.originalQueryParams.limit = limit / 3
  }
  return verifiedMetricParams
}

export function getBaoWuInfoTexts({
  rows,
  queryParams,
  metricConfig,
}: {
  rows: OlapRow[]
  queryParams: QueryParams
  metricConfig: MetricConfig
}) {
  const isSubCompanyQuery = isQueryBaoWuSubCompany(queryParams)
  // 问子公司且是非topN的问题时展示无数据公司的提示
  if (isSubCompanyQuery && !queryParams.limit) {
    // 检查是否是问指标比较的问题
    const isMetricComparisonQuery = queryParams.metricNames.some((metricName) => {
      if (!queryParams.where) return false
      const metricPattern = new RegExp(`${metricName}\\s*[<>]\\s*[\\d.]+`, 'i')
      return metricPattern.test(queryParams.where)
    })

    // 如果是问指标比较的问题，不显示无数据提示
    if (isMetricComparisonQuery) {
      return []
    }

    const queriedCompanies = getCompaniesFromOriginWhere(queryParams.where)

    const rowCompanies = new Set(queriedCompanies.map((item) => getOriginCompanyName(item)))
    console.info('getBaoWuInfoTexts rowCompanies----->>>>>', [...rowCompanies])
    const queriedMetricNames = getQueriedMetricNames(queryParams.metricNames, metricConfig)
    const usefulData = getUsefulData(rows, queriedMetricNames)
    // 如果一家公司到法人都没数据,就证明这家公司完全没数据, 就要展示无数据提示
    usefulData.map((item) => {
      if ('COMPANY_INNER_CODE_DES' in item) {
        const companyName = getOriginCompanyName(item.COMPANY_INNER_CODE_DES)
        rowCompanies.delete(companyName)
      }
    })
    const noDataCompanyStr = [...rowCompanies].join(',')
    return noDataCompanyStr ? [`以下公司无数据：${noDataCompanyStr}`] : []
  }
  return []
}
