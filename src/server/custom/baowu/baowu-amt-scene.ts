// 总账场景特殊逻辑

import { isBaoWuTopN } from 'src/shared/baowu-share-utils'
import { ChartType } from 'src/shared/common-types'
import { QueryParams } from 'src/shared/metric-types'
/**
 * 宝武业务逻辑适配器需求 https://mk70znjkuv.feishu.cn/wiki/QX8Fw6u59i70opkz8mTcKRQEndc
 * 改写总账场景queryMetric
 */
export function processAmtSceneQueryMetric({
  queryMetric,
  //   metricConfig,
}: {
  queryMetric: any
  //   metricConfig: MetricConfig
}) {
  /**
   * 非topN排序类问题都需要提参 “币种”
   * 总账场景非排序类问题, 一律增加groupBy:币种
   */
  if (!isBaoWuTopN(queryMetric.orderBys)) {
    if (queryMetric.groupBys?.length === 0) {
      queryMetric.groupBys = ['CURRENCY_CODE']
    } else {
      // 先把groupBy里的COMPANY_INNER_CODE_DES过滤掉
      // 存在的问题是问多个公司时, 不会分组, 不过目前没有这个情况的对应展示方案(不会展示公司维度), 所以先这样吧
      // 问题在于: 目前的图标组件, 超过两个维度, 不会显示多出来的数据了
      queryMetric.groupBys = queryMetric.groupBys.filter((group: string) => group !== 'COMPANY_INNER_CODE_DES')
      if (!queryMetric.groupBys.includes('CURRENCY_CODE')) {
        queryMetric.groupBys.push('CURRENCY_CODE')
      }
    }
    // 是否是目标指标
    const hasAimedBankMetric = isHasAmtAimedBankMetric(queryMetric.metricNames)
    /**
     *  银行类指标除开提参“币种”，还需要提参 “客商”分组
     * 1. 如果是目标银行类指标, 则增加groupBy: 客商
     * 2. 置信度选择【去除】客商维度
     */
    if (hasAimedBankMetric) {
      if (queryMetric.groupBys?.length === 0) {
        queryMetric.groupBys = ['ID_CODE_NAME']
      } else {
        if (!queryMetric.groupBys.includes('ID_CODE_NAME')) {
          queryMetric.groupBys.push('ID_CODE_NAME')
        }
      }
      console.info('queryMetric.groupBys----->>>>>', queryMetric.groupBys)
      const whereList = handleSplitWhere(queryMetric.where)
      console.info('whereList----->>>>>', whereList)
      const excludedConditions = handleExcludeCondition(whereList, ['ID_CODE_NAME'])
      console.info('excludedConditions----->>>>>', excludedConditions)
      queryMetric.where = excludedConditions.join(' AND ')
      console.info('queryMetric.where--->>>>', queryMetric.where)
    }
  }
}

// 切割where
function handleSplitWhere(where: string) {
  return where.split(/\s+AND\s+/)
}

// 处理排除部分条件的方法
function handleExcludeCondition(conditions: string[], keys: string[]) {
  const newConditions = conditions
    .map((condition) => {
      const result1 = condition.split(/\s+OR\s+/)
      const result2 = result1
        .filter((item) => {
          const hasAimedKey = keys.some((key) => {
            const regExp = new RegExp('^' + key + ' [=|IN]', 'i')
            if (regExp.test(item)) {
              return true
            }
          })
          return !hasAimedKey
        })
        .join(' OR ')
      return result2
    })
    .filter(Boolean)
  return newConditions
}

// 是否是需要特殊处理的相关银行指标
export function isHasAmtAimedBankMetric(metricNames: string[]) {
  // 银行存款, 长期借款,短期借款
  const aimedBankMetric = ['BANK_DEPOSIT_AMT', 'BANK_LOANS_SHORT_AMT', 'BANK_LOANS_LONG_AMT']
  return metricNames.some((name: string) => aimedBankMetric.includes(name))
}

// 处理总账场景默认图表类型
export function processAmtSceneChartType(
  _queryParams: QueryParams,
  recommendChartTypes: ChartType[],
  hasTrend: boolean,
) {
  // const aimedBankMetric = ['BANK_DEPOSIT_AMT', 'BANK_LOANS_SHORT_AMT', 'BANK_LOANS_LONG_AMT']
  // // 总账场景非排序类问题, 一律增加groupBy:币种
  // if (queryParams.orderBys?.length === 0) {
  //   const hasAimedBankMetric = queryParams.metricNames.some((name: string) => aimedBankMetric.includes(name))
  //   // 如果是目标银行类指标, 则增加groupBy: 客商
  //   if (hasAimedBankMetric) {
  //     return 'StackedColumnChart'
  //   }
  // }
  // 如果有Kpi优先展示Kpi 否则默认展示表格
  if (hasTrend && recommendChartTypes.includes('LineChart')) {
    return 'LineChart'
  } else if (recommendChartTypes.includes('Kpi')) {
    return 'Kpi'
  } else {
    return 'SimpleTable'
  }
}
