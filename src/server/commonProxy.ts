/**
 * @description 转发代理函数
 */

import { type Request, type Response } from 'express'
import axios from 'axios'
import FormData from 'form-data'
import { omit } from 'lodash'

// 获取设置的头
const getResponseSetHeaders = (headers: Record<string, any>) => {
  const pickKeys = ['content-type', 'content-disposition']
  const responseHeaders = Object.keys(headers || {})
    .filter((key) => pickKeys.includes(key) && headers[key])
    .reduce(
      (pre, key) => {
        pre[key] = headers[key]
        return pre
      },
      {} as Record<string, string>,
    )
  if (!responseHeaders['content-type']) {
    responseHeaders['content-type'] = 'application/json'
  }
  return responseHeaders
}

export async function commonProxy(req: Request, res: Response, url: string) {
  try {
    const { headers = {}, query, body, method } = req

    // 处理 multipart/form-data 请求
    if (headers['content-type']?.startsWith('multipart/form-data')) {
      const formData = new FormData()
      Object.keys(body).forEach((key) => {
        formData.append(key, body[key])
      })

      const response = await axios({
        method,
        url,
        data: formData,
        params: query,
        headers: {
          ...omit(headers, ['content-length']),
          ...formData.getHeaders(),
          // Authorization: 'init',
        },
        // responseType: 'arraybuffer',
      })
      res.set(getResponseSetHeaders(response.headers))
      return res.send(response.data)
    } else {
      // 处理其他类型的请求
      const response = await axios({
        method,
        url,
        data: body,
        params: query,
        headers: {
          ...(headers || {}),
          // Authorization: 'init',
        },
        // responseType: 'arraybuffer',
      })
      res.set(getResponseSetHeaders(response.headers))
      return res.send(response.data)
    }
  } catch (error: any) {
    console.error(`出现错误，请求地址${req.url}`, error?.message || error)
    const msg = typeof error === 'string' ? error : error?.message || '服务端错误'
    return res.status(500).json({ code: 500, msg })
  }
}
