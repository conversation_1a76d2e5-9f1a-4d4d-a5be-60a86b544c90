/**
 * 报告生成 API
 */
import express, { Router, Request, Response } from 'express'
import axios from 'axios'
import multer from 'multer'
import { askBIApiUrlsWithoutBaseUrl } from 'src/shared/url-map'
import { DOC_REPORT_TIMEOUT, MAX_COUNT_UPLOAD_LIMIT } from 'src/shared/constants'
import { getParamsExtractUrl, removeDomainFromUrl } from '../utils'
import { PROCESS_ENV } from '../server-constants'

const router: Router = express.Router()
const upload = multer()

/**
 * 生成大纲阶段的保存
 */
router.post('/outline/create', async (req: Request, res: Response) => {
  try {
    console.info('outline/create', req.body)
    const result = await axios.post(
      getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.postOutlineCreate,
      req.body,
      {
        timeout: DOC_REPORT_TIMEOUT,
      },
    )
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('outline/create', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 获取维度码值_new
 */
router.get('/column-value', async (req: Request, res: Response) => {
  try {
    console.info('column-value', req.query)
    const result = await axios.get(getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.getColumnValue, {
      params: req.query,
      timeout: DOC_REPORT_TIMEOUT,
    })
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('column-value-Error', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 模板列表
 */
router.get('/template/list', async (req: Request, res: Response) => {
  try {
    console.info('template/list', req.query)
    const result = await axios.get(getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.getTemplateList, {
      params: req.query,
      timeout: DOC_REPORT_TIMEOUT,
    })
    // 暂时node处理掉后端返回的url中带ip和端口前缀的，需要在下次迭代的时候让后端去掉前缀，不要暴露后台内部的 url 给浏览器
    const handleData = { ...result.data }
    if (handleData.data && handleData.data.templateList.length > 0) {
      handleData.data.templateList.forEach((item: { thumbnailPath: string }) => {
        if (item.thumbnailPath) {
          item.thumbnailPath = removeDomainFromUrl(item.thumbnailPath)
        }
      })
    }

    return res.json(handleData)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('template/list-Error', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})
/**
 * 获取运算符列表_new
 */
router.get('/data-filter-operator', async (req: Request, res: Response) => {
  try {
    console.info('data-filter-operator', req.query)
    const result = await axios.get(
      getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.dataFilterOperator,
      {
        params: req.query,
        timeout: DOC_REPORT_TIMEOUT,
      },
    )
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('data-filter-operator-Error', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})
/**
 * 获取指标模型字段分类情况
 */
router.get('/column-classify', async (req: Request, res: Response) => {
  try {
    console.info('column-classify', req.query)
    const result = await axios.get(
      getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.getColumnClassify,
      {
        params: req.query,
        timeout: DOC_REPORT_TIMEOUT,
      },
    )
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('column-classify-Error', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 获取报告列表
 */
router.get('/report/list', async (req: Request, res: Response) => {
  try {
    console.info('report/list', req.query)
    const result = await axios.get(getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.getReportList, {
      params: req.query,
      timeout: DOC_REPORT_TIMEOUT,
    })
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('report/list-Error', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 获取报告详情
 */
router.get('/report-detail', async (req: Request, res: Response) => {
  try {
    console.info('report-detail', req.query)
    const result = await axios.get(getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.getReportDetail, {
      params: req.query,
      timeout: DOC_REPORT_TIMEOUT,
    })
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('report-detail-Error', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 删除报告
 */
router.delete('/report', async (req: Request, res: Response) => {
  try {
    const result = await axios.delete(getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.deleteReport, {
      params: req.query,
    })
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('delete - report-Error', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 保存报告
 */
router.post('/report/save', async (req: Request, res: Response) => {
  try {
    console.info('report/save', req.body)
    const result = await axios.post(
      getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.saveReport,
      req.body,
      {
        timeout: DOC_REPORT_TIMEOUT,
      },
    )
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('report/save-Error', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 导出报告
 */
router.get('/report/export', async (req: Request, res: Response) => {
  try {
    console.info('report/export', req.query)
    const result = await axios.get(getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.exportReport, {
      params: req.query,
      timeout: DOC_REPORT_TIMEOUT,
    })
    const handleData = { ...result.data }
    const { pdfUrl, wordUrl } = handleData.data || {}

    if (pdfUrl) {
      handleData.data.pdfUrl = removeDomainFromUrl(pdfUrl)
    }
    if (wordUrl) {
      handleData.data.wordUrl = removeDomainFromUrl(wordUrl)
    }

    return res.json(handleData)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('report/export-Error', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 段落重命名
 */
router.put('/section-name', async (req: Request, res: Response) => {
  try {
    console.info('section-name', req.body)
    const result = await axios.put(
      getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.updateSectionName,
      req.body,
      {
        timeout: DOC_REPORT_TIMEOUT,
      },
    )
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('section-name', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})
/**
 * 更新报告大纲
 */
router.post('/template/outline/update', async (req: Request, res: Response) => {
  try {
    console.info('/template/outline/update', req.body)
    const result = await axios.post(
      getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.updateOutline,
      req.body,
      {
        timeout: DOC_REPORT_TIMEOUT,
      },
    )
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('/template/outline/update', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 更新目标段落
 */
router.post('/section/update', async (req: Request, res: Response) => {
  try {
    console.info('section/update', req.body)
    const result = await axios.post(
      getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.updateSection,
      req.body,
      {
        timeout: DOC_REPORT_TIMEOUT,
      },
    )
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('section/update-Error', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 获取报告段落配置
 */
router.get('/template/section-config', async (req: Request, res: Response) => {
  try {
    console.info('section-config', req.query)
    const result = await axios.get(getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.sectionConfig, {
      params: req.query,
      timeout: DOC_REPORT_TIMEOUT,
    })
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('section-config-Error', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 获取模版段落配置
 */
router.get('/template/section-config', async (req: Request, res: Response) => {
  try {
    console.info('template/section-config', req.query)
    const result = await axios.get(
      getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.templateSectionConfig,
      {
        params: req.query,
        timeout: DOC_REPORT_TIMEOUT,
      },
    )
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('template/section-config-Error', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 更新目标段落
 */
router.post('/report-copy', async (req: Request, res: Response) => {
  try {
    console.info('report-copy', req.body)
    const result = await axios.post(
      getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.reportCopy,
      req.body,
      {
        timeout: DOC_REPORT_TIMEOUT,
      },
    )
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('report-copy', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 新增或者更新段落数据算子
 */
router.post('/create-or-update-data-operator', async (req: Request, res: Response) => {
  try {
    console.info('create-or-update-data-operator', req.body)
    const result = await axios.post(
      getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.createOrUpdateDataOperator,
      req.body,
      {
        timeout: DOC_REPORT_TIMEOUT,
      },
    )
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('create-or-update-data-operator', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 *  新增或者更新段落文本算子
 */
router.post('/create-or-update-text-operator', async (req: Request, res: Response) => {
  try {
    console.info('create-or-update-text-operator', req.body)
    const result = await axios.post(
      getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.createOrUpdateTextOperator,
      req.body,
      {
        timeout: DOC_REPORT_TIMEOUT,
      },
    )
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('create-or-update-text-operator', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 获取数据算子计算类型列表
 */
router.get('/data-operator-compute-type', async (req: Request, res: Response) => {
  try {
    console.info('data-operator-compute-type', req.query)
    const result = await axios.get(getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.computeTypeList, {
      params: req.query,
      timeout: DOC_REPORT_TIMEOUT,
    })
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('data-operator-compute-type-Error', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 获取文本算子类型
 */
router.get('/text-operator-type', async (req: Request, res: Response) => {
  try {
    console.info('text-operator-type', req.query)
    const result = await axios.get(getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.textOperatorType, {
      params: req.query,
      timeout: DOC_REPORT_TIMEOUT,
    })
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('text-operator-type-Error', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 获取算子的类型列表
 */
router.get('/data-operator-type', async (req: Request, res: Response) => {
  try {
    console.info('data-operator-type', req.query)
    const result = await axios.get(getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.dataOperatorType, {
      params: req.query,
      timeout: DOC_REPORT_TIMEOUT,
    })
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('data-operator-type-Error', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 获取数据算子的码值
 */
router.get('/list-data-operator-column-value', async (req: Request, res: Response) => {
  try {
    console.info('list-data-operator-column-value', req.query)
    const result = await axios.get(
      getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.listDataOperatorColumnValue,
      {
        params: req.query,
        timeout: DOC_REPORT_TIMEOUT,
      },
    )
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('list-data-operator-column-value', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 获取模版详情
 */
router.get('/template/detail', async (req: Request, res: Response) => {
  try {
    console.info('template/detail', req.query)
    const result = await axios.get(getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.templateDetail, {
      params: req.query,
      timeout: DOC_REPORT_TIMEOUT,
    })
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('template/detail', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 *  数据算子预览
 */
router.post('/get-data-operator-preview', async (req: Request, res: Response) => {
  try {
    console.info('get-data-operator-preview', req.body)
    const result = await axios.post(
      getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.dataOperatorPreview,
      req.body,
      {
        timeout: DOC_REPORT_TIMEOUT,
      },
    )
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('get-data-operator-preview', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 文本算子预览
 */
router.post('/get-text-operator-preview', async (req: Request, res: Response) => {
  try {
    console.info('get-text-operator-preview', req.body)
    const result = await axios.post(
      getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.textOperatorPreview,
      req.body,
      {
        timeout: DOC_REPORT_TIMEOUT,
      },
    )
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('get-text-operator-preview', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 获取时间粒度
 */
router.get('/time_granularity', async (req: Request, res: Response) => {
  try {
    console.info('time_granularity', req.query)
    const result = await axios.get(getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.timeGranularity, {
      params: req.query,
      timeout: DOC_REPORT_TIMEOUT,
    })
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('time_granularity', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 获取模版段落算子详情
 */
router.get('/template/section-operators', async (req: Request, res: Response) => {
  try {
    console.info('/template/section-operators', req.query)
    const result = await axios.get(
      getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.getSectionOperators,
      {
        params: req.query,
        timeout: DOC_REPORT_TIMEOUT,
      },
    )
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('/template/section-operators', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 保存模版
 */
router.post('/template/save', async (req: Request, res: Response) => {
  try {
    console.info('/template/save', req.body)
    const result = await axios.post(
      getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.saveTemplate,
      req.body,
      {
        timeout: DOC_REPORT_TIMEOUT,
      },
    )
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('/template/save - Error', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 新增模版
 */
router.post('/template/create', async (req: Request, res: Response) => {
  try {
    console.info('/template/create', req.body)
    const result = await axios.post(
      getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.createTemplate,
      req.body,
      {
        timeout: DOC_REPORT_TIMEOUT,
      },
    )
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('/template/create - Error', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 删除模板
 */
router.delete('/template/delete', async (req: Request, res: Response) => {
  try {
    console.info('template/delete-Error', req.body)
    const result = await axios.delete(
      getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.deleteTemplate,
      {
        params: req.query,
      },
    )
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('template/delete-Error', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 获取打标文件列表
 */
router.get('/data-parser/file-list', async (req: Request, res: Response) => {
  try {
    console.info('/data-parser/file-list', req.query)
    const result = await axios.get(PROCESS_ENV.CHINA_TELECOM_MARKING + '/data-parser/file-list', {
      // params: req.query,
      timeout: DOC_REPORT_TIMEOUT,
    })
    const handleData = { ...result.data }
    if (handleData && handleData.data.length > 0) {
      handleData.data.forEach((item: { downloadURL: string }) => {
        if (item.downloadURL) {
          item.downloadURL = removeDomainFromUrl(item.downloadURL)
        }
      })
    }

    return res.json(handleData)
  } catch (error) {
    const errorInfo = (error as any).response?.message || ''
    console.error('/data-parser/file-list', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 开始打标
 */
router.get('/data-parser/start', async (req: Request, res: Response) => {
  try {
    console.info('/data-parser/start', req.query)
    const result = await axios.get(PROCESS_ENV.CHINA_TELECOM_MARKING + '/data-parser/start', {
      params: req.query,
      timeout: DOC_REPORT_TIMEOUT,
    })
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.message || ''
    console.error('/data-parser/start', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

router.post(
  '/data-parser/upload-file',
  upload.array('files', MAX_COUNT_UPLOAD_LIMIT),
  async (req: Request, res: Response) => {
    const file = (req.files as Express.Multer.File[])[0]
    const originalnameUTF8 = Buffer.from(file.originalname, 'latin1').toString('utf8')
    const reqUrl = PROCESS_ENV.CHINA_TELECOM_MARKING + '/data-parser/upload-file'
    if (!file) {
      return res.status(400).json({ code: 400, msg: '未接收到文件' })
    }
    const fileBlob = new Blob([file.buffer], { type: file.mimetype })
    const form = new FormData()
    form.append('file', fileBlob, originalnameUTF8)

    try {
      const result = await axios.post(reqUrl, form, {
        timeout: 2 * 60 * 1000, // 可能会有大文件上传， 所以设置2分钟
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })

      return res.status(200).json(result.data)
    } catch (error: any) {
      console.error('【电信打标上传】-upload-file', error?.response?.message)
      return res.json({ code: 500, msg: error?.response?.message || '' })
    }
  },
)

router.get('/markFiledownloadProxy', async (req: Request, res: Response) => {
  try {
    const backendUrl =
      req.query.p === 'example'
        ? PROCESS_ENV.CHINA_TELECOM_EXAMPLE_CSV_URL
        : PROCESS_ENV.CHINA_TELECOM_MARKING_DOWNLOAD_URL + req.query.p
    const parsedUrl = new URL(backendUrl)
    console.info('打标文件下载url', backendUrl)
    const pathname = parsedUrl.pathname
    const filename = pathname.split('/').pop()
    const response = await axios.get(backendUrl, { responseType: 'stream' })
    res.setHeader('Content-disposition', `attachment; filename=${filename}`)
    response.data.pipe(res)
  } catch (err) {
    console.error('文件下载失败', err)
    return res.status(200).json({
      code: 500,
      data: null,
      msg: `文件下载失败${(err as Error)?.message}`,
    })
  }
})

router.get('/department_value', async (req: Request, res: Response) => {
  try {
    console.info('department_value', req.query)
    const result = await axios.get(
      getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.getDepartmentValue,
      {
        params: req.query,
        timeout: DOC_REPORT_TIMEOUT,
      },
    )
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('department_value', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

router.get('/province_value', async (req: Request, res: Response) => {
  try {
    console.info('province_value', req.query)
    const result = await axios.get(getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.getProvinceValue, {
      params: req.query,
      timeout: DOC_REPORT_TIMEOUT,
    })
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('province_value', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

/**
 * 生成报告 - 新增省份和部分字段
 */
router.post('/auth-template/create-report', async (req: Request, res: Response) => {
  try {
    console.info('auth-template/create-report', req.body)
    const result = await axios.post(
      getParamsExtractUrl() + askBIApiUrlsWithoutBaseUrl.reportGenerate.authTemplateCreateReport,
      req.body,
      {
        timeout: DOC_REPORT_TIMEOUT,
      },
    )
    return res.json(result.data)
  } catch (error) {
    const errorInfo = (error as any).response?.data?.msg || (error as any).message || ''
    console.error('auth-template/create-report', error)
    return res.json({
      code: 500,
      msg: errorInfo,
    })
  }
})

export default router
