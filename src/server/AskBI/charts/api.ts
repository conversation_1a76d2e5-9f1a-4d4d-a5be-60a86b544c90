import express, { Router, Request, Response } from 'express'
import { produce } from 'immer'
import { Chart, Prisma } from '@prisma/client'
import { prisma } from 'src/server/dao/db'
import { ChartType, LlmType, OlapRow, ReadyChartResponse, RowsMetadata } from 'src/shared/common-types'
import { ChartThemeType } from 'src/shared/constants'
import { isAdminByUsername } from 'src/client/pages/AskBI/admin/Users/<USER>'
import { QueryParams } from 'src/shared/metric-types'
import { executeAllXengineSql } from 'src/server/dao/xengine-query'
import { ALL_CHART_MOCK_DATA } from './chart-mock-data'

const router: Router = express.Router()

/**
 * 获取所有图表的 tooltip 介绍信息
 */
router.get('/tooltip', async (_req: Request, res: Response) => {
  console.info('Get /charts/tooltip mock data')
  return res.json({ code: 0, data: ALL_CHART_MOCK_DATA })
})

async function querySelectedRows(chart: Chart): Promise<OlapRow[]> {
  const rows = (await executeAllXengineSql(chart.sql)).data
  console.info(`Chart Manage 正在使用 SQL 查询'${chart.chartTitle}'的数据... 共查到 ${rows.length} 条数据。`)
  return rows as unknown as OlapRow[]
}

/**
 * 查询和展示单个图表
 */
router.get('/:id', async (req: Request, res: Response) => {
  const chartId = req.params.id
  if (chartId == null) {
    return res.json({ code: 404, data: {}, msg: '缺少 chartId' })
  }
  try {
    console.info(`Get ${chartId} chart detail...`)
    const chart = await prisma.chart.findUnique({
      where: {
        id: chartId,
      },
    })
    if (chart == null) {
      return res.json({ code: 404, data: {}, msg: '找不到图表' + chartId })
    }
    const rows = await querySelectedRows(chart)

    // FIXME: 目前只支持 query-metric 类型的图表，其他类型的也需要支持下
    const data: ReadyChartResponse = {
      id: chart.id,
      ready: true,
      taskType: chart.taskType as 'query-metric',
      sql: chart.sql,
      sceneId: chart.semanticSceneId,
      chartType: chart.chartType as ChartType,
      chartTitle: chart.chartTitle,
      queryParamsVerified: {
        queryParams: chart.queryParams as QueryParams,
        originalQueryParams: chart.queryParams as QueryParams,
        extraParams: { extraMetricNames: [], extraGroupBys: [], extraOrderBys: [] },
      },
      recommendChartTypes: chart.recommendChartTypes.split(',') as ChartType[],
      rowsMetadata: chart.rowsMetadata as RowsMetadata,
      conversationId: 'no-conversation-id', // conversationId 无用
      rows: rows,
      originRows: rows, // TODO: remove this
      chartThemeType: chart.chartThemeType as ChartThemeType,
      createdAt: chart.createdAt,
      updatedAt: chart.updatedAt,
      queryParams: chart.queryParams as QueryParams,
      username: chart.username,
      llmType: chart.llmType as LlmType,
      ask: chart.ask,
      isPartialRow: false,
      partialRowMsg: '',
      infoTexts: [],
    }

    return res.json({ code: 0, data: data })
  } catch (error) {
    console.error(`Get ${chartId} chart detail with error: `, error)
    return res.json({ code: 500, data: {}, msg: `查询不到图表${chartId}相关的数据，${(error as Error).message}` })
  }
})

/**
 * 创建图表
 */
router.post('/', async (req: Request, res: Response) => {
  const username = req.user?.username
  const newChart = {
    ...req.body,
    username,
    rowsMetadata: JSON.parse(req.body.rowsMetadata),
  }
  // TODO: 这里需要做一些字段的安全检查
  console.info('Create a chart with data: ', newChart)
  try {
    const chart = await prisma.chart.create({
      data: newChart,
    })
    return res.json({ code: 0, data: chart })
  } catch (error) {
    console.error('Create chart failed with error: ', error)
    return res.json({ code: 500, msg: '创建图表失败' + (error as Error).message })
  }
})

/**
 * 更新图表
 */
router.put('/:id', async (req: Request, res: Response) => {
  const chartId = req.params.id
  if (chartId == null) {
    return res.json({ code: 404, data: {}, msg: '缺少 chartId，更新图表失败!' })
  }
  try {
    const newChart = {
      ...req.body,
      rowsMetadata: JSON.parse(req.body.rowsMetadata),
    }
    console.info(`Update chart id=${chartId}, data :`, newChart)
    const chart = await prisma.chart.update({
      where: { id: chartId },
      // TODO: 这里需要做一些字段的安全检查
      data: newChart,
    })
    if (chart == null) {
      return res.json({ code: 404, data: {}, msg: '找不到图表' + chartId })
    }
    return res.json({ code: 0, data: { id: chartId } })
  } catch (error) {
    console.error('更新图表失败，具体原因如下', error)
    return res.json({ code: 500, data: {}, msg: `更新图表${chartId}相关的数据失败` })
  }
})

/**
 * 查询所有图表
 * 带查询条件 SceneId user的权限
 */
router.get('/', async (req: Request, res: Response) => {
  const username = req.user?.username
  console.info(`Query chart list with by username: ${username}, query:`, req.query)

  const sceneId = req.query.sceneId?.toString()
  const isAdmin = req.query.ranger
  if (sceneId == null) {
    return res.json({ code: 404, data: {}, msg: '缺少场景ID，无法查询图表列表' })
  }

  try {
    const findManyArgs: Prisma.ChartFindManyArgs = {
      orderBy: { createdAt: 'desc' },
    }
    // 设置查询列表时的where条件
    const whereArgs: Prisma.ChartWhereInput = {}
    whereArgs.semanticSceneId = sceneId
    // 如果是admin的角色，可以查看所有图表
    if (!isAdmin) {
      whereArgs.username = username
    }
    findManyArgs.where = whereArgs
    const charts = await prisma.chart.findMany(findManyArgs)
    // 给图表追加rows属性，并对rows rowsMetaData重新进行排序 [不重新生成rowsMetaData]
    const newCharts = await Promise.all(
      charts.map(async (chart) => {
        try {
          const rows = await querySelectedRows(chart)
          return produce(chart, (draftChart: ReadyChartResponse) => {
            draftChart.ready = true
            draftChart.conversationId = 'no-conversation-id'
            draftChart.recommendChartTypes = chart.recommendChartTypes.split(',') as ChartType[]
            draftChart.rowsMetadata = chart.rowsMetadata as RowsMetadata
            draftChart.rows = rows
            draftChart.originRows = rows
            draftChart.sceneId = chart.semanticSceneId
            draftChart.llmType = chart.llmType as LlmType
          })
        } catch (error) {
          console.error(`当前查询失败的图表id为：'${chart.id}'，图表标题为：'${chart.chartTitle}'，错误信息：`, error)
          return { ...chart, errorMessage: (error as Error).message }
        }
      }),
    )

    return res.json({ code: 0, data: newCharts })
  } catch (error) {
    console.error('Get charts list data with error: ', error)
    return res.json({ code: 500, data: {}, msg: '获取图表列表失败，请联系管理员处理' })
  }
})

/**
 * 删除图表
 */
router.delete('/:id', async (req: Request, res: Response) => {
  const chartId = req.params.id
  if (chartId == null) {
    return res.json({ code: 404, msg: '缺少 chartId，无法删除此图表' })
  }
  try {
    // 删除时也要添加权限校验，防止意外的删除，管理员可以随意删除
    const username = req.user?.username
    if (!username) {
      return res.json({ code: 404, data: {}, msg: '未登录' })
    }
    const userInfo = await prisma.xUser.findUnique({
      where: { username },
    })
    if (!userInfo) {
      return res.json({ code: 404, data: {}, msg: '获取当前用户信息失败，请重新登录后再尝试!' })
    }
    const chartDeleteArgs: Prisma.ChartDeleteArgs = {
      where: { id: chartId },
    }
    const isAdmin = await isAdminByUsername(username)
    if (!isAdmin) {
      chartDeleteArgs.where = {
        id: chartId,
        username,
      }
    }
    console.info(`Delete ${chartId} chart...`)
    const chart = await prisma.chart.delete(chartDeleteArgs)

    if (chart == null) {
      return res.json({ code: 404, msg: `找不到图表${chartId}, 删除失败!` })
    }

    return res.json({ code: 0, data: { id: chartId } })
  } catch (error) {
    console.error('Delete chart with error: ', error)
    return res.json({ code: 500, data: {}, msg: '删除图表失败，请联系管理员处理' })
  }
})

export default router
