import { BuildChatMetricParams } from 'src/server/AskBI/chats/chat-pipelines'
import { sortAndFilterCompanies } from 'src/server/custom/baowu/baowu-utils'
import MetricConfig from 'src/server/MetricStore/metric-config'
import { ParamsExtractExactMatch, ParamsExtractQueryMetricResult, QueryParamsVerified } from 'src/shared/metric-types'
import { ChatResponseError, OlapRow } from 'src/shared/common-types'
import { getUsefulData } from 'src/shared/utils/data-process-utils'
import { isBaoWu } from 'src/shared/baowu-share-utils'

function processMultiScenesChatMetricParams(
  params: (BuildChatMetricParams | ChatResponseError)[],
): (BuildChatMetricParams | ChatResponseError)[] {
  console.info(`多场景后置处理方法共收到 ${params.length} 个结果。`)
  if (params.length === 1) return params
  const validResults = params.filter((r) => r.ready)
  const errorResults = params.filter((r) => !r.ready)
  if (validResults.length === 1) return validResults
  if (validResults.length === 0) return [errorResults[0]]

  const queryMetricScenes = validResults.filter((r) => r.resultOfParamsExtract.type === 'query-metric')
  const confidenceScenes = validResults.filter((r) => r.resultOfParamsExtract.type === 'metric-exact-match')
  const otherScenes = validResults.filter(
    (r) => r.resultOfParamsExtract.type !== 'metric-exact-match' && r.resultOfParamsExtract.type !== 'query-metric',
  )

  if (queryMetricScenes.length > 0 || confidenceScenes.length > 0) {
    const allScoresAreOneQueryMetricScenes = queryMetricScenes.filter((r) =>
      Object.values((r.resultOfParamsExtract as ParamsExtractQueryMetricResult)?.extraInfo?.metric_scores || {}).some(
        (score) => score === 1,
      ),
    )
    const allScoresAreOneConfidenceScenes = confidenceScenes.filter((r) =>
      Object.values((r.resultOfParamsExtract as ParamsExtractExactMatch)?.extraInfo?.metric_scores || {}).some(
        (score) => score === 1,
      ),
    )
    if (allScoresAreOneQueryMetricScenes.length === 1) {
      return [...allScoresAreOneQueryMetricScenes, ...otherScenes]
    } else if (allScoresAreOneConfidenceScenes.length === 1) {
      return [...allScoresAreOneConfidenceScenes, ...otherScenes]
    } else {
      const newConfidenceScenes = queryMetricScenes.map((data) => {
        const paramsExtractQueryMetricResult = data.resultOfParamsExtract as ParamsExtractQueryMetricResult
        return {
          ...data,
          resultOfParamsExtract: {
            ...paramsExtractQueryMetricResult,
            isMetricNamesExactMatch: false,
            isWhereExactMatch: false,
            type: 'metric-exact-match',
          } as ParamsExtractExactMatch,
        }
      })
      return [...newConfidenceScenes, ...confidenceScenes]
    }
  }

  return validResults
}

export function handleTransformRows({
  rows,
  metricConfig,
  verifiedMetricParams,
}: {
  rows: OlapRow[]
  metricConfig: MetricConfig
  verifiedMetricParams: QueryParamsVerified
}) {
  const queriedMetricNames = getQueriedMetricNames(verifiedMetricParams.queryParams.metricNames, metricConfig)
  const tempRows = getUsefulData(rows, queriedMetricNames)
  if (isBaoWu(metricConfig?.name)) {
    return sortAndFilterCompanies(tempRows, verifiedMetricParams)
  }
  return tempRows
}

function getQueriedMetricNames(metricNames: string[], metricConfig?: MetricConfig | null) {
  if (!metricConfig) {
    return metricNames
  }
  const allMetricNames = metricConfig.allMetrics.flatMap((metric) => {
    if (metricNames.includes(metric.name)) {
      return metric.type === 'list' ? metric?.typeParams?.metrics.map((i) => i.name) : metric.name
    }
    return []
  })

  return allMetricNames
}

export { processMultiScenesChatMetricParams }
