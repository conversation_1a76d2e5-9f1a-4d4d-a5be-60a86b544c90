/* eslint-disable @typescript-eslint/naming-convention */
import { exec } from 'child_process'
import path from 'path'
import { prisma } from 'src/server/dao/prisma-init'
import { getConverChatErrorTypeByResErrType } from 'src/server/utils'
import { ChatResponse, ConverChatErrorTypes, ConverChatErrorTypesMap, ElkResponse } from 'src/shared/common-types'

export const getChatConverErrorType = (errorTypeCode?: string) => {
  if (!errorTypeCode) {
    return ConverChatErrorTypes.DATA_ERROR_FEEDBACK
  }
  return ConverChatErrorTypesMap.DATA_ERROR_FEEDBACK + '-' + errorTypeCode
}

/**
 * 获取追问的参数
 * @param conversationId
 * @param chatId
 * @param enableFollowUpQuestion
 * @returns {Promise<any | null>}
 */
export async function getFollowUpQuestionParams(
  conversationId: string,
  chatId: string,
  enableFollowUpQuestion: boolean,
): Promise<any | null> {
  if (!enableFollowUpQuestion) return null

  try {
    const latestChat = await prisma.converChat.findFirst({
      where: {
        converId: conversationId,
        id: { not: chatId },
      },
      orderBy: {
        createdAt: 'desc',
      },
    })
    const oldLlmResponse = (latestChat?.llmResponse as any[]) || []
    if (oldLlmResponse.length > 0) {
      const selectedIndex = oldLlmResponse.findIndex((item) => item.current_select_scene === true)
      const index = selectedIndex !== -1 ? selectedIndex : 0
      const selectedResponse = oldLlmResponse[index]

      if (selectedResponse?.type === 'query-metric') {
        return selectedResponse
      }
    }
  } catch (error) {
    console.error('Error retrieving followUpQuestionParams from chat data:', error)
  }
  return null
}

/**
 * 更新某一个Chat llmResponse 用于追问 [单/多场景通用]
 * @param chatId
 * @param sceneId
 * @param response
 * @param config
 */
export async function updateChatLlmResponse(
  chatId: string,
  sceneId: string,
  response: any,
  config?: {
    metricNames: string[]
    where: string
  },
): Promise<void> {
  const { metricNames, where } = config || {}
  try {
    const currentChat = await prisma.converChat.findUnique({
      where: { id: chatId },
    })

    const oldLlmResponse = (currentChat?.llmResponse || []) as any[]

    const updatedLlmResponse = [...oldLlmResponse]
    const existingIndex = updatedLlmResponse.findIndex((item) => item.sceneId === sceneId)

    if (existingIndex !== -1) {
      // 更新metricNames where
      if (metricNames && updatedLlmResponse[existingIndex].type === 'query-metric') {
        updatedLlmResponse[existingIndex] = {
          ...updatedLlmResponse[existingIndex],
          current_select_scene: true,
          query_metric: {
            ...updatedLlmResponse[existingIndex].query_metric,
            metricNames,
            where,
          },
        }
      }
    } else {
      updatedLlmResponse.push({ ...response, sceneId })
    }

    await prisma.converChat.update({
      data: {
        llmResponse: updatedLlmResponse,
        updatedAt: new Date(),
      },
      where: { id: chatId },
    })
    console.info(`Update chat llmResponse succeed. chatId: ${chatId}, sceneId: ${sceneId}`)
  } catch (error) {
    console.error('Update chat llmResponse failed.', error)
  }
}

/**
 * 更新chat的response 单/多场景 通用
 * @param chatId {string}
 * @param responseArray {ChatResponse[]}
 */
export async function updateConverChatResponse(
  chatId: string,
  traceId: string,
  responseArray: ChatResponse[],
): Promise<void> {
  try {
    const chat = await prisma.converChat.findUnique({
      where: { id: chatId },
    })

    if (chat) {
      // 默认取第1个场景的errorType
      let errorType = ''
      const response = responseArray[0]
      if (response && response.ready === false) {
        errorType = getConverChatErrorTypeByResErrType(response.errType)
      }
      await prisma.converChat.update({
        data: {
          response: responseArray,
          updatedAt: new Date(),
          errorType,
          traceId,
        },
        where: { id: chatId },
      })
      console.info('Update converChat successfully.')
    } else {
      console.error('Update converChat failed, Chat not found.')
    }
  } catch (error) {
    console.error('Update converChat failed with error ', error)
  }
}
// /**
//  * 更新multi agent chat的response
//  * @param chatId {string}
//  * @param responseArray {ChatResponse[]}
//  */
// export async function updateMultiAgentConverChatResponse({
//   chatId,
//   traceId,
//   responseArray,
// }: {
//   chatId: string
//   traceId: string
//   responseArray: AssistantMultiAgentItem[]
// }): Promise<void> {
//   try {
//     const chat = await prisma.converChat.findUnique({
//       where: { id: chatId },
//     })

//     if (chat) {
//       // 默认取第1个场景的errorType
//       // let errorType = ''
//       const response = responseArray[0]
//       if (response) {
//         // errorType = getConverChatErrorTypeByResErrType(response.errType)
//       }
//       await prisma.converChat.update({
//         data: {
//           response: responseArray,
//           updatedAt: new Date(),
//           errorType: '',
//           traceId,
//         },
//         where: { id: chatId },
//       })
//       console.info('Update converChat successfully.')
//     } else {
//       console.error('Update converChat failed, Chat not found.')
//     }
//   } catch (error) {
//     console.error('Update converChat failed with error ', error)
//   }
// }

export async function getLogsByTraceId(traceId: string): Promise<ElkResponse[]> {
  return new Promise((resolve, reject) => {
    const match = traceId.match(/_(\d{8})\d{6}_/)
    if (!match) {
      return reject(new Error('无效的 traceId 格式'))
    }

    const dateStr = match[1] // 形如 20250514
    const formattedDate = `${dateStr.slice(0, 4)}-${dateStr.slice(4, 6)}-${dateStr.slice(6, 8)}`
    const logFileName = `${formattedDate}.log`
    const logDir = path.resolve(process.cwd(), 'logs')
    const command = `grep '"request_id":"${traceId}"' ${path.join(logDir, logFileName)}`

    console.info('[INFO] [getLogsByTraceId] Command:', command)

    exec(command, (error, stdout, stderr) => {
      if (error) {
        return reject(new Error('执行命令出错: ' + error.message))
      }

      if (stderr) {
        return reject(new Error('stderr: ' + stderr))
      }

      try {
        const rawLines = stdout.split('\n').filter((line) => line.trim() !== '')
        const jsonLines = rawLines.map((line) => JSON.parse(line)).map((x) => x.message) as ElkResponse[]
        const lines = jsonLines.filter((x) => x.module_type !== 'NODE_MIDDLEWARE')
        resolve(lines)
      } catch (parseError) {
        reject(new Error('日志解析失败: ' + (parseError as Error).message))
      }
    })
  })
}

export function mergeLogsToMainChain(logs: ElkResponse[]) {
  if (!logs || logs.length === 0) {
    console.info('[mergeLogsToMainChain] 🚨 日志为空或未传入 logs', { logs })
    return {}
  }

  const AGENT_CHAT_LOG = logs.find((i) => i.module_type === 'MULTI_AGENT_AGENT_CHAT')

  if (!AGENT_CHAT_LOG) {
    console.info('[mergeLogsToMainChain] 🚨 未找到 module_type=MULTI_AGENT_AGENT_CHAT 的日志', {
      logCount: logs.length,
      module_types: logs.map((i) => i.module_type),
    })
    return {}
  }

  if (!AGENT_CHAT_LOG.start_time) {
    console.info('[mergeLogsToMainChain] 🚨 找到 AGENT_CHAT_LOG 但缺少 start_time 字段', {
      AGENT_CHAT_LOG,
    })
    return {}
  }
  const { debug, timestamp, input, output, duration, result_code, module_type, end_time, ...rest } = AGENT_CHAT_LOG

  let resultCode = 0
  const firstNonZero = logs.find((log) => log.result_code !== 0)
  if (firstNonZero) {
    resultCode = firstNonZero.result_code
  }
  const endTime = logs.map((log) => new Date(log.end_time).getTime()).reduce((max, curr) => Math.max(max, curr), 0)

  const afterProcessLogs = logs
    .slice()
    .map((i) => {
      const {
        cluster_id,
        service_type,
        user_id,
        semantic_project_id,
        semantic_project_name,
        semantic_scene_id,
        semantic_scene_name,
        request_id,
        timestamp,
        ...rest
      } = i
      return rest
    })
    .sort((a, b) => new Date(a.start_time).getTime() - new Date(b.start_time).getTime())
    .reduce(
      (acc, cur, idx) => {
        acc[`step${idx + 1}`] = cur
        return acc
      },
      {} as Record<string, any>,
    )

  return {
    ...rest,
    result_code: resultCode,
    end_time: new Date(endTime).toISOString(),
    module_type: 'MAIN_CHAT_CHAIN',
    ...afterProcessLogs,
  }
}
