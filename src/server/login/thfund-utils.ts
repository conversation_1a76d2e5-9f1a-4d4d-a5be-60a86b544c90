/**
 * 天弘SSO相关的 helper function
 */

import axios from 'axios'
import { nanoid } from 'nanoid'
import { thfundAppName } from 'src/shared/thfund-constant'
import { thfundSecretKey, getTokenUrl, verifyTokenUrl, logoutUrl, TIANHONG_USERID_KEY } from './thfund-sso-constants'

function parseClaims(token: string): { [key: string]: string } | null {
  if (!token || typeof token !== 'string') {
    return null
  }
  try {
    const firstDotIndex = token.indexOf('.')
    const lastDotIndex = token.lastIndexOf('.')
    if (firstDotIndex !== -1 && lastDotIndex !== -1) {
      // 注意这里 多加一个点 可能都解密不出来
      const claimsBase64 = token.substring(firstDotIndex + 1, lastDotIndex)
      const decodedClaims = atob(claimsBase64) // 解码base64
      const parsedClaims = JSON.parse(decodedClaims)
      return parsedClaims
    }
  } catch (error) {
    console.error('Error in parseClaims:', error)
  }
  return null
}

/** 从tianhong-token中解析出 USERID */
function getTianHongUserId(token: string) {
  const claims = parseClaims(token)
  return claims ? claims[TIANHONG_USERID_KEY] : null
}

/* 根据授权码获取tianhong-token */
async function getTokenByAuthCodeApi(authCode: string): Promise<string> {
  const body = {
    appName: thfundAppName,
    authCode,
    secretKey: thfundSecretKey,
    requestId: nanoid(),
  }
  console.info('getTokenByAuthCode with body:', getTokenUrl, body)

  return axios
    .post(getTokenUrl, body, {
      headers: { 'Content-Type': 'application/json' },
    })
    .then((response) => {
      console.info('GetTokenByAuthCode Response:', response.data)
      if (response.data.success && response.data.result.verifyResult) {
        return response.data.result.token
      }
    })
    .catch((error) => {
      console.error('GetTokenByAuthCode Error:', error.message)
    })
}

/* 校验tianhong-token */
async function verifyTokenApi(token: string): Promise<boolean> {
  const body = {
    appName: thfundAppName,
    token,
    secretKey: thfundSecretKey,
    requestId: nanoid(),
  }
  console.info('verifyToken with body:', verifyTokenUrl, body)

  return axios
    .post(verifyTokenUrl, body, {
      headers: { 'Content-Type': 'application/json' },
    })
    .then((response) => {
      console.info('VerifyToken Response:', response.data)
      return response.data.result.verifyResult
    })
    .catch((error) => {
      console.error('VerifyToken Error:', error.message)
    })
}

/* 天弘登出 登出超时时间5s */
async function tianHongLogoutApi(token: string): Promise<boolean> {
  const body = {
    appName: thfundAppName,
    token,
    secretKey: thfundSecretKey,
    requestId: nanoid(),
  }
  console.info('tianHongLogout with body:', logoutUrl, body)

  return axios
    .post(logoutUrl, body, {
      headers: { 'Content-Type': 'application/json' },
      timeout: 5 * 1000,
    })
    .then((response) => {
      console.info('TianHong Logout Response:', response.data)
      return response.data.result.success
    })
    .catch((error) => {
      console.error('TianHong Logout Error:', error.message)
    })
}

export { getTokenByAuthCodeApi, verifyTokenApi, tianHongLogoutApi, getTianHongUserId }
