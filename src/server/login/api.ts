/**
 * 登录页 API 接口定义
 */
import express, { Router, Request, Response } from 'express'
import CryptoJS from 'crypto-js'
import jwt from 'jsonwebtoken'
import axios from 'axios'
import { askBIPageUrls } from 'src/shared/url-map'
import memoryCache from 'src/server/dao/memory-cache'
import { TOKEN_BI } from 'src/shared/constants'
import { encryptUserId } from 'src/shared/common-utils'
import { prisma } from '../dao/db'
import { checkToken, PROCESS_ENV, rangerGetAuthToken } from '../server-constants'
import { enforcer } from '../auth'
import { jwtOptions } from '../auth/passport'
import { clearAuthCookies, COOKIE_MAX_AGE, getSecondDomainOrIP } from './auth-utils'

const router: Router = express.Router()

// 将之前的dipeak.com后缀匹配改为正则域名检查，免去给客户部署还需要改匹配规则
// const domainRegex = /^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/

/** 获取auth的token，用于登录操作，需要放在登录的header上 */
router.get('/getToken', async (_req: Request, res: Response) => {
  try {
    const result = await axios.get(rangerGetAuthToken)
    console.info('getAuthToken=' + result.data.data)
    return res.json(result.data)
  } catch (error: any) {
    console.error('getAuthToken error', error.message)
    return res.json({ code: 500, msg: error.message })
  }
})

router.get('/checkUrlToken', async (req: Request, res: Response) => {
  try {
    const token = req.query.urlToken
    const checkResult = await axios.get(`${checkToken}/${token}`)
    return res.json(checkResult.data.data)
  } catch (error: any) {
    const hostname = req.query.hostname as string
    const domain = getSecondDomainOrIP(hostname)
    clearAuthCookies(req, res, domain)
    return res.json({ code: 500, msg: '校验token失败' })
  }
})

/** 校验token接口 */
router.get('/check-token', async (req: Request, res: Response) => {
  try {
    const authorization = req.header('authorization')
    if (authorization && authorization.startsWith('Bearer ')) {
      const jsessionid = authorization.slice(7).trim()
      const checkResult = await axios.get(`${checkToken}/${jsessionid}`)
      console.info('checkTokenResult = ', JSON.stringify(checkResult.data))
      return res.json(checkResult.data)
    } else {
      return res.json({ code: 500, msg: '校验token失败' })
    }
  } catch (error) {
    const hostname = req.query.hostname as string
    const domain = getSecondDomainOrIP(hostname)
    clearAuthCookies(req, res, domain)
    return res.json({ code: 500, msg: '校验token失败' })
  }
})

/** 如果已经登录，返回当前登录的用户信息；如果没有登录，则返回 null */
router.get('/current-user-basic-info', async (req: Request, res: Response) => {
  if (req.header('username')) {
    return res.json({ code: 0, data: { username: req.header('username') }, msg: '' })
  }

  if (PROCESS_ENV.RANGER_LOGIN_ENABLE) {
    const jsessionid = req.cookies.JSESSIONID
    // 如果没有 jsessionid 或未找到 username，则返回 null
    if (!jsessionid) {
      return res.json({ code: 0, data: null, msg: '' })
    }

    const username = memoryCache.get(jsessionid) || req.cookies.u_info
    if (!username) {
      return res.json({ code: 0, data: null, msg: '' })
    }

    return res.json({ code: 0, data: { username }, msg: '' })
  }
  const username = req.user?.username
  if (!username) {
    return res.json({ code: 0, data: null, msg: '未登录' })
  }

  const user = await prisma.user.findUnique({
    where: {
      username,
    },
    select: {
      id: true,
      username: true,
      userRoles: {
        select: {
          roleId: true,
          role: {
            select: {
              name: true,
            },
          },
        },
      },
    },
  })
  if (!user) {
    return res.json({ code: 0, data: null, msg: '未登录，用户不存在' })
  }
  return res.json({ code: 0, data: { username: user }, msg: '' })
})

/**
 * 自动登录
 * @param {string} thssoAuthCode - 天弘用户的身份验证令牌
 * 根据authCode获取token，校验token，从token中提取userID
 * 查看是否存在此用户 => 添加新用户 => 写入session实现登录
 */
// router.get('/tianhong-login', async (req: Request, res: Response) => {
//   console.info('Attention: Tianhong-auto-login is running with query: ', req.query)
//   const { thssoAuthCode } = req.query
//   if (thssoAuthCode == null) {
//     console.info({ code: 400, msg: '未获取到thssoAuthCode' })
//     return res.redirect(askBIPageUrls.login + '#no-auth-code')
//   }
//   try {
//     const currentToken = await getTokenByAuthCodeApi(thssoAuthCode as string)
//     const verifyTokenResult = await verifyTokenApi(currentToken)

//     if (!verifyTokenResult) {
//       console.info('Thfund login failed. token校验失败')
//       return res.redirect(askBIPageUrls.login + '#token-verify-failed')
//     }

//     // 从token中解析userID
//     const tianhongUserId = getTianHongUserId(currentToken)
//     console.info('UserID:', tianhongUserId)

//     if (!tianhongUserId) {
//       console.info('Thfund login failed. Parse user ID failed!')
//       return res.redirect(askBIPageUrls.login + '#token-parse-failed')
//     }

//     if (rangerEnable) {
//       const userInfo = await registerRangerUser(tianhongUserId)
//       if (!userInfo) {
//         console.info('Thfund login failed login with ranger failed')
//         return res.redirect(askBIPageUrls.login + '#token-verify-failed')
//       }
//       const { token, jwtToken } = userInfo
//       memoryCache.set(token, tianhongUserId, COOKIE_MAX_AGE)
//       memoryCache.set(jwtToken, tianhongUserId, COOKIE_MAX_AGE)
//       res.cookie(SESSION_ID, token, { maxAge: COOKIE_MAX_AGE, httpOnly: true })
//       res.cookie(JWT_ID, jwtToken, { maxAge: COOKIE_MAX_AGE, httpOnly: true })
//       return res.redirect(askBIPageUrls.home + '#success')
//     }

//     const existedUser = await prisma.user.findFirst({
//       where: {
//         username: tianhongUserId,
//       },
//     })
//     if (existedUser && !existedUser.isActive) {
//       console.info('Thfund login failed. 账号已失效')
//       return res.redirect(askBIPageUrls.login + '#account-invalid')
//     }
//     if (existedUser) {
//       // 更新lastLoginAt，写入 session即可
//       const loginUser = await prisma.user.update({
//         where: {
//           username: tianhongUserId,
//         },
//         data: {
//           lastLoginAt: new Date(),
//         },
//       })
//       req.session.cookie.maxAge = 12 * 60 * 60 * 1000
//       req.session.username = tianhongUserId
//       req.session.extra = { tianhongToken: currentToken }
//       console.info('Thfund login success:', { id: loginUser.id, username: loginUser.username })
//       return res.redirect(askBIPageUrls.home + '#success')
//     }

//     const newUser = await prisma.user.create({
//       data: {
//         isActive: true,
//         password: tianhongUserId,
//         username: tianhongUserId,
//         lastLoginAt: new Date(),
//         source: 'tianhong',
//       },
//     })
//     // 将用户定义成regular，统一给角色授权
//     const userRole = await prisma.userRole.create({
//       data: {
//         username: tianhongUserId,
//         roleId: 'regular',
//       },
//     })
//     req.session.cookie.maxAge = 12 * 60 * 60 * 1000
//     req.session.username = tianhongUserId
//     req.session.extra = { tianhongToken: currentToken }
//     console.info('Thfund login success:', { id: newUser.id, username: newUser.username, role: userRole })
//     return res.redirect(askBIPageUrls.home + '#success')
//   } catch (error) {
//     console.error('Thfund login failed:', error)
//     return res.redirect(askBIPageUrls.login + '#login-failed')
//   }
// })

// 客户的的 appId 和 appSecret。可以添加更多 App
const APP_AUTH_LIST = [
  {
    appId: 'baowu-askbi',
    appSecret: 'jG7rPfbbPZEK-4ucC7',
    rangerGroups: ['baowu'],
    projectNames: ['/宝武'],
    defaultLlms: ['/Dipeak LLM1-4k', '/Deepseek'],
  },
]

type AppAuth = (typeof APP_AUTH_LIST)[number]

function base64UrlEncode(str: string): string {
  return Buffer.from(str).toString('base64').replace(/=/g, '').replace(/\+/g, '-').replace(/\//g, '_')
}

function generateJwtToken(secret: string, data: string): string {
  // eslint-disable-next-line new-cap
  return CryptoJS.HmacSHA256(base64UrlEncode(data), secret).toString(CryptoJS.enc.Hex)
}

// 生成签名, AES 加密
function encryptSignature(appId: string, paramsStr: string) {
  const appSecret = APP_AUTH_LIST.find((item) => item.appId === appId)?.appSecret
  if (!appSecret) {
    throw new Error('Invalid appId')
  }

  return encodeURIComponent(CryptoJS.AES.encrypt(paramsStr, appSecret).toString())
}

// 从签名解密出数据，AES 解密
export function decryptSignature(appSecret: string, signature: string): Record<string, string> | string {
  try {
    const paramsStr = CryptoJS.AES.decrypt(decodeURIComponent(signature), appSecret).toString(CryptoJS.enc.Utf8)
    return JSON.parse(paramsStr)
  } catch (error) {
    console.error('解密失败字符串', signature, '失败', error)
    return '解密失败'
  }
}

/**
 * 请求签名的路由：appId, userId, username, timestamp, token 是必须的参数
 * 可以添加其他任意参数
 * 所有参数的值都必须是字符串，数字要先转成字符串，比如日期时间戳
 */
router.get('/request-signature', async (req: Request, res: Response) => {
  const { appId, userId, username, timestamp } = req.query
  const { token, ...otherQueryParams } = req.query

  if (!appId || !userId || !username || !timestamp || !token) {
    return res.json({ code: 1, msg: '参数格式错误' })
  }
  // 检查参数都是字符串
  if (
    typeof appId !== 'string' ||
    typeof userId !== 'string' ||
    typeof username !== 'string' ||
    typeof timestamp !== 'string'
  ) {
    return res.json({ code: 1, msg: 'appId 格式错误' })
  }

  // appId 要在 APP_AUTH_LIST 中
  const appAuth = APP_AUTH_LIST.find((item) => item.appId === appId)
  if (!appAuth) {
    return res.json({ code: 1, msg: 'appId 不存在' })
  }

  // 手动确定object的顺序，防止client/server string字符串不一致。测试通过，未来有问题改成Array
  const queryParams = {
    appId: otherQueryParams['appId'],
    userId: otherQueryParams['userId'],
    username: otherQueryParams['username'],
    timestamp: otherQueryParams['timestamp'],
    orgCode: otherQueryParams['orgCode'],
  }

  const paramsStr = JSON.stringify(queryParams)
  console.info(`收到生成签名的请求，参与加密的 paramsStr 为 ${paramsStr}`)

  const rightToken = generateJwtToken(appAuth.appSecret, paramsStr)
  if (token !== rightToken) {
    console.info(`请求的 token 非法，收到的 token 为 ${token}，正确的 token 为 ${rightToken}`)
    return res.json({ code: 2, msg: 'token 非法' })
  }

  const signature = encryptSignature(appId, paramsStr)

  res.json({ code: 0, signature })
})

/**
 * 宝武登录的接口
 * 接受签名，解析出 params，然后根据 params 中的 userId 来设置 session，完成登录
 * 0903新增ask参数, 接收传递过来推荐问题, 打开页面直接提问
 * 1030新增plat参数, 表示平台, 用于区分宝武微聊跟独立app BWEJ：独立app, ASKBI：宝武微聊, Test：本地
 */
router.get('/app-login', async (req: Request, res: Response) => {
  try {
    // 根据 signature 解析出 params
    const {
      appId,
      signature,
      ask,
      showSpeech,
      plat = 'Test',
    } = req.query as {
      appId?: string
      signature?: string
      ask?: string
      showSpeech?: string
      plat: 'Test' | 'BWEJ' | 'ASKBI'
    }
    console.info(`收到 app login 请求 appId=${appId}, signature=${signature}, ask=${ask} ,plat=${plat}`)

    if (!appId || !signature) {
      throw new Error('参数格式错误')
    }

    // appId 要在 APP_AUTH_LIST 中
    const appAuth = APP_AUTH_LIST.find((item) => item.appId === appId)
    if (!appAuth) {
      throw new Error('appId 不存在')
    }

    // 解密 signature
    const userData = decryptSignature(appAuth.appSecret, signature)
    if (typeof userData === 'string') {
      console.info(`解密失败，失败信息为 ${userData}`)
      throw new Error('解密失败')
    }

    const { userId, username, timestamp } = userData
    if (!userId || !username || !timestamp) {
      throw new Error('解密后的数据格式错误')
    }

    // 检查请求是否过期
    if (Date.now() - Number(timestamp) > 30 * 1000) {
      throw new Error('请求已过期，有效期30秒')
    }

    // 登录成功
    console.info('签名检查成功，开始登录：', { userId, username })

    // 注册用户，并设置登录
    const createRangerUserResult = await createUserInRanger(userId, username, appAuth)
    const createNodeUserResult: any = await createUserInNode(userId, username, appAuth)

    if (!createRangerUserResult) {
      throw new Error('登陆失败，Ranger 未返回数据')
    }
    if (!createNodeUserResult) {
      throw new Error('创建node用户失败')
    }
    const host = (req.headers['egw-node-ip'] || req.headers['x-forwarded-host'] || req.headers.host || '')
      .toString()
      .split(':')[0]
    if (!host) {
      console.error('获取 host 失败')
      throw new Error('获取 host 失败')
    }

    // 设置 session 和 cookies
    // req.user?.username = finalResult.username
    const domain = getSecondDomainOrIP(host)
    console.info('app-login domain--host------->>>>>', domain, host)
    console.info('app-login req.headers------->>>>>', JSON.stringify(req.headers))
    const expiresTime = new Date().getTime() + COOKIE_MAX_AGE
    const cookieOptions = {
      domain: domain,
      // domain: 'efamily.baowugroup.com',
      expires: new Date(expiresTime),
      path: '/',
      httpOnly: true,
    }
    const token = jwt.sign(
      { username: createNodeUserResult.username, id: createNodeUserResult.id },
      jwtOptions.secretOrKey,
      {
        expiresIn: '24h',
      },
    )
    createNodeUserResult.token = token
    res.cookie(TOKEN_BI, createNodeUserResult.token, cookieOptions)
    // res.cookie(TOKEN_RANGER, createRangerUserResult.jwtToken, cookieOptions)
    // 跳转到首页
    console.info('登录成功，开始重定向到首页')
    // 增加宝武判断标识
    console.info(
      'redirect url ->>>>>>',
      askBIPageUrls.chatNew +
        `?${showSpeech ? 'showSpeech=1' : 'autofocus=1'}&isBaowu=1&plat=${plat}${ask ? '&ask=' + ask : ''}&watermarkUsername=${createNodeUserResult.username}`,
    )

    res.redirect(
      askBIPageUrls.chatNew +
        `?${showSpeech ? 'showSpeech=1' : 'autofocus=1'}&isBaowu=1&plat=${plat}${ask ? '&ask=' + ask : ''}&watermarkUsername=${createNodeUserResult.username}`,
    )
  } catch (error) {
    console.error('处理登录请求时发生错误:', error)
    let errorMessage = 'unknown-error'
    if (error instanceof Error) {
      errorMessage = error.message
    }
    res.redirect(askBIPageUrls.loginError + '#' + errorMessage)
  }
})

async function createUserInRanger(
  username: string,
  firstName: string,
  appAuth: AppAuth,
): Promise<{ username: string; token: string; jwtToken: string }> {
  const reqData = {
    username: username,
    password: '#PASS22ds.322#',
    firstName: firstName,
    projects: appAuth.projectNames,
    llmList: appAuth.defaultLlms,
    groups: appAuth.rangerGroups,
  }
  console.info('调用 ranger 创建用户 ====', reqData)
  const result = await axios.post(PROCESS_ENV.AUTH_LOGIN_HOST + '/api/auth/createAndAuthorize', reqData)
  const finalResult = result.data.data
  return finalResult
}

async function createUserInNode(
  username: string,
  firstName: string,
  appAuth: AppAuth,
): Promise<{ username: string; token: string; jwtToken: string }> {
  const tag = PROCESS_ENV.BI_AES_KEY || ''
  const code = PROCESS_ENV.BI_AES_IV || ''

  const password = '#PASS22ds.322#'
  const reqData = {
    username: username,
    password: encryptUserId(password, tag, code),
    roleNames: appAuth.rangerGroups,
    rangerUsername: username,
    rangerPassword: encryptUserId(password, tag, code),
    groups: [],
    firstName: firstName,
    projects: appAuth.projectNames,
    llmList: appAuth.defaultLlms,
  }
  const result = await createUser(reqData)
  const finalResult = result as any
  return finalResult
}

async function createUser(params: any) {
  const { username, password, roles, groups, roleNames, groupNames, rangerUsername, rangerPassword } = params
  const isExist = await prisma.xUser.findFirst({ where: { username } })
  if (isExist) {
    return await prisma.xUser.update({
      data: {
        username,
        password,
        rangerUsername,
        rangerPassword,
      },
      where: {
        id: isExist.id,
      },
    })
  }
  const user = await prisma.xUser.create({
    data: {
      username,
      password,
      rangerUsername,
      rangerPassword,
    },
  })
  if (Array.isArray(roles)) {
    await enforcer.unionUserRole({ v0: { id: user.id, type: 'user' }, v1: { type: 'roles', ids: roles } })
  }
  if (Array.isArray(groups)) {
    await enforcer.unionUserRole({ v0: { id: user.id, type: 'user' }, v1: { type: 'groups', ids: groups } })
  }
  if (Array.isArray(roleNames)) {
    const idListFromNames = (
      await prisma.xRole.findMany({ where: { roleName: { in: roleNames } }, select: { id: true } })
    ).map((v) => v.id)
    await enforcer.unionUserRole({
      v0: { id: user.id, type: 'user' },
      v1: { type: 'roles', ids: idListFromNames },
    })
  }
  if (Array.isArray(groupNames)) {
    const idListFromNames = (
      await prisma.xGroup.findMany({ where: { groupName: { in: groupNames } }, select: { id: true } })
    ).map((v) => v.id)
    await enforcer.unionUserRole({
      v0: { id: user.id, type: 'user' },
      v1: { type: 'groups', ids: idListFromNames },
    })
  }

  return user
}

export default router
