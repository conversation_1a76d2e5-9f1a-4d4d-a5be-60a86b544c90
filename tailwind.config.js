/** @type {import('tailwindcss').Config} */
// import * as colors from 'tailwindcss/colors'

function withOpacity(variableName) {
  return ({ opacityValue }) => {
    if (opacityValue !== undefined) {
      return `rgba(var(${variableName}), ${opacityValue})`
    }
    return `rgb(var(${variableName}))`
  }
}
// 透明度没法适配antd的css变量, 所以先设计antd主题的css变量禁用透明度

export default {
  content: ['./src/client/**/*.{html,js,jsx,tsx,ts}', './index.html'],
  darkMode: 'class',
  theme: {
    extend: {
      width: { 'minus-11': 'calc(100% - 44px)' },
      colors: {
        primary: 'var(--color-primary)',
        primaryHoverColor: 'var(--color-primary-hover)',
        link: 'var(--color-link)',
        borderColor: 'var(--color-border-color)',
        metricUnselectedBorderColor: withOpacity('--color-metric-unselected-border-color'),
        metricSelectedBorderColor: withOpacity('--color-metric-selected-border-color'),
        metricUnselectedBgColor: withOpacity('--color-metric-unselected-bg-color'),
        metricSelectedBgColor: withOpacity('--color-metric-selected-bg-color'),
        metricColor: withOpacity('--color-metric-selected-bg-color'),
        dimensionColor: withOpacity('--color-dimension-selected-bg-color'),
        dimensionLightColor: withOpacity('--color-dimension-light-color'),
        dimensionUnselectedBorderColor: withOpacity('--color-dimension-unselected-border-color'),
        dimensionSelectedBorderColor: withOpacity('--color-dimension-selected-border-color'),
        dimensionUnselectedBgColor: withOpacity('--color-dimension-unselected-bg-color'),
        dimensionSelectedBgColor: withOpacity('--color-dimension-selected-bg-color'),
      },
      rotate: {
        12: '12deg',
        24: '24deg',
        36: '36deg',
        48: '48deg',
        60: '60deg',
        72: '72deg',
        84: '84deg',
        96: '96deg',
        108: '108deg',
        120: '120deg',
      },
      screens: {
        xs: '375', // 小于 640px 的屏幕尺寸
      },
      boxShadow: {
        md: '0px 0px 2px 0px rgba(138, 138, 138, 0.40);',
      },
    },
  },
  plugins: [],
}
