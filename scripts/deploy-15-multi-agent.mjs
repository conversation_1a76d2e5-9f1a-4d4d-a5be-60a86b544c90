/**
 * 一键部署PRE环境
 * 如果没有SSH KEY，可以手动输入密码，或者替换SSH
 * 批量清空：docker images | grep registry.gitlab.dipeak.com  | awk '{print $3}' | xargs docker rmi -f
 */
import dayjs from 'dayjs'
import { execSync } from 'child_process'
import path from 'path'
import url from 'url'

const now = dayjs()
const run = (c) => {
  console.log(`\n\n==> [${c}]`)
  execSync(c, { stdio: 'inherit', env: { ...process.env, VITE_PROJECT_ENV: 'huaxia' } })
}
const resolve = (...p) => path.resolve(path.dirname(url.fileURLToPath(import.meta.url)), '..', ...p)
const time =
  //  '0402-2224' ??
  now.format('MMDD-HHmm')
const repo = `registry.gitlab.dipeak.com/dipeak/generic-repository/ask-bi-multi-agent:${time}`

// run(
//   `docker buildx build --platform linux/amd64 -t ask-bi ${resolve()}  --output type=docker  -f ${resolve('deploy', 'node.Dockerfile')} --tag ${repo}`,
// )

run(`docker push ${repo}`)

const cmds = [
  `docker pull ${repo}`,
  `docker rm -fv ask-bi-multi-agent-node`,
  `docker run --env-file=/home/<USER>/.env --restart=always --name ask-bi-multi-agent-node -d -p 8000:8000 ${repo}`,
]
console.log('===[SERVER CMD]===')
console.log(cmds.join('\n'))

run(`ssh -T liyigang@************** "${cmds.join('; ')}"`)

// run_docker_node
// docker rm -fv ask-bi-multi-agent-node
// docker run --env-file=$(shell pwd)/.env --restart=always --name ask-bi-multi-agent-node -d -p 8000:8000 -v /home/<USER>/askbot/askbot-logs/frontend:/app/logs registry.gitlab.dipeak.com/dipeak/generic-repository/ask-bi-multi-agent:0214-1626
