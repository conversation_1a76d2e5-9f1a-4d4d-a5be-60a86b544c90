import { PrismaClient } from '@prisma/client'
import { createResource } from '../src/server/utils'
const prisma = new PrismaClient()

async function main() {
  if (process.env.NODE_ENV !== 'X-Engine') {
    // 用来兼容 之前只部署了 xengine 的情况
    // 如果没有超级管理员，创建一个
    if (!(await prisma.xUser.count({ where: { username: 'root' } }))) {
      await prisma.xUser.create({
        data: {
          id: 'root',
          username: 'root',
          password: 's69Z/drxLqmsFGEqsxbkOype0rc7PTFWEH8tymPeJMM=',
          rangerUsername: 'root',
          rangerPassword: 's69Z/drxLqmsFGEqsxbkOype0rc7PTFWEH8tymPeJMM=',
        },
      })
    }
  }

  // 初始化有所有场景和项目的角色 这个是为了兼容之前的天弘和平安
  let initRole = await prisma.xRole.findFirst({
    where: {
      roleName: 'init_role_with_all_scene_project_read_permission',
    },
  })

  if (!initRole) {
    initRole = await prisma.xRole.create({
      data: {
        roleName: 'init_role_with_all_scene_project_read_permission',
      },
    })
  }

  const allSceneReadResource = await prisma.xResource.findFirst({
    where: {
      name: 'all_scene_read',
    },
  })
  const allProjectReadResource = await prisma.xResource.findFirst({
    where: {
      name: 'all_project_read',
    },
  })
  // TODO 合并代码以后主分支用了新的页面权限配置， 等合并代码以后再进行处理
  // const allMenuReadForXEngine = await prisma.xResource.findFirst({
  //   where: {
  //     name: 'all_menu_read_for_xengine',
  //   },
  // })
  // 创建资源
  if (!allSceneReadResource) {
    createResource(
      'all_scene_read',
      'scene',
      ['*'],
      [{ id: initRole.id, type: 'role', action: 'read' }], // 创建规则
    )
  }

  if (!allProjectReadResource) {
    createResource(
      'all_project_read',
      'project',
      ['*'],
      [{ id: initRole.id, type: 'role', action: 'read' }], // 创建规则
    )
  }
  // if (!allMenuReadForXEngine) {
  //   createResource(
  //     'all_menu_read_for_xengine',
  //     'page',
  //     ['*'],
  //     [{ id: initRole.id, type: 'role', action: 'read' }], // 创建规则
  //   )
  // }
  // if (process.env.VITE_CUSTOMER === 'development') {
  //   // xengine 是全员有 ranger 需要初始化全员有ranger
  //   createResource(
  //     'ranger_menu_read',
  //     'scene',
  //     ['*'],
  //     [{ id: initRole.id, type: 'role', action: 'read' }], // 创建规则
  //   )
  // }
}

main()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (e) => {
    console.error(e)
    await prisma.$disconnect()
    process.exit(1)
  })
