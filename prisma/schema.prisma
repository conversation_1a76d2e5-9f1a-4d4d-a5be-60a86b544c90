generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "linux-musl-openssl-3.0.x", "rhel-openssl-1.0.x", "rhel-openssl-1.1.x"]
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
  relationMode = "foreignKeys"
}

enum ConversationVersion {
  v1
  v2
}

// 存储 AskBI 的会话列表，对应的会话类型为 Chat，因为 Conversation 名字有重复，这里先叫 Conver
model Conver {
  id       String              @id @default(nanoid())
  title    String              @db.VarChar(255) // 标题，暂时为空
  username String
  llmType  String              @map("llm_type") // model 模型 GPT or GLM or ...
  asks     Json // string[]
  isDraft  Boolean             @default(true) @map("is_draft") // 是否是草稿
  version  ConversationVersion // messages 的 version，目前都是 v1，用于未来给 messages 做数据订正

  semanticSceneId   String?          @map("semantic_scene_id") @db.VarChar(255) // 关联的语义模型
  semanticScene     SemanticScene?   @relation(fields: [semanticSceneId], references: [id], onUpdate: Cascade, onDelete: Cascade)
  semanticProjectId String?          @map("semantic_project_id") @db.VarChar(255) // 关联的语义项目
  semanticProject   SemanticProject? @relation(fields: [semanticProjectId], references: [id], onUpdate: Cascade, onDelete: Cascade)

  text2SqlMessages Json @map("text2sql_messages") // 用户的提问

  converChats ConverChat[]

  createdAt DateTime @default(now()) @map("create_time") @db.Timestamp(6)
  updatedAt DateTime @default(now()) @updatedAt @map("update_time") @db.Timestamp(6)

  @@index(username)
  @@map("convers")
}

model ConverChat {
  id       String  @id @default(nanoid())
  // 会话的 id
  converId String  @map("conver_id")
  ask      String // 一次问题的请求
  parentId String? @map("parent_id") // 追问的时候，父级的会话ID
  traceId  String? @map("trace_id") // elk链路id
  response Json    @map("response") // AskBI 一次问题的响应完整 JSON，包括 rows

  errorType String? @map("error_type") // 错误类型

  llmResponse Json? @map("llm_response") // AskBI LLM 的响应，用于记录 LLM 的响应
  docResponse Json? @map("doc_response") // AskDoc 接口的响应 JSON

  extraInfo Json? @map("extra_info") // 额外的信息，比如 AskDoc 中的文档信息。

  conver Conver @relation(fields: [converId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now()) @map("create_time") @db.Timestamp(6)
  updatedAt DateTime @default(now()) @updatedAt @map("update_time") @db.Timestamp(6)

  @@index(createdAt)
  @@map("conver_chats")
}

model ChatErrorReport {
  id           String @id @default(nanoid())
  ask          String // 问题
  taskType     String // 问题类型
  traceId      String @map("trace_id")
  converChatId String @map("conver_chat_id")

  createdAt DateTime @default(now()) @map("create_time") @db.Timestamp(6)
  updatedAt DateTime @default(now()) @updatedAt @map("update_time") @db.Timestamp(6)

  @@map("chat_error_reports")
}

enum EmbedStatus {
  pending
  success
  failure
}

// 保留历史的图表
model Chart {
  id                  String  @id @default(nanoid())
  chartType           String  @map("chart_type")
  chartTitle          String  @map("chart_title")
  taskType            String // 任务类型
  // 用户的提问
  ask                 String
  // 推荐的图表类型，多个类型用英文逗号分隔
  recommendChartTypes String  @map("recommend_chart_types")
  sql                 String  @map("sql") @db.Text
  semanticSceneId     String  @map("semantic_model_id") @db.VarChar(255) // 关联的语义模型
  rowsMetadata        Json    @map("rows_metadata")
  queryParams         Json    @map("query_params")
  chartThemeType      String  @map("chart_theme_type")
  username            String  @map("username")
  llmType             String? @map("llm_type")

  createdAt DateTime @default(now()) @map("create_time") @db.Timestamp(6)
  updatedAt DateTime @default(now()) @updatedAt @map("update_time") @db.Timestamp(6)

  @@map("charts")
}

model User {
  id       String  @id @default(nanoid())
  username String  @unique @db.VarChar(255)
  password String  @db.VarChar(255)
  isActive Boolean @default(true) @map("is_active")

  lastLoginAt    DateTime? @map("last_login_at") @db.Timestamp(6)
  failLoginCount Int       @default(0) @map("fail_login_count")
  // 注册的来源，比如 askbi、tianhong 等
  source         String?   @map("source")

  // 添加PermissionDatasource关联
  permissionDatasources PermissionDatasource[]
  permissionLlms        PermissionLlm[]
  permissionProjects    PermissionProject[]
  Session               Session[]

  // 一个用户可以属于多个角色
  roles     Role[]     @relation("UserRole")
  userRoles UserRole[]

  createdAt DateTime @default(now()) @map("create_time") @db.Timestamp(6)
  updatedAt DateTime @default(now()) @updatedAt @map("update_time") @db.Timestamp(6)

  @@map("users")
}

model Role {
  id        String     @id // 需要用户取有意义的名字
  name      String     @unique @db.VarChar(255)
  users     User[]     @relation("UserRole")
  userRoles UserRole[]

  rolePermissionDatasources RolePermissionDatasource[]
  rolePermissionLlms        RolePermissionLlm[]
  rolePermissionProjects    RolePermissionProject[]

  @@map("roles")
}

// 关联表 UserRoles，用于用户和角色的多对多关系
model UserRole {
  username String @map("username")
  roleId   String @map("role_id")
  user     User   @relation(fields: [username], references: [username], onUpdate: Cascade, onDelete: Cascade)
  role     Role   @relation(fields: [roleId], references: [id], onUpdate: Cascade, onDelete: Cascade)

  @@id([username, roleId])
  @@map("user_roles")
}

model Session {
  id       String   @id @default(nanoid())
  username String   @db.VarChar(255)
  expireAt DateTime // @map("expire_at") @db.Timestamp(6)
  data     String   @db.VarChar(10000)

  user User @relation(fields: [username], references: [username], onDelete: Cascade, map: "SESSION_RELATION_IN_USER")

  @@map("sessions")
}

model PermissionDatasource {
  id           String @id @default(nanoid())
  username     String @db.VarChar(255)
  datasourceId String @map("datasource_id") @db.VarChar(255)

  // 定义与User模型的username字段的外键关系
  user       User       @relation(fields: [username], references: [username], onDelete: Cascade, map: "PERMISSION_DATASOURCE_RELATION_IN_USER")
  // 定义与 Datasource 模型的 datasourceId 字段的外键关系
  datasource Datasource @relation(fields: [datasourceId], references: [id], onDelete: Cascade, map: "PERMISSION_DATASOURCE_RELATION_IN_DATASOURCE")

  @@map("permission_datasource")
}

model PermissionProject {
  id                String @id @default(nanoid())
  username          String @db.VarChar(255)
  semanticProjectId String @map("semantic_project_id") @db.VarChar(255)

  user            User            @relation(fields: [username], references: [username], onDelete: Cascade)
  semanticProject SemanticProject @relation(fields: [semanticProjectId], references: [id], onDelete: Cascade)

  @@map("permission_project")
}

model RolePermissionProject {
  id                String @id @default(nanoid())
  roleId            String @map("role_id")
  semanticProjectId String @map("semantic_project_id") @db.VarChar(255)

  role            Role            @relation(fields: [roleId], references: [id], onDelete: Cascade)
  semanticProject SemanticProject @relation(fields: [semanticProjectId], references: [id], onDelete: Cascade)

  @@map("role_permission_projects")
}

model PermissionLlm {
  id       String @id @default(nanoid())
  username String @db.VarChar(255)
  llmType  String @map("llm_type") @db.VarChar(255)

  user User @relation(fields: [username], references: [username], onDelete: Cascade)

  @@map("permission_llm")
}

model RolePermissionDatasource {
  id           String @id @default(nanoid())
  roleId       String @map("role_id")
  datasourceId String @map("datasource_id") @db.VarChar(255)

  role       Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)
  datasource Datasource @relation(fields: [datasourceId], references: [id], onDelete: Cascade)

  @@map("role_permission_datasources")
}

model RolePermissionLlm {
  id     String @id @default(nanoid())
  roleId String @map("role_id")
  role   Role   @relation(fields: [roleId], references: [id], onDelete: Cascade)

  llmType String @map("llm_type") @db.VarChar(255)

  @@map("role_permission_llms")
}

model RequestLog {
  id             String  @id @default(nanoid())
  host           String
  method         String
  url            String  @db.Text
  // 请求的参数，用于 POST 和 PUT 请求
  body           String? @db.Text
  params         String? @db.Text
  traceId        String? @map("trace_id")
  // chat 请求才有的会话 id
  conversationId String? @map("conversation_id")
  responseCode   Int     @map("response_code")
  responseBody   String  @map("response_body") @db.Text
  // 请求的持续时间
  duration       Int     @default(0)
  // 用户的 ip 地址
  userIp         String  @map("user_ip")
  // 用户的名字，在 askbi1、askbi2、askbi3 上登录后可以拿到
  username       String?

  createdAt DateTime @default(now()) @map("create_time") @db.Timestamp(6)
  updatedAt DateTime @default(now()) @updatedAt @map("update_time") @db.Timestamp(6)

  @@map("request_log")
}

// 每一次调用 GPT 就会插入一条记录
model ChatLog {
  id             String  @id @default(nanoid())
  // 会话的 id
  conversationId String  @map("conversation_id")
  // 会话的类型，对应 ChatLogType
  type           String?
  // 会话等待的时间，单位毫秒
  duration       Int     @default(0)
  // llmType 模型 GPT or GLM or ...
  llmType        String? @map("llm_type")
  username       String?
  ready          Boolean
  // 用户最后的一句话
  lastMessage    String  @map("last_message") @db.Text
  // 发给 GPT 的完整消息  
  messagesJson   String  @map("messages_json") @db.Text
  // GPT 返回的完整消息
  responseJson   String  @map("response_json") @db.Text

  createdAt DateTime @default(now()) @map("create_time") @db.Timestamp(6)
  updatedAt DateTime @default(now()) @updatedAt @map("update_time") @db.Timestamp(6)

  @@map("chat_log")
}

enum DbTypeEnum {
  mysql
  postgresql
  xengine
}

// 数据源信息
model Datasource {
  id           String     @id @default(nanoid())
  name         String     @unique @db.VarChar(255)
  dbType       DbTypeEnum @default(mysql) @map("db_type")
  host         String
  port         Int
  username     String
  password     String
  databaseName String
  // 是否启用SSL/TLS加密
  ssl          Boolean    @default(false)

  defaultTableName String? // 本数据源默认选中的表名

  permissionDatasources     PermissionDatasource[]
  rolePermissionDatasources RolePermissionDatasource[]
  TableMeta                 TableMeta[]

  createdAt DateTime @default(now()) @map("create_time") @db.Timestamp(6)
  updatedAt DateTime @default(now()) @updatedAt @map("update_time") @db.Timestamp(6)

  @@map("datasources")
}

// 应用程序的配置信息
model GlobalSetting {
  id          String  @id @default(nanoid())
  name        String  @db.VarChar(255)
  value       String  @db.Text
  description String? @db.Text
  // 环境的标识，比如 askbi1、askbi2、askbi3、dev、prod、test 等
  environment String  @db.VarChar(255)

  createdAt DateTime @default(now()) @map("create_time") @db.Timestamp(6)
  updatedAt DateTime @default(now()) @updatedAt @map("update_time") @db.Timestamp(6)

  @@map("global_settings")
}

// 下面为 Metric Store 需要的表
model FewShot {
  id               String @id @default(nanoid())
  metrics          Json
  dimensions       Json
  categories       Json // 分类，字符串数组。排名、占比、排名前N、趋势、码值查询等
  whereConstraints Json   @map("where_constraints")
  template         String @db.Text

  createdAt DateTime @default(now()) @map("create_time") @db.Timestamp(6)
  updatedAt DateTime @default(now()) @updatedAt @map("update_time") @db.Timestamp(6)

  @@map("few_shot")
}

model SemanticProject {
  id   String @id @default(nanoid())
  name String @unique @db.VarChar(255)

  createdBy String? @map("created_by") @db.VarChar(255)
  updatedBy String? @map("updated_by") @db.VarChar(255)

  description String? @db.VarChar(512)

  semanticScenes      SemanticScene[]
  semanticMetrics     SemanticMetric[]
  semanticMetricTrees SemanticMetricTree[]

  permissionProjects     PermissionProject[]
  rolePermissionProjects RolePermissionProject[]

  semanticMetricTreeRoots SemanticMetricTreeRoot[]

  createdAt DateTime @default(now()) @map("create_time") @db.Timestamp(6)
  updatedAt DateTime @default(now()) @updatedAt @map("update_time") @db.Timestamp(6)

  nl2MetricFewShots      Nl2MetricFewShot[]
  convers                Conver[]
  SemanticExternalReport SemanticExternalReport[]

  @@map("semantic_projects")
}

model SemanticScene {
  id               String  @id @default(nanoid())
  tableName        String  @map("table_name") @db.VarChar(255) // 表名，禁止使用 double underscores (__)
  label            String  @db.VarChar(255) // 中文名
  description      String? @db.Text
  aggTimeDimension String? @map("agg_time_dimension") // 聚合时间维度。本来在 defaults 当中，这里单独拿出来

  timeDimensionType   String? @map("time_dimension_type") // 时间维度类型，比如 "date", "datetime", "string"
  timeDimensionFormat String? @map("time_dimension_format") // 时间维度格式，当 timeDimensionType 为 string 的时候生效，比如 "yyyy-MM-dd", "yyyy-MM", "yyyyMM"
  timeGranularityMin  String? @map("time_granularity_min") // 时间粒度最小单位，只能是 "day", "month", "year"

  enableMetricExactMatch            Boolean? @default(false) @map("enable_metric_exact_match") // 是否启用指标精确匹配，启用后会开启置信度计算
  enableFollowUpQuestion            Boolean? @default(false) @map("enable_follow_up_question") // 是否启动追问功能，启用后默认为追问模式
  enableTryQueryUp                  Boolean? @default(false) @map("enable_try_query_up") // 是否开启数据往前追溯
  enableSelectToastWhenEmptyData    Boolean? @default(false) @map("enable_select_toast_when_empty_data") // 是否开启置信度选择空数据提示
  enableAccMetricToastWhenEmptyData Boolean? @default(false) @map("enable_acc_metric_toast_when_empty_data") // 是否开启可累加指标无数据区间提示
  enableDimensionAliasFirst         Boolean? @default(false) @map("enable_dimension_alias_first") // 是否开启优先展示维度同义词

  agent     String? @db.VarChar(255) // AI Agent
  modelId   String? @map("model_id") @db.VarChar(255)
  iconType  Int?    @map("icon_type")
  createdBy String? @map("created_by") @db.VarChar(255)
  updatedBy String? @map("updated_by") @db.VarChar(255)

  tableMetaId String?    @map("table_meta_id") @db.VarChar(255)
  tableMeta   TableMeta? @relation(fields: [tableMetaId], references: [id], onUpdate: Cascade, onDelete: Cascade)

  semanticProjectId String          @map("semantic_project_id")
  semanticProject   SemanticProject @relation(fields: [semanticProjectId], references: [id])
  convers           Conver[]

  createdAt DateTime @default(now()) @map("create_time") @db.Timestamp(6)
  updatedAt DateTime @default(now()) @updatedAt @map("update_time") @db.Timestamp(6)

  @@map("semantic_scenes")
}

// 数据模型（对应dbt model）
model SemanticModel {
  name        String  @id @db.VarChar(255) // 数据模型名(dbt model)
  label       String? @db.VarChar(255) // 中文名
  tableName   String  @map("table_name") @db.VarChar(255) // 数据表名
  description String? @db.Text

  timeDimensionName   String? @map("time_dimension_name") // 聚合时间维度。
  timeDimensionType   String? @map("time_dimension_type") // 时间维度类型，取值 "date", "datetime", "string"
  timeDimensionFormat String? @map("time_dimension_format") // 时间维度格式，当 timeDimensionType 为 string 的时候生效，比如 "yyyy-MM-dd", "yyyy-MM", "yyyyMM"
  timeGranularityMin  String? @map("time_granularity_min") // 时间粒度最小单位，取值为 "day", "month", "year"

  debugInfo String? @map("debug_info") // debug信息，包含 容器id|ASK_BI_HOST|Port

  semanticDimensions SemanticDimension[]
  semanticMeasures   SemanticMeasure[]

  createdAt DateTime @default(now()) @map("create_time") @db.Timestamp(6)
  updatedAt DateTime @default(now()) @updatedAt @map("update_time") @db.Timestamp(6) // 数据来源 sourceCreateTime

  @@map("semantic_models")
}

model SemanticDimension {
  name         String  @db.VarChar(255) // 维度名
  label        String  @default("") @db.VarChar(255) // 中文名
  type         String  @db.VarChar(255) // 维度类型, 取值 categorical(类目), time(时间), time_default(主时间)
  typeParams   Json    @map("type_params")
  synonyms     Json // 维度名同义词
  description  String? @db.Text
  expr         String  @db.Text // 维度表达式, 通常是列名，也可以是 SQL 生成
  values       Json? // 维度码值列表, 对象数组类型, 注意维度码值也是有同义词列表
  filterSwitch Boolean @default(true) @map("filter_switch") // 数据来自指标模型（ModelDesc），filterSwitch = true 才把码值加入索引和召回，默认都加入召回

  // 时间，主时间 维度具备以下属性
  timeDimensionType   String? @map("time_dimension_type") // 时间维度类型，取值 "date", "datetime", "string"
  timeDimensionFormat String? @map("time_dimension_format") // 时间维度格式，当 timeDimensionType 为 string 的时候生效，比如 "yyyy-MM-dd", "yyyy-MM", "yyyyMM"
  timeGranularityMin  String? @map("time_granularity_min") // 时间粒度最小单位，取值为 "day", "month", "year"

  semanticModelName String        @map("semantic_model_name") @db.VarChar(255)
  semanticModel     SemanticModel @relation(fields: [semanticModelName], references: [name], onUpdate: Cascade, onDelete: Cascade)

  createdAt DateTime @default(now()) @map("create_time") @db.Timestamp(6)
  updatedAt DateTime @default(now()) @updatedAt @map("update_time") @db.Timestamp(6)

  @@id([name, semanticModelName])
  @@map("semantic_dimensions")
}

model SemanticMeasure {
  name                 String   @db.VarChar(255) // DataModel中唯一
  label                String   @db.VarChar(255) // 中文名
  synonyms             Json // 同义词
  description          String?  @db.Text
  agg                  String   @db.VarChar(255) // 聚合方式,支持 sum, max, min, average, median, percentile, count_distinct, and sum_boolean
  createMetric         Boolean? @default(true) @map("create_metric") // 是否创建指标，默认创建
  formatTemplate       String?  @map("format_template") // 显示格式，比如 ￥,.2f 代表"￥#,###.00",dbt 保留字段，暂不用
  expr                 String   @db.Text // 度量表达式，通常是表中列名，也可以是 SQL 生成的新列
  nonAdditiveDimension Json?    @map("non_additive_dimension") // 度量不支持的聚合维度,举例银行卡账户余额,dbt 保留字段，暂不用
  aggParams            Json?    @map("agg_params") // 聚合参数，dbt 保留字段,暂不用
  aggTimeDimension     String?  @map("agg_time_dimension") // 可指定度量单独的聚合时间维度,通常置空实际使用 SemanticModel.aggTimeDimension, dbt 保留字段，暂不用

  semanticModelName String        @map("semantic_model_name") @db.VarChar(255)
  semanticModel     SemanticModel @relation(fields: [semanticModelName], references: [name], onUpdate: Cascade, onDelete: Cascade)

  createdAt DateTime @default(now()) @map("create_time") @db.Timestamp(6)
  updatedAt DateTime @default(now()) @updatedAt @map("update_time") @db.Timestamp(6)

  @@id([name, semanticModelName])
  @@map("semantic_measures")
}

model SemanticMetric {
  id          String  @id @default(nanoid())
  name        String  @db.VarChar(255) // 在所有 Model 中唯一，不属于某个 Model
  label       String  @db.VarChar(255)
  type        String  @db.VarChar(255) // simple, ratio, cumulative, derived, rank, meta
  rank        Int     @default(-1) // 指标排名
  keypoint    Boolean @default(false) // 重点指标标记
  category    String? // 指标分类, 格式: category_name%rank, 举例: sales%8
  synonyms    Json
  typeParams  Json    @map("type_params")
  description String? @db.Text
  filter      String? @db.Text // like WHERE clause
  filterRefs  Json?   @map("filter_refs") // filter 中引用的维度, string array
  config      Json? // Provide the specific configurations for your metric.	
  meta        Json?

  // 格式化字符串，ratio 默认为百分比，rank 默认为整数
  formatTemplate      String? @map("format_template") // 显示格式，比如 ￥,.2f 代表"￥#,###.00"
  // 实时指标计算窗口配置
  windowDescConfigStr String? @map("window_configure") @db.VarChar(1000)

  semanticProjectId String          @map("semantic_project_id")
  semanticProject   SemanticProject @relation(fields: [semanticProjectId], references: [id])

  // 每个指标都归属于一个 scene
  semanticSceneId String? @map("semantic_scene_id")

  // 自动依据 Backend Meta 接口 DataModelDesc.createMetric 标记创建指标，只会在司内指标平台中值为 true，外部指标平台默认值即可
  // 值为 true 时，指标会自动依据 DataModelDesc 更新
  autoCreateByMeasure Boolean @default(false) @map("auto_create_by_measure")

  // 值为 true 时，表示该指标可被累加
  isCumulative Boolean? @default(false) @map("is_cumulative")

  createdAt DateTime @default(now()) @map("create_time") @db.Timestamp(6)
  updatedAt DateTime @default(now()) @updatedAt @map("update_time") @db.Timestamp(6)
  creator String? @default("admin") @db.VarChar(255) // 创建人

  @@unique([name, semanticProjectId])
  @@map("semantic_metrics")
}

// 外部报表
model SemanticExternalReport {
  id          String  @id @default(nanoid())
  name        String  @db.VarChar(255) // 外部报表name
  label       String  @db.VarChar(255) // 外部报表中文名称
  type        String  @db.VarChar(255) // enum: baowu_report
  synonyms    Json // 同义词 JSONArray
  description String? @db.Text // 可选描述

  semanticSceneId String @map("semantic_scene_id")

  semanticProjectId String          @map("semantic_project_id")
  semanticProject   SemanticProject @relation(fields: [semanticProjectId], references: [id])

  createdAt DateTime @default(now()) @map("create_time") @db.Timestamp(6)
  updatedAt DateTime @default(now()) @updatedAt @map("update_time") @db.Timestamp(6)

  @@unique([name, semanticProjectId])
  @@map("semantic_external_report")
}

model SemanticMetricTreeRoot {
  id         String @id @default(nanoid())
  treeName   String @map("tree_name") @db.VarChar(255) // 指标树的名字
  metricName String @map("metric_name") // 根节点指标的标识

  semanticProjectId   String               @map("semantic_project_id")
  semanticProject     SemanticProject      @relation(fields: [semanticProjectId], references: [id])
  semanticMetricTrees SemanticMetricTree[]

  createdAt DateTime @default(now()) @map("create_time") @db.Timestamp(6)
  updatedAt DateTime @default(now()) @updatedAt @map("update_time") @db.Timestamp(6)

  @@map("semantic_metric_tree_roots")
}

model SemanticMetricTree {
  id            String  @id @default(nanoid())
  metricName    String  @map("metric_name")
  exprCalc      String? @map("expr_calc") @db.VarChar(1024) // expr_calc 表示可以计算的关系，expr_calc 和 expr_relation 二选一
  exprRelation  String? @map("expr_relation") @db.VarChar(1024) // expr_relation 表示相关性非计算的关系，expr_calc 和 expr_relation 二选一
  childrenNames Json    @map("children_names")

  semanticProjectId String          @map("semantic_project_id")
  semanticProject   SemanticProject @relation(fields: [semanticProjectId], references: [id])

  semanticMetricTreeRootId String?                 @map("semantic_metric_tree_root_id")
  semanticMetricTreeRoot   SemanticMetricTreeRoot? @relation(fields: [semanticMetricTreeRootId], references: [id], onUpdate: Cascade, onDelete: Cascade)

  createdAt DateTime @default(now()) @map("create_time") @db.Timestamp(6)
  updatedAt DateTime @default(now()) @updatedAt @map("update_time") @db.Timestamp(6)

  @@map("semantic_metric_tree")
}

model TableMeta {
  id           String  @id @default(nanoid())
  databaseName String  @map("database_name")
  tableName    String  @map("table_name")
  tableComment String? @map("table_comment")

  datasourceId String     @map("datasource_id") @db.VarChar(255)
  datasource   Datasource @relation(fields: [datasourceId], references: [id], onUpdate: Cascade, onDelete: Cascade)

  versionDate DateTime @map("version_date") // 版本日期，比如 2021-09-01 12:00:14

  TableColumnMeta TableColumnMeta[]
  semanticScene   SemanticScene[]

  createdAt DateTime @default(now()) @map("create_time") @db.Timestamp(6)

  @@map("table_meta")
}

model TableColumnMeta {
  id            String  @id @default(nanoid())
  columnName    String  @map("column_name")
  columnType    String  @map("column_type")
  isNullable    Boolean @default(false) @map("is_nullable")
  columnComment String? @map("column_comment")
  isPrimaryKey  Boolean @default(false) @map("is_primary_key")
  values        Json?   @map("values") // 字段的码值, string array

  tableMetaId String    @map("table_meta_id")
  tableMeta   TableMeta @relation(fields: [tableMetaId], references: [id], onUpdate: Cascade, onDelete: Cascade)

  createdAt DateTime @default(now()) @map("create_time") @db.Timestamp(6)

  @@map("table_column_meta")
}

// AskDoc 产品使用的表
model Doc {
  id          String      @id @default(nanoid())
  name        String      @db.VarChar(255)
  size        Int
  thumbnail   String?     @db.MediumText
  embedStatus EmbedStatus @default(pending) @map("embed_status")
  mimeType    String      @map("mime_type")

  folderId String @map("folder_id")
  folder   Folder @relation(fields: [folderId], references: [id])

  createdAt DateTime @default(now()) @map("create_time") @db.Timestamp(6)
  updatedAt DateTime @default(now()) @updatedAt @map("update_time") @db.Timestamp(6)

  @@map("doc")
}

model Folder {
  id   String @id @default(nanoid())
  name String @unique @db.VarChar(255)
  docs Doc[]

  createdAt DateTime @default(now()) @map("create_time") @db.Timestamp(6)
  updatedAt DateTime @default(now()) @updatedAt @map("update_time") @db.Timestamp(6)

  @@map("folder")
}

model Nl2MetricFewShot {
  id                String          @id @default(nanoid())
  semanticProjectId String          @map("semantic_project_id") @db.VarChar(255)
  semanticProject   SemanticProject @relation(fields: [semanticProjectId], references: [id], onDelete: Cascade)

  metrics    Json // 指标, eg: [{"name": "total_bid_amount_SUM", "label": "中标金额（亿元）"}]
  dimensions Json // 维度, eg: [{"name": "operator_label", "label": "运营商标签"}]
  think      String @db.Text
  result     Json // 结果, eg: {"orderBys": ["total_bid_amount_SUM desc"]}',
  labels     Json // 标签，eg: {"PROJECT_NAME": "示例项目"}
  scene      String // '场景, eg: metric, where, group_by, order_by'

  question    String? @db.Text
  scenesId    String? @map("scenes_id") @db.VarChar(255)
  agent       String? @db.VarChar(255)
  taskType    String? @map("task_type") @db.VarChar(255)
  subTaskType String? @map("sub_task_type") @db.VarChar(255)

  createTime DateTime? @default(now()) @map("create_time") @db.Timestamp(6)
  createdBy  String?   @map("created_by") @db.VarChar(255)
  updateTime DateTime? @default(now()) @map("update_time") @db.Timestamp(6)
  updatedBy  String?   @map("updated_by") @db.VarChar(255)

  @@map("nl2_metric_few_shots")
}

model SemanticScenesModels {
  id        String @id @default(nanoid()) @db.VarChar(255)
  sceneId   String @map("scene_id") @db.VarChar(255)
  modelName String @map("model_name") @db.VarChar(255)

  @@index([sceneId])
  @@index([modelName])
  @@map("semantic_scenes_models")
}

model Prompt {
  id               String    @id @default(nanoid())
  prefix           String    @db.Text
  name             String    @db.VarChar(255)
  suffix           String?   @db.Text
  multipleExamples String?   @map("multiple_examples") @db.Text
  question         String?   @db.Text
  createTime       DateTime? @default(now()) @map("create_time") @db.Timestamp(6)
  createdBy        String?   @map("created_by") @db.VarChar(255)
  updateTime       DateTime? @default(now()) @map("update_time") @db.Timestamp(6)
  updatedBy        String?   @map("updated_by") @db.VarChar(255)
  scenesId         String?   @map("scenes_id") @db.VarChar(255)
  agent            String?   @db.VarChar(255)
  taskType         String?   @map("task_type") @db.VarChar(255)
  subTaskType      String?   @map("sub_task_type") @db.VarChar(255)
  promptType       String?   @map("prompt_type") @db.VarChar(255)
  promptTpl        String?   @map("prompt_tpl") @db.Text
  version          String?   @db.VarChar(255)
}

model FewShotStrategy {
  id                String    @id @default(nanoid())
  prefix            String?   @db.VarChar(255)
  algorithmStrategy String?   @map("algorithm_strategy") @db.VarChar(255)
  fewShots          String?   @map("few_shots") @db.Text
  scenesId          String?   @map("scenes_id") @db.VarChar(255)
  taskType          String?   @map("task_type") @db.VarChar(255)
  subTaskType       String?   @map("sub_task_type") @db.VarChar(255)
  agent             String?   @db.VarChar(255)
  createTime        DateTime? @default(now()) @map("create_time") @db.Timestamp(6)
  createdBy         String?   @map("created_by") @db.VarChar(255)
  updateTime        DateTime? @default(now()) @map("update_time") @db.Timestamp(6)
  updatedBy         String?   @map("updated_by") @db.VarChar(255)
  version           String?   @db.VarChar(255)

  @@map("few_shot_strategy")
}

model MetricFrequency {
  id                String   @id @default(uuid()) // 使用 UUID 作为主键
  metricName        String   @map("metric_name") @db.VarChar(255) // 指标名称
  semanticSceneId   String   @map("semantic_scene_id") // 语义场景 ID
  semanticProjectId String   @map("semantic_project_id") // 语义项目 ID
  frequency         Int // 频率
  createdAt         DateTime @default(now()) @map("created_at") // 创建时间
  updatedAt         DateTime @updatedAt @map("updated_at") // 更新时间

  @@unique([semanticSceneId, metricName], name: "unique_semanticSceneId_metricName")
  @@map("metric_frequency")
}

model LoginLog {
  id       String  @id @default(uuid()) // 使用 UUID 作为主键
  userId   String  @map("user_id") @db.VarChar(50)
  username String  @db.VarChar(50)
  action   String? @db.VarChar(50) // 记录登录操作来源等信息, 当前主要用于记录宝武查看了自己app上哪个模块的功能

  loginAt String @map("login_at")

  createdAt DateTime @default(now()) @map("create_time") @db.Timestamp(6)
  updatedAt DateTime @default(now()) @updatedAt @map("update_time") @db.Timestamp(6)

  @@map("login_log")
}

model XUser {
  id             String   @id @default(nanoid())
  username       String   @unique @db.VarChar(50)
  password       String   @db.VarChar(255)
  rangerUsername String?  @map("ranger_username") @db.VarChar(50)
  rangerPassword String?  @map("ranger_password") @db.VarChar(50)
  createdAt      DateTime @default(now()) @map("create_time") @db.Timestamp(6)
  updatedAt      DateTime @default(now()) @updatedAt @map("update_time") @db.Timestamp(6)

  @@map("x_user")
}

model XRole {
  id        String   @id @default(nanoid())
  roleName  String   @unique @map("role_name") @db.VarChar(50)
  createdAt DateTime @default(now()) @map("create_time") @db.Timestamp(6)
  updatedAt DateTime @default(now()) @updatedAt @map("update_time") @db.Timestamp(6)

  @@map("x_role")
}

model XGroup {
  id        String   @id @default(nanoid())
  groupName String   @unique @map("group_name") @db.VarChar(50)
  createdAt DateTime @default(now()) @map("create_time") @db.Timestamp(6)
  updatedAt DateTime @default(now()) @updatedAt @map("update_time") @db.Timestamp(6)

  @@map("x_group")
}

model XResource {
  id          String       @id @default(nanoid())
  name        String       @unique @db.VarChar(50)
  type        String       @db.VarChar(255) // 资源类型, 取值 page
  typeData    Json?        @map("type_data")
  description String?      @db.Text // 可选描述
  createdAt   DateTime     @default(now()) @map("create_time") @db.Timestamp(6)
  updatedAt   DateTime     @default(now()) @updatedAt @map("update_time") @db.Timestamp(6)
  rules       CasbinRule[]

  @@map("x_resource")
}

model CasbinRule {
  id        String   @id @default(nanoid())
  ptype     String
  v0        String?
  v1        String?
  v2        String?
  v3        String?
  v4        String?
  v5        String?
  createdAt DateTime @default(now()) @map("create_time") @db.Timestamp(6)
  updatedAt DateTime @default(now()) @updatedAt @map("update_time") @db.Timestamp(6)

  resourceId String?    @map("resource_id") @db.VarChar(255) // 关联的语义模型
  resource   XResource? @relation(fields: [resourceId], references: [id], onDelete: Cascade)

  @@map("casbin_rule")
}

model RecommendQuestion {
  id String @id @default(uuid()) // 使用 UUID 作为主键

  fromUserId String @map("from_user_id") @db.VarChar(50) // 推荐人id
  toUserIds  Json   @map("to_user_ids") @db.Json

  ask String // 推荐问题

  expiredAt DateTime @default(now()) @map("expired_at") @db.Timestamp(6) // 过期时间

  semanticSceneId   String @map("semantic_scene_id") // 语义场景 ID
  semanticProjectId String @map("semantic_project_id") // 语义项目 ID

  createdAt DateTime @default(now()) @map("create_time") @db.Timestamp(6)
  updatedAt DateTime @default(now()) @updatedAt @map("update_time") @db.Timestamp(6)

  @@map("recommend_question")
}

model ConfigManage {
  id String @id @default(uuid())

  configKey   String   @unique @map("config_key") 
  configValue String   @map("config_value") @db.Text // 可能需要存储json的配置,所以使用text
  description String

  createdAt   DateTime @default(now()) @map("create_time") @db.Timestamp(6)
  updatedAt   DateTime @default(now()) @updatedAt @map("update_time") @db.Timestamp(6)

  @@map("config_manage")
}
