import fs from 'fs'
import { join, dirname } from 'path'
import { fileURLToPath } from 'url'
const dirName = dirname(fileURLToPath(import.meta.url))
// 定义文件路径
const schemaPath = join(dirName, 'schema.prisma')

// 读取文件内容
fs.readFile(schemaPath, 'utf8', (err, data) => {
  if (err) {
    console.error('Error reading schema.prisma:', err)
    process.exit(1)
  }

  // 替换 relationMode 的值
  const updatedData = data.replace('relationMode = "foreignKeys"', 'relationMode = "prisma"')

  // 写回文件
  fs.writeFile(schemaPath, updatedData, 'utf8', (err) => {
    if (err) {
      console.error('Error writing schema.prisma:', err)
      process.exit(1)
    }
    console.warn('relationMode successfully updated to "prisma"')
  })
})
