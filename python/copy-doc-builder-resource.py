import os
import boto3
import requests
from urllib.parse import urlparse

# S3 配置（可从环境变量读取）
access_key = os.getenv("access_key", "pxpxMQIu2972J3GGgBlK")
secret_key = os.getenv("secret_key", "09DvvwWeKaNBHV3ox0E8SN4yRiOcbC6HolqWi5wh")
region_name = os.getenv("region_name", "us-east-1")
endpoint_url = os.getenv("endpoint_url", "http://192.168.110.19:9000")
bucket_name = os.getenv("bucket_name", "ask-doc")

# 要拉取的 S3 路径（前缀）
REQUIRED_PATHS = [
    "ask_doc_models/embedding_model_path/alime-embedding-large-zh/",
    "nl2document_resources/"
]

# 下载 S3 前缀下的所有文件
def download_s3_folder(s3_client, bucket, prefix, local_dir):
    paginator = s3_client.get_paginator("list_objects_v2")
    for page in paginator.paginate(Bucket=bucket, Prefix=prefix):
        for obj in page.get("Contents", []):
            s3_key = obj["Key"]
            relative_path = os.path.relpath(s3_key, prefix)
            local_path = os.path.join(local_dir, relative_path)

            os.makedirs(os.path.dirname(local_path), exist_ok=True)
            s3_client.download_file(bucket, s3_key, local_path)
            print(f"✅ Downloaded: {s3_key} → {local_path}")

def download_all_s3_resources(local_dir="/resources/"):
    os.makedirs(local_dir, exist_ok=True)
    s3 = boto3.client(
        "s3",
        aws_access_key_id=access_key,
        aws_secret_access_key=secret_key,
        region_name=region_name,
        endpoint_url=endpoint_url,
    )
    for prefix in REQUIRED_PATHS:
        download_s3_folder(s3, bucket_name, prefix, os.path.join(local_dir, prefix.strip("/")))
    print("✅ All S3 resources downloaded.")

# 下载远程压缩包并解压
def download_and_extract_tar(url, target_dir, auth=None):
    os.makedirs(target_dir, exist_ok=True)
    file_name = os.path.basename(urlparse(url).path)
    file_path = os.path.join(target_dir, file_name)

    with requests.get(url, auth=auth, stream=True) as r:
        r.raise_for_status()
        with open(file_path, "wb") as f:
            for chunk in r.iter_content(chunk_size=8192):
                f.write(chunk)

    extract_dir = os.path.join(target_dir, "nltk_data")
    os.makedirs(extract_dir, exist_ok=True)
    os.system(f"tar -xf {file_path} -C {extract_dir}")
    print(f"✅ Extracted to {extract_dir}")

if __name__ == "__main__":
    download_all_s3_resources()
    os.system(f"mv /resources/nl2document_resources/fonts /usr/share/")
    os.system(f"mv /resources/nl2document_resources/.paddleocr /root/")
    nltk_url = "https://gitlab.dipeak.com/api/v4/projects/255/packages/generic/ask_bi_nltk/0.0.1/nltk_dir.tar.gz"
    download_and_extract_tar(nltk_url, "/root/", auth=("user", "8sMDSKRKKbsNgk_A9hwu"))