from utils import img2base64
import requests
def call_yolo_ocr_inference(image_path: str, model_type: str = "ouryolo"):
    image_base64, _ = img2base64(image_path)
    url = "http://localhost:8000/yolo-ocr/inference"
    data = {
        "image": image_base64,
        "model_type": model_type
    }
    response = requests.post(url, json=data)
    return response.json()

def call_bert_inference(text: str):
    url = "http://localhost:8000/bert/inference"
    data = {
        "text": text
    }
    response = requests.post(url, json=data)
    return response.json()

if __name__ == "__main__":
    image_path = "/test_image.png"
    model_type = "ouryolo"
    result = call_yolo_ocr_inference(image_path, model_type)
    print(result)
    text = "1234567890"
    result = call_bert_inference(text)
    print(result)
