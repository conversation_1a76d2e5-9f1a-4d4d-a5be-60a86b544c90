# YOLO服务配置示例
# 复制此文件为 .env 并根据需要修改配置

# 模型路径配置
OURYOLO_MODEL_PATH=/ask_doc_ocr/AskDoc_OCR/runs/detect/train39/weights/best.pt
DOCLAYOUT_MODEL_PATH=/ask_doc_ocr/AskDoc_OCR/runs/detect/train39/weights/doclayout_yolo_docstructbench_imgsz1024.pt
YOLO_TABLE_DET_MODEL_PATH=/ask_doc_ocr/AskDoc_OCR/runs/detect/train39/weights/table_yolo.pt
BERT_MODEL_PATH=/resources/nl2document_resources/ask_doc_ocr/AskDoc_OCR/runs/detect/train39/weights
BERT_MODEL_NAME=bert-base-chinese
BERT_WEIGHTS=bert_best.pt

# 模型配置
DEVICE=cpu
BERT_DROPOUT=0.3

# 标签映射（JSON格式）
# LABEL2ID={"A": 0, "B": 1, "0": 0, "1": 1}

# 日志配置
LOG_LEVEL=INFO
LOG_DIR=logs
LOG_MAX_BYTES=10485760
LOG_BACKUP_COUNT=5
LOG_TO_CONSOLE=true
LOG_TO_FILE=true
