import cv2
import base64
import numpy as np
import os
from typing import <PERSON>ple, List

def img2base64(img_path: str) -> <PERSON><PERSON>[str, str]:
    """将图片文件转换为base64字符串
    
    Args:
        img_path: 图片文件路径
        
    Returns:
        Tuple[str, str]: (base64字符串, 图片路径)
    """
    with open(img_path, 'rb') as image_file:
        base64_image = base64.b64encode(image_file.read()).decode('utf-8')
    return base64_image, img_path

def base64_to_numpy(base64_str: str) -> np.ndarray:
    """将base64编码的图片转换为numpy数组
    
    Args:
        base64_str: base64编码的图片字符串
        
    Returns:
        np.ndarray: 图片的numpy数组表示
        
    Raises:
        ValueError: 当base64解码失败时
    """
    try:
        # 移除base64头信息(如果存在)
        if ',' in base64_str:
            base64_str = base64_str.split(',')[1]
            
        # 解码base64字符串
        img_data = base64.b64decode(base64_str)
        
        # 转换为numpy数组
        nparr = np.frombuffer(img_data, np.uint8)
        
        # 解码图片
        img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        if img is None:
            raise ValueError("Failed to decode image")
            
        return img
        
    except Exception as e:
        raise ValueError(f"Failed to convert base64 to image: {str(e)}")

def save_img(input: np.ndarray, save_path: str) -> None:
    """保存图片到指定路径
    
    Args:
        input: 图片数组
        save_path: 保存路径
    """
    dirname = os.path.dirname(save_path)
    filename = os.path.basename(save_path)
    cv2.imwrite(f"{dirname}/res_{filename}", input)

def get_pos(text: str) -> List[Tuple[str, str]]:
    """获取文本的位置信息
    
    Args:
        text: 输入文本
        
    Returns:
        List[Tuple[str, str]]: 文本位置信息列表
    """
    lines = text.split("\n")
    spans = []
    
    for i, line in enumerate(lines):
        if i == 0:
            spans.append(("", line))
        else:
            spans.append((lines[i-1], line))
            
    return spans 