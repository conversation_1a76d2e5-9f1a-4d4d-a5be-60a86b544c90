# PaddleOCR Service

基于FastAPI的PaddleOCR图像检测服务，支持OCR识别、表格检测和表格单元格检测。

## 功能特性

- **PaddleOCR文本识别**: 支持中文OCR识别
- **表格检测**: 检测图像中的表格结构
- **表格单元格检测**: 检测表格中的单元格
- **完善的日志系统**: 支持控制台和文件双重输出，自动日志轮转
- **配置管理**: 基于Pydantic的配置管理，支持环境变量

## 日志系统

### 日志特性

- **双重输出**: 同时输出到控制台和文件
- **日志轮转**: 自动按文件大小进行日志轮转，避免单个文件过大
- **分级日志**: 普通日志和错误日志分别存储
- **详细格式**: 包含时间戳、模块名、行号等详细信息
- **可配置**: 通过配置文件或环境变量灵活配置

### 日志文件结构

```
logs/
├── paddle_service_20231201.log        # 主日志文件（所有级别）
├── paddle_service_20231201.log.1      # 轮转备份文件
├── paddle_service_20231201.log.2
├── paddle_service_error_20231201.log  # 错误日志文件（仅ERROR和CRITICAL）
└── paddle_service_error_20231201.log.1
```

### 日志配置

在 `.env` 文件中配置日志参数：

```bash
# 日志级别: DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_LEVEL=INFO

# 日志目录
LOG_DIR=logs

# 单个日志文件最大大小（字节）
LOG_MAX_BYTES=10485760  # 10MB

# 保留的备份文件数量
LOG_BACKUP_COUNT=5

# 是否输出到控制台
LOG_TO_CONSOLE=true

# 是否输出到文件
LOG_TO_FILE=true
```

### 日志格式

**控制台输出格式**:
```
2023-12-01 10:30:45,123 - INFO - PaddleOCR服务启动
```

**文件输出格式**:
```
2023-12-01 10:30:45,123 - app - INFO - app.py:29 - PaddleOCR服务启动
```

## API接口

### 1. PaddleOCR文本识别

**POST** `/paddle-ocr/inference`

```json
{
  "image": "base64_encoded_image",
  "threshold": 0.5,
  "batch_size": 1
}
```

### 2. 表格检测

**POST** `/table-det/inference`

```json
{
  "image": "base64_encoded_image",
  "threshold": 0.5,
  "batch_size": 1
}
```

### 3. 表格单元格检测

**POST** `/table-cell-det/inference`

```json
{
  "image": "base64_encoded_image",
  "threshold": 0.5,
  "batch_size": 1
}
```

## 快速开始

1. **安装依赖**:
   ```bash
   pip install -r requirements.txt
   ```

2. **配置环境**:
   ```bash
   cp .env.example .env
   # 编辑 .env 文件，配置模型路径等参数
   ```

3. **启动服务**:
   ```bash
   python app.py
   ```

4. **查看日志**:
   ```bash
   # 实时查看日志
   tail -f logs/paddle_service_$(date +%Y%m%d).log
   
   # 查看错误日志
   tail -f logs/paddle_service_error_$(date +%Y%m%d).log
   ```

## 配置说明

所有配置项都可以通过环境变量或 `.env` 文件进行设置。详细配置请参考 `.env.example` 文件。

## 架构说明

- **app.py**: FastAPI应用主文件，定义API接口
- **services.py**: 业务逻辑层，包含模型服务类
- **models.py**: Pydantic数据模型定义
- **config.py**: 配置管理
- **utils.py**: 工具函数
- **logging_config.py**: 日志配置模块
