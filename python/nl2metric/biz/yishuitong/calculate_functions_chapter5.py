from collections import defaultdict
from datetime import datetime
from dateutil.relativedelta import relativedelta


class ReportChapter5:
    # 5.1
    def calculate_elastic_modulus(self, dt, data, threshold1=0.8, threshold2=1.2):
        """
        5.1 销售额变动率与增值税应纳税额变动率弹性系数检查
        参数:
            data (dict): 包含企业财税数据的字典
        返回:
            list: 包含每年销售额、应纳税额、销售额变动率、应纳税额变动率和弹性系数的列表，格式如下：
                [
                    {
                        "所属期": "2021",
                        "销售额（元）": 2944.5,
                        "应纳税额（元）": 2944.5,
                        "销售额变动率": "32.1%",
                        "应纳税额变动率": "32.1%",
                        "弹性系数": 0.3
                    },
                    ...
                ]
            str: 风险描述
        """
        # 税号确认
        taxid = data["data"]["enterpriseInfo"]["taxpayerId"]
        default_result = [{'所属期':'','销售额（元）':'','应纳税额（元）':'', '应纳税额变动率':'', '弹性系数':''}]
        default_risk_desc = "风险描述：该指标项未检测到风险"
        all_zero = True
        if taxid != self.company_taxid:
            return default_result, default_risk_desc
        company_name = data["data"]["enterpriseInfo"]["taxpayerName"]
        # 时间处理
        dt_format = datetime.strptime(dt, "%Y%m")
        end_year = (dt_format - relativedelta(years=1)).strftime("%Y")
        start_year = (dt_format - relativedelta(years=3)).strftime("%Y")

        value_added = data["data"]["valueAdded"]
        detail_data = defaultdict(lambda: [])

        # 提取销售额和应纳税额
        # 小规模->一般纳税人的时间节点...
        small_end_date = max(
            v["endDate"]
            for v in value_added
            if v.get("levyProjectName") == "《增值税及附加税费申报表（小规模纳税人适用）》"
        )
        small_end = datetime.strptime(small_end_date, "%Y-%m-%d %H:%M:%S").strftime(
            "%Y%m%d"
        )

        for value in value_added:
            if value["invalidMark"] == "Y":
                continue
            dt_start = datetime.strptime(
                value["beginDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y%m%d")
            dt_end = datetime.strptime(value["endDate"], "%Y-%m-%d %H:%M:%S").strftime(
                "%Y%m%d"
            )
            dt_end_year = datetime.strptime(
                value["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year or dt_end_year < start_year:
                continue

            if value["levyProjectName"] == "《增值税及附加税费申报表（一般纳税人适用）》":
                if dt_end != dt_end_year + "1231":
                    continue
                # 即征即退项目-本年累计
                immed_amount = (
                    float(value.get("immediateRetreatYearAccumulativeAmount", 0.0))
                    if value.get("immediateRetreatYearAccumulativeAmount", 0.0) != None
                    else 0.0
                )
                # 一般项目-本年累计
                general_amount = (
                    float(value.get("generalYearAccumulativeAmount", 0.0))
                    if value.get("generalYearAccumulativeAmount", 0.0) != None
                    else 0.0
                )

                if value["projectName"] in [
                    "（一）按适用税率计税销售额",
                    "（二）按简易办法计税销售额",
                    "（三）免、抵、退办法出口销售额",
                    "（四）免税销售额",
                ] or value["projectName"] in ["应纳税额合计"]:
                    detail_data[(dt_start, dt_end)].append(
                        {
                            "source_type": "general",
                            "amount": immed_amount + general_amount,
                            "project_name": value["projectName"],
                            "change_type": value["changeType"],
                        }
                    )
                else:
                    continue

            elif value["levyProjectName"] == "《增值税及附加税费申报表（小规模纳税人适用）》":
                if dt_end != small_end:
                    continue
                # 本年累计-货物及劳务
                immed_amount = (
                    float(value.get("currentYearAccumulativeGoods", 0.0))
                    if value.get("currentYearAccumulativeGoods", 0.0) != None
                    else 0.0
                )
                # 本年累计-服务不动产和无形资产
                general_amount = (
                    float(value.get("currentYearAccumulativeService", 0.0))
                    if value.get("currentYearAccumulativeService", 0.0) != None
                    else 0.0
                )

                if value["projectName"] in [
                    "（一）应征增值税不含税销售额（3%征收率）",
                    "（二）应征增值税不含税销售额（5%征收率）",
                    "（三）销售使用过的固定资产不含税销售额",
                    "（四）免税销售额",
                    "（五）出口免税销售额",
                ] or value["projectName"] in ["应纳税额合计"]:
                    detail_data[(dt_start, dt_end)].append(
                        {
                            "source_type": "small",
                            "amount": immed_amount + general_amount,
                            "project_name": value["projectName"],
                            "change_type": value["changeType"],
                        }
                    )

                else:
                    continue

        def filter_detail_data(detail_data):
            # 存储 (dt_start, dt_end, source_type, project_name) -> 最佳记录
            best_records = {}

            # 遍历所有日期范围和记录
            for date_range, records in detail_data.items():
                dt_start, dt_end = date_range
                for record in records:
                    key = (
                        dt_start,
                        dt_end,
                        record["source_type"],
                        record["project_name"],
                    )
                    current_change_type = record["change_type"]

                    # 如果当前记录是 "更正后全量模式"，直接覆盖
                    if current_change_type == "更正后新产生的申报表（全量模式）":
                        best_records[key] = record
                    # 否则，仅当 key 不存在时才保留
                    elif key not in best_records:
                        best_records[key] = record

            # 重新构建 detail_data（按原时间分组）
            new_detail_data = {}
            for key, record in best_records.items():
                dt_start, dt_end, _, _ = key
                date_range = (dt_start, dt_end)
                if date_range not in new_detail_data:
                    new_detail_data[date_range] = []
                new_detail_data[date_range].append(record)

            return new_detail_data

        new_detail_data = filter_detail_data(detail_data)

        annual_data = defaultdict(lambda: {"tax_amount": 0.00, "sell_amount": 0.00})
        for (dt_start, dt_end), value in new_detail_data.items():
            for item in value:
                if item["project_name"] == "应纳税额合计":
                    annual_data[dt_end[:4]]["tax_amount"] += item["amount"]
                else:
                    annual_data[dt_end[:4]]["sell_amount"] += item["amount"]

        # 按年份排序
        sorted_years = sorted(annual_data.keys(), reverse=False)

        # 计算变动率和弹性系数
        result = []
        risk = []
        for i, year in enumerate(sorted_years):
            if i > 0:  # 从第二年开始计算变动率
                prev_year = sorted_years[i - 1]
                # 销售额变动率
                if annual_data[prev_year]["sell_amount"] != 0:
                    sales_growth_rate = (
                        annual_data[year]["sell_amount"]
                        - annual_data[prev_year]["sell_amount"]
                    ) / annual_data[prev_year]["sell_amount"]
                else:
                    sales_growth_rate = None  # 上一年销售额为 0，无法计算变动率

                # 应纳税额变动率
                if annual_data[prev_year]["tax_amount"] != 0:
                    tax_growth_rate = (
                        annual_data[year]["tax_amount"]
                        - annual_data[prev_year]["tax_amount"]
                    ) / annual_data[prev_year]["tax_amount"]
                else:
                    tax_growth_rate = None  # 上一年应纳税额为 0，无法计算变动率
            else:
                # 第一年没有上一年数据，变动率为 null
                sales_growth_rate = None
                tax_growth_rate = None

            # 计算弹性系数
            if (
                sales_growth_rate is not None
                and tax_growth_rate is not None
                and tax_growth_rate != 0
            ):
                elastic_modulus = (sales_growth_rate / tax_growth_rate) * 100
            else:
                elastic_modulus = None  # 无法计算弹性系数

            # 格式化输出

            formatted_sales_growth_rate = (
                "{:,.2f}".format(sales_growth_rate * 100, 2) + "%"
                if sales_growth_rate is not None
                else "--"
            )
            formatted_tax_growth_rate = (
                "{:,.2f}".format(tax_growth_rate * 100, 2) + "%"
                if tax_growth_rate is not None
                else "--"
            )
            formatted_elastic_modulus = (
                "{:,.2f}".format(elastic_modulus, 2)
                if elastic_modulus is not None
                else "--"
            )
            if annual_data[year]["sell_amount"] or annual_data[year]["tax_amount"]: all_zero = False
            result.append(
                {
                    "所属期": year + "年",
                    "销售额（元）": "{:,.2f}".format(annual_data[year]["sell_amount"], 2)
                    if annual_data[year]["sell_amount"] is not None
                    else "--",
                    "应纳税额（元）": "{:,.2f}".format(annual_data[year]["tax_amount"], 2)
                    if annual_data[year]["tax_amount"] is not None
                    else "--",
                    "销售额变动率": formatted_sales_growth_rate,
                    "应纳税额变动率": formatted_tax_growth_rate,
                    "弹性系数": formatted_elastic_modulus,
                }
            )

            # 风险识别：检查弹性系数是否在合理范围（0.8 - 1.2）
            if elastic_modulus is not None and (
                elastic_modulus < threshold1 or elastic_modulus > threshold2
            ):
                risk.append({"年份": year + "年", "弹性系数": formatted_elastic_modulus})

        if all_zero: return default_result, default_risk_desc
        risk_desc = f"风险描述："
        if risk:
            for i in risk:
                risk_desc += f"{i['年份']}年度，弹性系数为{i['弹性系数']}，"
            risk_desc += (
                f"弹性系数（正常范围{threshold1}～{threshold2}）异常可能指示存在销售额或应纳税额的异常波动，需要进一步核查。\n"
            )
        else:
            risk_desc += f"该指标项未检测到风险"

        return result, risk_desc

    # 5.2
    def calculate_tax_growth_rate(self, dt, data, threshold=0.5):
        """
        5.2  增值税应纳税额同比变动率检查
        参数:
            current_date
            data (dict): 包含企业财税数据的字典
        返回:
            list: 包含每年应纳税额和应纳税额变动率的列表，格式如下：
                [
                    {
                        "所属期": "2021",
                        "应纳税额（元）": 2944.5,
                        "应纳税额变动率": "32.1%"
                    },
                    ...
                ]
            str: 风险描述
        """
        # 税号确认
        taxid = data["data"]["enterpriseInfo"]["taxpayerId"]
        default_result = [{'所属期':'','应纳税额（元）':'','应纳税额同比变动率':''}]
        default_risk_desc = "风险描述：该指标项未检测到风险"
        all_zero = True
        if taxid != self.company_taxid:
            return default_result, default_risk_desc
        company_name = data["data"]["enterpriseInfo"]["taxpayerName"]
        # 时间处理
        dt_format = datetime.strptime(dt, "%Y%m")
        end_year = (dt_format - relativedelta(years=1)).strftime("%Y")
        start_year = (dt_format - relativedelta(years=3)).strftime("%Y")

        value_added = data["data"]["valueAdded"]
        detail_data = defaultdict(lambda: [])

        # 提取销售额和应纳税额
        # 小规模->一般纳税人的时间节点...
        small_end_date = max(
            v["endDate"]
            for v in value_added
            if v.get("levyProjectName") == "《增值税及附加税费申报表（小规模纳税人适用）》"
        )
        small_end = datetime.strptime(small_end_date, "%Y-%m-%d %H:%M:%S").strftime(
            "%Y%m%d"
        )

        for value in value_added:
            if value["invalidMark"] == "Y":
                continue
            dt_start = datetime.strptime(
                value["beginDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y%m%d")
            dt_end = datetime.strptime(value["endDate"], "%Y-%m-%d %H:%M:%S").strftime(
                "%Y%m%d"
            )
            dt_end_year = datetime.strptime(
                value["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            # 过去3年
            if dt_end_year > end_year or dt_end_year < start_year:
                continue
            if value["levyProjectName"] == "《增值税及附加税费申报表（一般纳税人适用）》":
                if dt_end != dt_end_year + "1231":
                    continue
                # 即征即退项目-本年累计
                immed_amount = (
                    float(value.get("immediateRetreatYearAccumulativeAmount", 0.0))
                    if value.get("immediateRetreatYearAccumulativeAmount", 0.0) != None
                    else 0.0
                )
                # 一般项目-本年累计
                general_amount = (
                    float(value.get("generalYearAccumulativeAmount", 0.0))
                    if value.get("generalYearAccumulativeAmount", 0.0) != None
                    else 0.0
                )

                if value["projectName"] in ["应纳税额合计"]:
                    detail_data[(dt_start, dt_end)].append(
                        {
                            "source_type": "general",
                            "amount": immed_amount + general_amount,
                            "project_name": value["projectName"],
                            "change_type": value["changeType"],
                        }
                    )
                else:
                    continue
            elif value["levyProjectName"] == "《增值税及附加税费申报表（小规模纳税人适用）》":
                if dt_end != small_end:
                    continue
                # 本年累计-货物及劳务
                immed_amount = (
                    float(value.get("currentYearAccumulativeGoods", 0.0))
                    if value.get("currentYearAccumulativeGoods", 0.0) != None
                    else 0.0
                )
                # 本年累计-服务不动产和无形资产
                general_amount = (
                    float(value.get("currentYearAccumulativeService", 0.0))
                    if value.get("currentYearAccumulativeService", 0.0) != None
                    else 0.0
                )
                if value["projectName"] in ["应纳税额合计"]:
                    detail_data[(dt_start, dt_end)].append(
                        {
                            "source_type": "small",
                            "amount": immed_amount + general_amount,
                            "project_name": value["projectName"],
                            "change_type": value["changeType"],
                        }
                    )
                else:
                    continue

        def filter_detail_data(detail_data):
            # 存储 (dt_start, dt_end, source_type, project_name) -> 最佳记录
            best_records = {}

            # 遍历所有日期范围和记录
            for date_range, records in detail_data.items():
                dt_start, dt_end = date_range
                for record in records:
                    key = (
                        dt_start,
                        dt_end,
                        record["source_type"],
                        record["project_name"],
                    )
                    current_change_type = record["change_type"]

                    # 如果当前记录是 "更正后全量模式"，直接覆盖
                    if current_change_type == "更正后新产生的申报表（全量模式）":
                        best_records[key] = record
                    # 否则，仅当 key 不存在时才保留
                    elif key not in best_records:
                        best_records[key] = record

            # 重新构建 detail_data（按原时间分组）
            new_detail_data = {}
            for key, record in best_records.items():
                dt_start, dt_end, _, _ = key
                date_range = (dt_start, dt_end)
                if date_range not in new_detail_data:
                    new_detail_data[date_range] = []
                new_detail_data[date_range].append(record)

            return new_detail_data

        new_detail_data = filter_detail_data(detail_data)

        annual_data = defaultdict(lambda: {"tax_amount": 0.00})
        for (dt_start, dt_end), value in new_detail_data.items():
            for item in value:
                annual_data[dt_end[:4]]["tax_amount"] += item["amount"]

        # # 按年份排序
        sorted_years = sorted(annual_data.keys(), reverse=False)
        # # 计算变动率
        result = []
        risks = []
        for i, year in enumerate(sorted_years):
            if i > 0:  # 从第二年开始计算变动率
                prev_year = sorted_years[i - 1]
                # 应纳税额变动率
                if annual_data[prev_year]["tax_amount"] != 0:
                    tax_growth_rate = (
                        annual_data[year]["tax_amount"]
                        - annual_data[prev_year]["tax_amount"]
                    ) / annual_data[prev_year]["tax_amount"]
                else:
                    tax_growth_rate = None  # 上一年应纳税额为 0，无法计算变动率
            else:
                # 第一年没有上一年数据，变动率为 null
                tax_growth_rate = None

            if tax_growth_rate is not None and abs(tax_growth_rate) > threshold:
                growth_or_decline = "增长" if tax_growth_rate > 0 else "下降"
                risks.append(
                    {
                        "year": year + "年",
                        "growth_or_decline": growth_or_decline,
                        "tax_growth_rate": ":,.2f".format(abs(tax_growth_rate * 100), 2)
                        + "%",
                    }
                )
            if annual_data[year]["tax_amount"]: all_zero = False
            # 格式化输出
            result.append(
                {
                    "所属期": year + "年",
                    "应纳税额（元）": "{:,.2f}".format(annual_data[year]["tax_amount"], 2),
                    "应纳税额同比变动率": "{:,.2f}".format(tax_growth_rate * 100, 2) + "%"
                    if tax_growth_rate is not None
                    else "--",
                }
            )

        formatted_threshold = "{:,.2f}".format(threshold * 100, 2) + "%"
        if all_zero: return default_result, default_risk_desc

        if risks:
            risk_desc = "风险描述：近三年内，"
            descriptions = [
                f"{risk['year']}年{risk['growth_or_decline']}{risk['tax_growth_rate']}"
                for risk in risks
            ]
            risk_desc += "、".join(descriptions)
            risk_desc += f"，波动超过了{formatted_threshold}。大幅波动可能指示企业经营状况或税务申报存在异常。\n"
        else:
            risk_desc = "风险描述：该指标项未检测到风险"

        return result, risk_desc

    # 5.3
    def calculate_revenue_sales_diff(self, dt, data, threshold=0.1):
        """
        5.3 所得税和增值税收入匹配检查
        参数:
            current_date
            data (dict): 包含企业财税数据的字典
        返回:
            list: 包含每年营业收入、增值税销售额和差额的列表，格式如下：
                [
                    {
                        "所属期": "2021",
                        "营业收入": 2944.5,
                        "增值税销售额（元）": 2944.5,
                        "差额（元）": 0
                    },
                    ...
                ]
        """
        # 税号确认
        taxid = data["data"]["enterpriseInfo"]["taxpayerId"]
        default_result = [{'所属期': '', '营业收入（元）': '', '增值税销售额（元）': '', '差额（元）': ''}]
        default_risk_desc = "风险描述：该指标项未检测到风险"
        if taxid != self.company_taxid:
            return default_result, default_risk_desc

        # 时间处理
        dt_format = datetime.strptime(dt, "%Y%m")
        end_year = (dt_format - relativedelta(years=1)).strftime("%Y")
        start_year = (dt_format - relativedelta(years=3)).strftime("%Y")

        """step1: 增值税"""
        value_added = data["data"]["valueAdded"]  # valueAdded 表示增值税
        detail_data_add = defaultdict(lambda: [])

        # 小规模->一般纳税人的时间节点...
        small_end_date = max(
            v["endDate"]
            for v in value_added
            if v.get("levyProjectName") == "《增值税及附加税费申报表（小规模纳税人适用）》"
        )
        small_end = datetime.strptime(small_end_date, "%Y-%m-%d %H:%M:%S").strftime(
            "%Y%m%d"
        )
        for value in value_added:
            if value["invalidMark"] == "Y":
                continue
            dt_start = datetime.strptime(
                value["beginDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y%m%d")
            dt_end = datetime.strptime(value["endDate"], "%Y-%m-%d %H:%M:%S").strftime(
                "%Y%m%d"
            )
            dt_end_year = datetime.strptime(
                value["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year or dt_end_year < start_year:
                continue
            if value["levyProjectName"] == "《增值税及附加税费申报表（一般纳税人适用）》":
                if dt_end != dt_end_year + "1231":
                    continue
                # 即征即退项目-本年累计
                immed_amount = (
                    float(value.get("immediateRetreatYearAccumulativeAmount", 0.0))
                    if value.get("immediateRetreatYearAccumulativeAmount", 0.0) != None
                    else 0.0
                )
                # 一般项目-本年累计
                general_amount = (
                    float(value.get("generalYearAccumulativeAmount", 0.0))
                    if value.get("generalYearAccumulativeAmount", 0.0) != None
                    else 0.0
                )

                if value["projectName"] in [
                    "（一）按适用税率计税销售额",
                    "（二）按简易办法计税销售额",
                    "（三）免、抵、退办法出口销售额",
                    "（四）免税销售额",
                ]:
                    detail_data_add[dt_end_year].append(
                        {
                            "source_type": "general",
                            "amount": immed_amount + general_amount,
                            "project_name": value["projectName"],
                            "change_type": value["changeType"],
                        }
                    )
                else:
                    continue
            elif value["levyProjectName"] == "《增值税及附加税费申报表（小规模纳税人适用）》":
                if dt_end != small_end:
                    continue
                # 本年累计-货物及劳务
                immed_amount = (
                    float(value.get("currentYearAccumulativeGoods", 0.0))
                    if value.get("currentYearAccumulativeGoods", 0.0) != None
                    else 0.0
                )
                # 本年累计-服务不动产和无形资产
                general_amount = (
                    float(value.get("currentYearAccumulativeService", 0.0))
                    if value.get("currentYearAccumulativeService", 0.0) != None
                    else 0.0
                )

                if value["projectName"] in [
                    "（一）应征增值税不含税销售额（3%征收率）",
                    "（二）应征增值税不含税销售额（5%征收率）",
                    "（三）销售使用过的固定资产不含税销售额",
                    "（四）免税销售额",
                    "（五）出口免税销售额",
                ]:
                    detail_data_add[dt_end_year].append(
                        {
                            "source_type": "small",
                            "amount": immed_amount + general_amount,
                            "project_name": value["projectName"],
                            "change_type": value["changeType"],
                        }
                    )

                else:
                    continue

        """step2: 企业所得税"""
        income_records = data["data"]["corporateIncome"]  # # corporateIncome 表示所得税
        detail_data_income = defaultdict(lambda: [])
        for record in income_records:
            dt_end_year = datetime.strptime(
                record["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year or dt_end_year < start_year:
                continue
            if record["invalidMark"] == "Y":
                continue
            # if dt_end != dt_year + '1231': continue
            # 检查是否为年度纳税申报表
            if record["levyProjectName"] == "《中华人民共和国企业所得税年度纳税申报表（A类）》（A100000）":
                amount = float(record["accumulativeAmount"] or 0)
                # 提取营业收入
                if record["projectName"] == "一、营业收入（填写A101010/101020/103000）":
                    detail_data_income[dt_end_year].append(
                        {
                            "amount": amount,
                            "project_name": "营业收入",
                            "change_type": record["changeType"],
                        }
                    )

        def filter_detail_data(detail_data):
            # 存储 (dt_start, dt_end, source_type, project_name) -> 最佳记录
            best_records = {}
            # 遍历所有日期范围和记录
            for date_range, records in detail_data.items():
                for record in records:
                    key = (date_range, record["project_name"])
                    current_change_type = record["change_type"]
                    # 如果当前记录是 "更正后全量模式"，直接覆盖
                    if current_change_type == "更正后新产生的申报表（全量模式）":
                        best_records[key] = record
                    # 否则，仅当 key 不存在时才保留
                    elif key not in best_records:
                        best_records[key] = record

            # 重新构建 detail_data（按原时间分组）
            new_detail_data = {}
            for key, record in best_records.items():
                date_range, _ = key
                if date_range not in new_detail_data:
                    new_detail_data[date_range] = []
                new_detail_data[date_range].append(record)
            return new_detail_data

        new_detail_data_add = filter_detail_data(detail_data_add)
        new_detail_data_income = filter_detail_data(detail_data_income)

        annual_data = defaultdict(lambda: {"sell_amount": 0.00, "income_amount": 0.00})
        # 增值税聚合：
        for dt_year, value in new_detail_data_add.items():
            for item in value:
                annual_data[dt_year]["sell_amount"] += item["amount"]
        # 企业所得税聚合：
        for dt_year, value in new_detail_data_income.items():
            for item in value:
                annual_data[dt_year]["income_amount"] += item["amount"]

        all_zero = True  # 标记是否所有应纳税所得额和营业收入都是00
        # 计算差额并格式化输出
        result = []
        risks = []
        for year in sorted(annual_data.keys()):
            revenue = annual_data[year]["income_amount"]
            sales_amt = annual_data[year]["sell_amount"]
            diff = revenue - sales_amt
            if revenue and sales_amt:
                all_zero = False

            if diff is not None and revenue != 0 and abs(diff / revenue) > threshold:
                risks.append(
                    {
                        "所属期": year + "年",
                        "营业收入": revenue,
                        "增值税销售额（元）": sales_amt,
                        "差额（元）": round(diff, 2),
                    }
                )

            result.append(
                {
                    "所属期": year + "年",
                    "营业收入": "{:,.2f}".format(revenue, 2)
                    if revenue is not None
                    else "--",
                    "增值税销售额（元）": "{:,.2f}".format(sales_amt, 2)
                    if sales_amt is not None
                    else "--",
                    "差额（元）": "{:,.2f}".format(diff, 2) if diff is not None else "--",
                }
            )

        formated_threshold = "{:,.2f}".format(threshold, 2)
        risk_desc = f"风险描述："
        if all_zero:
            return default_result, default_risk_desc
        if risks:
            risk_desc += f"近三年内，"
            risk_desc += ",".join(
                [
                    f"{i['所属期']}所得税申报的营业收入为{i['营业收入']}元，增值税申报的销售额为{i['增值税销售额（元）']}元，差额为{i['差额（元）']}元"
                    for i in risks
                ]
            )
            risk_desc += f"超过了阈值{formated_threshold}，较大差异可能指示存在收入不一致的风险，需要核实两者的差异原因。\n"
        else:
            risk_desc += f"该指标项未检测到风险"

        return result, risk_desc

    # 5.4
    def calculate_revenue_diff(self, dt, data, threshold=0.1):
        """
        5.4 企业所得税营业收入与利润表的营业收入比对
        参数:
            current_date
            data (dict): 包含企业财税数据的字典
        返回:
            list: 包含每年企业所得税营业收入、利润表的营业收入和差额的列表，格式如下：
                [
                    {
                        "所属期": "2021",
                        "年报营业收入": 2944.5,
                        "财报营业收入": 2944.5,
                        "差额（元）": 0
                    },
                    ...
                ]
        """
        # 税号确认
        taxid = data["data"]["enterpriseInfo"]["taxpayerId"]
        default_result = [{'所属期':'','年报营业收入（元）':'', '财报营业收入（元）':'', "差额（元）":''}]

        default_risk_desc = "风险描述：该指标项未检测到风险"
        if taxid != self.company_taxid:

            return default_result, default_risk_desc

        # 时间处理
        dt_format = datetime.strptime(dt, "%Y%m")
        end_year = (dt_format - relativedelta(years=1)).strftime("%Y")
        start_year = (dt_format - relativedelta(years=3)).strftime("%Y")
        annual_data = defaultdict(
            lambda: {"tax_income_amount": 0.00, "profit_income_amount": 0.00}
        )

        """step1: 企业所得税：营业收入"""
        income_records = data["data"]["corporateIncome"]  # # corporateIncome 表示所得税
        detail_data_income = defaultdict(lambda: [])
        for record in income_records:
            dt_end_year = datetime.strptime(
                record["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year or dt_end_year < start_year:
                continue
            if record["invalidMark"] == "Y":
                continue
            # if dt_end != dt_year + '1231': continue
            # 检查是否为年度纳税申报表
            if record["levyProjectName"] == "《中华人民共和国企业所得税年度纳税申报表（A类）》（A100000）":
                amount = float(record["accumulativeAmount"] or 0)
                # 提取营业收入
                if record["projectName"] == "一、营业收入（填写A101010/101020/103000）":
                    detail_data_income[dt_end_year].append(
                        {
                            "amount": amount,
                            "project_name": "营业收入",
                            "change_type": record["changeType"],
                        }
                    )

        def filter_detail_data(detail_data):
            # 存储 (dt_start, dt_end, source_type, project_name) -> 最佳记录
            best_records = {}
            # 遍历所有日期范围和记录
            for date_range, records in detail_data.items():
                for record in records:
                    key = (date_range, record["project_name"])
                    current_change_type = record["change_type"]
                    # 如果当前记录是 "更正后全量模式"，直接覆盖
                    if current_change_type == "更正后新产生的申报表（全量模式）":
                        best_records[key] = record
                    # 否则，仅当 key 不存在时才保留
                    elif key not in best_records:
                        best_records[key] = record

            # 重新构建 detail_data（按原时间分组）
            new_detail_data = {}
            for key, record in best_records.items():
                date_range, _ = key
                if date_range not in new_detail_data:
                    new_detail_data[date_range] = []
                new_detail_data[date_range].append(record)
            return new_detail_data

        new_detail_data_income = filter_detail_data(detail_data_income)
        # 企业所得税聚合：
        for dt_year, value in new_detail_data_income.items():
            for item in value:
                annual_data[dt_year]["tax_income_amount"] += item["amount"]

        """step2:利润表：收入"""
        # 提取利润表的营业收入
        financeProfit = data["data"]["financeProfit"]
        for profit in financeProfit:
            dt_end_year = datetime.strptime(
                profit["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year or dt_end_year < start_year:
                continue
            if profit["period"] == "Year" and profit["projectName"] == "一、营业收入":
                amount = float(profit["currentYearAccumulativeAmount"] or 0)
                annual_data[dt_end_year]["profit_income_amount"] = amount

        # 计算差额并格式化输出

        result = []
        risks = []
        all_zero = True
        for year in sorted(annual_data.keys()):
            tax_income_amount = annual_data[year]["tax_income_amount"]
            profit_income_amount = annual_data[year]["profit_income_amount"]
            diff = tax_income_amount - profit_income_amount
            if tax_income_amount or profit_income_amount:
                all_zero = False
            if (
                diff is not None
                and tax_income_amount != 0
                and abs(diff / tax_income_amount) > threshold
            ):
                risks.append(
                    {
                        "所属期": year + "年",
                        "年报营业收入（元）": "{:,.2f}".format(tax_income_amount, 2),
                        "财报营业收入（元）": "{:,.2f}".format(profit_income_amount, 2),
                        "差额（元）": ":,.2f".format(diff, 2),
                    }
                )

            result.append(
                {
                    "所属期": year + "年",
                    "年报营业收入（元）": "{:,.2f}".format(tax_income_amount, 2),
                    "财报营业收入（元）": "{:,.2f}".format(profit_income_amount, 2),
                    "差额（元）": diff,
                }
            )
        formatted_threshold = "{:,.2f}".format(threshold, 2)
        risk_desc = f"风险描述："
        if all_zero:
            return default_result, default_risk_desc
        if risks:
            risk_desc += f"近三年内，"
            risk_desc += ",".join(
                [
                    f"{i['所属期']}年所得税申报的营业收入为{i['年报营业收入']}元，利润表中的营业收入为{i['财报营业收入']}元，差额为{i['差额（元）']}元，"
                    for i in risks
                ]
            )
            risk_desc += f"超过了阈值{formatted_threshold}。较大差异可能指示存在收入不一致的风险，需要核实两者的差异原因。\n"
        else:
            risk_desc += f"该指标项未检测到风险"

        return result, risk_desc
