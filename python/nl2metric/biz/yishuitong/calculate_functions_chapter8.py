from collections import defaultdict
from datetime import datetime
from dateutil.relativedelta import relativedelta


class ReportChapter8:
    # 8.1
    def calculate_business_net_profit_rate(self, dt, data, threshold=0.05):
        ## 8.1 营业净利率
        # 税号确认
        default_result = [{
            '所属期':'',
            '净利润（元）':'',
            '营业收入（元）':'',
            '营业净利润（元）':''
        }]
        default_risk_desc = "风险描述：该指标项未检测到风险"
        taxid = data["data"]["enterpriseInfo"]["taxpayerId"]
        if taxid != self.company_taxid:

            return default_result, default_risk_desc
        company_name = data["data"]["enterpriseInfo"]["taxpayerName"]

        # 时间处理
        dt_format = datetime.strptime(dt, "%Y%m")
        end_year = (dt_format - relativedelta(years=1)).strftime("%Y")
        start_year = (dt_format - relativedelta(years=3)).strftime("%Y")
        annual_data = defaultdict(
            lambda: {"income_amount": 0.00, "profit_amount": 0.00}
        )

        """利润表：净利润+营业收入"""
        # 提取利润表的营业收入:取年报表（一定存在年粒度财报，从3个年表里面会填写一个）
        financeProfit = data["data"]["financeProfit"]
        for profit in financeProfit:
            dt_end_year = datetime.strptime(
                profit["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year or dt_end_year < start_year:
                continue
            if profit["period"] == "Year" and profit["projectName"] == "一、营业收入":
                amount = float(profit["currentYearAccumulativeAmount"] or 0)
                annual_data[dt_end_year]["income_amount"] = amount
            elif (
                profit["period"] == "Year"
                and profit["projectName"] == "四、净利润（净亏损以“-”号填列）"
            ):
                amount = float(profit["currentYearAccumulativeAmount"] or 0)
                annual_data[dt_end_year]["profit_amount"] = amount

        # 计算差额并格式化输出
        result = []
        risk_years = []
        all_zero = True
        formatted_threshold = "{:,.2f}".format(threshold * 100, 2) + "%"
        for year in sorted(annual_data.keys()):
            income_amount = annual_data[year]["income_amount"]
            profit_amount = annual_data[year]["profit_amount"]
            if income_amount or profit_amount:
                all_zero = False
            net_profit_rate = profit_amount / income_amount if income_amount else None
            annual_data[year]["net_profit_rate"] = net_profit_rate

            formatted_profit_amount = "{:,.2f}".format(profit_amount, 2)
            formatted_income_amount = "{:,.2f}".format(income_amount, 2)
            formatted_net_profit_rate = (
                "{:,.2f}".format(net_profit_rate * 100, 2) + "%"
                if net_profit_rate
                else "--"
            )
            result.append(
                {
                    "所属期": year + "年",
                    "净利润（元）": formatted_profit_amount,
                    "营业收入（元）": formatted_income_amount,
                    "营业净利率": formatted_net_profit_rate,
                }
            )
            if net_profit_rate is not None and net_profit_rate < threshold:
                risk_years.append(
                    {
                        "所属期": year + "年",
                        "净利润（元）": formatted_profit_amount,
                        "营业收入（元）": formatted_income_amount,
                        "营业净利率": formatted_net_profit_rate,
                        "描述": "是负值"
                        if net_profit_rate and net_profit_rate < 0
                        else f"低于{formatted_threshold}",
                    }
                )

        if all_zero:
            return default_result, default_risk_desc
        if risk_years:
            risk_desc = f"风险描述：{company_name}最近3年以来，"
            risk_desc += ",".join(
                [
                    f"{item['所属期']}的营业净利率({item['营业净利率']}){item['描述']}"
                    for item in risk_years
                ]
            )
            risk_desc += f"。表明营业净利率偏低或波动较大可能反映企业盈利能力不足，或存在收入确认不规范、成本费用异常波动等问题，需核查企业的收入真实性及成本费用管控情况。"
        else:
            risk_desc = "风险描述：该指标项未检测到风险"

        return result, risk_desc

    # 8.2
    def calculate_total_assets_net_profit_rate(self, dt, data, threshold=0.03):
        # 8.2 总资产净利率
        # 税号确认
        taxid = data["data"]["enterpriseInfo"]["taxpayerId"]
        default_result = [{
            '所属期':'',
            '净利润（元）':'',
            '平均总资产（元）':'',
            '总资产净利率':''
        }]
        default_risk_desc = "风险描述：该指标项未检测到风险"
        if taxid != self.company_taxid:

            return default_result, default_risk_desc
        company_name = data["data"]["enterpriseInfo"]["taxpayerName"]

        # 时间处理
        dt_format = datetime.strptime(dt, "%Y%m")
        end_year = (dt_format - relativedelta(years=1)).strftime("%Y")
        start_year = (dt_format - relativedelta(years=3)).strftime("%Y")
        annual_data = defaultdict(
            lambda: {"balance_amount": 0.00, "profit_amount": 0.00}
        )

        """step1: 利润表：净利润"""
        # 提取financeProfit数据：取年报表（一定存在年粒度财报，从3个年表里面会填写一个）
        financeProfit = data["data"]["financeProfit"]
        for profit in financeProfit:
            dt_end_year = datetime.strptime(
                profit["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year or dt_end_year < start_year:
                continue
            if (
                profit["period"] == "Year"
                and profit["projectName"] == "四、净利润（净亏损以“-”号填列）"
            ):
                amount = float(profit["currentYearAccumulativeAmount"] or 0)
                annual_data[dt_end_year]["profit_amount"] = amount

        """step2: 资产负债表：期初+期末/2"""
        # 提取financeBalance数据：取年报表
        financeBalance = data["data"]["financeBalance"]
        for balance in financeBalance:
            dt_end_year = datetime.strptime(
                balance["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year or dt_end_year < start_year:
                continue
            if balance["period"] == "Year" and balance["projectName"] == "资产合计":
                ending_balance = float(balance["endingBalance"] or 0)
                init_balance = float(balance["initialBalance"] or 0)
                avg_balance = (ending_balance + init_balance) / 2
                annual_data[dt_end_year]["balance_amount"] = avg_balance

        result = []
        risk_years = []
        all_zero = True
        formatted_threshold = "{:,.2f}".format(threshold * 100, 2) + "%"
        for year in sorted(annual_data.keys()):
            balance_amount = annual_data[year]["balance_amount"]
            profit_amount = annual_data[year]["profit_amount"]
            if balance_amount or profit_amount:
                all_zero = False
            profit_balance_rate = (
                profit_amount / balance_amount if balance_amount else None
            )
            annual_data[year]["profit_balance_rate"] = profit_balance_rate

            # 格式化：
            formatted_balance_amount = "{:,.2f}".format(balance_amount, 2)
            formatted_profit_amount = "{:,.2f}".format(profit_amount, 2)
            formatted_profit_balance_rate = (
                "{:,.2f}".format(profit_balance_rate * 100, 2) + "%"
                if profit_balance_rate
                else "--"
            )

            result.append(
                {
                    "所属期": year + "年",
                    "净利润（元）": formatted_profit_amount,
                    "平均总资产": formatted_balance_amount,
                    "总资产净利率": formatted_profit_balance_rate,
                }
            )
            if profit_balance_rate is not None and profit_balance_rate < threshold:
                risk_years.append(
                    {
                        "所属期": year + "年",
                        "净利润（元）": formatted_profit_amount,
                        "平均总资产": formatted_balance_amount,
                        "总资产净利率": formatted_profit_balance_rate,
                        "描述": "是负值"
                        if profit_balance_rate and profit_balance_rate < 0
                        else f"低于{formatted_threshold}",
                    }
                )
        if all_zero:
            return default_result, default_risk_desc

        if risk_years:
            risk_desc = "风险描述："
            risk_desc += ",".join(
                [
                    f"{item['所属期']}的总资产净利率（{item['总资产净利率']}）{item['描述']}"
                    for item in risk_years
                ]
            )
            risk_desc += (
                f"。总资产净利率过低可能表明企业资产利用效率较差，或存在资产虚增、低效资产配置等问题，需关注企业资产管理及盈利能力的实际情况。"
            )

        else:
            risk_desc = "风险描述：该指标项未检测到风险"

        return result, risk_desc

    # 8.3
    def calculate_equity_net_profit_rate(self, dt, data, threshold=0.08):
        # 8.3 投资回报率（权益净利率）
        # 税号确认
        taxid = data["data"]["enterpriseInfo"]["taxpayerId"]
        default_result = [{
            '所属期':'',
            '净利润（元）':'',
            '股东权益（元）':'',
            '权益净利率':''
        }]
        default_risk_desc = "风险描述：该指标项未检测到风险"
        if taxid != self.company_taxid:

            return default_result, default_risk_desc
        company_name = data["data"]["enterpriseInfo"]["taxpayerName"]

        # 时间处理
        dt_format = datetime.strptime(dt, "%Y%m")
        end_year = (dt_format - relativedelta(years=1)).strftime("%Y")
        start_year = (dt_format - relativedelta(years=3)).strftime("%Y")
        annual_data = defaultdict(lambda: {"stock_amount": 0.00, "profit_amount": 0.00})

        """step1: 利润表：净利润"""
        # 提取financeProfit数据：取年报表（一定存在年粒度财报，从3个年表里面会填写一个）
        financeProfit = data["data"]["financeProfit"]
        for profit in financeProfit:
            dt_end_year = datetime.strptime(
                profit["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year or dt_end_year < start_year:
                continue
            if (
                profit["period"] == "Year"
                and profit["projectName"] == "四、净利润（净亏损以“-”号填列）"
            ):
                amount = float(profit["currentYearAccumulativeAmount"] or 0)
                annual_data[dt_end_year]["profit_amount"] = amount

        """step2: 资产负债表：（股东权益）期初+期末/2"""
        # 提取financeBalance数据：取年报表
        financeBalance = data["data"]["financeBalance"]
        for balance in financeBalance:
            dt_end_year = datetime.strptime(
                balance["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year or dt_end_year < start_year:
                continue
            if (
                balance["period"] == "Year"
                and balance["projectName"] == "所有者权益（或股东权益）合计"
            ):
                ending_balance = float(balance["endingBalance"] or 0)
                init_balance = float(balance["initialBalance"] or 0)
                avg_balance = (ending_balance + init_balance) / 2
                annual_data[dt_end_year]["stock_amount"] = avg_balance

        result = []
        risk_years = []
        all_zero = True
        formatted_threshold = "{:,.2f}".format(threshold * 100, 2) + "%"
        for year in sorted(annual_data.keys()):
            stock_amount = annual_data[year]["stock_amount"]
            profit_amount = annual_data[year]["profit_amount"]
            if stock_amount or profit_amount:
                all_zero = False
            profit_stock_rate = profit_amount / stock_amount if stock_amount else None
            annual_data[year]["profit_stock_rate"] = profit_stock_rate

            # 格式化：
            formatted_stock_amount = "{:,.2f}".format(stock_amount, 2)
            formatted_profit_amount = "{:,.2f}".format(profit_amount, 2)
            formatted_profit_stock_rate = (
                "{:,.2f}".format(profit_stock_rate * 100, 2) + "%"
                if profit_stock_rate
                else "--"
            )

            result.append(
                {
                    "所属期": year + "年",
                    "净利润（元）": formatted_profit_amount,
                    "股东权益（元）": formatted_stock_amount,
                    "权益净利率": formatted_profit_stock_rate,
                }
            )
            if profit_stock_rate is not None and profit_stock_rate < threshold:
                risk_years.append(
                    {
                        "所属期": year + "年",
                        "净利润（元）": formatted_profit_amount,
                        "股东权益（元）": formatted_stock_amount,
                        "权益净利率": formatted_profit_stock_rate,
                        "描述": "是负值"
                        if profit_stock_rate and profit_stock_rate < 0
                        else f"低于{formatted_threshold}",
                    }
                )
        if all_zero:
            return default_result, default_risk_desc

        if risk_years:
            risk_desc = f"风险描述：{company_name}在过去3年以来，"
            risk_desc += ",".join(
                [
                    f"{item['所属期']}的权益净利率（{item['权益净利率']}）{item['描述']}"
                    for item in risk_years
                ]
            )
            risk_desc += f"。权益净利率偏低或波动较大可能反映企业股东权益未能有效带来收益，或存在资本结构不合理、净利润异常波动等问题，需关注企业的盈利能力及资本管理效率。"

        else:
            risk_desc = "风险描述：该指标项未检测到风险"

        return result, risk_desc
