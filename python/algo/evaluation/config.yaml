url: 'http://192.168.110.100:12123/api/v1/agent'
headers:
  accept: 'application/json'
  Content-Type: 'application/json'
  userId: 'End2EndTest'
  Traceid: 'End2EndTest'
data:
  messages:
    - role: 'user'
      content: '<PERSON><PERSON><PERSON>订购的产品数量总数是多少（不用early_stop）'
      extra_info: {}
  model_type: 'deepseek-agent-14b'
  chat_model_type: 'deepseek-14b'
  project_id: 'rcf8vkDv7AEzbrjl'
  model_id: 'Y0NBWKwpfJmGk7pi'
  task_id: 'test_ts'
  file_ids:
    - 'string'
  enable_internet_search: false
  enable_doc_search: false
  additional_info: {}

bird_car_retails:
  project_id: 'rcf8vkDv7AEzbrjl'
  model_id: 'Y0NBWKwpfJmGk7pi'

bird_regional_sales:
  project_id: 'rcf8vkDv7AEzbrjl'
  model_id: 'arWaGS7rgs5Rurc81TWR9'
