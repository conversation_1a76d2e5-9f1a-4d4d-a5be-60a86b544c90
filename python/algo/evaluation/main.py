# import json
import json_repair
import os
import re
import time
from openpyxl import Workbook
from openpyxl.reader.excel import load_workbook

from utils import ReadCSV, MyAgent, PromptTemplate, ChatDS


def result_analyse(result):
    res = result.split('</think>')[-1].strip()
    if "\"" in res:
        res = res.replace('"', '')
    if '======sourceNodes=======' in res:
        res = res.split('======sourceNodes=======')[0].strip()
    if '\n' in res:
        res = res.replace('\n', ' ').strip()
    # if "\"" in res:
    #     res = res.replace('"', '')
    # if "=" in res:
    #     res = res.replace('=', '')
    # if "sourceNodes" in res:
    #     res = res.replace('sourceNodes', '')
    # if ":" in res:
    #     res = res.replace(':', '')
    return res.strip()


def response_analyse(response):
    if "```" in response:
        pattern = r'```json(.*?)```'

        json_data = re.findall(pattern, response, re.DOTALL)

        if json_data:
            # 提取到的json字符串
            extracted_json = json_data[0].strip()
            # 将字符串转换为JSON对象
            res_data = json_repair.repair_json(extracted_json, return_objects=True)
            print(res_data)
            return res_data
        else:
            print("未找到JSON数据")
            return None
    else:
        res_data = json_repair.repair_json(response, return_objects=True)
        print(res_data)
        return res_data


if __name__ == '__main__':
    dataset_path = r'data/公开数据集汇总-0509-latest.csv'

    save_excel_file = r'result/result-公开数据集汇总-0509-latest.xlsx'
    if not os.path.exists(save_excel_file):
        wb = Workbook()
        ws = wb.active
        ws.append(['index', 'sceneName', 'question', 'agent result', 'input', 'output', 'summary', 'evaluate time'])
        wb.save(save_excel_file)

    start_row = 40
    end_row = None
    f = ReadCSV(dataset_path, file_type=dataset_path.split('.')[-1])
    data = f.get_data(start_row=start_row, end_row=end_row,
                      sceneName='sceneName',
                      questionID='questionID',
                      question='question',
                      groundTruth='groundTruth')

    # print(data)
    # print(data['sceneName'][59], data['questionID'][59], data['question'][59], data['groundTruth'][59])

    sceneName_list = data['sceneName']
    questionID_list = data['questionID']
    question_list = data['question']
    groundTruth_list = data['groundTruth']

    assert len(sceneName_list) == len(questionID_list) == len(question_list) == len(groundTruth_list)
    print(len(sceneName_list), len(questionID_list), len(question_list), len(groundTruth_list))

    agent = MyAgent()
    prompt_template = PromptTemplate()
    client = ChatDS()  # 评估模型 ds14b

    n = len(sceneName_list)
    # n = 1
    for i in range(n):
        idx = i + start_row
        sceneName = sceneName_list[idx]
        questionID = questionID_list[idx]
        question = question_list[idx]
        groundTruth = groundTruth_list[idx]
        start_time = time.time()

        # test
        # sceneName = 'bird_car_retails'
        # questionID = 1558
        # question = '哪个客户的订单数最多？'
        # groundTruth = 'Euro+ Shopping Channel发出的订单最多，一共有26笔'

        print('---------', idx+1, sceneName, questionID, question, groundTruth, '---------')
        # break
        state_code, result = agent.chat(question, scene=sceneName, question_id=questionID)
        if state_code != 200 or isinstance(result, dict):
            wb = load_workbook(save_excel_file)
            ws = wb.active
            ws.append([str(idx + 1), sceneName, question, f'agent state_code {state_code}', str(result), 'error', 'error', str(-1)])
            wb.save(save_excel_file)
            continue
        # print(result_analyse(result))
        print('result:', result)
        answer = result_analyse(result)
        print('answer:', answer)

        prompt = prompt_template.set_prompt(answer, groundTruth)

        response = client.chat(prompt)
        end_time = time.time()
        elapsed_time = end_time - start_time
        print(f"程序运行时间: {elapsed_time}秒")
        # print(response)
        print('==============={response}===============')
        print(response.choices[0].text)
        response = response.choices[0].text
        response = response.split('</think>')[-1].strip()

        wb = load_workbook(save_excel_file)
        ws = wb.active

        try:
            res = response_analyse(response)
            if res is None:
                ws.append([str(idx + 1), sceneName, question, result, response, 'error', 'error', str(elapsed_time)])
            else:
                ws.append([str(idx + 1), sceneName, question, result, str(res['input']), str(res['output']), str(res['summary']), str(elapsed_time)])
        except Exception as e:
            ws.append([str(idx + 1), sceneName, question, result, response, 'error', f'{e}', str(elapsed_time)])

        wb.save(save_excel_file)


