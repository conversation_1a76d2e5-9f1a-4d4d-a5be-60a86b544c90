# 评估目录结构说明

本项目目录结构如下：

```
evaluation/
├── main.py       # 本地样本集端到端评估脚本
├── utils.py      # 包含评估脚本用到的一些工具函数
├── config.yaml   # agent调用的参数设置
├── README.md     # 说明文档
```

## 目录详细说明

- **main.py**  
  - 本地样本集的端到端评估脚本。
  - 运行前需要在脚本内指定样本集(.csv)路径和评估结果(.xlsx)保存路径。
  - 需要在服务器上提前启用评估模型的服务。

- **utils.py**  
  - 包含评估脚本用到的一些工具函数，包括agent调用、prompt设置以及评估模型调用等。

- **config.yaml**  
  - python脚本调用agent的参数设置，包括url、headers和data等

- **README.md**  
  - 当前目录文件的介绍
