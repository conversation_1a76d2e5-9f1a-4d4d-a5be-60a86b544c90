import json

import pandas as pd
import requests
import yaml
from openai import OpenAI
import time
from datetime import datetime


class ReadCSV:
    def __init__(self, file_path, file_type='csv'):
        self.file_path = file_path
        if file_type == 'csv':
            self.df = pd.read_csv(self.file_path, encoding='utf-8')
        else:
            self.df = pd.read_excel(self.file_path, sheet_name=0)

    def get_data(self, start_row=0, end_row=None, **kwargs):
        info = {}
        df_subset = self.df.iloc[start_row:end_row]
        for k, v in kwargs.items():
            if v not in df_subset.columns:
                print(f'{k} not found in dataframe')
            else:
                content = df_subset[v]
                info[v] = content

        return info


# "http://123.181.192.99:28005/v1/chat/completions"
class ChatDS:
    def __init__(self, model_path='ds14b', max_tokens=10000):
        # 配置 OpenAI API 密钥和 Base URL 以连接 vLLM 服务
        self.openai_api_key = "EMPTY"  # vLLM 服务不需要 API 密钥，可以使用任意字符串
        self.openai_api_base = "http://123.181.192.99:29005/v1"  # 请确保端口号与您启动 vLLM 服务时设置的端口号一致
        self.model_path = model_path
        self.client = OpenAI(
            api_key=self.openai_api_key,
            base_url=self.openai_api_base,
        )
        self.max_tokens = max_tokens

    def chat(self, prompt):
        response = self.client.completions.create(
            model=self.model_path,  # 请确保模型名称与您下载的模型一致
            prompt=prompt,
            stream=False,  # 设置为 False 表示不使用流式输出
            max_tokens=self.max_tokens
        )
        return response


class MyAgent:
    def __init__(self, config_path='config.yaml'):
        with open(config_path, 'r', encoding='utf-8') as file:
            self.config = yaml.safe_load(file)

        self.url = self.config['url']
        self.headers = self.config['headers']
        self.data = self.config['data']

        self.bird_car_retails = self.config['bird_car_retails']
        self.bird_regional_sales = self.config['bird_regional_sales']

    def test_curl(self):
        current_timestamp = time.time()
        date_time = datetime.fromtimestamp(current_timestamp).strftime('%Y%m%d%H%M%S')
        self.headers['Traceid'] = 'End2EndTest-1000-' + str(date_time)
        # print('data:', self.data)
        response = requests.post(self.url, headers=self.headers, data=json.dumps(self.data))
        response = response.json()
        result = response['data']['data'][0]['result']
        # print(result)
        return result

    def __set_id(self, scene='bird_car_retails', question_id=1000):
        assert scene in ['bird_car_retails', 'bird_regional_sales']
        # 获取当前时间的时间戳
        current_timestamp = time.time()
        date_time = datetime.fromtimestamp(current_timestamp).strftime('%Y%m%d%H%M%S')
        self.headers['Traceid'] = f'End2EndTest-{question_id}-' + str(date_time)
        if scene == 'bird_car_retails':
            self.data['project_id'] = self.bird_car_retails['project_id']
            self.data['model_id'] = self.bird_car_retails['model_id']
        elif scene == 'bird_regional_sales':
            self.data['project_id'] = self.bird_regional_sales['project_id']
            self.data['model_id'] = self.bird_regional_sales['model_id']
        else:
            raise NotImplementedError

    def __set_content(self, query):
        query += '（不使用earlystop）'
        # print('new query:', query)
        self.data['messages'][0]['content'] = query

    def chat(self, query, scene='bird_car_retails', question_id=1000):
        self.__set_id(scene, question_id)
        self.__set_content(query)
        print('agent message:', self.data)
        response = requests.post(self.url, headers=self.headers, data=json.dumps(self.data))

        if response.status_code == 200:
            print('agent response:', response.json())
            result = (response.json())['data']['data'][0]['result']
            # result = response.json()
            return response.status_code, result
        else:
            print(response.status_code)
            result = response.json()
            return response.status_code, result


def dict2str(prompt):
    keys = ['description', 'input', 'output', 'summary']
    prompt_str = ''
    for k, v in prompt.items():
        if isinstance(v, dict):  # 如果值是字典，递归处理
            v = '\n' + dict2str(v)
        if k in keys:
            prompt_str += f'[{k}]:\n{v}\n'
        else:
            prompt_str += f'{k}: {v}\n'

    return prompt_str.strip()


prompt_template = r'''
[description]:
请对以下两段文本[Answer]和[GroundTruth]进行对比和分析，判断它们表述的内容是否一致。你需要从以下几个方面进行判断：
1. 数值一致性：检查两段文本中的所有数值是否一致。例如，日期、时间、金额、数量等。
2. 实体一致性：检查文本中提到的实体名称（如人名、地点名、公司名等）是否一致。

请在评估时，使用中文进行回答。
请注意，如果[Answer]部分的内容表述的是“很抱歉，我无法直接回答您的问题”，你不需要进行进一步各方面细致的评估，只需要在[output]和[summary]部分输出“模型无法回答问题，评估失败。”
尽量给出具体的对比结果，并指出不一致之处。如果一致，请说明一致的方面。


[input]:
Answer: __ANSWER__
GroundTruth: __GROUND_TRUTH__

[output]:
数值一致性: 
    result: 是/否
    details: 如果有不一致，请详细列出
实体一致性: 
    result: 是/否
    details: 如果有不一致，请详细列出

[summary]:
如果任何一个方面（数值一致性、实体一致性）显示为“不一致”，则判断这两段文本的内容不一致，输出结果：“错误”。
如果所有方面都一致，则判断这两段文本内容一致，输出结果：“正确”。

以下是一些判断示例:
[example]:
1. [input]:
Answer: 来自西班牙的顾客有7个？
GroundTruth: 共有7位来自西班牙的顾客

[output]:
数值一致性: 
    result: 是
    details: 数值信息都是“7个”，没有出错
实体一致性: 
    result: 是
    details: 实体信息都包含了“西班牙”和“顾客”，没有出现错误

[summary]:
本组文本评估的各个方面都一致，判断这两段文本内容一致。
输出结果：正确

2. [input]:
Answer: 顾客的名字是Jean King
GroundTruth: 顾客的名字是Jane Queen

[output]:
数值一致性: 
    result: 是
    details: 这两段文本中没有出现数值信息，所以没有出错
实体一致性: 
    result: 否
    details: 实体信息中顾客名字“Jean King”和“Jane Queen”不一致，出现不一致的错误

[summary]:
本组文本评估的实体信息不一致，判断这两段文本内容不一致。
输出结果：错误

3. [input]:
Answer: 产品编号为S12_1108的产品售出了7个
GroundTruth: 产品编号为S12_1108的产品售出了27个

[output]:
数值一致性: 
    result: 否
    details: 数值信息中“7”和“27”不一致，出现不一致的错误
实体一致性: 
    result: 是
    details: 实体信息中产品编号都是“S12_1108”，没有出现错误

[summary]:
本组文本评估的数值信息不一致，判断这两段文本内容不一致。
输出结果：错误

4. [input]:
Answer: 很抱歉，我无法直接查询到哪个客户的订单数最多。为了更好地帮助您，请您提供更详细的信息，例如具体的时间范围或订单状态等，以便我能够更准确地为您查询。
GroundTruth: Euro+ Shopping Channel发出的订单最多，一共有26笔

[output]:
模型无法回答问题，评估失败。

[summary]:
模型无法回答问题，评估失败。
输出结果：评估失败

请注意，你需要使用中文回答，并且回答一定要按照前面展示的[input]、[output]和[summary]的格式输出，并且在最后给出一个最终的输出结果，不要使用其他格式。
如果[Answer]部分的内容表述的意思是“很抱歉，我无法直接回答您的问题”，你不需要进行进一步各方面细致的评估，只需要在[output]和[summary]部分输出“模型无法回答问题，评估失败。”
'''

prompt_template_json = r'''
[description]:
请对以下两段文本[Answer]和[GroundTruth]进行对比和分析，判断它们表述的内容是否一致。你需要从以下几个方面进行判断：
1. 数值一致性(numerical)：检查两段文本中的所有数值是否一致。例如，日期、时间、金额、数量等。
2. 实体一致性(entity)：检查文本中提到的实体名称（如人名、地点名、公司名等）是否一致。

请在评估时，使用中文进行回答，但是要按照给定的JSON格式进行输出，且JSON中给定的键需要使用英文输出，同时要保证你输出的JSON内容可以被python代码解析，也就是你要将JSON的非字典值都用英文双引号包裹起来。
请注意，如果[Answer]部分的内容出现了“很抱歉”的字样或者表述的含义类似于是“很抱歉，我无法直接回答您的问题。如果您能提供更多详细信息或重新表述您的问题，我将很乐意帮助您。”，你不需要进行进一步各方面细致的评估，只需要在[output]部分输出“模型无法回答问题，评估失败。”
尽量给出具体的对比结果，并指出不一致之处。如果一致，请说明一致的方面。
如果评估结果为不一致或者无法评估，则应该在[output]的[result]部分输出字符串false，然后在[output]的[result]部分说明不一致的地方或者无法评估；
如果评估结果为一致，则应该在[output]的[result]部分输出字符串true，然后在[output]的[result]部分说明一致的方面。
最后在[summary]部分需要总结前面的两个方面是否都保持一致，如果都一致则在[result]部分输出字符串true，否则输出字符串false，然后在[details]部分说明详细的信息。
你的输出的[result]部分一定是字符串true或者false，不要是其他内容。

{
    "input": {
        "Answer": "__ANSWER__",
        "GroundTruth": "__GROUND_TRUTH__"
    },
    "output": {
        "numerical": {
            "result": "true/false",
            "details": "如果有不一致，请详细列出。如果[Answer]部分的内容出现了很抱歉的字样或者含义类似于很抱歉，我无法直接回答您的问题，则应该为评估失败"
        },
        "entity": {
            "result": "true/false",
            "details": "如果有不一致，请详细列出。如果[Answer]部分的内容出现了很抱歉的字样或者含义类似于很抱歉，我无法直接回答您的问题，则应该为评估失败"
        }
    },
    "summary": {
        "result": "true/false",
        "details": "如果任何一个方面（数值一致性、实体一致性）显示为不一致或者模型无法评估，则判断这两段文本的内容不一致，输出结果：false。如果所有方面都一致，则判断这两段文本内容一致，输出结果：true"
    }
}

以下是一些判断示例:
[example]:
1. 
{
    "input": {
        "Answer": "来自西班牙的顾客有7个",
        "GroundTruth": "共有7位来自西班牙的顾客"
    },
    "output": {
        "numerical": {
            "result": "true",
            "details": "数值信息都是7个，没有出错"
        },
        "entity": {
            "result": "true",
            "details": "实体信息都包含了西班牙和顾客，没有出现错误"
        }
    },
    "summary": {
        "result": "true",
        "details": "本组文本评估的各个方面都一致，判断这两段文本内容一致。输出结果：true"
    }
}

2. 
{
    "input": {
        "Answer": "顾客的名字是Jean King",
        "GroundTruth": "顾客的名字是Jane Queen"
    },
    "output": {
        "numerical": {
            "result": "true",
            "details": "这两段文本中没有出现数值信息，所以没有出错"
        },
        "entity": {
            "result": "false",
            "details": "实体信息中顾客名字Jean King和Jane Queen不一致，出现不一致的错误"
        }
    },
    "summary": {
        "result": "false",
        "details": "本组文本评估的实体信息不一致，判断这两段文本内容不一致。输出结果：false"
    }
}

3. 
{
    "input": {
        "Answer": "产品编号为S12_1108的产品售出了7个",
        "GroundTruth": "产品编号为S12_1108的产品售出了27个"
    },
    "output": {
        "numerical": {
            "result": "false",
            "details": "数值信息中7和27不一致，出现不一致的错误"
        },
        "entity": {
            "result": "true",
            "details": "实体信息中产品编号都是“S12_1108”，没有出现错误"
        }
    },
    "summary": {
        "result": "false",
        "details": "本组文本评估的数值信息不一致，判断这两段文本内容不一致。输出结果：false"
    }
}

4. 
{
    "input": {
        "Answer": "很抱歉，我无法直接查询到哪个客户的订单数最多。为了更好地帮助您，请您提供更详细的信息，例如具体的时间范围或订单状态等，以便我能够更准确地为您查询。",
        "GroundTruth": "Euro+ Shopping Channel发出的订单最多，一共有26笔"
    },
    "output": {
        "numerical": {
            "result": "false",
            "details": "模型无法回答问题，评估失败。"
        },
        "entity": {
            "result": "false",
            "details": "模型无法回答问题，评估失败。"
        }
        
    },
    "summary": {
        "result": "false",
        "details": "模型无法回答问题，评估失败。输出结果：false"
    }
}

请在评估时，使用中文进行回答，但是要按照给定的JSON格式进行输出，且JSON中给定的键需要使用英文输出，同时要保证你输出的JSON内容可以被python代码解析，也就是你要将JSON的非字典值都用英文双引号包裹起来。
请注意，如果[Answer]部分的内容出现了“很抱歉”的字样或者表述的含义类似于是“很抱歉，我无法直接回答您的问题。如果您能提供更多详细信息或重新表述您的问题，我将很乐意帮助您。”，你不需要进行进一步各方面细致的评估，只需要在[output]部分输出“模型无法回答问题，评估失败。”
尽量给出具体的对比结果，并指出不一致之处。如果一致，请说明一致的方面。
如果评估结果为不一致或者无法评估，则应该在[output]的[result]部分输出字符串false，然后在[output]的[result]部分说明不一致的地方或者无法评估；
如果评估结果为一致，则应该在[output]的[result]部分输出字符串true，然后在[output]的[result]部分说明一致的方面。
最后在[summary]部分需要总结前面的两个方面是否都保持一致，如果都一致则在[result]部分输出字符串true，否则输出字符串false，然后在[details]部分说明详细的信息。
你的输出的[result]部分一定是字符串true或者false，不要是其他内容。
'''


class PromptTemplate:
    def __init__(self):
        self.prompt_template = prompt_template_json
        # print(self.prompt_template)

    def set_prompt(self, answer='', ground_truth=''):
        prompt = self.prompt_template

        prompt = prompt.replace('__ANSWER__', answer)
        prompt = prompt.replace('__GROUND_TRUTH__', ground_truth)
        return prompt


if __name__ == '__main__':
    scenes = ['bird_car_retails', 'bird_regional_sales']
    agent = MyAgent()
    result = agent.test_curl()
    # result = agent.chat('购买渠道为Online的订单占全订单百分之多少？', scene='bird_car_retails')
    print(result)

    # prompt_template = PromptTemplate()
    # prompt = prompt_template.set_prompt('有多少客户的信用额度超过20万', '有20个')
    # print(prompt)
    # dataset_path = r'data/公开数据集汇总.csv'
    # f = ReadCSV(dataset_path, file_type=dataset_path.split('.')[-1])
    # data = f.get_data(end_row=5,
    #                   sceneName='sceneName',
    #                   questionID='questionID',
    #                   question='question',
    #                   groundTruth='groundTruth')
    #
    # print(data)
    # print(data['question'][0])

"""
error: 
18年各月通过Online购买方式下单的变化趋势（不使用earlystop）  bird_regional_sales
18年Clocks的订单数量趋势（不使用earlystop）  bird_regional_sales
购买渠道为Online的订单占全订单百分之多少？（不使用earlystop）  bird_car_retails

客户ID14的全称是什么？

"""

